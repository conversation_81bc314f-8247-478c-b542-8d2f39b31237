SERVER_BASE_URL=https://rebook-it.s3.us-east-1.amazonaws.com/
BOOKS_BASE_URL=https://openlibrary.org
secretKey=ojnnjjnijhuewhuew89x89ewzzzxiheq98y8392xuiniuewhu
PORT=4002
DB_URL=mongodb://localhost:27017/rebookit
RABBIT_MQ_SERVER=amqp://localhost
REDIS_HOST=localhost
REDIS_PORT=6379
STRIPE_API_KEY=sk_test_51RNr3iHBP1s03z5oBaPu8oW3QvIXRpTtziqvJsi7tdo0Een01dbrvwJ2JRx3O6oAmaWg8oBfDCA14SFhkqMSKHgK00Z7IfW4i7
STRIPE_WEBHOOK_SECRET=whsec_KwzJdyHeU7eXFK4RnEQp2kzjiqI3qaEb
FILE_SIZE=5 # 5mb
S3_ACCESS_KEY=********************
S3_SECRET_KEY=pTTJi4dp8M23z3cjXbxLhuycmcrlVsnW8ahNAjqK
S3_BUCKET_REGION=us-east-1
BUCKET_NAME=rebook-it
EMAIL_USER=<EMAIL>
SMS_GATEWAY_URL=
SMS_GATEWAY_TOKEN=
WHATSAPP_TOKEN=
WHATSAPP_PHONE_NUMBER_ID=
PAGE_SIZE=50
SUBSCRIPTION_NOTIFICATIONS='[ 1, 3, 7]'
HELPDESK_EMAIL=<EMAIL>
HELPDESK_PASSWORD=#heCr0$S198~6
SENDGRID_API_KEY=*********************************************************************
