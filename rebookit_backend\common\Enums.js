const PaymentStatusEnum = Object.freeze({
  PENDING: "pending",
  SUCCESS: "success",
  FAILED: "failed",
});

const ItemListStatusEnum = Object.freeze({
  PENDING: "pending",
  ACCEPTED: "accepted",
  REJECTED: "rejected",
  MARKED_AS_SOLD: "marked as sold",
  DELETED: "deleted", //deleted by end user
  ADMIN_DELETED: "removed by admin", // removed by admin for policy voilations
  EXPIRED: "expired", // for auto removal from listing usig cron jobs
});

const RebookerListTypeEnum = Object.freeze({
  YEARLY: "yearly",
  MONTHLY: "monthly",
  WEEKLY: "weekly",
  EVERYDAY: "everyday",
});

const ChatTypeEnum = Object.freeze({
  Buyers: "buyers",
  SELLERS: "sellers",
});

const RoleEnum = Object.freeze({
  ADMIN: "admin",
  USER: "user",
  EDITOR: "editor",
  VIEWER: "viewer",
  SUPERADMIN: "superadmin",
});

const PlanMonthsTypeEnum = Object.freeze({
  MONTHLY: "Monthly",
  QUARTERLY: "Quarterly",
  HALF_YEARLY: "Half-Yearly",
  YEARLY: "Yearly",
});

const PlanTypeEnum = Object.freeze({
  FREE: "free",
  PAID: "paid",
});

const PlanStatusEnum = Object.freeze({
  ACTIVE: "active",
  EXPIRED: "expired",
  QUEUED: "queued",
  UPGRADED: "upgraded",
});

const NotificationTypesEnum = Object.freeze({
  EMAIL: "email",
  SMS: "sms",
  WHATSAPP: "whatsapp",
});

const BookConditionEnum = Object.freeze({
  "Brand New": "brand new",
  Opened: "opened",
  "Like New:": "like new",
  "Very Good": "very good",
  Good: "good",
});

const SchoolTypeEnum = Object.freeze({
  PUBLIC: "public",
  PRIVATE: "private",
  INTERNATIONAL: "intetnamtional",
  CONVENT: "convent",
  GOVERNMENT: "government",
});

const ActivityTypeEnum = Object.freeze({
  PHYSICAL: "physical",
  CREATIVE: "creative",
  TECHNICAL: "technical",
  LEADERSHIP: "leadership",
  CULTURAL: "cultural",
  OTHERS: "others",
});

const FrequencyEnum = Object.freeze({
  DAILY: "daily",
  WEEKLY: "weekly",
  MONTHLY: "monthly",
  SEASONALLY: "seasonally",
  EVENT_BASED: "event-based",
});

const EventModeEnum = Object.freeze({
  ONLINE: "online",
  INPERSON: "inperson",
  HYBRID: "hybrid",
});

const QualificationTypeEnum = Object.freeze({
  HIGH_SCHOOL_DIPLOMA: "high_school_diploma", // Level 3 — High School Diploma
  SKILLS_CERTIFICATION: "skills_certification", // Level 4 — Skills Certification
  PROFESSIONAL_CERTIFICATION: "professional_certification", // Level 4 — Professional Certification
  ASSOCIATES_DEGREE: "associates_degree", // Level 5 — Associate's Degree
  BACHELOR: "bachelor", // Level 6 — Bachelor's Degree
  POSTGRAD_DIPLOMA: "postgrad_diploma", // Level 7 — Postgraduate Diploma/Certificate
  MASTER: "masters", // Level 8 — Master's Degree
  DOCTORATE: "doctorate", // Level 9 — Doctoral Degree
});

const TargetClassTypeEnum = Object.freeze({
  PRIMARY_1_TO_6: "primary 1 to 6", // compulsory primary school
  LOWER_SECONDARY_7_TO_9: "lower secondary 7 to 9",
  UPPER_SECONDARY_10_TO_11: "upper secondary 10 to 11", // CSEC exams
  SIXTH_FORM_12_TO_13: "sixth form 12 to 13", // CAPE / A‑levels
  DIPLOMA: "diploma", // post-secondary non-degree (e.g., HEART‑NTA, community colleges)
  ASSOCIATE_DEGREE: "associate degree", // regionally offered via CXC/HE institutions
  BACHELOR: "bachelor", // undergraduate degree at universities
  MASTER: "master", // postgraduate degree
  DOCTORATE: "doctorate", // terminal academic degree
});

const ExperienceTypeEnum = Object.freeze({
  LESS_THAN_ONE_YEAR: "<1 year",
  ONE_TO_TWO_YEARS: "1-2 years",
  TWO_TO_FIVE_YEARS: "2-5 years",
  FIVE_TO_TEN_YEARS: "5-10 years",
  TEN_TO_FIFTEEN_YEARS: "10-15 years",
  MORE_THAN_FIFTEEN_YEARS: ">15 years",
});

const itemListTypeEnum = Object.freeze({
  BOOK: "book",
  TUTOR: "tutor",
  SCHOOL: "school",
  EXTRACURRICULAR_ACTIVITY: "extracurricularActivity",
  EVENT: "event",
  SCHOLARSHIP_AND_AWARD: "scholarshipAndAward",
});

const ClassesOffered = Object.freeze({
  PRIMARY_1_TO_6: "primary 1 to 6", // compulsory primary school
  LOWER_SECONDARY_7_TO_9: "lower secondary 7 to 9",
  UPPER_SECONDARY_10_TO_11: "upper secondary 10 to 11", // CSEC exams
  SIXTH_FORM_12_TO_13: "sixth form 12 to 13", // CAPE / A‑levels
  DIPLOMA: "diploma", // post-secondary non-degree (e.g., HEART‑NTA, community colleges)
  ASSOCIATE_DEGREE: "associate degree", // regionally offered via CXC/HE institutions
  BACHELOR: "bachelor", // undergraduate degree at universities
  MASTER: "master", // postgraduate degree
  DOCTORATE: "doctorate", // terminal academic degree
});

const KindEnum = Object.freeze({
  BOOK_ITEM: "BookItem",
  TUTOR_ITEM: "TutorItem",
  SCHOOL_ITEM: "SchoolItem",
  EXTRACURRICULAR_ACTIVITY_ITEM: "ExtracurricularActivityItem",
  EVENT_ITEM: "EventItem",
  SCHOLARSHIP_AWARD_ITEM: "ScholarshipAwardItem",
});

const ParishesListEnum = Object.freeze({
  KINGSTON: "kingston",
  ST_ANDREW: "saint andrew",
  PORTLAND: "portland",
  ST_THOMAS: "saint thomas",
  ST_CATHERINE: "saint catherine",
  CLARENDON: "clarendon",
  MANCHESTER: "manchester",
  ST_ELIZABETH: "saint elizabeth",
  WESTMORELAND: "westmoreland",
  HANOVER: "hanover",
  ST_JAMES: "saint james",
  TRELAWNY: "trelawny",
  ST_ANN: "saint ann",
  ST_MARY: "saint mary",
});

const AdResourceTypeEnum = Object.freeze({
  BANNER: "banner",
  GRID: "grid",
});

const AdPageTypeEnum = Object.freeze({
  HOMEPAGE: "home page",
  SELLPAGE: "sell page",
  COMMUNITYPAGE: "community page",
  LISTINGPAGE: "listing page",
});

const AdPositionTypeEnum = Object.freeze({
  TOP: "top",
  MIDDLE: "middle",
  BOTTOM: "bottom",
  NOT_FIXED: "No fixed position",
});

const UserStatusEnum = Object.freeze({
  ACTIVE: "active",
  SUSPENDED: "suspended",
  DELTED: "deleted",
  PENDING: "pending",
});

const UnitEnum = Object.freeze({
  PCS: "pcs",
  HOUR: "hour",
  DAY: "day",
  MONTH: "month",
  YEAR: "year",
  PERSON: "person",
  TICKET: "ticket",
  PACK: "pack",
  SESSION: "session",
});

module.exports = {
  ItemListStatusEnum,
  RebookerListTypeEnum,
  ChatTypeEnum,
  RoleEnum,
  PaymentStatusEnum,
  PlanMonthsTypeEnum,
  PlanTypeEnum,
  PlanStatusEnum,
  NotificationTypesEnum,
  BookConditionEnum,
  SchoolTypeEnum,
  ActivityTypeEnum,
  FrequencyEnum,
  EventModeEnum,
  itemListTypeEnum,
  QualificationTypeEnum,
  TargetClassTypeEnum,
  ExperienceTypeEnum,
  ClassesOffered,
  KindEnum,
  ParishesListEnum,
  AdResourceTypeEnum,
  AdPageTypeEnum,
  AdPositionTypeEnum,
  UserStatusEnum,
  UnitEnum,
};
