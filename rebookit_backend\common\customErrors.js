class BadRequestError extends <PERSON>rror {
  constructor(message = "Bad Request") {
    super(message);
    this.statusCode = 400;
  }
}

class UnauthorizedError extends Error {
  constructor(message = "Unauthorized access") {
    super(message);
    this.statusCode = 401;
  }
}
// this error is only router not found error, for resource not found please use badRequestError
class NotFoundError extends Error {
  constructor(message = "Not found") {
    super(message);
    this.statusCode = 404;
  }
}

class InternalServerError extends Error {
  constructor(message = "Internal Server Error") {
    super(message);
    this.statusCode = 500;
  }
}

class ForbiddenError extends Error {
  constructor(message = "limit exceeded for your subscription plan") {
    super(message);
    this.statusCode = 403;
  }
 
}
module.exports = {
  BadRequestError,
  InternalServerError,
  UnauthorizedError,
  NotFoundError,
  ForbiddenError
};
