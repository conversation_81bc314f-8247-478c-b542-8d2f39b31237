const adService = require("../services/ad-management.service");


const createAdResources = async (req, res) =>  await adService.createAdResource(req.body);
const getAdResources = async (req, res) => await adService.getAllAdResources();
const getAdResourcesById = async (req, res) => await adService.getAdResourcesById(req.params.id);
const updateAdResources = async (req, res) =>  await adService.updateAdResource(req.params.id, req.body);
const deleteAdResources = async (req, res) =>  await adService.deleteAdResource(req.params.id);
const updateBasePrices = async (req, res) => await adService.updateBasePrice(req.body);
const setPriceRules = async (req, res) => await adService.setPriceRule(req.body);
const updatePriceRules = async (req, res) => await adService.updatePriceRule(req.body);
const deletePriceRules = async (req, res) => await adService.deletePriceRule(req.body);
const setOverrideRules = async (req, res) => await adService.setOverrideRule(req.body);
const updateOverrideRules = async (req, res) => await adService.updateOverrideRule(req.body);
const deleteOverrides = async (req, res) => await adService.deleteOverride(req.body);

const createAds = async (req, res) => await adService.createAd(req.user, req.body);
const createCompaigns = async (req, res) => await adService.createCompaign(req.body);
const updateAdStatus = async(req, res) => await adService.updateAdStatus(req.params.id, req.body);
const createAdPaymentIntent = async(req, res) => await adService.createPaymentIntent(req.body);
const webhook = async(req, res) => await adService.webhook(req);
const getAdPlans = async(req, res) => await adService.getAdPlansService(req.query.type, req.query.position, req.query.page=1, req.query.limit=10);
const getAdPlanById = async (req, res) => await adService.getAdPlan(req.params.id);

module.exports = {
 createAdResources,
 getAdResources,
 getAdResourcesById,
 updateAdResources,
 deleteAdResources,
 updateBasePrices,
 setPriceRules,
 updatePriceRules,
 deletePriceRules,
 setOverrideRules,
 updateOverrideRules,
 deleteOverrides,
 createAds,
 createCompaigns,
 updateAdStatus,
 createAdPaymentIntent,
 webhook,
 getAdPlans,
 getAdPlanById
};
