const adminService = require("../services/admin.service");
const {
  uploadMultipleFileToS3,
  uploadFileToS3,
} = require("../utils/fileUpload");

const uploadSingleFile = async (req, res) => await uploadFileToS3(req.file);
const uploadMultipleFile = async (req, res) => await uploadMultipleFileToS3(req.files);
const getAdminDetails = async (req, res) => await adminService.getAdminById(req.user._id);
const createSubscriptionPlan = async (req, res) => await adminService.addSubscriptionPlan(req.body);
const getSubscriptionPlans = async (req, res) => await adminService.subscriptionPlansList();
const updateSubscriptionPlan = async (req, res) => await adminService.updateSubscriptionPlan(req.params.id, req.body);
const filterMembers = async ( req, res) => await adminService.filterMembers(req.body, req.query.page, req.query.pageSize);

const itemStatusUpdate = async (req, res) => await adminService.modifyItemStatus(req.params.id, req.body);
const deleteItem = async (req, res) => await adminService.deleteListedItem(req.params.id, req.query.hard);

const getUsers = async (req, res) => await adminService.getUsers(req.body, req.query.page, req.query.pageSize);
const getUserById = async ( req, res) => await adminService.getUserById( req.params.id);


// DASHBOARD ROUTES
const getOverview = async (req, res) => await adminService.getOverview(req.user._id); // need to REDO

const getLastMonthSubscribers = async (req, res) => await adminService.getLastMonthSubscription(req.params.thisMonthStartDate, req.params.thisMonthEndDate, req.params.lastMonthStartDate, req.params.lastMonthEndDate);

const getTotalAdRequest = async (req, res) => await adminService.getTotalBannerAd();


// TESTIMONIAL
const createTestimonials = async (req, res) => await adminService.createTestimonial(req.body);
const updateTestimonials = async (req, res) => await adminService.updateTestimonial(req.params.id, req.body);
const deleteTestimonials = async (req, res) => await adminService.deleteTestimonial(req.params.id);
const getTestimonials = async (req, res) => await adminService.getTestimonial();


module.exports = {
  getAdminDetails,

  createSubscriptionPlan,
  getSubscriptionPlans,
  updateSubscriptionPlan,
  filterMembers,

  uploadSingleFile,
  uploadMultipleFile,
  itemStatusUpdate,
  deleteItem,

  getUsers,
  getUserById,
  getOverview,
  getLastMonthSubscribers,
  getTotalAdRequest,
  createTestimonials,
  updateTestimonials,
  deleteTestimonials,
  getTestimonials
};
