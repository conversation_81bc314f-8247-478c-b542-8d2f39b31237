const { socketService } = require("../services/chat.service.js");

const getAllChats = async (req, res) => await socketService.getAllChats(req.user._id, req.body);
const getChatById = async (req, res) => await socketService.getChatById(req.user, req.params.id, req.query.page);
const deleteChat = async (req, res) => await socketService.deleteChat(req.user, req.params.id);
const getChatByItemId = async (req, res) => await socketService.getChatByItemId(req.user, req.params.id, req.query.page);
module.exports = {
  getAllChats,
  getChatById,
  deleteChat,
  getChatByItemId
};
