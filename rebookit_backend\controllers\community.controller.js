const communityService = require("../services/community.service");

const addQuestion = async (req, res) => await communityService.postQuestion(req.user, req.body);
const submitAnswer = async (req, res) => await communityService.responseToQuestion(req.user, req.body);
const editAnswer = async ( req, res) => await communityService.editAnswer( req.user, req.body, req.query.answerId);
const getUserQuestions = async (req, res) => await communityService.getUserQuestions(req.user, req.query.page, req.query.pageSize, req.query.searchTerm);
const getAllQuestions = async (req, res) => await communityService.getAllQuestions(req.query.userId, req.query.page, req.query.pageSize,req.query.categoryId, req.query.searchTerm, req.query.sort);
const getUserQuestionsWithAnswers = async (req, res) => await communityService.getUserQuestionsWithAnswers(req.user, req.query.page, req.query.pageSize, req.query.searchTerm);
const getUserAnswers = async (req, res) => await communityService.userAnswers(req.user, req.query.page, req.query.pageSize, req.query.searchTerm);
const fetchQuestionAndResponses = async (req, res) => await communityService.retrieveQuestionAndResponses(req.params.id);
const deleteQuestion = async (req, res) => await communityService.removeQuestion(req.user,req.params.id);
const deleteAnswer = async (req, res) => await communityService.removeAnswer(req.user, req.params.id);

module.exports = {
  addQuestion,
  submitAnswer,
  editAnswer,
  getUserQuestions,
  getAllQuestions,
  getUserQuestionsWithAnswers,
  getUserAnswers,
  deleteQuestion,
  deleteAnswer,
  fetchQuestionAndResponses,
};
