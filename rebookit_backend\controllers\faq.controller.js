const FaqService = require("../services/faq.service");

const addFaq = async (req, res) => await FaqService.addFaq(req.user, req.body);
const filterFaqs = async (req, res) => await FaqService.filterFaqs(req.user, req.body, req.query.page, req.query.pageSize);
const updateFaq = async (req, res) => await FaqService.updateFaq( req.params.id, req.body);
const deleteFaq = async (req, res) => await FaqService.deleteFaq(req.params.id);

module.exports = {
  addFaq,
  filterFaqs,
  updateFaq,
  deleteFaq,
};
