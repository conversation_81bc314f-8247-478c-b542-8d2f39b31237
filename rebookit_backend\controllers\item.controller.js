const itemService = require("../services/item.service");

const addItem = async (req, res) => await itemService.listItem(req.user, req.body);
const searchItems = async (req, res) => await itemService.searchItems(null, req.body, req.query.page, req.query.pageSize, req.query.userId);
const searchItemsAdmin = async (req, res) => await itemService.searchItems(req.user, req.body, req.query.page, req.query.pageSize, null);
const searchItemsCurrentUser =  async (req, res) => await itemService.searchItems(req.user, req.body, req.query.page, req.query.pageSize, null);
const getAutoCompleteItems = async (req, res) => await itemService.getAutoCompleteItems(req.query.page, req.query.pageSize, req.query.searchTerm);
const getItemById = async (req, res) => await itemService.getListedItemById(req.params.id, req.query.userId);
const updateItem = async (req, res) => await itemService.updateItem(req.user, req.params.id, req.body);
const boostItem = async (req, res) => await itemService.boostItem(req.user, req.params.id);
const getNearbyTutors = async ( req, res) => await itemService.getNearbyTutors( req.query.userId);

const itemBookmarked = async (req, res) => await itemService.bookmarkAnItem(req.user, req.body);
const bookmarksList = async (req, res) => await itemService.listOfBookmarks(req.user._id);
const removeBookmark = async (req, res) => await itemService.bookmarkRemoved(req.user._id, req.params.id);

const addReview = async (req, res) => await itemService.addReview_service(req.user, req.body);
const GetReviews = async (req, res) => await itemService.getUserReviews(req.params.userId);

module.exports = {
    addItem,
    searchItems,
    searchItemsAdmin,
    searchItemsCurrentUser,
    getAutoCompleteItems,
    getItemById,
    updateItem,
    getNearbyTutors,

    itemBookmarked,
    bookmarksList,
    removeBookmark,
    
    addReview,
    GetReviews,
    boostItem
};
