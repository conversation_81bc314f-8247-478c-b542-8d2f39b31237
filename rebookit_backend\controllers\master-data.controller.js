const MasterdataService = require("../services/master-data.service");

const addCategory = async (req, res) => await MasterdataService.addCategory(req.user, req.body);
const addSubcategory = async (req, res) => await MasterdataService.addSubCategory(req.user, req.body);
const addSubSubCategory = async (req, res) => await MasterdataService.addSubSubCategory(req.user, req.body);
const addSubSubSubCategory = async (req, res) => await MasterdataService.addSubSubSubCategory(req.user, req.body);

const getCategories = async (req, res) => await MasterdataService.getCategories();
const getSubCategories = async (req, res) => await MasterdataService.getSubCategories(req.params.id);
const getSubSubCategories = async (req, res) => await MasterdataService.getSubSubCategories(req.params.id);
const getSubSubSubCategories = async (req, res) => await MasterdataService.getSubSubSubCategories(req.params.id);

const editCategory = async (req, res) => await MasterdataService.editCategory(req.params.id, req.body);
const editSubCategory = async (req, res) => await MasterdataService.editSubCategory(req.params.id, req.body);
const editSubSubCategory = async (req, res) => await MasterdataService.editSubSubCategory(req.params.id, req.body);
const editSubSubSubCategory = async (req, res) => await MasterdataService.editSubSubSubCategory(req.params.id, req.body);

const deleteCategory = async (req, res) => await MasterdataService.deleteCategory(req.params.id);
const deleteSubCategory = async (req, res) => await MasterdataService.deleteSubCategory(req.params.id);
const deleteSubSubCategory = async (req, res) => await MasterdataService.deleteSubSubCategory(req.params.id);
const deleteSubSubSubCategory = async (req, res) => await MasterdataService.deleteSubSubSubCategory(req.params.id);

module.exports = {
  addCategory,
  addSubcategory,
  addSubSubCategory,
  addSubSubSubCategory,

  getCategories,
  getSubCategories,
  getSubSubCategories,
  getSubSubSubCategories,

  editCategory,
  editSubCategory,
  editSubSubCategory,
  editSubSubSubCategory,

  deleteCategory,
  deleteSubCategory,
  deleteSubSubCategory,
  deleteSubSubSubCategory,

};
