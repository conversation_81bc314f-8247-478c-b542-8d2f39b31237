const statusCodes = require("../common/statusCodes");
const paymentService= require("../services/payment.service");
const Stripe = require("stripe");
const stripe = new Stripe(process.env.STRIPE_API_KEY);


const createPaymentIntent = async ( req, res) => await paymentService.createPaymentIntent( req.user, req.body);
const handleWebhook = async ( req, res) => await paymentService.handleWebhook( req.headers, req.body);
const paymentHistory = async ( req, res ) => await paymentService.getPaymentHistory( req.user );
const queryTranscations = async ( req, res) => await paymentService.queryTranscations( req.query, req.query.page, req.query.pageSize);
module.exports = {
    queryTranscations,
    handleWebhook,
    createPaymentIntent,
    paymentHistory
}





