const UserService = require("../services/user.service");

const GetUser = async (req, res) => await UserService.UserInfo(req?.query?.id || req.user._id)
const RegisterUser = async (req) => await UserService.UserRegistration(req.body);
const LoginUser = async (req) =>  await UserService.UserLogin(req.body);
const SendOTP = async (req, res) => await UserService.SendUserOtp(req.body);
const VerifyOTP = async (req, res) => await UserService.VerifyUserOTP(req.body)
const VerifyUserEmailOrMobile = async (req, res) => await UserService.VerifyUserData({ user: req.user, ...req.body })
const ForgotPassword = async (req, res) => await UserService.PasswordForgot(req.body);
const verifyForgotPasswordOtp = async( req, res) => await UserService.verifyForgotPasswordOtp( req.body);
const VerifyAndUpdatePassword = async (req, res) => await UserService.resetPassword(req.body)
const UserEditProfile = async (req, res) =>  await UserService.UpdateProfile({ user: req.user, body : req.body})
const GetPlans = async( req ) => await UserService.getPlans( req.user);

const RebookerOfTheMonth = async (req, res) => await UserService.RebookerOfTheMonth();
const GetTestimonials = async (req, res) => await UserService.GetTestimonials();
const updateUser = async ( req, res) => await UserService.updateUser( req.user, req.params.id, req.body);
const SupportRequest = async (req, res) => await UserService.SupportRequest(req.body);
const getAllSupport = async (req, res) => await UserService.getAllSupportRequest(req.body,req.query);
const updateSupportRequest = async (req, res) => await UserService.updateSupportRequest(req.body,req.query);


module.exports = {
    SendOTP,
    VerifyOTP,
    RegisterUser,
    GetUser,
    LoginUser,
    ForgotPassword,
    verifyForgotPasswordOtp,
    VerifyAndUpdatePassword,
    UserEditProfile,
    VerifyUserEmailOrMobile,
    GetPlans,
    RebookerOfTheMonth,
    GetTestimonials,
    updateUser,
    SupportRequest,
    getAllSupport,
    updateSupportRequest
};
