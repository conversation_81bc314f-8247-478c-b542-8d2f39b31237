const cron = require("node-cron");
const handlePlanExpiration = require("./handlers/plan-expiration");
const handleSendReminder = require("./handlers/reminder");
const handleActiveQueuedPlan = require("./handlers/active-queued-plan");
const handleItemExpiration = require("./handlers/item-expiration");

//plan expiration - runs every half an hour
cron.schedule('0 */30 * * * *', async () => {
  try {
    console.log("-------------------Plan expiration cron job executed---------------------------");
    await handlePlanExpiration();
  } catch (error) {
    console.log("error in plan expiration cron job");
    console.error(error);
  }
}, 
{
  timezone: 'America/Jamaica'
});

//send subscription expiration reminder - runs every half an hour
// */30 * * * * *
cron.schedule("0 */30 * * * *",  async () => {
  try {
    console.log("-------------------Send reminder cron job executed---------------------------");
    await handleSendReminder();
  } catch (error) {
    console.log("error in send subscription expiration reminder cron job");
    console.error(error);
  }
}, 
{
  timezone: 'America/Jamaica'
});

//auto renew plan. - runs every half an hour
cron.schedule("0 */30 * * * *", async () => {
  try {
    console.log("-------------------Auto renew plan cron job executed---------------------------");
    await handleActiveQueuedPlan();
  } catch (error) {
    console.log("error in auto renew plan cron job.");
    console.error(error);
  }
}, 
{
  timezone: 'America/Jamaica'
});


//expire item that has exceeded their validity - 
cron.schedule("0 */35 * * * *", async () => {
  try {
    console.log("-------------------expire item cron job executed---------------------------");
    await handleItemExpiration();
  } catch (error) {
    console.log("error in expire item cron job.");
    console.error(error);
  }
}, 
{
  timezone: 'America/Jamaica'
});

