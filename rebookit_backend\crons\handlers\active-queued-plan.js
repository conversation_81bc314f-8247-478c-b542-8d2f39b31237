const subscriptionModel = require("../../tables/schema/subscription");
const { PlanStatusEnum } = require("../../common/Enums");
const { sendPlanActiveEmail } = require("../../utils/mail");
const { sendData } = require("../../queue/configuration");
const { queueName, NotificationEventName } = require("../../queue/queueNames");

async function handleActiveQueuedPlan() {
  const now = new Date();

    // Find all subscriptions that are queued and whose startDate <= now
    const subsToActivate = await subscriptionModel
      .find({
        status: PlanStatusEnum.QUEUED,
        startDate: { $lte: now },
      })
      .populate({
        path: "planId",
        select: {
          _id: 1,
          planName: 1,
        },
      })
      .populate({
        path: "userId",
        select: {
          _id: 1,
          email: 1,
          firstName: 1
        },
      });

  console.log(
    `Found ${subsToActivate.length} queued subscriptions to activate.`
  );

  if (!subsToActivate.length) return;

  const ids = subsToActivate.map((s) => s._id);
  await subscriptionModel.updateMany(
    { _id: { $in: ids } },
    { $set: { status: PlanStatusEnum.ACTIVE } }
  );

    for (const sub of subsToActivate) {
      await sendData(queueName["NOTIFICATION_QUEUE"], {
        EventName: NotificationEventName.ACTIVE_QUEUED_PLAN,
        data: {
          _id: sub?.userId._id,
          body: sub,
        },
      });
      console.log(`Activation email sent to ${sub.userId.email}`);
    }
}

module.exports = handleActiveQueuedPlan;
