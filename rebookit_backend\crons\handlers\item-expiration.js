const { ItemListStatusEnum } = require("../../common/Enums");
const { itemlistModel } = require("../../tables/schema/itemList");

async function handleItemExpiration() {
  const result = await itemlistModel.updateMany({
    status: {
      $in: [
        ItemListStatusEnum.ACCEPTED
      ]
    }, expireAt: { $lte: new Date() }
  }, { $set: { status: ItemListStatusEnum.EXPIRED, isActive: false } });
  console.log(`${result.modifiedCount} items expired.`);
}

module.exports = handleItemExpiration;
