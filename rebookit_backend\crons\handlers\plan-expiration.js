const subscriptionModel = require("../../tables/schema/subscription");
const { PlanTypeEnum, PlanStatusEnum } = require("../../common/Enums");
const subscriptionPlanModel = require("../../tables/schema/subscription_plans");
const { calculateMonthCount } = require("../../services/payment.service");
const moment = require("moment");

async function handlePlanExpiration() {
  const foundExpiredSubscriptions = await subscriptionModel.find({
    status: PlanStatusEnum.ACTIVE,
    endDate: {
      $lte: new Date(),
    },
  });

  console.log(`${foundExpiredSubscriptions.length} expired subscription found`);
  if (foundExpiredSubscriptions.length > 0) {
    await subscriptionModel.updateMany(
      {
        _id: {
          $in: foundExpiredSubscriptions.map((o) => o._id),
        },
      },
      {
        $set: {
          status: PlanStatusEnum.EXPIRED,
        },
      }
    );
    const freeSubscriptionPlan = await subscriptionPlanModel.findOne({
      planType: PlanTypeEnum.FREE,
    });

    const freeExpiredSubscription = foundExpiredSubscriptions.filter((sub) => sub.planId.toString() == freeSubscriptionPlan._id.toString());
    const userSubsWithNoQueuedPlan = [];
    for (const currentSub of foundExpiredSubscriptions) {
      const foundQueuedPlan = await subscriptionModel.findOne({ userId : currentSub.userId, status: PlanStatusEnum.QUEUED});
      if(!foundQueuedPlan) userSubsWithNoQueuedPlan.push(currentSub)
    }
    
    const planForRenewing = [ ...userSubsWithNoQueuedPlan, ...freeExpiredSubscription]
   
    if (planForRenewing.length > 0) {
      const subscriptionPlans = [];
      for (const sub of planForRenewing) {

        const monthCount = calculateMonthCount(freeSubscriptionPlan.planMonths);
        const endDate = moment().add(monthCount, "M").toDate();

        const newSubscription = {
          userId: sub.userId,
          planId: freeSubscriptionPlan._id,
          startDate: moment().toDate(),
          endDate: endDate,
          status: PlanStatusEnum.ACTIVE
        };
        subscriptionPlans.push(newSubscription);
      }
      const activatedSubs = await subscriptionModel.insertMany(subscriptionPlans);
      console.log(activatedSubs.length + "Free plans renewed");
    }
  }

}

module.exports = handlePlanExpiration;
