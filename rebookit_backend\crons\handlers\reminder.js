const subscriptionModel = require("../../tables/schema/subscription");
const { PlanStatusEnum } = require("../../common/Enums");
const { sendData } = require("../../queue/configuration");
const { NotificationEventName, queueName } = require("../../queue/queueNames");

async function handleSendReminder() {
  const subsToRemind = await subscriptionModel.aggregate([
    {
      $match: {
        endDate: {
          $lte: new Date(),
        },
      },
    },
    { $unwind: "$notifications" },
    {
      $match: {
        "notifications.sendAt": {
          $lte: new Date(),
        },
        "notifications.isSent": false,
      },
    },
    {
      $lookup: {
        from: "users",
        foreignField: "_id",
        localField: "userId",
        as: "userDoc",
      },
    },
    {
      $lookup: {
        from: "subscription_plans",
        foreignField: "_id",
        localField: "planId",
        as: "subscriptionPlanDoc",
      },
    },
    { $unwind: { path: "$userDoc" } },
    { $unwind: { path: "$subscriptionPlanDoc" } },
  ]);

  console.log(`Found ${subsToRemind.length} subscriptions expiring soon.`);
  if (!subsToRemind.length) return;

  for (const sub of subsToRemind) {
    console.log("current Subscription", sub);
    sendData(queueName["NOTIFICATION_QUEUE"], {
      EventName: NotificationEventName.SUBSCRIPTION_GETTING_EXPIRE,
      data: {
        email: sub.userDoc.email,
        userName: sub.userDoc.firstName,
        planName: sub.subscriptionPlanDoc.planName,
        dayBefore: sub.notifications.dayBefore,
        endDate: sub.endDate,
      },
    });
    await subscriptionModel.findOneAndUpdate(
      { _id: sub._id, "notifications._id": sub.notifications._id },
      {
        $set: {
          "notifications.$.isSent": true,
        },
      }
    );
  }
}

module.exports = handleSendReminder;
