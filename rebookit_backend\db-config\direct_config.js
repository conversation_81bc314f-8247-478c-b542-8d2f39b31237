const mongoose = require('mongoose');
mongoose.set('debug', true)
const dotenv = require('dotenv');
const LoggerFactory = require('../logger/logger-factory.js');


dotenv.config({})

function createConnection() {
    const dbConfigLogger = LoggerFactory.createChildLogger({ name :"dbconfig"})
    mongoose.connect(process.env.DB_URL).then(() => {
        dbConfigLogger.info('Database connected');
    }).catch((err) => {
        dbConfigLogger.error('Mongoose connection error:', err);
    });
}

module.exports = {
    createConnection,

}



