const { UnitEnum } = require("../common/Enums");

module.exports = {
  components: {
    securitySchemes: {
      bearerAuth: {
        type: "http",
        scheme: "bearer",
        bearerFormat: "JWT",
        in: "header",
      },
    },
    schemas: {
      sendotp: {
        type: "object",
        properties: {
          email: {
            type: "string",
            description: "Input your email",
            example: "<EMAIL>",
          },
          codeType: {
            type: "string",
            description: "Type of code to be sent",
            example: "verification",
            enum: ["verification", "verification_on_listing"],
          },
        },
      },
      BaseItem: {
        type: "object",
        required: ["title", "categoryId", "address", "kind"],
        properties: {
          title: {
            type: "string",
            description: "Title of the item",
            example: "Advanced Mathematics Tutor",
          },
          categoryId: {
            type: "string",
            description: "ID of the category",
            example: "60d5f484f1b2c8b1a8b3c4d5",
          },
          subCategoryId: {
            type: "string",
            description: "ID of the sub-category",
            example: "60d5f484f1b2c8b1a8b3c4d6",
          },
          subSubCategoryId: {
            type: "string",
            description: "ID of the sub-sub-category",
            example: "60d5f484f1b2c8b1a8b3c4d7",
          },
          subSubSubCategoryId: {
            type: "string",
            description: "ID of the sub-sub-sub-category",
            example: "60d5f484f1b2c8b1a8b3c4d8",
          },
          description: {
            type: "string",
            description: "Detailed description of the item",
            example: "Experienced tutor specializing in advanced mathematics.",
          },
          price: {
            type: "integer",
            description: "Price of the item",
            example: 100,
          },
          unit: {
            type: "string",
            description: "unit of the item",
            enum: Object.values(UnitEnum),
            example: "pcs",
          },
          tags: {
            type: "array",
            items: {
              type: "string",
              example: "mathematics, tutoring",
            },
          },
          images: {
            type: "array",
            items: {
              type: "string",
              format: "uri",
              example: "https://example.com/image1.jpg",
            },
          },
          address: {
            $ref: "#/components/schemas/EmbeddedAddress",
          },
          kind: {
            type: "string",
            enum: ["BookItem", "TutorItem", "SchoolItem", "ExtracurricularActivityItem", "EventItem", "ScholarshipAwardItem"],
          },
        },
      },
      EmbeddedAddress: {
        type: "object",
        required: ["formatted_address", "geometry", "country", "parish", "administrative_area_level_1"],
        properties: {
          formatted_address: {
            type: "string",
            example: "144-41 Sutphin Blvd, Jamaica, NY 11435, USA",
          },
          geometry: {
            $ref: "#/components/schemas/EmbeddedGeometry",
          },
          country: {
            type: "string",
            example: "United States",
          },
          parish: {
            type: "string",
            enum: [
              "kingston",
              "saint andrew",
              "saint catherine",
              "saint james",
              "clarendon",
              "manchester",
              "westmoreland",
              "hanover",
              "saint elizabeth",
              "trelawny",
              "saint ann",
              "mary",
              "portland",
              "saint thomas",
            ],
            example: "kingston",
          },
          administrative_area_level_1: {
            type: "string",
            example: "New York",
          },
        },
      },
      EmbeddedGeometry: {
        type: "object",
        properties: {
          location: {
            type: "object",
            properties: {
              type: {
                type: "string",
                enum: ["Point"],
                example: "Point",
              },
              coordinates: {
                type: "array",
                items: { type: "number" },
                example: [-73.456, 40.789],
                minItems: 2,
                maxItems: 2,
              },
            },
          },
        },
      },
      BookItem: {
        allOf: [
          { $ref: "#/components/schemas/BaseItem" },
          {
            type: "object",
            required: ["isbn_number", "authors", "condition"],
            properties: {
              kind: {
                type: "string",
                enum: ["BookItem"],
              },
              isbn_number: {
                type: "string",
                example: "978-1234567890",
              },
              authors: {
                type: "array",
                items: { type: "string" },
                example: ["J.K. Rowling"],
              },
              condition: {
                type: "string",
                enum: ["New", "Like New", "Used"],
                example: "New",
              },
            },
          },
        ],
      },
      TutorItem: {
        allOf: [
          { $ref: "#/components/schemas/BaseItem" },
          {
            type: "object",
            required: ["highestQualification", "targetClasses", "experience"],
            properties: {
              kind: {
                type: "string",
                enum: ["TutorItem"],
              },
              highestQualification: {
                type: "string",
                enum: ["Diploma", "Bachelor", "Master", "PhD"],
                example: "Master",
              },
              targetClasses: {
                type: "string",
                enum: ["Primary", "High School", "University"],
                example: "High School",
              },
              experience: {
                type: "string",
                enum: ["<1 year", "1-3 years", "3-5 years", "5+ years"],
                example: "5+ years",
              },
              website: {
                type: "string",
                example: "https://tutor-example.com",
              },
            },
          },
        ],
      },
      SchoolItem: {
        allOf: [
          { $ref: "#/components/schemas/BaseItem" },
          {
            type: "object",
            required: ["schoolType", "classesOffered"],
            properties: {
              kind: {
                type: "string",
                enum: ["SchoolItem"],
              },
              schoolType: {
                type: "string",
                enum: ["public", "private", "international", "convent", "government"],
                example: "public",
              },
              classesOffered: {
                type: "string",
                enum: [
                  "primary 1 to 6",
                  "lower secondary 7 to 9",
                  "upper secondary 10 to 11",
                  "sixth form 12 to 13",
                  "diploma",
                  "associate degree",
                  "bachelor",
                  "master",
                  "doctorate",
                ],
                example: "primary 1 to 6",
              },
              website: {
                type: "string",
                example: "https://school-example.com",
              },
            },
          },
        ],
      },
      ExtracurricularActivityItem: {
        allOf: [
          { $ref: "#/components/schemas/BaseItem" },
          {
            type: "object",
            required: ["activityType", "frequency", "targetStudents"],
            properties: {
              kind: {
                type: "string",
                enum: ["ExtracurricularActivityItem"],
              },
              activityType: {
                type: "string",
                example: "sports",
              },
              frequency: {
                type: "string",
                example: "weekly",
              },
              targetStudents: {
                type: "string",
                example: "all",
              },
            },
          },
        ],
      },
      EventItem: {
        allOf: [
          { $ref: "#/components/schemas/BaseItem" },
          {
            type: "object",
            required: ["eventStartDate", "eventEndDate", "eventMode"],
            properties: {
              kind: {
                type: "string",
                enum: ["EventItem"],
              },
              eventStartDate: {
                type: "string",
                format: "date-time",
                example: "2024-06-01T10:00:00Z",
              },
              eventEndDate: {
                type: "string",
                format: "date-time",
                example: "2024-06-01T12:00:00Z",
              },
              eventMode: {
                type: "string",
                example: "online",
              },
              website: {
                type: "string",
                example: "https://event-example.com",
              },
            },
          },
        ],
      },
      ScholarshipAwardItem: {
        allOf: [
          { $ref: "#/components/schemas/BaseItem" },
          {
            type: "object",
            required: ["scholarshipType", "eligibilityCriteria"],
            properties: {
              kind: {
                type: "string",
                enum: ["ScholarshipAwardItem"],
              },
              scholarshipType: {
                type: "string",
                example: "Merit",
              },
              eligibilityCriteria: {
                type: "string",
                example: "GPA > 3.5",
              },
              website: {
                type: "string",
                example: "https://scholarship-example.com",
              },
            },
          },
        ],
      },
    },
  },
};
