module.exports = {
  "/api/ad-management/": {
    parameters: [],
    post: {
      tags: ["ad"],
      summary: "create-adResource",
      description: "This api can be used to create an adResource",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                type: {
                  type: "string",
                  description: "Type of the ad resource",
                  example: "banner",
                  enum: ["grid", "banner"],
                },
                page: {
                  type: "string",
                  description: "Page where the ad will be displayed",
                  example: "home page",
                  enum: ["home page", "sell page", "community page", "listing page"],
                },
                position: {
                  type: "string",
                  description: "Position of the ad on the page",
                  example: "top",
                  enum: ["top", "middle", "bottom"],
                },
                basePrice: {
                  type: "string",
                  description: "Base price of the ad resource",
                  example: "1000",
                },
              },
            },
          },
        },
      },
    },
    get: {
      tags: ["ad"],
      summary: "getAll-adResource",
      responses: {},
    },
  },
  "/api/ad-management/6870c92e5a4c34c76a63332f": {
    parameters: [],
    put: {
      tags: ["ad"],
      summary: "update-adResource",
      description: "This api can be used to update an adResource",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                basePrice: {
                  type: "string",
                  description: "Base price of the ad resource",
                  example: "1000",
                },
              },
            },
          },
        },
      },
    },
  },
  "/api/ad-management/6870c9475a4c34c76a633339": {
    parameters: [],
    delete: {
      tags: ["ad"],
      summary: "delete-adResource",
      description: "This api can be used to delete an adResource",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
            },
          },
        },
      },
    },
  },
  "/api/ad-management/6874b62c47a2915a6ae0b92d": {
    parameters: [],
    get: {
      tags: ["ad"],
      summary: "getById-adResource",
      description: "This api can be used to get the detail of an adResource",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
  },
  "/api/ad-management/set-price/6874b62c47a2915a6ae0b92d": {
    parameters: [],
    post: {
      tags: ["ad"],
      summary: "setPrice-in-calender",
      description: "This api can be used to set the price rule of ad in calender(datewise)",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                startDate: {
                  type: "string",
                  format: "date-time",
                  description: "Start date for the price rule",
                  example: "2023-10-01T00:00:00Z",
                },
                endDate: {
                  type: "string",
                  format: "date-time",
                  description: "End date for the price rule",
                  example: "2023-10-31T23:59:59Z",
                },
                priority: {
                  type: "integer",
                  description: "Priority of the price rule",
                  example: 40,
                },
                price: {
                  type: "integer",
                  description: "Price for the specified date range",
                  example: 1000,
                },
              },
            },
          },
        },
      },
    },
  },
  "/api/ad-management/set-overridePrice/6874b62c47a2915a6ae0b92d": {
    parameters: [],
    post: {
      tags: ["ad"],
      summary: "setOverridePrice-in-calender",
      description: "This api can be used to set the override price of ad in calender(datewise)",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                date: {
                  type: "string",
                  format: "date-time",
                  description: "Date for which the override price applies",
                  example: "2023-10-01T00:00:00Z",
                },
                price: {
                  type: "integer",
                  description: "Override price for the specified date",
                  example: 1500,
                },
              },
            },
          },
        },
      },
    },
  },
  "/api/ad-management/get-Adprice/6871e1be24513e36e5aa7152": {
    parameters: [],
    get: {
      tags: ["ad"],
      summary: "getPrice-in-calender",
      description: "This api can be used to get the price of ad",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                date: {
                  type: "string",
                  format: "date-time",
                  description: "Date for which the price is requested",
                  example: "2023-10-01T00:00:00Z",
                },
              },
            },
          },
        },
      },
    },
  },
};
