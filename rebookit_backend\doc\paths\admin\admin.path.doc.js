const { PlanMonthsTypeEnum, PlanTypeEnum } = require("../../../common/Enums");

module.exports = {
  "/api/admin/search-users": {
    parameters: [],
    post: {
      tags: ["admin"],
      summary: "Get users (only for admin)",
      description: "Get all the users for admin with the search and pagination",
      parameters: [
        {
          naeme: "page",
          in: "qury",
          required: false,
          example: "1",
          schema: {
            type: "integer",
            minimum: 1,
          },
        },
      ],
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                searchTerm: {
                  type: "string",
                  description: "Search term to filter users",
                  example: "john",
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/admin/item/actionStatus/{itemId}": {
    parameters: [],
    post: {
      tags: ["admin"],
      summary: "Update existing item status",
      description: "update the status of an existing item by admin, such as accepted, rejected",

      parameters: [
        {
          name: "id",
          in: "path",
          required: true,
          example: "6874a2b9500a5f6e5ff6829f",
          schema: {
            type: "string",
          },
        },
      ],
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                status: {
                  type: "string",
                  description: "Status of the item by admin",
                  example: "accepted",
                  enums: ["pending", "accepted", "rejected", "mark as sold", "deleted", "removed by admin", "expired"],
                },
                reason: {
                  type: "string",
                  description: "Reason for the rejection of item by admin",
                  example: "Item is not in good condition",
                },
              },
            },
          },
        },
      },
    },
  },
  "/api/admin/testimonial/": {
    parameters: [],
    post: {
      tags: ["admin"],
      summary: "testimonial",
      description: "This api can be used to create a testimonial for the admin dashboard",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                },
                title: {
                  type: "string",
                },
                content: {
                  type: "string",
                },
                image: {
                  type: "string",
                  format: "uri",
                },
              },
            },
          },
        },
      },
    },
  },
  "/api/admin/item/6874ab7f4aceadc215a81743": {
    parameters: [],
    delete: {
      tags: ["admin"],
      summary: "delete item",
      description: "This api can be used to delete an item by admin",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      parameters: [
        {
          name: "hard",
          in: "query",
          required: false,
          example: "true",
          schema: {
            type: "boolean",
            description: "If true, item will be deleted permanently",
          },
        },
      ],
      responses: {},
    },
  },

  "/api/admin/plan": {
    parameters: [],
    post: {
      tags: ["admin"],
      summary: "Create new plan by the admin",
      description: "Create new subscription plan by admin, only for admins",
      parameters: [],
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {
        200: {
          headers: {
            "X-Powered-By": {
              schema: {
                type: "string",
              },
              example: "Express",
            },
            "Access-Control-Allow-Origin": {
              schema: {
                type: "string",
              },
              example: "*",
            },
            "Content-Type": {
              schema: {
                type: "string",
              },
              example: "application/json; charset=utf-8",
            },
            "Content-Length": {
              schema: {
                type: "integer",
              },
              example: "358",
            },
            ETag: {
              schema: {
                type: "string",
              },
              example: 'W/"166-r5vZc3IT8O3FwAWqgEvtb97/Ccw"',
            },
            Date: {
              schema: {
                type: "string",
              },
              example: "Mon, 21 Jul 2025 06:49:28 GMT",
            },
            Connection: {
              schema: {
                type: "string",
              },
              example: "keep-alive",
            },
            "Keep-Alive": {
              schema: {
                type: "string",
              },
              example: "timeout=5",
            },
          },
          description: "create plan",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  subscriptionPlan: {
                    type: "object",
                    properties: {
                      planName: {
                        type: "string",
                        description: "Name of Plan",
                        example: "Basic",
                      },
                      planMonths: {
                        type: "string",
                        description: "Duration of plan, for instance - MONTHLY, QUARTERLY",
                        example: "MONTHLY",
                        enum: Object.values(PlanMonthsTypeEnum),
                      },
                      planType: {
                        type: "string",
                      },
                      price: {
                        type: "integer",
                      },
                      boosts: {
                        type: "integer",
                      },
                      listings: {
                        type: "array",
                        items: {
                          type: "object",
                          properties: {
                            category: {
                              type: "string",
                            },
                            noOfListing: {
                              type: "integer",
                            },
                            listingValidityDays: {
                              type: "integer",
                            },
                            _id: {
                              type: "string",
                            },
                          },
                        },
                      },
                      active: {
                        type: "boolean",
                      },
                      _id: {
                        type: "string",
                      },
                      createdAt: {
                        type: "string",
                        format: "date-time",
                      },
                      updatedAt: {
                        type: "string",
                        format: "date-time",
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                planName: {
                  type: "string",
                  description: "Name of Plan",
                  example: "Basic",
                },
                planMonths: {
                  type: "string",
                  description: "Duration of plan, for instance - MONTHLY, QUARTERLY",
                  example: "MONTHLY",
                  enum: ["MONTHLY", "QUARTERLY", "HALF-YEARLY", "YEARLY"],
                },
                planType: {
                  type: "string",
                  description: "Type of Plan, if its paid or free",
                  example: "Free",
                  enum: ["free", "paid"],
                },
                price: {
                  type: "integer",
                  description: "Price of plan",
                  example: 8800,
                },
                listings: {
                  type: "array",
                  description: "Array of Object of allowed listing in different categories",
                  items: {
                    type: "object",
                    description: "Object of allowed listing in current category",
                    properties: {
                      category: {
                        type: "string",
                        description: "Category Id",
                        example: "68612cc0ba947189b202a825",
                      },
                      noOfListing: {
                        type: "integer",
                        description: "Number of allowing listing",
                        example: 10,
                      },
                      listingValidityDays: {
                        type: "integer",
                        description: "Number of Days, after item will be marked as expired by system",
                        example: 90,
                      },
                    },
                  },
                },
                boosts: {
                  type: "integer",
                  description: "Number of allowed boosts in plan",
                  example: 2,
                },
                active: {
                  type: "boolean",
                  description: "If this plan is active or not",
                  example: true,
                },
              },
            },
          },
        },
      },
    },
    get: {
      tags: ["admin"],
      summary: "Get complete list of subscription plan",
      description: "Get all the subscription plans, regardless of active/inactive status, only for admin users",
      parameters: [],
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {
        200: {
          headers: {
            "X-Powered-By": {
              schema: {
                type: "string",
              },
              example: "Express",
            },
            "Access-Control-Allow-Origin": {
              schema: {
                type: "string",
              },
              example: "*",
            },
            "Content-Type": {
              schema: {
                type: "string",
              },
              example: "application/json; charset=utf-8",
            },
            "Content-Length": {
              schema: {
                type: "integer",
              },
              example: "576",
            },
            ETag: {
              schema: {
                type: "string",
              },
              example: 'W/"240-euIFVWeGW1OmFp6Qea9/4lTW2fs"',
            },
            Date: {
              schema: {
                type: "string",
              },
              example: "Mon, 21 Jul 2025 06:52:10 GMT",
            },
            Connection: {
              schema: {
                type: "string",
              },
              example: "keep-alive",
            },
            "Keep-Alive": {
              schema: {
                type: "string",
              },
              example: "timeout=5",
            },
          },
          description: "get all plans",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  subscriptionPlans: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        _id: {
                          type: "string",
                          description: "Unique identifier for the subscription plan",
                          example: "6878ccb689b4b5fe31870c55",
                        },
                        planName: {
                          type: "string",
                          description: "Name of the subscription plan",
                          example: "Basic Plan",
                        },
                        planMonths: {
                          type: "string",
                          description: "Duration of the subscription plan",
                          example: "Monthly",
                          enum: ["MONTHLY", "QUARTERLY", "HALF-YEARLY", "YEARLY"],
                        },
                        planType: {
                          type: "string",
                          description: "Type of the subscription plan",
                          example: "Free",
                          enum: ["Free", "Paid"],
                        },
                        price: {
                          type: "integer",
                          description: "Price of the subscription plan",
                          example: 999,
                        },
                        active: {
                          type: "boolean",
                          description: "Indicates if the subscription plan is active",
                          example: true,
                        },
                        listings: {
                          type: "array",
                          description: "Array of allowed listings in different categories",
                          items: {
                            type: "object",
                            properties: {
                              category: {
                                type: "string",
                                description: "Category Id",
                                example: "68612cc0ba947189b202a825",
                              },
                              noOfListing: {
                                type: "integer",
                                description: "Number of allowing listing",
                                example: 10,
                              },
                              listingValidityDays: {
                                type: "integer",
                                description: "Number of Days, after item will be marked as expired by system",
                                example: 90,
                              },
                            },
                          },
                        },
                        createdAt: {
                          type: "string",
                          format: "date-time",
                          description: "Date when the subscription plan was created",
                          example: "2023-10-01T12:00:00Z",
                        },
                        updatedAt: {
                          type: "string",
                          format: "date-time",
                          description: "Date when the subscription plan was last updated",
                          example: "2023-10-01T12:00:00Z",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
  "/api/admin/plan/6878ccb689b4b5fe31870c55": {
    parameters: [],
    put: {
      tags: ["admin"],
      summary: "Update existing subscription plan",
      description: "Update existing subscription plan by id, only for admins",
      parameters: [],
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                planName: {
                  type: "string",
                  description: "Name of Plan",
                  example: "Basic",
                },
                planMonths: {
                  type: "string",
                  description: "Duration of plan, for instance - MONTHLY, QUARTERLY",
                  example: "MONTHLY",
                  enum: ["MONTHLY", "QUARTERLY", "HALF-YEARLY", "YEARLY"],
                },
                planType: {
                  type: "string",
                  description: "Type of Plan, if its paid or free",
                  example: "Free",
                  enum: ["free", "paid"],
                },
                price: {
                  type: "integer",
                  description: "Price of plan",
                  example: 8800,
                },
                listings: {
                  type: "array",
                  description: "Array of Object of allowed listing in different categories",
                  items: {
                    type: "object",
                    description: "Object of allowed listing in current category",
                    properties: {
                      category: {
                        type: "string",
                        description: "Category Id",
                        example: "68612cc0ba947189b202a825",
                      },
                      noOfListing: {
                        type: "integer",
                        description: "Number of allowing listing",
                        example: 10,
                      },
                      listingValidityDays: {
                        type: "integer",
                        description: "Number of Days, after item will be marked as expired by system",
                        example: 90,
                      },
                    },
                  },
                },
                boosts: {
                  type: "integer",
                  description: "Number of allowed boosts in plan",
                  example: 2,
                },
                active: {
                  type: "boolean",
                  description: "If this plan is active or not",
                  example: true,
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/admin/members/filter-search": {
    parameters: [],
    post: {
      tags: ["admin"],
      summary: "filter search member",
      description: "This api can be used to search member by admin in dashboard",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      parameters: [
        {
          name: "page",
          in: "query",
          required: false,
          example: "1",
          schema: {
            type: "integer",
          },
        },
        {
          name: "pageSize",
          in: "query",
          required: false,
          example: "10",
          schema: {
            type: "integer",
          },
        },
      ],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
            },
          },
        },
      },
    },
  },

  "/api/admin/user/686b53ba8bdcc6cf4521c917": {
    parameters: [],
    get: {
      tags: ["admin"],
      summary: "get user by id",
      description: "This api can be used to get detail of the users by admin",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      parameters: [
        {
          name: "Content-Type",
          in: "header",
          required: false,
          example: "application/json",
          schema: {
            type: "string",
          },
        },
      ],
      responses: {},
    },
  },

  "/api/admin/single-upload": {
    parameters: [],
    post: {
      tags: ["admin"],
      summary: "single file upload",
      description: "This api can be used to upload file by admin",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
  },
  "/api/admin/multiple-upload": {
    parameters: [],
    post: {
      tags: ["admin"],
      summary: "multiple file upload",
      description: "This api can be used to upload multiple files by admin",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
  },

  "/api/admin/dashboard/overview": {
    parameters: [],
    get: {
      tags: ["admin"],
      summary: "Get Dashboard overview ",
      description: "Get the all over statstics such as users, transcations etc",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
  },
  "/api/admin/dashboard/lastMonthSubscribers": {
    parameters: [],
    get: {
      tags: ["admin"],
      summary: "Get last month subscribers",
      description: "Get the last month subscribers count",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
  },
  "/api/admin/dashboard/totalAdRequest": {
    parameters: [],
    get: {
      tags: ["admin"],
      summary: "Get total ad request",
      description: "Get the total ad request count",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
  },

  "/api/admin/testimonial": {
    parameters: [],
    post: {
      tags: ["admin"],
      summary: "Create testimonial",
      description: "This api can be used to create a testimonial for the admin dashboard",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                  description: "Name of the person giving the testimonial",
                  example: "John Doe",
                },
                title: {
                  type: "string",
                  description: "Title of the person giving the testimonial",
                  example: "CEO of Example Corp",
                },
                content: {
                  type: "string",
                  description: "Content of the testimonial",
                  example: "This is a great product!",
                },
                image: {
                  type: "string",
                  format: "uri",
                  description: "URL of the image associated with the testimonial",
                  example: "https://example.com/image.jpg",
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/admin/testimonial": {
    parameters: [],
    get: {
      tags: ["admin"],
      summary: "Get all testimonials",
      description: "This api can be used to get all the testimonials for admin dashboard",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
  },
  "/api/admin/testimonial/:id": {
    parameters: [],
    put: {
      tags: ["admin"],
      summary: "Update testimonial",
      description: "This api can be used to update a testimonial for the admin dashboard",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      parameters: [
        {
          name: "id",
          in: "path",
          required: true,
          example: "6878ccb689b4b5fe31870c55",
          schema: {
            type: "string",
          },
        },
      ],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                  description: "Name of the person giving the testimonial",
                  example: "John Doe",
                },
                title: {
                  type: "string",
                  description: "Title of the person giving the testimonial",
                  example: "CEO of Example Corp",
                },
                content: {
                  type: "string",
                  description: "Content of the testimonial",
                  example: "This is a great product!",
                },
                image: {
                  type: "string",
                  format: "uri",
                  description: "URL of the image associated with the testimonial",
                  example: "https://example.com/image.jpg",
                },
              },
            },
          },
        },
      },
    },
    delete: {
      tags: ["admin"],
      summary: "Delete testimonial",
      description: "This api can be used to delete a testimonial for the admin dashboard",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      parameters: [
        {
          name: "id",
          in: "path",
          required: true,
          example: "6878ccb689b4b5fe31870c55",
          schema: {
            type: "string",
          },
        },
      ],
      responses: {},
    },
  },
};
