module.exports = {
  "/api/books/isbn/1408855658": {
    parameters: [],
    get: {
      tags: ["book"],
      summary: "get book by ISBN",
      descriptions: "This api can be used to get the books by isbn number",
      parameters: {
        name: "isbn",
        in: "params",
        required: false,
        example: "ISBN:1408855658",
      },
      responses: {},
    },
  },
  "/api/books/search": {
    parameters: [],
    get: {
      tags: ["book"],
      summary: "search",
      description: "This api can be used to search book",
      parameters: [
        {
          name: "q",
          in: "query",
          required: false,
          example: "Language Tree Jamaica Workbook",
          schema: {
            type: "string",
          },
        },
      ],
      responses: {},
    },
  },
};
