module.exports = {
  "/api/chat/all": {
    parameters: [],
    post: {
      tags: ["chat"],
      summary: "all messages",
      description: "This api can be used to send the messages",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
            },
          },
        },
      },
    },
  },
  "/api/chat/{conversationId}": {
    parameters: [],
    get: {
      tags: ["chat"],
      summary: "get chat by id",
      description: "This api can be used to get all the messages of a chat",
      parameters: [
        {
          name: "page",
          in: "query",
          required: false,
          example: "1",
          schema: {
            type: "integer",
          },
        },
      ],
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
    delete: {
      tags: ["chat"],
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      summary: "delete chat",
      description: "This api can be used to delete the chat",
      responses: {},
    },
  },
  "/api/chat/item/{itemId}": {
    get: {
      tags: ["chat"],
      summary: "Get chat by item id",
      description: "Get chat by item id",
      parameters: [
        {
          name: "itemId",
          in: "path",
          description: "Unique identifier of the item",
          required: true,

          schema: {
            type: "string",
            format: "objectId",
            pattern: "^[0-9a-fA-F]{24}$",
            example: "60a7e1f2b3c4d5e6f7a8b9c0",
          },
        },
      ],
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {
        200: {
          description: "get chat by item id",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  count: {
                    type: "integer",
                  },
                  conversation: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        _id: {
                          type: "string",
                        },
                        sellerItemId: {
                          type: "string",
                        },
                        createdAt: {
                          type: "string",
                          format: "date-time",
                        },
                        deletedBy: {
                          type: "array",
                          items: {},
                        },
                        participants: {
                          type: "array",
                          items: {
                            type: "string",
                          },
                        },
                        status: {
                          type: "string",
                        },
                        type: {
                          type: "string",
                        },
                        updatedAt: {
                          type: "string",
                          format: "date-time",
                        },
                        participants_docs: {
                          type: "array",
                          items: {
                            type: "object",
                            properties: {
                              _id: {
                                type: "string",
                              },
                              email: {
                                type: "string",
                                format: "email",
                              },
                              isEmailVerified: {
                                type: "boolean",
                              },
                              isProfileCompleted: {
                                type: "boolean",
                              },
                              allListingItems: {
                                type: "array",
                                items: {},
                              },
                              status: {
                                type: "string",
                              },
                              createdAt: {
                                type: "string",
                                format: "date-time",
                              },
                              updatedAt: {
                                type: "string",
                                format: "date-time",
                              },
                              firstName: {
                                type: "string",
                              },
                              lastName: {
                                type: "string",
                              },
                              location: {
                                type: "object",
                                properties: {
                                  type: {
                                    type: "string",
                                  },
                                  coordinates: {
                                    type: "array",
                                    items: {
                                      type: "integer",
                                    },
                                  },
                                },
                              },
                              loggedIn: {
                                type: "string",
                                format: "date-time",
                              },
                              password: {
                                type: "string",
                              },
                              registeredAt: {
                                type: "string",
                                format: "date-time",
                              },
                              roleId: {
                                type: "string",
                              },
                            },
                          },
                        },
                        item: {
                          type: "array",
                          items: {
                            type: "object",
                            properties: {
                              _id: {
                                type: "string",
                              },
                              title: {
                                type: "string",
                              },
                              description: {
                                type: "string",
                              },
                              categoryId: {
                                type: "string",
                              },
                              subCategoryId: {
                                type: "string",
                              },
                              subSubCategoryId: {
                                type: "string",
                              },
                              address: {
                                type: "object",
                                properties: {
                                  formatted_address: {
                                    type: "string",
                                  },
                                  geometry: {
                                    type: "object",
                                    properties: {
                                      location: {
                                        type: "object",
                                        properties: {
                                          type: {
                                            type: "string",
                                          },
                                          coordinates: {
                                            type: "array",
                                            items: {
                                              type: "number",
                                            },
                                          },
                                        },
                                      },
                                    },
                                  },
                                  country: {
                                    type: "string",
                                  },
                                  parish: {
                                    type: "string",
                                  },
                                  administrative_area_level_1: {
                                    type: "string",
                                  },
                                  locality: {
                                    type: "string",
                                  },
                                  political: {
                                    type: "string",
                                  },
                                  political_short: {
                                    type: "string",
                                  },
                                },
                              },
                              images: {
                                type: "array",
                                items: {
                                  type: "string",
                                  format: "uri",
                                },
                              },
                              tags: {
                                type: "array",
                                items: {
                                  type: "string",
                                },
                              },
                              price: {
                                type: "integer",
                              },
                              status: {
                                type: "string",
                              },
                              expireAt: {
                                type: "string",
                                format: "date-time",
                              },
                              isActive: {
                                type: "boolean",
                              },
                              createdBy: {
                                type: "string",
                              },
                              updatedBy: {
                                type: "string",
                              },
                              __t: {
                                type: "string",
                              },
                              isbn_number: {
                                type: "string",
                              },
                              authors: {
                                type: "array",
                                items: {
                                  type: "string",
                                },
                              },
                              condition: {
                                type: "string",
                              },
                              boostedAt: {
                                type: "string",
                                format: "date-time",
                              },
                              createdAt: {
                                type: "string",
                                format: "date-time",
                              },
                              updatedAt: {
                                type: "string",
                                format: "date-time",
                              },
                              publishedAt: {
                                type: "string",
                                format: "date-time",
                              },
                            },
                          },
                        },
                      },
                    },
                  },
                  messages: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        _id: {
                          type: "string",
                        },
                        conversation: {
                          type: "string",
                        },
                        text: {
                          type: "string",
                        },
                        isMedia: {
                          type: "boolean",
                        },
                        isSeen: {
                          type: "boolean",
                        },
                        deletedBy: {
                          type: "array",
                          items: {},
                        },
                        sender: {
                          type: "string",
                        },
                        to: {
                          type: "string",
                        },
                        createdAt: {
                          type: "string",
                          format: "date-time",
                        },
                        updatedAt: {
                          type: "string",
                          format: "date-time",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },
};