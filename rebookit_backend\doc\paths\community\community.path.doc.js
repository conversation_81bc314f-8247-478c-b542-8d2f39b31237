module.exports = {
  "/api/community/question": {
    parameters: [],
    post: {
      tags: ["community"],
      summary: "add question",
      description: "This api can be used to add a question in community",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                title: {
                  type: "string",
                  description: "The title of the question",
                  example: "What is the best way to sell books?",
                },
              },
            },
          },
        },
      },
    },
  },
  "/api/community/answer": {
    parameters: [],
    post: {
      tags: ["community"],
      summary: "submit answer",
      description: "This api can be used to add a answer of a question in community",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                questionId: {
                  type: "string",
                  description: "The ID of the question to which the answer is being submitted",
                  example: "64b8c8f8f8f8f8f8f8f8f8f",
                },
                answerText: {
                  type: "string",
                  description: "The text of the answer being submitted",
                  example: "I think the best way to sell books is through Rebookit.club advertisement marketing.",
                },
              },
            },
          },
        },
      },
    },
    put: {
      tags: ["community"],
      summary: "edit answer",
      parameters: [
        {
          name: "answerId",
          in: "query",
          required: false,
          example: "6851106623b8dd603666ccf0",
          schema: {
            type: "integer",
          },
        },
      ],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                answerText: {
                  type: "string",
                  description: "The updated text of the answer",
                  example: "I think the best way to sell books is through Rebookit.club advertisement marketing.",
                },
              },
            },
          },
        },
      },
    },
  },
  "/api/community/user-questions": {
    parameters: [],
    get: {
      tags: ["community"],
      summary: "my questions",
      description: "This api can be used to get all question in community posted by the user",
      parameters: [
        {
          name: "searchTerm",
          in: "query",
          required: false,
          example: "alchemist",
          schema: {
            type: "string",
          },
        },
        {
          name: "pageSize",
          in: "query",
          required: false,
          example: "10",
          schema: {
            type: "integer",
          },
        },
      ],
      responses: {},
    },
  },
  "/api/community/questions": {
    parameters: [],
    get: {
      tags: ["community"],
      summary: "questions",
      description: "This api can be used to get all the questions in community",
      parameters: [
        {
          name: "pageSize",
          in: "query",
          required: false,
          example: "100",
          schema: {
            type: "integer",
          },
        },
        {
          name: "categoryId",
          in: "query",
          required: false,
          example: "6811d7ccd8e7e0ae74fd55ba",
          schema: {
            type: "integer",
          },
        },
        {
          name: "searchTerm",
          in: "query",
          required: false,
          example: "john",
          schema: {
            type: "string",
          },
        },
        {
          name: "page",
          in: "query",
          required: false,
          example: "1",
          schema: {
            type: "integer",
          },
        },
        {
          name: "sort",
          in: "query",
          required: false,
          example: "oldest",
          schema: {
            type: "string",
          },
          enum: ["popularity", "oldest", "leastPopularity"],
        },
      ],
      responses: {},
    },
  },
  "/api/community/user-questions-with-answer": {
    parameters: [],
    get: {
      tags: ["community"],
      summary: "user questions with answer",
      description: "This api can be used to question and answer of user in community",
      parameters: [
        {
          name: "searchTerm",
          in: "query",
          required: false,
          example: "asheer",
          schema: {
            type: "string",
          },
        },
      ],
      responses: {},
    },
  },
  "/api/community/user-answers": {
    parameters: [],
    get: {
      tags: ["community"],
      summary: "user answers",
      description: "This api can be used to get answers by the user in community",
      parameters: [
        {
          name: "searchTerm",
          in: "query",
          required: false,
          example: "power",
          schema: {
            type: "string",
          },
        },
        {
          name: "page",
          in: "query",
          required: false,
          example: "1",
          schema: {
            type: "integer",
          },
        },
        {
          name: "pageSize",
          in: "query",
          required: false,
          example: "5",
          schema: {
            type: "integer",
          },
        },
      ],
      responses: {},
    },
  },
  "/api/community/question/684a6fbbba5a8937695fb415": {
    parameters: [],
    get: {
      tags: ["community"],
      summary: "get question by id",
      description: "This api can be used to get a question in community",
      responses: {},
    },
    delete: {
      tags: ["community"],
      summary: "delete question",
      responses: {},
    },
  },
  "/api/community/answer/684bc8ace2f0d05196d81185": {
    parameters: [],
    delete: {
      tags: ["community"],
      summary: "delete answer",
      description: "This api can be used to delete an asnwer in community",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
  },
};
