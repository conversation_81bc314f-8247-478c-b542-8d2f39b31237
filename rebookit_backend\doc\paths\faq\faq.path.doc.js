module.exports = {
  "/api/faqs": {
    parameters: [],
    post: {
      tags: ["faq"],
      summary: "create faq",
      description: "This api can be used to create a faq",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                question: {
                  type: "string",
                  description: "The question for the FAQ",
                  example: "How can I reset my password?",
                },
                answer: {
                  type: "string",
                  description: "The answer for the FAQ",
                  example: "To reset your password, go to the settings page and click on 'Reset Password'.",
                },
                category: {
                  type: "string",
                  description: "The category of the FAQ",
                  example: "General",
                },
                order: {
                  type: "integer",
                  description: "The order in which the FAQ should be displayed",
                  example: 1,
                },
                tags: {
                  type: "array",
                  items: {
                    type: "string",
                    description: "Tags associated with the FAQ",
                    example: "password, reset, help",
                  },
                },
                isActive: {
                  type: "boolean",
                  description: "Indicates if the FAQ is active",
                  example: true,
                },
              },
            },
          },
        },
      },
    },
  },
  "/api/faqs/filter-search": {
    parameters: [],
    post: {
      tags: ["faq"],
      summary: "filter and search faqs",
      description: "This api can be used to search in faq",
      parameters: [
        {
          name: "page",
          in: "query",
          required: false,
          example: "1",
          schema: {
            type: "integer",
          },
        },
      ],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
            },
          },
        },
      },
    },
  },
  "/api/faqs/filter-search-admin": {
    parameters: [],
    post: {
      tags: ["faq"],
      summary: "filter and search faqs (admin)",
      description: "This api can be used to search in faq by admin",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      parameters: [
        {
          name: "category",
          in: "query",
          required: false,
          example: "Billing",
          schema: {
            type: "string",
          },
          enum: ["General", "Membership", "Billing", "Technical", "Other"],
        },
        {
          name: "page",
          in: "query",
          required: false,
          example: "1",
          schema: {
            type: "integer",
          },
        },
        {
          name: "pageSize",
          in: "query",
          required: false,
          example: "5",
          schema: {
            type: "integer",
          },
        },
      ],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
            },
          },
        },
      },
    },
  },
  "/api/faqs/684bf01bd80e8016dfa57b5d": {
    parameters: [],
    put: {
      tags: ["faq"],
      summary: "update faq",
      description: "This api can be used to upate a faq",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                question: {
                  type: "string",
                  description: "The question for the FAQ",
                  example: "How can I reset my password?",
                },
                answer: {
                  type: "string",
                  description: "The answer for the FAQ",
                  example: "To reset your password, go to the settings page and click on 'Reset Password'.",
                },
                category: {
                  type: "string",
                  description: "The category of the FAQ",
                  example: "General",
                },
                order: {
                  type: "integer",
                  description: "The order in which the FAQ should be displayed",
                  example: 1,
                },
                tags: {
                  type: "array",
                  items: {
                    type: "string",
                    description: "Tags associated with the FAQ",
                    example: "password, reset, help",
                  },
                },
                isActive: {
                  type: "boolean",
                  description: "Indicates if the FAQ is active",
                  example: true,
                },
              },
            },
          },
        },
      },
    },
  },
  "/api/faqs/684c08efbb4a4c37c9f7c72e": {
    parameters: [],
    delete: {
      tags: ["faq"],
      summary: "delete faq",
      description: "This api can be used to delete a faq",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
  },
};
