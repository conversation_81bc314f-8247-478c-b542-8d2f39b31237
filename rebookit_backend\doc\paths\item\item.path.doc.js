module.exports = {
  "/api/item": {
    parameters: [],
    post: {
      tags: ["item"],
      summary: "add item",
      description: "This api can be used to add item by user",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              anyOf: [
                { $ref: "#/components/schemas/BookItem" },
                { $ref: "#/components/schemas/TutorItem" },
                { $ref: "#/components/schemas/SchoolItem" },
                { $ref: "#/components/schemas/ExtracurricularActivityItem" },
                { $ref: "#/components/schemas/EventItem" },
                { $ref: "#/components/schemas/ScholarshipAwardItem" },
              ],
              discriminator: {
                propertyName: "kind",
                mapping: {
                  BookItem: "#/components/schemas/BookItem",
                  TutorItem: "#/components/schemas/TutorItem",
                  SchoolItem: "#/components/schemas/SchoolItem",
                  ExtracurricularActivityItem: "#/components/schemas/ExtracurricularActivityItem",
                  EventItem: "#/components/schemas/EventItem",
                  ScholarshipAwardItem: "#/components/schemas/ScholarshipAwardItem",
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/item/{itemId}": {
    parameters: [],
    put: {
      tags: ["item"],
      summary: "update listed item",
      description: "This api can be used to update detail of the item by user",

      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              anyOf: [
                { $ref: "#/components/schemas/BookItem" },
                { $ref: "#/components/schemas/TutorItem" },
                { $ref: "#/components/schemas/SchoolItem" },
                { $ref: "#/components/schemas/ExtracurricularActivityItem" },
                { $ref: "#/components/schemas/EventItem" },
                { $ref: "#/components/schemas/ScholarshipAwardItem" },
              ],
              discriminator: {
                propertyName: "kind",
                mapping: {
                  BookItem: "#/components/schemas/BookItem",
                  TutorItem: "#/components/schemas/TutorItem",
                  SchoolItem: "#/components/schemas/SchoolItem",
                  ExtracurricularActivityItem: "#/components/schemas/ExtracurricularActivityItem",
                  EventItem: "#/components/schemas/EventItem",
                  ScholarshipAwardItem: "#/components/schemas/ScholarshipAwardItem",
                },
              },
            },
          },
        },
      },
    },
    "/api/item/search": {
      parameters: [],
      post: {
        tags: ["item"],
        summary: "search items",
        description: "This api can be used to search item by user",
        parameters: [
          {
            name: "userId",
            in: "query",
            required: false,
            example: "6811d7ad7d79fae9362714b4",
            schema: {
              type: "integer",
            },
          },
          {
            name: "page",
            in: "query",
            required: false,
            example: "1",
            schema: {
              type: "integer",
            },
          },
          {
            name: "pageSize",
            in: "query",
            required: false,
            example: "50",
            schema: {
              type: "integer",
            },
          },
        ],
        responses: {},
        requestBody: {
          content: {
            "application/json": {
              schema: {
                type: "object",
              },
            },
          },
        },
      },
    },
    "/api/item/search/admin": {
      parameters: [],
      post: {
        tags: ["item"],
        summary: "search items - admin",
        description: "This api can be used to search item by admin",
        parameters: [
          {
            name: "page",
            in: "query",
            required: false,
            example: "1",
            schema: {
              type: "integer",
            },
          },
          {
            name: "pageSize",
            in: "query",
            required: false,
            example: "50",
            schema: {
              type: "integer",
            },
          },
        ],
        responses: {},
      },
    },
    "/api/item/search/current-user": {
      parameters: [],
      post: {
        tags: ["item"],
        summary: "search my items",
        description: "This api can be used to search own item by user",
        parameters: [
          {
            name: "page",
            in: "query",
            required: false,
            example: "1",
            schema: {
              type: "integer",
            },
          },
          {
            name: "pageSize",
            in: "query",
            required: false,
            example: "50",
            schema: {
              type: "integer",
            },
          },
        ],
        responses: {},
        requestBody: {
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  filter: {
                    type: "object",
                    properties: {
                      keyword: {
                        type: "string",
                        description: "Keyword to search items",
                        example: "tutor",
                      },
                      categoryId: {
                        type: "string",
                        description: "ID of the category to filter items",
                        example: "60d5f484f1b2c8b1a8b3c4d5",
                      },
                      subCategoryId: {
                        type: "string",
                        description: "ID of the sub-category to filter items",
                        example: "60d5f484f1b2c8b1a8b3c4d6",
                      },
                      parish: {
                        type: "string",
                        description: "Parish to filter items",
                        example: "kingston",
                        enum: [
                          "kingston",
                          "saint andrew",
                          "saint catherine",
                          "saint james",
                          "clarendon",
                          "manchester",
                          "westmoreland",
                          "hanover",
                          "saint elizabeth",
                          "trelawny",
                          "saint ann",
                          "saint mary",
                          "portland",
                          "saint thomas",
                        ],
                      },
                    },
                  },
                  sort: {
                    type: "object",
                    properties: {
                      price: {
                        type: "integer",
                        description: "Sort items by price",
                        example: 900,
                      },
                      createdAt: {
                        type: "string",
                        description: "Sort items by creation date",
                        example: "desc",
                        enum: ["asc", "desc"],
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/item/auto-complete": {
    parameters: [],
    get: {
      tags: ["item"],
      summary: "auto complete",
      description: "This api can be used to get auto-complete in search",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },

      parameters: [
        {
          name: "searchTerm",
          in: "query",
          required: false,
          example: "a",
          schema: {
            type: "string",
          },
        },
        {
          name: "page",
          in: "query",
          required: false,
          example: "1",
          schema: {
            type: "integer",
          },
        },
      ],
      responses: {},
    },
  },

  "/api/item/search": {
    parameters: [],
    post: {
      tags: ["item"],
      summary: "search items",
      parameters: [
        {
          name: "page",
          in: "query",
          required: false,
          example: "1",
          schema: {
            deprecated: false,
          },
        },
        {
          name: "pageSize",
          in: "query",
          required: false,
          example: "50",
          schema: {
            deprecated: false,
          },
        },
        {
          name: "userId",
          in: "query",
          required: false,
          example: "688859763eb04c1b1a6abfea",
          schema: {
            deprecated: false,
          },
        },
      ],
      responses: {
        200: {
          headers: {
            "X-Powered-By": {
              schema: {
                type: "string",
              },
              example: "Express",
            },
            "Access-Control-Allow-Origin": {
              schema: {
                type: "string",
              },
              example: "*",
            },
            "Content-Type": {
              schema: {
                type: "string",
              },
              example: "application/json; charset=utf-8",
            },
            "Content-Length": {
              schema: {
                type: "integer",
              },
              example: "14934",
            },
            ETag: {
              schema: {
                type: "string",
              },
              example: 'W/"3a56-H0XsZZsiL1rGOHgi3cT0xixeXI4"',
            },
            Date: {
              schema: {
                type: "string",
              },
              example: "Fri, 01 Aug 2025 12:15:53 GMT",
            },
            Connection: {
              schema: {
                type: "string",
              },
              example: "keep-alive",
            },
            "Keep-Alive": {
              schema: {
                type: "string",
              },
              example: "timeout=5",
            },
          },
          description: "search items",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  page: {
                    type: "integer",
                  },
                  pageSize: {
                    type: "integer",
                  },
                  totalPages: {
                    type: "integer",
                  },
                  totalCount: {
                    type: "integer",
                  },
                  data: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        _id: {
                          type: "string",
                        },
                        title: {
                          type: "string",
                        },
                        description: {
                          type: "string",
                        },
                        categoryId: {
                          type: "string",
                        },
                        subCategoryId: {
                          type: "string",
                        },
                        subSubCategoryId: {
                          type: "string",
                        },
                        subSubSubCategoryId: {
                          type: "string",
                        },
                        address: {
                          type: "object",
                          properties: {
                            formatted_address: {
                              type: "string",
                            },
                            geometry: {
                              type: "object",
                              properties: {
                                location: {
                                  type: "object",
                                  properties: {
                                    type: {
                                      type: "string",
                                    },
                                    coordinates: {
                                      type: "array",
                                      items: {
                                        type: "number",
                                      },
                                    },
                                  },
                                },
                              },
                            },
                            country: {
                              type: "string",
                            },
                            parish: {
                              type: "string",
                            },
                            administrative_area_level_1: {
                              type: "string",
                            },
                            administrative_area_level_2: {
                              type: "string",
                            },
                            political_short: {
                              type: "string",
                            },
                            sublocality: {
                              type: "string",
                            },
                            sublocality_level_1: {
                              type: "string",
                            },
                            route: {
                              type: "string",
                            },
                            street_number: {
                              type: "string",
                            },
                          },
                        },
                        images: {
                          type: "array",
                          items: {
                            type: "string",
                            format: "uri",
                          },
                        },
                        tags: {
                          type: "array",
                          items: {
                            type: "string",
                          },
                        },
                        price: {
                          type: "number",
                        },
                        status: {
                          type: "string",
                        },
                        expireAt: {
                          type: "string",
                          format: "date-time",
                        },
                        isActive: {
                          type: "boolean",
                        },
                        createdBy: {
                          type: "string",
                        },
                        updatedBy: {
                          type: "string",
                        },
                        __t: {
                          type: "string",
                        },
                        isbn_number: {
                          type: "string",
                          format: "utc-millisec",
                        },
                        authors: {
                          type: "array",
                          items: {
                            type: "string",
                          },
                        },
                        condition: {
                          type: "string",
                        },
                        boostedAt: {
                          type: "string",
                          format: "date-time",
                        },
                        createdAt: {
                          type: "string",
                          format: "date-time",
                        },
                        updatedAt: {
                          type: "string",
                          format: "date-time",
                        },
                        publishedAt: {
                          type: "string",
                          format: "date-time",
                        },
                        categoryDoc: {
                          type: "object",
                          properties: {
                            _id: {
                              type: "string",
                            },
                            name: {
                              type: "string",
                            },
                          },
                        },
                        subcategoryDoc: {
                          type: "object",
                          properties: {
                            _id: {
                              type: "string",
                            },
                            name: {
                              type: "string",
                            },
                          },
                        },
                        subsubcategoryDoc: {
                          type: "object",
                          properties: {
                            _id: {
                              type: "string",
                            },
                            name: {
                              type: "string",
                            },
                          },
                        },
                        subsubsubcategoryDoc: {
                          type: "object",
                          properties: {
                            _id: {
                              type: "string",
                            },
                            name: {
                              type: "string",
                            },
                          },
                        },
                        createdByDoc: {
                          type: "object",
                          properties: {
                            _id: {
                              type: "string",
                            },
                            email: {
                              type: "string",
                              format: "email",
                            },
                            firstName: {
                              type: "string",
                            },
                            lastName: {
                              type: "string",
                            },
                          },
                        },
                        reviewDoc: {
                          type: "array",
                          items: {},
                        },
                        bookmarkDoc: {
                          type: "array",
                          items: {},
                        },
                        hasBookmarked: {
                          type: "boolean",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
      requestBody: {
        required: true,
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                filters: {
                  type: "object",
                  description: "filters for items, if no filters, this objects must be empty",
                  properties: {
                    keyword: { type: "string", description: "Search Term for searching items", example: "harry" },
                    coordinates: {
                      description: "Coordinates for finding nearby items, please note longitude comes first, then latitude",
                      type: "array",
                      items: { type: "number" },
                      example: [77.345841, 28.535744],
                    },
                    maxDistanceInKm: { type: "number", description: "Maximum distance for finding nearby items", example: 100 },
                    minPrice: { type: "number", description: "Minimum price filter", example: 1 },
                    maxPrice: { type: "number", description: "Maximum price filter", example: 1000 },
                    category: {
                      type: "array",
                      description: "Categories filter",
                      items: { type: "string" },
                      example: ["68612cc0ba947189b202a825"],
                    },
                    subCategory: {
                      type: "array",
                      description: "Sub Categories filter",
                      items: { type: "string" },
                      example: ["6811d7ad7d79fae9362714b4"],
                    },
                    subSubCategory: {
                      type: "array",
                      description: "Sub Sub Categories filter",
                      items: { type: "string" },
                      example: ["6811f56d0fc7ca9045096845"],
                    },
                    subSubSubCategory: {
                      type: "array",
                      description: "Sub Sub Sub Categories filter",
                      items: { type: "string" },
                      example: ["6889be477eba45afc4692be8"],
                    },
                    authors: {
                      type: "array",
                      description: "authors filter",
                      items: { type: "string" },
                      example: [],
                    },
                    parish: {
                      type: "array",
                      description: "parish filter",
                      items: { type: "string" },
                      example: ["kingston"],
                    },
                  },
                },
                sort: {
                  type: "object",
                  description: "sorting keys for items, if no sort needed, this object must be empty",
                  minProperties: 1,
                  maxProperties: 1,
                  additionalProperties: false,
                  properties: {
                    createdAt: {
                      type: "integer",
                      enum: [1, -1],
                      example: -1,
                    },
                    price: {
                      type: "integer",
                      enum: [1, -1],
                      example: 1,
                    },
                  },
                },
              },
              required: ["filters", "sort"],
            },
            example: {
              filters: {
                keyword: "harry",
                coordinates: [77.345841, 28.535744],
                maxDistanceInKm: 100,
                minPrice: 1,
                maxPrice: 1000,
                category: ["68612cc0ba947189b202a825"],
                subCategory: ["6811d7ad7d79fae9362714b4"],
                subSubCategory: ["6811f56d0fc7ca9045096845"],
                subSubSubCategory: ["6889be477eba45afc4692be8"],
                authors: [],
                parish: ["kingston"],
              },
              sort: {
                createdAt: -1,
              },
            },
          },
        },
      },
    },
  },

  "/item/67ff3214da494debbcaf22ab": {
    parameters: [],
    delete: {
      tags: ["item"],
      summary: "delete listed item - admin",
      description: "This api can be used to delete the item by admin",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
  },

  "/api/item/boost/{itemId}": {
    parameters: [],
    put: {
      tags: ["item"],
      summary: "boost item",
      description: "This api can be used to boost the item by user",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
  },

  "/api/item/bookmark": {
    parameters: [],
    post: {
      tags: ["item"],
      summary: "bookmarked item",
      description: "This api can be used to boomark an item by user",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
  },

  "/api/item/686b638c36cfb22895ef6355": {
    parameters: [],
    get: {
      tags: ["item"],
      summary: "get item by id",
      description: "This api can be used to get detail of the item",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },

      parameters: [
        {
          name: "userId",
          in: "query",
          required: false,
          example: "686786927ce40aad3b23eee6",
          schema: {
            type: "integer",
          },
        },
      ],
      responses: {},
    },
  },

  "/api/item/6864df34bca51fe5bac5288a": {
    parameters: [],
    delete: {
      tags: ["item"],
      summary: "delete item",
      description: "This api can be used to delete the item by user",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
  },

  "/api/item/686b92d630ba45d009730312": {
    parameters: [],
    put: {
      tags: ["item"],
      summary: "update item",
      description: "This api can be used to update the item by user",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                kind: {
                  type: "string",
                  description: "Kind of the item",
                  example: "tutor",
                  enum: ["BookItem", "TutorItem", "SchoolItem", "ExtracurricularActivityItem", "EventItem, ScholarshipAwardItem"],
                },
                description: {
                  type: "string",
                  description: "Detailed description of the item",
                  example: "Experienced tutor specializing in advanced mathematics for high school students.",
                },
                categoryId: {
                  type: "string",
                  description: "Category ID of the item",
                  example: "60d21b4667d0d8992e610c85",
                },
                subCategoryId: {
                  type: "string",
                  description: "Sub-category ID of the item",
                  example: "60d21b4667d0d8992e610c86",
                },
                subSubCategoryId: {
                  type: "string",
                  description: "Sub-sub-category ID of the item",
                  example: "60d21b4667d0d8992e610c87",
                },
                address: {
                  type: "string",
                  description: "Address of the item",
                  example: "123 Main St, Anytown, USA",
                },
                images: {
                  type: "array",
                  items: {
                    type: "string",
                    format: "uri",
                    description: "Image URL",
                    example: "https://example.com/image.jpg",
                  },
                },
                price: {
                  type: "number",
                  description: "Price of the item",
                  example: 100,
                },
                tags: {
                  type: "array",
                  items: {
                    type: "string",
                    description: "Tags associated with the item",
                    example: "tutor, mathematics, high school",
                  },
                },
                location: {
                  type: "object",
                },
                schoolType: {
                  type: "string",
                  description: "Type of the school",
                  example: "public",
                  enum: ["public", "private", "international", "convent", "government"],
                },
                classesOffered: {
                  type: "string",
                  description: "Classes offered by the item",
                  example: "primary 1 to 6",
                  enum: [
                    "primary 1 to 6",
                    "lower secondary 7 to 9",
                    "upper secondary 10 to 11",
                    "sixth form 12 to 13",
                    "diploma",
                    "associate degree",
                    "bachelor",
                    "master",
                    "doctorate",
                  ],
                },
                website: {
                  type: "string",
                  format: "uri",
                  description: "Website URL of the item",
                  example: "https://example.com",
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/item/addReview": {
    parameters: [],
    post: {
      tags: ["item"],
      summary: "post-review",
      responses: {},
      description: "This api can be used to post review of the item by user",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                userId: {
                  type: "string",
                  description: "ID of the user posting the review",
                  example: "60d5f484f1b2c8b1a8b3c4d5",
                },
                rating: {
                  type: "number",
                  description: "Rating given by the user",
                  example: 4.5,
                },
                comment: {
                  type: "string",
                  description: "Comment provided by the user",
                  example: "Great item, highly recommend!",
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/item/686786927ce40aad3b23eee6/reviews": {
    parameters: [],
    get: {
      tags: ["item"],
      summary: "get all reviews",
      description: "This api can be used to get all the reviews of an item",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
  },
};
