module.exports = {
  "/api/master/category": {
    parameters: [],
    post: {
      tags: ["master"],
      summary: "add category",
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                },
                image: {
                  type: "string",
                  format: "uri",
                },
              },
            },
            example: {
              name: "add test category",
              image: "https://google.com",
            },
          },
        },
      },
    },
    get: {
      tags: ["master"],
      summary: "get categories",
      parameters: [],
      responses: {},
    },
  },
  "/api/master/subCategory": {
    parameters: [],
    post: {
      tags: ["master"],
      summary: "add subcategory",
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                },
                image: {
                  type: "string",
                  format: "uri",
                },
                categoryId: {
                  type: "string",
                },
              },
            },
            example: {
              name: "add test SubCateory",
              image: "https://google.com",
              categoryId: "6889b74361d20d1f4faec039",
            },
          },
        },
      },
    },
  },
  "/api/master/subSubCategory": {
    parameters: [],
    post: {
      tags: ["master"],
      summary: "add subsubcategory",
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                },
                image: {
                  type: "string",
                  format: "uri",
                },
                subCategoryId: {
                  type: "string",
                },
              },
            },
            example: {
              name: "add test subsubcategory",
              image: "https://google.com",
              subCategoryId: "6889b77e61d20d1f4faec03e",
            },
          },
        },
      },
    },
  },
  "/api/master/subsubsubcategory": {
    parameters: [],
    post: {
      tags: ["master"],
      summary: "add subsusubbcategory",
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                },
                image: {
                  type: "string",
                  format: "uri",
                },
                subSubCategoryId: {
                  type: "string",
                },
              },
            },
            example: {
              name: "add test subsubsubcategory",
              image: "https://google.com",
              subSubCategoryId: "6889b7cc61d20d1f4faec049",
            },
          },
        },
      },
    },
  },
  "/api/master/sub-category/68612cc0ba947189b202a825": {
    parameters: [],
    get: {
      tags: ["master"],
      summary: "get subcategories",
      parameters: [],
      responses: {},
    },
    delete: {
      tags: ["master"],
      summary: "delete sub category",
      parameters: [],
      responses: {},
    },
  },
  "/api/master/sub-sub-category/6811d7e3cb24f0b11c79e5a2": {
    parameters: [],
    get: {
      tags: ["master"],
      summary: "get subsubcategories",
      parameters: [],
      responses: {},
    },
  },
  "/api/master/sub-sub-sub-category/6811f56d0fc7ca9045096845": {
    parameters: [],
    get: {
      tags: ["master"],
      summary: "get subsubsubcategories",
      parameters: [],
      responses: {},
    },
  },
  "/api/master/category/68612cc0ba947189b202a825": {
    parameters: [],
    put: {
      tags: ["master"],
      summary: "edit category",
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                category: {
                  type: "string",
                },
                name: {
                  type: "string",
                },
                image: {
                  type: "string",
                  format: "uri",
                },
              },
            },
            example: {
              category: "68612cc1ab947189b202a825",
              name: "Books",
              image: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/08a2a65a-4609-4696-bdea-84ad718911b4.png",
            },
          },
        },
      },
    },
    delete: {
      tags: ["master"],
      summary: "delete category",
      parameters: [],
      responses: {},
    },
  },
  "/api/master/sub-category/6811d7ad7d79fae9362714b4": {
    parameters: [],
    put: {
      tags: ["master"],
      summary: "edit subcategory",
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                categoryId: {
                  type: "string",
                },
                name: {
                  type: "string",
                },
                image: {
                  type: "string",
                  format: "uri",
                },
              },
            },
            example: {
              categoryId: "68612cc0ba947189b202a826",
              name: "Early Childhood",
              image: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/08a2a65a-4609-4696-bdea-84ad718911b4.png",
            },
          },
        },
      },
    },
  },
  "/api/master/sub-sub-category/6811f6666d543be2551da39d": {
    parameters: [],
    put: {
      tags: ["master"],
      summary: "edit subsubcategory",
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                subCategoryId: {
                  type: "string",
                },
                name: {
                  type: "string",
                },
                image: {
                  type: "string",
                  format: "uri",
                },
              },
            },
            example: {
              subCategoryId: "6874f893dd64cc0bd7e9b9a4",
              name: "Day Care Centre",
              image: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/08a2a65a-4609-4696-bdea-84ad718911b4.png",
            },
          },
        },
      },
    },
  },
  "/api/master/sub-sub-sub-category/6889be477eba45afc4692be8": {
    parameters: [],
    put: {
      tags: ["master"],
      summary: "edit subsubsubcategory",
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                subSubCategoryId: {
                  type: "string",
                },
                name: {
                  type: "string",
                },
                image: {
                  type: "string",
                  format: "uri",
                },
              },
            },
            example: {
              subSubCategoryId: "6811f56d0fc7ca9045096845",
              name: "Art & Science",
              image: "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/08a2a65a-4609-4696-bdea-84ad718911b4.png",
            },
          },
        },
      },
    },
  },
  "/api/master/sub-sub-category/68612cc0ba947189b202a825": {
    parameters: [],
    delete: {
      tags: ["master"],
      summary: "delete sub sub category",
      parameters: [],
      responses: {},
    },
  },
  "/api/master/sub-sub-sub-category/68612cc0ba947189b202a825": {
    parameters: [],
    delete: {
      tags: ["master"],
      summary: "delete sub sub sub category",
      parameters: [],
      responses: {},
    },
  },
};
