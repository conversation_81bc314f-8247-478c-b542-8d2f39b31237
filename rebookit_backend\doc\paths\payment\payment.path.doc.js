module.exports = {
  "/api/payment/payment-intent": {
    parameters: [],
    post: {
      tags: ["payment"],
      summary: "payment intent",
      description: "This api can be used to create payment intent",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                planId: {
                  type: "string",
                  description: "The ID of the plan for which the payment intent is created",
                  example: "64b8c8f8f8f8f8f8f8f8f8f",
                },
                planQty: {
                  type: "integer",
                  description: "The quantity of the plan being purchased",
                  example: 1,
                },
              },
            },
          },
        },
      },
    },
  },
  "/api/payment/webhook": {
    parameters: [],
    post: {
      tags: ["payment"],
      summary: "payment webhook",
      description: "This api can be used for payment webhook",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                event: {
                  type: "object",
                  description: "The event object containing payment details",
                  properties: {
                    id: {
                      type: "string",
                      description: "The unique identifier for the payment event",
                      example: "evt_1J2Y3Z4A5B6C7D8E9F0G",
                    },
                    type: {
                      type: "string",
                      description: "The type of the event",
                      example: "payment_intent.succeeded",
                      enum: ["payment_intent.succeeded", "payment_intent.payment_failed"],
                    },
                  },
                },
                transactionId: {
                  type: "string",
                  description: "The ID of the transaction associated with the payment",
                  example: "64b8c8f8f8f8f8f8f8f8f8f",
                },
                planQty: {
                  type: "integer",
                  description: "The quantity of the plan being purchased",
                  example: 1,
                },
              },
            },
          },
        },
      },
    },
  },
  "/api/payment/history": {
    parameters: [],
    get: {
      tags: ["payment"],
      summary: "history",
      description: "This api can be used to get the history of the payments",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      responses: {},
    },
  },
  "/api/payment/query": {
    parameters: [],
    get: {
      tags: ["payment"],
      summary: "query payments",
      description: "This api can be used to get the detail of payment by query",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      parameters: [
        {
          name: "page",
          in: "query",
          required: false,
          example: "1",
          schema: {
            type: "integer",
          },
        },
        {
          name: "pageSize",
          in: "query",
          required: false,
          example: "10",
          schema: {
            type: "integer",
          },
        },
        {
          name: "status",
          in: "query",
          required: false,
          example: "success",
          schema: {
            type: "string",
          },
          enum: ["pending", "success", "failed"],
        },
        {
          name: "fromDate",
          in: "query",
          required: false,
          example: "2024-07-10T09:52:39.194Z",
          schema: {
            type: "integer",
          },
        },
        {
          name: "toDate",
          in: "query",
          required: false,
          example: "2026-07-14T09:52:39.194",
          schema: {
            type: "integer",
          },
        },
        {
          name: "minAmt",
          in: "query",
          required: false,
          example: "1",
          schema: {
            type: "integer",
          },
        },
        {
          name: "maxAmt",
          in: "query",
          required: false,
          example: "800",
          schema: {
            type: "integer",
          },
        },
        {
          name: "sortBy",
          in: "query",
          required: false,
          example: "status",
          schema: {
            type: "string",
          },
          enum: ["amount", "status", "createdAt"],
        },
        {
          name: "order",
          in: "query",
          required: false,
          example: "desc",
          schema: {
            type: "string",
          },
          enum: ["asc", "desc"],
        },
      ],
      responses: {},
    },
  },
};
