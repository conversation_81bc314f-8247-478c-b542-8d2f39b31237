module.exports = {
  "/api/user/send-otp/": {
    parameters: [],
    post: {
      tags: ["user"],
      summary: "send otp",
      description: "This api can be used to send otp for signup email verification",
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                email: {
                  type: "string",
                  description: "Input your email",
                  example: "<EMAIL>",
                },
                codeType: {
                  type: "string",
                  description: "Type of code to be sent",
                  example: "verification",
                  enum: ["verification"],
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/user/verify-otp": {
    parameters: [],
    post: {
      tags: ["user"],
      summary: "verify otp",
      description: "This api can be used to verify otp for signup email verification",
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                email: {
                  type: "string",
                  format: "email",
                  description: "Input your email",
                  example: "<EMAIL>",
                },
                otp: {
                  type: "string",
                  description: "Input the OTP sent to your email",
                  example: "123456",
                },
                codeType: {
                  type: "string",
                  description: "Type of code to be sent",
                  example: "verification",
                  enum: ["verification", "verification_on_listing", "verification_on_update"],
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/user/register": {
    parameters: [],
    post: {
      tags: ["user"],
      summary: "register",
      description: "This api can be used to register a new user after verifying email",
      parameters: [
        {
          name: "Content-Type",
          in: "header",
          required: false,
          example: "application/json",
          schema: {
            type: "string",
          },
        },
      ],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                firstName: {
                  type: "string",
                  description: "Input your first name",
                  example: "John",
                  maxLength: 50,
                  minLength: 2,
                },
                lastName: {
                  type: "string",
                  description: "Input your last name",
                  example: "Doe",
                  maxLength: 50,
                  minLength: 2,
                },
                password: {
                  type: "string",
                  description: "Input your password",
                  example: "Password@123",
                  minLength: 8,
                },
                email: {
                  type: "string",
                  format: "email",
                  description: "Input your email",
                  example: "<EMAIL>",
                },
                location: {
                  type: "object",
                  properties: {
                    type: {
                      type: "string",
                      description: "Type of location",
                      example: "Point",
                    },
                    coordinates: {
                      type: "array",
                      items: {
                        type: "integer",
                        description: "Coordinates of the location",
                        example: [77.5946, 12.9716],
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/user": {
    parameters: [],
    get: {
      tags: ["user"],
      summary: "get-user",
      description: "This api can be used to get user details",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      parameters: [],
      responses: {},
    },
  },

  "/api/user/login": {
    parameters: [],
    post: {
      tags: ["user"],
      summary: "login user",
      description: "This api can be used to login user",
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                email: {
                  type: "string",
                  format: "email",
                  description: "Input your email",
                  example: "<EMAIL>",
                },
                password: {
                  type: "string",
                  description: "Input your password",
                  example: "Password@123",
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/user/forgot-password": {
    parameters: [],
    post: {
      tags: ["user"],
      summary: "send otp forgot-password",
      description: "This api can be used to send otp for forgot password",
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                email: {
                  type: "string",
                  format: "email",
                  description: "Input your email",
                  example: "<EMAIL>",
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/user/verify-forgot-otp": {
    parameters: [],
    post: {
      tags: ["user"],
      summary: "verify otp forgot password",
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                email: {
                  type: "string",
                  format: "email",
                  description: "Input your email",
                  example: "<EMAIL>",
                },
                otp: {
                  type: "string",
                  description: "Input the OTP sent to your email",
                  example: "123456",
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/user/reset-password": {
    parameters: [],
    post: {
      tags: ["user"],
      summary: "reset password",
      description: "This api can be used to reset password after verifying otp for forgot password",
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                email: {
                  type: "string",
                  format: "email",
                  description: "Input your email",
                  example: "<EMAIL>",
                },
                otp: {
                  type: "string",
                  description: "Input the OTP sent to your email",
                  example: "123456",
                },
                newPassword: {
                  type: "string",
                  description: "Input your new password",
                  example: "newPassword123",
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/user/edit-profile": {
    parameters: [],
    put: {
      tags: ["user"],
      summary: "edit profile",
      description: "This api can be used to edit user profile data",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                  description: "Input your name",
                  example: "John Doe",
                },
                age: {
                  type: "integer",
                  description: "Input your age",
                  example: 30,
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/user/plan": {
    parameters: [],
    get: {
      tags: ["user"],
      summary: "Get list of available subscription plans for users, along with currently active",
      description:
        "Get list of available subscription plans (One of these object will contain extra key with 'currentPlan' with value true to identify which is current plan, for not showing upgrade or downgrade button ) for users along with current subscription plan of user",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      parameters: [],
      responses: {
        200: {
          headers: {
            "X-Powered-By": {
              schema: {
                type: "string",
              },
              example: "Express",
            },
            "Access-Control-Allow-Origin": {
              schema: {
                type: "string",
              },
              example: "*",
            },
            "Content-Type": {
              schema: {
                type: "string",
              },
              example: "application/json; charset=utf-8",
            },
            "Content-Length": {
              schema: {
                type: "integer",
              },
              example: "900",
            },
            ETag: {
              schema: {
                type: "string",
              },
              example: 'W/"384-lIrvTO4tDIvtcDlr52kySQl1EQk"',
            },
            Date: {
              schema: {
                type: "string",
              },
              example: "Mon, 21 Jul 2025 07:56:10 GMT",
            },
            Connection: {
              schema: {
                type: "string",
              },
              example: "keep-alive",
            },
            "Keep-Alive": {
              schema: {
                type: "string",
              },
              example: "timeout=5",
            },
          },
          description: "plans",
          content: {
            "application/json": {
              schema: {
                type: "object",
                properties: {
                  currentSubscription: {
                    type: "object",
                    properties: {
                      _id: {
                        type: "string",
                        description: "Unique identifier for the subscription",
                        example: "60c72b2f9b1e8c001c8f4e2a",
                      },
                      userId: {
                        type: "string",
                        description: "Unique identifier for the user",
                        example: "60c72b2f9b1e8c001c8f4e2b",
                      },
                      startDate: {
                        type: "string",
                        format: "date-time",
                        description: "Start date of the subscription",
                        example: "2025-07-21T07:56:10Z",
                      },
                      endDate: {
                        type: "string",
                        format: "date-time",
                        description: "End date of the subscription",
                        example: "2025-08-21T07:56:10Z",
                      },
                      status: {
                        type: "string",
                        description: "Current status of the subscription",
                        example: "active",
                      },
                      planId: {
                        type: "object",
                        properties: {
                          _id: {
                            type: "string",
                            description: "Unique identifier for the plan",
                            example: "60c72b2f9b1e8c001c8f4e2c",
                          },
                          planName: {
                            type: "string",
                            description: "Name of the plan",
                            example: "Premium Plan",
                          },
                          planMonths: {
                            type: "string",
                            description: "Duration of the plan",
                            example: "Monthly",
                            enum: ["Monthly", "Quarterly", "Half-Yearly", "Yearly"],
                          },
                          planType: {
                            type: "string",
                            description: "Type of the plan",
                            example: "paid",
                            enum: ["free", "paid"],
                          },
                          price: {
                            type: "integer",
                            description: "Price of the plan",
                            example: 9999,
                          },
                          active: {
                            type: "boolean",
                            description: "Indicates if the plan is active",
                            example: true,
                          },
                          listings: {
                            type: "array",
                            items: {},
                          },
                          createdAt: {
                            type: "string",
                            format: "date-time",
                            description: "Timestamp when the plan was created",
                            example: "2021-06-01T12:00:00Z",
                          },
                          updatedAt: {
                            type: "string",
                            format: "date-time",
                            description: "Timestamp when the plan was last updated",
                            example: "2021-07-24T12:00:00Z",
                          },
                        },
                      },
                      remainingListings: {
                        type: "array",
                        items: {},
                        description: "List of remaining listings under the subscription",
                        example: [
                          {
                            _id: "60c72b2f9b1e8c001c8f4e2d",
                            category: "Electronics",
                            noOfListing: 5,
                            listingValidityDays: 30,
                          },
                        ],
                      },
                      notifications: {
                        type: "array",
                        items: {},
                      },
                      createdAt: {
                        type: "string",
                        format: "date-time",
                        description: "Timestamp when the subscription was created",
                        example: "2021-06-01T12:00:00Z",
                      },
                      updatedAt: {
                        type: "string",
                        format: "date-time",
                        description: "Timestamp when the subscription was last updated",
                        example: "2021-07-24T12:00:00Z",
                      },
                    },
                  },
                  queuedPlanCount: {
                    type: "integer",
                    description: "Number of plans queued for the user",
                    example: 2,
                  },
                  plans: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        _id: {
                          type: "string",
                          description: "Unique identifier for the plan",
                          example: "60c72b2f9b1e8c001c8f4e2c",
                        },
                        planName: {
                          type: "string",
                          description: "Name of the plan",
                          example: "Premium Plan",
                        },
                        planMonths: {
                          type: "string",
                          description: "Duration of the plan",
                          example: "Monthly",
                          enum: ["Monthly", "Quarterly", "Half-Yearly", "Yearly"],
                        },
                        planType: {
                          type: "string",
                          description: "Type of the plan,",
                          example: "paid",
                          enum: ["free", "paid"],
                        },
                        price: {
                          type: "integer",
                          description: "Price of the plan",
                          example: 9999,
                        },
                        boosts: {
                          type: "integer",
                          description: "Number of boosts included in the plan",
                          example: 5,
                        },
                        listings: {
                          type: "array",
                          items: {
                            type: "object",
                            properties: {
                              category: {
                                type: "string",
                                description: "Category of the listing",
                                example: "Books",
                              },
                              noOfListing: {
                                type: "integer",
                                description: "Number of listings in the plan",
                                example: 10,
                              },
                              listingValidityDays: {
                                type: "integer",
                                description: "Validity period of the listings in days",
                                example: 30,
                              },
                              _id: {
                                type: "string",
                                description: "Unique identifier for the listing",
                                example: "60d5ec49f1c2b14b2c8f4e1a",
                              },
                            },
                          },
                        },
                        active: {
                          type: "boolean",
                          description: "Indicates if the plan is active",
                          example: true,
                        },
                        createdAt: {
                          type: "string",
                          format: "date-time",
                          description: "Timestamp when the plan was created",
                          example: "2021-06-01T12:00:00Z",
                        },
                        updatedAt: {
                          type: "string",
                          format: "date-time",
                          description: "Timestamp when the plan was last updated",
                          example: "2021-06-01T12:00:00Z",
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/user/rebooker-of-month": {
    parameters: [],
    get: {
      tags: ["user"],
      summary: "rebooker-of-the-month",
      parameters: [],
      responses: {},
    },
  },

  "/api/user/testimonials/": {
    parameters: [],
    get: {
      tags: ["user"],
      summary: "testimonials",
      parameters: [],
      responses: {},
    },
  },

  "/api/user/{userId}": {
    parameters: [],
    put: {
      tags: ["user"],
      summary: "update user - admin",
      description: "This api can be used to update user details by admin",
      security: {
        bearerAuth: [{ bearerAuth: [] }],
      },
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                status: {
                  type: "string",
                  description: "New status of the user",
                  example: "active",
                  enum: ["active", "suspended"],
                },
              },
            },
          },
        },
      },
    },
  },

  "/api/user/support-request": {
    parameters: [],
    post: {
      tags: ["user"],
      summary: "support-request",
      description: "This api can be used to send support request",
      parameters: [],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                email: {
                  type: "string",
                  format: "email",
                  description: "Input your email",
                  example: "<EMAIL>",
                },
                name: {
                  type: "string",
                  description: "Input your name",
                  example: "John Doe",
                },
                message: {
                  type: "string",
                  description: "Input your message",
                  example: "I need help with my account",
                },
                subject: {
                  type: "string",
                  description: "Subject of the support request",
                  example: "Account Assistance",
                },
              },
            },
          },
        },
      },
    },
  },
  "/api/user/filter-support-request": {
    parameters: [],
    post: {
      tags: ["user"],
      summary: "filter-support-request",
      description: "This api can be used to get support request on the bases of filters",
      parameters: [
        {
          name: "page",
          in: "query",
          required: false,
          example: "1",
          schema: {
            type: "integer",
          },
        },
        {
          name: "pageSize",
          in: "query",
          required: false,
          example: "50",
          schema: {
            type: "integer",
          },
        },
      ],
      responses: {},
      requestBody: {
        content: {
          "application/json": {
            schema: {
              type: "object",
              properties: {
                name: {
                  type: "string",
                  format: "name",
                  description: "Input your name",
                  example: "testName",
                },
                subject: {
                  type: "string",
                  description: "Input your subject",
                  example: "testsubject",
                },
              },
            },
          },
        },
      },
    },
  },
};
