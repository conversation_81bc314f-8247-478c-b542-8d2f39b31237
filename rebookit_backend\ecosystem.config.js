const os = require("os");
const dotenv = require("dotenv");
dotenv.config();

const noOfCpu = os.cpus.length();
const instances = noOfCpu;
const basePort = process.env.PORT;

const apps = [];

for (let i = 0; i < instances; i++) {
  const port = basePort + i;
  apps.push({
    name: `rebookit-backend-${port}`,
    script: "server.js", 
    instances: 1,
    exec_mode: "fork",
    env: {
      NODE_ENV: "production",
      PORT: port,
      // add other env vars (DB URLs, REDIS_URL, etc)
    },
    // optional log and restart settings
    watch: false,
    max_restarts: 10,
  });
}

module.exports = {
  apps,
};
