const mongoose = require("mongoose");
mongoose.set("debug", true);
const dotenv = require("dotenv");
dotenv.config();

const { BadRequestError } = require("./common/customErrors");
const { PlanTypeEnum } = require("./common/Enums");
const subscriptionModel = require("./tables/schema/subscription");
const subscriptionPlanModel = require("./tables/schema/subscription_plans");

async function connect() {
  try {
    await mongoose.connect(process.env.DB_URL);
    console.log("Connected to MongoDB");
  } catch (error) {
    console.error("Error connecting to MongoDB:", error);
  }
}

const assignFreePlan = async () => {
  await connect();
  const freePlan = await subscriptionPlanModel.findOne({ planType: PlanTypeEnum.FREE });
  if (!freePlan) throw new BadRequestError("Free plan not found.");

  const freeUsers = await subscriptionModel.find({ planId: freePlan._id }).populate("planId");
  console.log(`${freeUsers.length} free users found.`);

  for (const freeUser of freeUsers) {
    // console.log("free user",  freeUser.remainingBoosts, freeUser.remainingListings)
    await subscriptionModel.findOneAndUpdate(
      { _id: freeUser._id },
      {
        $set: {
          remainingListings: freeUser.planId.listings,
          remainingBoosts: freeUser.planId.boosts,
        },
      }
    );
  }
};

assignFreePlan();
