const pino = require("pino");
const config = require("./config");
const dotenv = require("dotenv");
const { ENVIRONMENTS } = require("./constants");
// const rotationStream = require("./rotation");
const pinoElastic = require("pino-elasticsearch");

dotenv.config();
class LoggerFactory {
  static #instance = null;
  #logger = null;

  constructor() {
    if (LoggerFactory.#instance) {
      return LoggerFactory.#instance;
    }
    LoggerFactory.#instance = this;
  }

  initialize(options = {}) {
    const env = process.env.NODE_ENV || ENVIRONMENTS.DEVELOPMENT;
    const baseConfig = config[env];
    try {
      const streamToElastic =
        env === ENVIRONMENTS.PRODUCTION
          ? pinoElastic({
              index: "app-log",
              node: process.env.ELASTICSEARCH_URL,
              esVersion: 7,
              flushBytes: 1000,
            })
          : {};

      this.#logger = pino(
        {
          ...baseConfig,
          ...options,
        },
        streamToElastic
      );

      // pino({ level: 'info' }, streamToElastic)

      // Log initialization success
      this.#logger.info(
        {
          env,
          nodeVersion: process.version,
          pid: process.pid,
        },
        "Logger initialized successfully"
      );
    } catch (error) {
      // Fallback to basic configuration if there's an error
      console.error("Error initializing logger:", error);
      this.#logger = pino({
        level: "info",
        timestamp: true,
      });
    }

    return this.#logger;
  }

  getLogger() {
    if (!this.#logger) {
      this.initialize();
    }
    return this.#logger;
  }

  createChildLogger(bindings) {
    const logger = this.getLogger();
    return logger.child(bindings);
  }
}

module.exports = new LoggerFactory();
