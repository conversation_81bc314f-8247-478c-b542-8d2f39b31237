const { ForbiddenError } = require("../common/customErrors");
const { PlanStatusEnum } = require("../common/Enums");
const subscriptionModel = require("../tables/schema/subscription");

const checkPlanLimit = async (req, res, next) => {
  try {
    const activeSubscription = await subscriptionModel.findOne({
      userId: req.user._id,
      status: PlanStatusEnum.ACTIVE,
    });

    // Need to create free plan if there is no queued plan
    if(!activeSubscription) {
      throw new ForbiddenError("You don't have any active subscription plan, please try again after one hour");
    }
    
    if(activeSubscription.remainingPostListing < 1) {
      throw new ForbiddenError("You've used all of your listing quota");
    }

    if(activeSubscription.remainingAds < 1) {
      throw new ForbiddenError("You've used all of your ads quota");
    }

    if(activeSubscription.remainingFeautureListings < 1) {
      throw new ForbiddenError("You've used all of your featured listing quota");
    }
    next();
  } catch (error) {
    next(error);
  }
};

module.exports = {
  checkPlanLimit,
};
