const Joi = require("joi");
const { InternalServerError } = require("../common/customErrors");
const ErrorLogger = require("../logger/error-logger");

const errorHandler = (err, req, res, next) => {
  ErrorLogger.logWarning(err);
  let error = {
    statusCode: err.statusCode || 500,
    message: err.message || "Internal Server Error",
    errors: [],
  };

  // Joi Validation Error
  if (err instanceof Joi.ValidationError) {
    error.statusCode = 400;
    error.message = "Validation Error";
    error.errors = err.details.map((detail) => ({
      field: detail.path.join("."),
      message: detail.message.replace(/['"]+/g, ""),
      type: detail.type,
    }));
  }

  // Mongoose Validation Error
  else if (err.name === "ValidationError") {
    error.statusCode = 400;
    error.message = "Validation Error";
    error.errors = Object.values(err.errors).map((e) => ({
      field: e.path,
      message: e.message,
      type: e.kind,
    }));
  }
  // Mongoose Cast Error
  else if (err.name === "CastError") {
    error.statusCode = 400;
    error.message = `Can not cast to object of value ${err.value}`;
  }
  // MongoDB Duplicate Key Error
  else if (err.code === 11000) {
    error.statusCode = 400;
    error.message = "Duplicate field value entered";
    const field = Object.keys(err.keyValue)[0];
    error.errors.push({
      field,
      message: `${field} already exists`,
    });
  }
  const internalServerStatusCode = new InternalServerError().statusCode;
  if (error.statusCode === internalServerStatusCode) {
    ErrorLogger.logError(err, {
      url: req.url,
      payload: req.body,
      query: req.query,
      user: req.user,
    });
  }

  const response = {
    statusCode: error.statusCode,
    message: error.message,
  };

  if (error.errors.length > 0) response.errors = error.errors;
  if (process.env.NODE_ENV === "development") response.stack = err.stack;

  res.status(error.statusCode).json(response);
};

module.exports = errorHandler;
