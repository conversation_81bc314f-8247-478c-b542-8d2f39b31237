const jwt = require("jsonwebtoken");
const dotenv = require("dotenv");
const userModel = require("../tables/schema/user");
const { UnauthorizedError, BadRequestError } = require("../common/customErrors");
const { UserStatusEnum } = require("../common/Enums");

dotenv.config({});

const verifyNormalUser = async function (req, res, next) {
  try {
    
    const authHeader = req?.headers?.["authorization"]?.split("Bearer ")?.[1];

    if (!authHeader) throw new UnauthorizedError();
    let userData = jwt.verify(authHeader, process.env.secretKey);
    
    if (userData?._id) {
      const user = await userModel.findById(userData?._id);

      if (user) {
        if( user.status === UserStatusEnum.SUSPENDED) throw new UnauthorizedError("Your account has been suspended, please contact admin")
        req.user = user;
        next();
      } else throw new UnauthorizedError();
    }
  } catch (error) {
    next(error);
  }
};

const verifyAdmin = async function (req, res, next) {
  try {
    const authHeader = req?.headers?.["authorization"]?.split("Bearer ")?.[1];

    if (!authHeader) throw new UnauthorizedError();
    let userData = jwt.verify(authHeader, process.env.secretKey);
    if (userData) {
      const user = await userModel.findById(userData._id).populate("roleId");

      if (user?.roleId?.roleName == "admin") {
        req.user = user;
        next();
      } else throw new UnauthorizedError();
    }
  } catch (error) {
    next(error)
  }
};

const verifySocketConnection = async function (socket, next) {
  try {
    const authHeader =
      socket?.handshake.headers?.["authorization"]?.split("Bearer ")?.[1];
    if (!authHeader) next(new Error("Not authorized"));
    let userData = jwt.verify(authHeader, process.env.secretKey);
    if (userData?._id) {
      const user = await userModel.findById(userData._id);

      if (user) {
        socket.user = user;
        next();
      } else next(new Error("Not authorized"));
    }
  } catch (error) {
    next(new Error("Not authorized"));
  }
};

module.exports = {
  verifyNormalUser,
  verifyAdmin,
  verifySocketConnection,
};
