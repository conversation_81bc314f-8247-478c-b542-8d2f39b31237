{"name": "rebookit", "version": "1.0.0", "type": "commonjs", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon server.js", "start": "node --max-old-space-size=6000 server.js", "seedData": "node ./seed/seed.js"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@sendgrid/mail": "^8.1.5", "@socket.io/redis-adapter": "^8.3.0", "amqplib": "^0.10.7", "axios": "^1.8.4", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.7", "express": "^5.1.0", "express-rate-limit": "^7.5.0", "install": "^0.13.0", "ioredis": "^5.6.0", "joi": "^17.13.3", "jsonwebtoken": "^9.0.2", "luxon": "^3.7.1", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "mongoose": "^8.13.2", "morgan": "^1.10.0", "multer": "^1.4.5-lts.2", "node-cron": "^4.0.7", "nodemailer": "^7.0.3", "nodemon": "^3.1.9", "npm": "^11.5.1", "pino": "^9.7.0", "pino-elasticsearch": "^8.1.0", "pino-pretty": "^13.1.1", "prisma": "^6.5.0", "rotating-file-stream": "^3.2.6", "socket.io": "^4.8.1", "stripe": "^18.0.0", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0"}}