const amqp = require("amqplib");
const { queueName } = require("./queueNames");

let channel, connection;

async function initQueue(retries = 5, delay = 3000) {
  while (retries) {
    try {
      connection = await amqp.connect(process.env.RABBIT_MQ_SERVER);
      channel = await connection.createChannel();

      // Await all assertions
      await Promise.all(Object.values(queueName).map((q) => channel.assertQueue(q, { durable: true })));

      console.log("Queue connected");
      return connection;
    } catch (err) {
      console.log(`RabbitMQ not ready, retrying in ${delay / 1000}s…`);
      retries--;
      await new Promise((res) => setTimeout(res, delay));
    }
  }
  throw new Error("Failed to connect to RabbitMQ after retries");
}

async function sendData(queueName, data) {
  if (!channel) throw new Error("Channel not initialized");
  channel.sendToQueue(queueName, Buffer.from(JSON.stringify(data)), { persistent: true });
  console.log("data sent to queue", queueName, data);
}

function receiveData(callback) {
  if (!channel) throw new Error("Channel not initialized");
  Object.keys(queueName).map((queue) => {
    channel.consume(
      queueName[queue],
      (msg) => {
        const content = msg.content.toString();
        callback(queue, content);
        channel.ack(msg);
      },
      {
        noAck: false,
      }
    );
    // console.log(queue + " queue intialized")
  });
}

module.exports = {
  initQueue,
  sendData,
  receiveData,
};