const { queueName, NotificationEventName } = require("./queueNames");
const {
  sendWelcomeEmail,
  sendOtpForgotPassword,
  send7DayReminderEmail,
  send3DayReminderEmail,
  send1DayReminderEmail,
  sendOtpEmailVerification,
  sendPlanPaymentSuccessEmail,
  sendPlanPaymentFailureEmail,
  sendItemApprovedEmail,
  sendItemRejectedEmail,
  sendPlanActiveEmail,
} = require("../utils/mail");
const userModel = require("../tables/schema/user");

const QueueEventHandlers = async (queue, message) => {
  try {
    const data = JSON.parse(message) || message;

    if (queue == queueName["NOTIFICATION_QUEUE"]) {
      console.log("Event recieved:", queue);
      console.log("Data recieved", data)

      switch (data.EventName) {
        case NotificationEventName["USER_REGISTERED"]: {
          try {
            console.log("User Registerd Congratulation:", data.data._id);
            let UserInfo = await userModel.findById(data.data._id);
            sendWelcomeEmail({
              to: UserInfo.email,
            });
          } catch (error) {
            console.log(error);
          }
          break;
        }

        case NotificationEventName["SEND_OTP_FORGOT_PASS"]: {
          try {
            const { email } = data.data;
            let UserInfo = await userModel.findOne({ email });

            sendOtpForgotPassword({
              to: UserInfo.email,
              Otp: data.data.otpNumber,
            });
          } catch (error) {
            console.log(error);
          }
          break;
        }

        case NotificationEventName["SUBSCRIPTION_GETTING_EXPIRE"]: {
          try {
            const { email, userName, planName, endDate, dayBefore } = data.data;
            const emailInputs = { to: email, userName, planName, endDate };
            if (dayBefore == 7) {
              send7DayReminderEmail(emailInputs);
            } else if (dayBefore == 3) {
              send3DayReminderEmail(emailInputs);
            } else if (dayBefore == 1) {
              send1DayReminderEmail(emailInputs);
            } else break;
          } catch (error) {
            console.log(error);
          }
          break;
        }

        case NotificationEventName["SUBSCRIPTON_PURCHASE_SUCCESSFULL"]: {
          try {
            sendPlanPaymentSuccessEmail(data.data);
          } catch (error) {
            console.log(error);
          }
          break;
        }

        case NotificationEventName["SUBSCRIPTON_PURCHASE_FAILED"]: {
          try {
            sendPlanPaymentFailureEmail(data.data);
          } catch (error) {
            console.log(error);
          }
          break;
        }

        // CASE FOR ITEM APPROVED NOTIFICATION
        case NotificationEventName["ITEM_APPROVED"]: {
          try {
            let recipientEmail = null;
            if (data.data.body.createdBy) {
              const user = await userModel.findById(data.data.body.createdBy).lean();
              if (user && user.email) {
                recipientEmail = user.email;
              }
            }
            if (!recipientEmail) {
              throw new Error("No recipient email found for ITEM_APPROVED notification");
            }

            sendItemApprovedEmail({
              to: recipientEmail,
              itemName: data.data.body.title,
              itemId: data.data.id,
            });
            console.log("data recieved:", data.data);
          } catch (error) {
            console.log(error);
          }
          break;
        }


        // CASE FOR ITEM REJECT NOTIFICATION
        case NotificationEventName["ITEM_REJECTED"]: {
          try {            // Fetch the user who created the item to get their email
            let recipientEmail = null;
            if (data.data.body.createdBy) {
              const user = await userModel.findById(data.data.body.createdBy).lean();
              if (user && user.email) {
                recipientEmail = user.email;
              }
            }
            if (!recipientEmail) {
              throw new Error("No recipient email found for ITEM_REJECTED notification");
            }
            sendItemRejectedEmail({
              to: recipientEmail,
              itemName: data.data.body.title,
              // itemId: data.data.id,
              reason: data.data.reason? data.data.reason : ""
            });
            console.log("data recieved:", data.data);
          } catch (error) {
            console.log(error);
          }
          break;
        }


        // CASE FOR QUEUED PLAN ACTIVATION
        case NotificationEventName["ACTIVE_QUEUED_PLAN"]: {
          try {            // Fetch the user who created the item to get their email
            let recipientEmail = null;
            if (data.data.body.userId._id) {
              const user = await userModel.findById(data.data.body.userId._id).lean();
              if (user && user.email) {
                recipientEmail = user.email;
              }
            }
            if (!recipientEmail) {
              throw new Error("No recipient email found for ACTIVE_QUEUED_PLAN notification");
            }
            sendPlanActiveEmail({
              to: recipientEmail,
              planName: data.data.body.planId.planName,
              userName: data.data.body.userId.firstName
            });
            console.log("data recieved:", data.data);
          } catch (error) {
            console.log(error);
          }
          break;
        }
      }
    }
  } catch (e) {
    console.error("Invalid JSON message:", message);
  }
};

module.exports = {
  QueueEventHandlers,
};
