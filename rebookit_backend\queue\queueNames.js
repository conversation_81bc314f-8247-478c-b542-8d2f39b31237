let queueName = {
  NOTIFICATION_QUEUE: "NOTIFICATION_QUEUE",
  ELASTIC_SEARCH_QUEUE: "ELASTIC_SEARCH_QUEUE",
};

let NotificationEventName = {
  USER_REGISTERED: "USER_REGISTERED",
  SEND_OTP_FORGOT_PASS: "SEND_OTP_FORGOT_PASS",
  SUBSCRIPTION_GETTING_EXPIRE: "SUBSCRIPTION_GETTING_EXPIRE",
  ITEM_APPROVED: "ITEM_APPROVED",
  ITEM_REJECTED: "ITEM_REJECTED",
  ITEM_HOLD: "ITEM_HOLD",
  SUBSCRIPTON_PURCHASE_SUCCESSFULL: "SUBSCRIPTON_PURCHASE_SUCCESSFULL",
  SUBSCRIPTON_PURCHASE_FAILED: "SUBSCRIPTON_PURCHASE_FAILED",
  ACTIVE_QUEUED_PLAN: "ACTIVE_QUEUED_PLAN"
};
let ElasticsearchEventName = {
  create_index: "create_index",
  index_document: "index_document",
  search: "search",
};

module.exports = {
  queueName,
  NotificationEventName,
  ElasticsearchEventName,
};
