const UserRoutes = require("./routes/user.routes");
const AdminRoutes = require("./routes/admin.routes");
const BooksRoutes = require("./routes/books.routes");
const PaymentRoutes = require("./routes/payment.routes");
const ItemRoutes = require("./routes/item.routes");
const MasterDataRoutes = require("./routes/master-data.routes");
const ChatRoutes = require("./routes/chat.routes");
const CommunityRoutes = require("./routes/community.routes");
const FaqRoutes = require("./routes/faq.routes");
const AdRoutes = require("./routes/ad.routes");

const routes = [
  {
    path: "/api/user",
    router: UserRoutes,
  },
  {
    path: "/api/admin",
    router: AdminRoutes,
  },
  {
    path: "/api/books",
    router: BooksRoutes,
  },
  {
    path: "/api/item",
    router: ItemRoutes,
  },
  {
    path: "/api/community",
    router: CommunityRoutes,
  },
  {
    path: "/api/master",
    router: MasterDataRout<PERSON>,
  },
  {
    path: "/api/chat",
    router: ChatRoutes,
  },
  {
    path: "/api/payment",
    router: PaymentRoutes,
  },
  {
    path: "/api/faqs",
    router: FaqRoutes,
  },
   {
   path: "/api/ad-management",
   router: AdRoutes,
 }
];


module.exports = routes;
