const express = require("express");
const router = express.Router();
const AdController = require("../controllers/ad-management.controller");
const validator = require("../validation/validator");
const { verifyNormalUser, verifyAdmin } = require("../middleware/middleware");
const wrapAsync = require("../common/wrapAsync");
const { idParamSchema, CreateAdResourcesSchema, UpdateAdResourcesSchema, setPriceRuleAd, UpdateOverridePriceRuleAd, setOverridePriceRuleAd, updatePriceRuleAd, updateBasePriceSchema } = require("../validation/ad.validation");


//Unprotected routes
// USER ROUTES
router.get("/getAdPlans", wrapAsync(AdController.getAdPlans));
router.get("/getAdPlanById/:id", wrapAsync(AdController.getAdPlanById));
router.post("/create", verifyNormal<PERSON>ser, wrapAsync(AdController.createAds));
router.post("/compaigns", verifyNormal<PERSON>ser, wrapAsync(AdController.createCompaigns));
router.post("/create-payment-intent", verifyNormalUser, wrapAsync(AdController.createAdPaymentIntent));


// Protected routes: ADMIN ROUTES
router.get("/", verifyAdmin, wrapAsync(AdController.getAdResources));
router.post("/", verifyAdmin, validator(CreateAdResourcesSchema), wrapAsync(AdController.createAdResources));
router.post("/priceRule", verifyAdmin, validator(setPriceRuleAd), wrapAsync(AdController.setPriceRules));
router.put("/priceRule", verifyAdmin, validator(updatePriceRuleAd), wrapAsync(AdController.updatePriceRules));
router.post("/overrideRule", validator(setOverridePriceRuleAd), verifyAdmin, wrapAsync(AdController.setOverrideRules));
router.put("/overrideRule", validator(UpdateOverridePriceRuleAd), verifyAdmin, wrapAsync(AdController.updateOverrideRules));
router.delete("/priceRule/", verifyAdmin, wrapAsync(AdController.deletePriceRules));
router.delete("/overrideRule/", verifyAdmin, wrapAsync(AdController.deleteOverrides));
router.put("/basePrice", validator(updateBasePriceSchema), verifyAdmin, wrapAsync(AdController.updateBasePrices));

// router.put("/:id", validator(idParamSchema, "params"), verifyAdmin, validator(UpdateAdResourcesSchema), wrapAsync(AdController.updateAdResources));
router.put("/ads/:id", verifyAdmin, wrapAsync(AdController.updateAdStatus));
router.delete("/:id", validator(idParamSchema, "params"), verifyAdmin, wrapAsync(AdController.deleteAdResources));
router.get("/:id", verifyAdmin, wrapAsync(AdController.getAdResourcesById));

router.post("/webhook", verifyNormalUser, wrapAsync(AdController.webhook));




module.exports = router;