const express = require("express");
const router = express.Router();

const AdminController = require("../controllers/admin.controller");

const validator = require("../validation/validator");
const { verifyAdmin, verifyNormalUser } = require("../middleware/middleware");
const { subscriptionPlanSchema, updateSubscriptionPlanSchema, filterSearchMemberSchema } = require("../validation/admin.subscription.validation");
const { upload } = require("../utils/fileUpload");
const { pendingItemsList } = require("../controllers/item.controller");
const { itemModifySchema } = require("../validation/admin.item.validation");
const wrapAsync = require("../common/wrapAsync");
const { createTestimonialSchema, updateTestimonialSchema, idParamSchema } = require("../validation/admin.testimonial.validation");
const { paginationQuerySchema } = require("../validation/common-schema");


// upload routes
router.post("/single-upload", verifyNormalUser, upload.single("file"), wrapAsync(AdminController.uploadSingleFile))
router.post("/multiple-upload", verifyNormalUser, upload.array("files", 20), wrapAsync(AdminController.uploadMultipleFile))


router.get("/", verifyAdmin, wrapAsync(AdminController.getAdminDetails));

// subscriptions plans routes
router.post("/plan", verifyAdmin, validator(subscriptionPlanSchema), wrapAsync(AdminController.createSubscriptionPlan));
router.get("/plan", wrapAsync(AdminController.getSubscriptionPlans));
router.put("/plan/:id", verifyAdmin, validator(updateSubscriptionPlanSchema), wrapAsync(AdminController.updateSubscriptionPlan));
router.post("/members/filter-search", verifyAdmin, validator(paginationQuerySchema,'query'), validator(filterSearchMemberSchema), wrapAsync(AdminController.filterMembers));

// item
router.post("/item/actionStatus/:id", verifyAdmin, validator(itemModifySchema), wrapAsync(AdminController.itemStatusUpdate))
router.delete("/item/:id", verifyAdmin, wrapAsync(AdminController.deleteItem));

//user routes for admin
router.post("/search-users", verifyAdmin, wrapAsync(AdminController.getUsers));
router.get("/user/:id", verifyAdmin, validator(idParamSchema), wrapAsync(AdminController.getUserById));


// DASHBOARD ROUTES
router.get("/dashboard/overview", verifyAdmin, wrapAsync(AdminController.getOverview));   // need to REDO

router.get("/dashboard/lastMonthSubscribers", verifyAdmin, wrapAsync(AdminController.getLastMonthSubscribers));

router.get("/dashboard/totalAdRequest", verifyAdmin, wrapAsync(AdminController.getTotalAdRequest));


// TESTIMONIAL ROUTES
router.post("/testimonial", verifyAdmin, validator(createTestimonialSchema), wrapAsync(AdminController.createTestimonials));   // CREATE TESTIMONIAL
router.put("/testimonial/:id", validator(idParamSchema, "params"), verifyAdmin, validator(updateTestimonialSchema), wrapAsync(AdminController.updateTestimonials));    // UPDATE TESTIMONIAL
router.delete("/testimonial/:id", validator(idParamSchema, "params"), verifyAdmin, wrapAsync(AdminController.deleteTestimonials)); // DELETE TESTIMONIAL
router.get("/testimonial", verifyAdmin, wrapAsync(AdminController.getTestimonials)); 


module.exports = router