const express = require("express");
const router = express.Router();

const { deleteChat, getAllChats, getChatById, getChatByItemId } = require("../controllers/chat.controller.js");
const { verifyNormalUser } = require("../middleware/middleware.js");
const wrapAsync = require("../common/wrapAsync.js");

router.post("/all", verifyNormalUser, wrapAsync(getAllChats));
router.get("/:id", verifyNormalUser, wrapAsync(getChatById));
router.delete("/:id", verifyNormalUser, wrapAsync(deleteChat));
router.get("/item/:id", verifyNormalUser, wrapAsync(getChatByItemId));

module.exports = router;
