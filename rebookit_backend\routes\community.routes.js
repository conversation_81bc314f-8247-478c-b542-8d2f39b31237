const express = require("express");
const {
  addQuestion,
  fetchCommunityFilteredData,
  submitAnswer,
  deleteQuestion,
  deleteAnswer,
  fetchQuestionAndResponses,
  getUserQuestions,
  getAllQuestions,
  getUserQuestionsWithAnswers,
  getUserAnswers,
  editAnswer,
} = require("../controllers/community.controller");
const { verifyNormalUser, verifyAdmin } = require("../middleware/middleware");
const validator = require("../validation/validator");
const {
  questionSchema,
  answerSchema,
  allQuestionsQuerySchema,
  idParamSchema,
  userQuestionsQuerySchema,
  editAnswerQuerySchema,
  editAnswerBodySchema,
} = require("../validation/community.validation");
const wrapAsync = require("../common/wrapAsync");

const router = express.Router();

router.post("/question", verifyNormal<PERSON>ser, validator(questionSchema), wrapAsync(addQuestion));
router.post("/answer", verifyNorm<PERSON><PERSON><PERSON>, validator(answerSchema), wrapAsync(submitAnswer));
router.put("/answer", verifyNormal<PERSON>ser, validator(editAnswerQuerySchema, "query"), validator(editAnswerBodySchema), wrapAsync(editAnswer));
router.get("/user-questions", verifyNormalUser, validator(userQuestionsQuerySchema, "query"), wrapAsync(getUserQuestions));
router.get("/questions", validator(allQuestionsQuerySchema, "query"), wrapAsync(getAllQuestions));
router.get(
  "/user-questions-with-answer",
  verifyNormalUser,
  validator(userQuestionsQuerySchema, "query"),
  wrapAsync(getUserQuestionsWithAnswers)
);
router.get("/user-answers", verifyNormalUser, validator(userQuestionsQuerySchema, "query"), wrapAsync(getUserAnswers));
router.get("/question/:id", validator(idParamSchema, "params"), wrapAsync(fetchQuestionAndResponses));
router.delete("/question/:id", verifyNormalUser, validator(idParamSchema, "params"), wrapAsync(deleteQuestion));
router.delete("/answer/:id", verifyNormalUser, validator(idParamSchema, "params"), wrapAsync(deleteAnswer));

module.exports = router;
