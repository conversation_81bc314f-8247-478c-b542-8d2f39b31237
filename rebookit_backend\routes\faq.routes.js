const express = require("express");
const router = express.Router();
const FaqController = require("../controllers/faq.controller");
const { createFaqSchema, filterFaqsSchema, updateFaqSchema, faqIdParam, filterFaqsQuerySchema } = require("../validation/faq.validation");
const validator = require("../validation/validator");
const { verifyNormalUser, verifyAdmin } = require("../middleware/middleware");
const wrapAsync = require("../common/wrapAsync");

router.post("/", verifyAdmin, validator(createFaqSchema), wrapAsync(FaqController.addFaq));
router.post("/filter-search", verifyNormalUser, validator(filterFaqsSchema), validator(filterFaqsQuerySchema,'query'), wrapAsync(FaqController.filterFaqs));
router.post("/filter-search-admin", verifyAdmin, validator(filterFaqsSchema), validator(filterFaqsQuerySchema,'query'), wrapAsync(FaqController.filterFaqs));
router.put("/:id", verifyAdmin, validator(faqIdParam, "params"), validator(updateFaqSchema), wrapAsync(FaqController.updateFaq));
router.delete("/:id", verifyAdmin, validator(faqIdParam, "params"), wrapAsync(FaqController.deleteFaq));

module.exports = router;
