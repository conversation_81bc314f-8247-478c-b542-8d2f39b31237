const express = require("express");
const {
  addItem,
  getItemById,
  itemBookmarked,
  bookmarksList,
  removeBookmark,
  addReview,
  searchItems,
  GetReviews,
  getAutoCompleteItems,
  updateItem,
  boostItem,
  searchItemsAdmin,
  searchItemsCurrentUser,
  getNearbyTutors,
} = require("../controllers/item.controller");
const {
  itemBookmarkSchema,
  addReviewValidate,
  createItemSchema,
  searchBooksSchema,
  userIdParam,
  searchBookQuerySchema,
  getAutoCompleteItemsQuerySchema,
  updateItemSchema,
  idParamSchema,
  searchBookQuerySchemaPublic,
} = require("../validation/item.validation");
const { verifyNormalUser, verifyAdmin } = require("../middleware/middleware");
const validator = require("../validation/validator");
const wrapAsync = require("../common/wrapAsync");
const { checkPlanLimit } = require("../middleware/check-plan-limit.middelware");
const { paginationQuerySchema } = require("../validation/common-schema");

const router = express.Router();

router.post("/", verifyNormalUser, validator(createItemSchema), wrapAsync(addItem));
router.post("/search", validator(searchBooksSchema), validator(searchBookQuerySchemaPublic, "query"), wrapAsync(searchItems));
router.post("/search/admin", verifyAdmin, validator(searchBooksSchema), validator(paginationQuerySchema, "query"), wrapAsync(searchItemsAdmin));
router.post("/search/current-user", verifyNormalUser, validator(searchBooksSchema), validator(paginationQuerySchema, "query"), wrapAsync(searchItemsCurrentUser));
router.get("/auto-complete", validator(getAutoCompleteItemsQuerySchema, "query"), wrapAsync(getAutoCompleteItems));
router.put("/:id", verifyNormalUser, validator(idParamSchema, "params"), validator(updateItemSchema), wrapAsync(updateItem));
router.put("/boost/:id", verifyNormalUser, validator(idParamSchema, "params"), wrapAsync(boostItem));
router.post("/nearbyTutors", verifyNormalUser, wrapAsync(getNearbyTutors));

router.post("/bookmark", verifyNormalUser, validator(itemBookmarkSchema), wrapAsync(itemBookmarked));
router.get("/bookmarks", verifyNormalUser, wrapAsync(bookmarksList));
router.delete("/bookmark/:id", verifyNormalUser, wrapAsync(removeBookmark));

router.post("/addReview", verifyNormalUser, validator(addReviewValidate), wrapAsync(addReview));
router.get("/:userId/reviews", validator(userIdParam, "params"), wrapAsync(GetReviews));

router.get("/:id", wrapAsync(getItemById));


module.exports = router;
