const express = require("express");
const router = express.Router();

const Masterdata = require("../controllers/master-data.controller");
const validator = require("../validation/validator");
const { verifyAdmin } = require("../middleware/middleware");
const {
    categorySchema,
    subCategorySchema,
    subSubCategorySchema,
    subSubSubCategorySchema,
    editCategorySchema,
    editSubCategorySchema,
    editSubSubCategorySchema,
    editSubSubSubCategorySchema,
} = require("../validation/master-data.category");
const wrapAsync = require("../common/wrapAsync");
const { idParamSchema } = require("../validation/item.validation");

router.post("/category", verifyAdmin, validator(categorySchema), wrapAsync(Masterdata.addCategory));
router.post("/subCategory", verifyAdmin, validator(subCategorySchema), wrapAsync(Masterdata.addSubcategory));
router.post("/subSubCategory", verifyAdmin, validator(subSubCategorySchema), wrapAsync(Masterdata.addSubSubCategory));
router.post("/subSubSubCategory", verifyAdmin, validator(subSubSubCategorySchema), wrapAsync(Masterdata.addSubSubSubCategory));

router.get("/category", wrapAsync(Masterdata.getCategories));
router.get("/sub-category/:id", wrapAsync(Masterdata.getSubCategories));
router.get("/sub-sub-category/:id", wrapAsync(Masterdata.getSubSubCategories));
router.get("/sub-sub-sub-category/:id", wrapAsync(Masterdata.getSubSubSubCategories));

router.put("/category/:id", verifyAdmin, validator(idParamSchema, "params"), validator(editCategorySchema), wrapAsync(Masterdata.editCategory));
router.put("/sub-category/:id", verifyAdmin, validator(idParamSchema, "params"), validator(editSubCategorySchema), wrapAsync(Masterdata.editSubCategory));
router.put("/sub-sub-category/:id", verifyAdmin, validator(idParamSchema, "params"), validator(editSubSubCategorySchema), wrapAsync(Masterdata.editSubSubCategory));
router.put("/sub-sub-sub-category/:id", verifyAdmin, validator(idParamSchema, "params"), validator(editSubSubSubCategorySchema), wrapAsync(Masterdata.editSubSubSubCategory));

router.delete("/category/:id", verifyAdmin, validator(idParamSchema, "params"), wrapAsync(Masterdata.deleteCategory));
router.delete("/sub-category/:id", verifyAdmin, validator(idParamSchema, "params"), wrapAsync(Masterdata.deleteSubCategory));
router.delete("/sub-sub-category/:id", verifyAdmin, validator(idParamSchema, "params"), wrapAsync(Masterdata.deleteSubSubCategory));
router.delete("/sub-sub-sub-category/:id", verifyAdmin, validator(idParamSchema, "params"), wrapAsync(Masterdata.deleteSubSubSubCategory));


module.exports = router;
