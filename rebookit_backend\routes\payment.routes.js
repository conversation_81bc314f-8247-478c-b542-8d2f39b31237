const express = require("express");
const router = express.Router();
const PaymentController = require("../controllers/payment.controller");
const validator = require("../validation/validator");
const { verifyNormalUser, verifyAdmin } = require("../middleware/middleware");
const { paymentIntentSchema, queryPaymentsQuerySchema } = require("../validation/payment.validation");
const wrapAsync = require("../common/wrapAsync");

router.post("/payment-intent", verifyNormalUser, validator(paymentIntentSchema), wrapAsync(PaymentController.createPaymentIntent));
// router.post( "/webhook", wrapAsync(PaymentController.handleWebhook));
router.get("/history", verifyNormalUser, wrapAsync(PaymentController.paymentHistory));
router.get("/query", verifyAdmin, validator(queryPaymentsQuerySchema,'query'), wrapAsync(PaymentController.queryTranscations));

module.exports = router;
