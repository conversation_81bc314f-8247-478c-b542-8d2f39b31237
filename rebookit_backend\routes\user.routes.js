const express = require("express");
const router = express.Router();
const UserController = require("../controllers/user.controller");
const {
  loginSchema,
  registerUserSchema,
  verifyAndCreatePassword,
  verifyOtpSchema,
  sendOtpSchema,
  editProfile,
  forgotPasswordOtpSchema,
  verifyForgotPasswordOtpSchema,
  updateUserSchema,
  supportMailSchema,
  getAllsupportMailSchema,
  updateSupportMailSchema,
  updateSupportMailSchemaQuery,
} = require("../validation/user.auth.validation");
const validator = require("../validation/validator");
const { verifyNormalUser, verifyAdmin } = require("../middleware/middleware");
const wrapAsync = require("../common/wrapAsync");
const { paginationQuerySchema } = require("../validation/common-schema");

router.post("/send-otp", validator(sendOtpSchema), wrapAsync(UserController.SendOTP));
router.post("/verify-otp", validator(verifyOtpSchema), wrapAsync(UserController.VerifyOTP));
router.post("/register", validator(registerUserSchema), wrapAsync(UserController.RegisterUser));
router.post("/login", validator(loginSchema), wrapAsync(UserController.LoginUser));
router.get("/", verifyNormalUser, wrapAsync(UserController.GetUser));
router.post("/forgot-password", validator(forgotPasswordOtpSchema), wrapAsync(UserController.ForgotPassword));
router.post("/verify-forgot-otp", validator(verifyForgotPasswordOtpSchema), wrapAsync(UserController.verifyForgotPasswordOtp));
router.post("/reset-password", validator(verifyAndCreatePassword), wrapAsync(UserController.VerifyAndUpdatePassword));
router.post("/verify-user-data", verifyNormalUser, validator(verifyOtpSchema), wrapAsync(UserController.VerifyUserEmailOrMobile)); // for email or mobile verification
router.put("/edit-profile", validator(editProfile), verifyNormalUser, wrapAsync(UserController.UserEditProfile));
router.get("/plan", verifyNormalUser, wrapAsync(UserController.GetPlans));

router.get("/rebooker-of-month", wrapAsync(UserController.RebookerOfTheMonth));
router.get("/testimonials", wrapAsync(UserController.GetTestimonials));
router.post("/support-request", validator(supportMailSchema), wrapAsync(UserController.SupportRequest));
router.post(
  "/filter-support-request",
  verifyAdmin,
  validator(getAllsupportMailSchema),
  validator(paginationQuerySchema, "query"),
  wrapAsync(UserController.getAllSupport)
);
router.put(
  "/update-support-request",
  
  validator(updateSupportMailSchema),
  validator(updateSupportMailSchemaQuery, "query"),
  wrapAsync(UserController.updateSupportRequest)
);



//admin
router.put("/:id", verifyAdmin, validator(updateUserSchema), wrapAsync(UserController.updateUser));

module.exports = router;
