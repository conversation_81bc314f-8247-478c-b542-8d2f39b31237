const subCategory = [
  {
    _id: "6811d7ad7d79fae9362714b4",
    name: "Early Childhood",
    image: "",
    categoryId: "68612cc0ba947189b202a825",
  },
  {
    _id: "6811d7ccd8e7e0ae74fd55ba",
    name: "Primary /Preparatory School",
    image: "",
    categoryId: "68612cc0ba947189b202a825",
  },
  {
    _id: "6811d7d4887fa095677d4e11",
    name: "Secondary / High School",
    image: "",
    categoryId: "68612cc0ba947189b202a825",
  },
  {
    _id: "6811d7dcda1dd17fadff14ae",
    name: "Tertiary / University / College",
    image: "",
    categoryId: "68612cc0ba947189b202a825",
  },
  {
    _id: "6811d7e3cb24f0b11c79e5a2",
    name: "Professional Certifications",
    image: "",
    categoryId: "68612cc0ba947189b202a825",
  },
  // {
  //   _id: "6811d7ead84ab9fc70140b06",
  //   name: "Primary Exit Profile (PEP)",
  //   image: "",
  //   categoryId: "68612cc0ba947189b202a825",
  // },
  // {
  //   _id: "6811d7fa2299b22a9dcc2c7d",
  //   name: "Caribbean Secondary Education Certificate (CSEC)",
  //   image: "",
  //   categoryId: "68612cc0ba947189b202a825",
  // },
  // {
  //   _id: "6811d812e0aae5ae340d6bac",
  //   name: "Caribbean Advanced Proficiency Exam (CAPE)",
  //   image: "",
  //   categoryId: "68612cc0ba947189b202a825",
  // },
  {
    _id: "6811d81a1680f8a7d435cb7a",
    name: "Fiction",
    image: "",
    categoryId: "68612cc0ba947189b202a825",
  },
  {
    _id: "6811d821ebe38531a1fe3fee",
    name: "Non-Fiction",
    image: "",
    categoryId: "68612cc0ba947189b202a825",
  },
];

const subSubCategory = [
  // Early Childhood
  {
    _id: "6811f56d0fc7ca9045096845",
    name: "Preschool/Kindergarten/Basic ",
    image: "",
    subCategoryId: "6811d7ad7d79fae9362714b4",
  },
  {
    _id: "6811f6666d543be2551da39d",
    name: "Day Care Centres",
    image: "",
    subCategoryId: "6811d7ad7d79fae9362714b4",
  },
  {
    _id: "6811f66fa3520de28310ef23",
    name: "Nursery",
    image: "",
    subCategoryId: "6811d7ad7d79fae9362714b4",
  },

  //Primary /Preparatory School
  {
    _id: "6881ec93e746c52cc1228041",
    name: "K1",
    image: "",
    subCategoryId: "6811d7ccd8e7e0ae74fd55ba",
  },
  {
    _id: "6881eca1c6fcb92fa81eb130",
    name: "K2",
    image: "",
    subCategoryId: "6811d7ccd8e7e0ae74fd55ba",
  },
  {
    _id: "6881ecaf9a227b8d57d5a5a0",
    name: "K3",
    image: "",
    subCategoryId: "6811d7ccd8e7e0ae74fd55ba",
  },
  {
    _id: "6881ecb8b668d81649fa7fdd",
    name: "K4",
    image: "",
    subCategoryId: "6811d7ccd8e7e0ae74fd55ba",
  },
  {
    _id: "6881ecbe87f0265e9ba3ee70",
    name: "K5",
    image: "",
    subCategoryId: "6811d7ccd8e7e0ae74fd55ba",
  },
  {
    _id: "6811f67566ba209b701f6aa5",
    name: "Grade 1",
    image: "",
    subCategoryId: "6811d7ccd8e7e0ae74fd55ba",
  },
  {
    _id: "6811f67c190507c103f5948b",
    name: "Grade 2",
    image: "",
    subCategoryId: "6811d7ccd8e7e0ae74fd55ba",
  },
  {
    _id: "6811f683f169ee061710bc20",
    name: "Grade 3",
    image: "",
    subCategoryId: "6811d7ccd8e7e0ae74fd55ba",
  },
  {
    _id: "6811f68cbb6f1bdbdc268489",
    name: "Grade 4",
    image: "",
    subCategoryId: "6811d7ccd8e7e0ae74fd55ba",
  },
  {
    _id: "6811f696d36fdd117504f4a2",
    name: "Grade 5",
    image: "",
    subCategoryId: "6811d7ccd8e7e0ae74fd55ba",
  },
  {
    _id: "6811f69e48287d8142a64a1a",
    name: "Grade 6",
    image: "",
    subCategoryId: "6811d7ccd8e7e0ae74fd55ba",
  },

  // /"Secondary / High School"
  {
    _id: "6811f6a58ddb72d6d47d61f0",
    name: "Grade 7",
    image: "",
    subCategoryId: "6811d7d4887fa095677d4e11",
  },
  {
    _id: "6811f6adecf3a47cbde82ce9",
    name: "Grade 8",
    image: "",
    subCategoryId: "6811d7d4887fa095677d4e11",
  },
  {
    _id: "6811f6b5db90f4e18f1312ee",
    name: "Grade 9",
    image: "",
    subCategoryId: "6811d7d4887fa095677d4e11",
  },
  {
    _id: "6811f6bd934080a15c4f3155",
    name: "Grade 10",
    image: "",
    subCategoryId: "6811d7d4887fa095677d4e11",
  },
  {
    _id: "6811f6c504ef2d90f0b4824a",
    name: "Grade 11",
    image: "",
    subCategoryId: "6811d7d4887fa095677d4e11",
  },
  {
    _id: "6811f6ccd4aa6c311911818e",
    name: "Grade 12",
    image: "",
    subCategoryId: "6811d7d4887fa095677d4e11",
  },
  {
    _id: "6811f6d484068337bde61d9b",
    name: "Grade 13",
    image: "",
    subCategoryId: "6811d7d4887fa095677d4e11",
  },

  //Tertiary / University / College

  {
    _id: "6811f6db07fc20f19898aeb9",
    name: "Diploma",
    image: "",
    subCategoryId: "6811d7dcda1dd17fadff14ae",
  },
  {
    _id: "6811f6e577988201f319a66f",
    name: "Associates Degree",
    image: "",
    subCategoryId: "6811d7dcda1dd17fadff14ae",
  },
  {
    _id: "6811f6ec6b13ae198f280f35",
    name: "First Degree/ Bachelors Degree",
    image: "",
    subCategoryId: "6811d7dcda1dd17fadff14ae",
  },
  {
    _id: "6811f6f3a1a7d838b6b6d8f4",
    name: "Graduate Degree/ Masters Degree",
    image: "",
    subCategoryId: "6811d7dcda1dd17fadff14ae",
  },
  {
    _id: "6811f6fac671752f8933696e",
    name: "Postgraduate Degree",
    image: "",
    subCategoryId: "6811d7dcda1dd17fadff14ae",
  },

  //Professional cerfitications
  // {
  //   _id: "6811f7020cdd0b5cd86577f1",
  //   name: "Business Certifications",
  //   image: "",
  //   subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  // },
  {
    _id: "6811f70f04d2392ebf7fa593",
    name: "Accounting (e.g. ACCA)",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6811f716ae86fb570c1e5969",
    name: "Architecture",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6811f71c1f929e5e91ba1eda",
    name: "Auditing (e.g. CIA)",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6811f72512fdb8f20d737ff1",
    name: "Design",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6811f72ec4fae802c982f9b5",
    name: "Engineering",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6881fb7702b80887a3850c4b",
    name: "Education/Teaching",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6881fb84ebf75b5c3e8fae38",
    name: "Fashion Design",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6881fb8aa6005c98c67684d9",
    name: "Finance (e.g. CFA)",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6881fb939cb5a5935c0cd058",
    name: "Human Resource Management (e.g. CIPD)",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6881fb9b73edf3e450d22572",
    name: "Information Technology (e.g. CompTia)",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6881fba328fadc9856a7e1f2",
    name: "Insurance (e.g. CIPS)",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6881fbab64ff9843d064d609",
    name: "Law",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6881fbb6171082a4ac4d6efe",
    name: "Management",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6881fbbc7406a8d80661892e",
    name: "Marketing",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6881fbc5f182f0591226b98d",
    name: "Medicine/Nursing",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6881fbcda11cc0590a6eeb95",
    name: "Project Management (e.g. PMP)",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6881fbd3ed57992abb95a953",
    name: "Sales",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },
  {
    _id: "6881fbd98f2f0359ab917c4a",
    name: "Supervisory",
    image: "",
    subCategoryId: "6811d7e3cb24f0b11c79e5a2",
  },

  // //Primary Exit Profile (PEP)
  // {
  //   _id: "6811f7353f9fbc06a204c3a9",
  //   name: "Grade 4",
  //   image: "",
  //   subCategoryId: "6811d7ead84ab9fc70140b06",
  // },
  // {
  //   _id: "6811f73d408935ff5e78b275",
  //   name: "Grade 5",
  //   image: "",
  //   subCategoryId: "6811d7ead84ab9fc70140b06",
  // },
  // {
  //   _id: "6811f7452bc1510b11ecb1e1",
  //   name: "Grade 6",
  //   image: "",
  //   subCategoryId: "6811d7ead84ab9fc70140b06",
  // },

  // //   Caribbean Secondary Education Certificate (CSEC)
  // {
  //   _id: "6811f74ef2c1a21ccb11a6a7",
  //   name: "Grade 10",
  //   image: "",
  //   subCategoryId: "6811d7fa2299b22a9dcc2c7d",
  // },
  // {
  //   _id: "6811f75696361ddec2338dc4",
  //   name: "Grade 11",
  //   image: "",
  //   subCategoryId: "6811d7fa2299b22a9dcc2c7d",
  // },

  // //Caribbean Advanced Proficiency Exam (CAPE)
  // {
  //   _id: "6811f75c1892554c41553ece",
  //   name: "Grade 12",
  //   image: "",
  //   subCategoryId: "6811d812e0aae5ae340d6bac",
  // },
  // {
  //   _id: "6811f7717da54af2dcda0d8b",
  //   name: "Grade 13",
  //   image: "",
  //   subCategoryId: "6811d812e0aae5ae340d6bac",
  // },

  //Fiction
  {
    _id: "6811f79735cecc06f0464e82",
    name: "Adolescent/Young Adult",
    image: "",
    subCategoryId: "6811d81a1680f8a7d435cb7a",
  },
  {
    _id: "6811f79d44241c038c7cc310",
    name: "Fantasy",
    image: "",
    subCategoryId: "6811d81a1680f8a7d435cb7a",
  },
  {
    _id: "6811f7b2fd6532165c778642",
    name: "Historical Fiction",
    image: "",
    subCategoryId: "6811d81a1680f8a7d435cb7a",
  },
  {
    _id: "6811f7b8e41430366a917c45",
    name: "Horror",
    image: "",
    subCategoryId: "6811d81a1680f8a7d435cb7a",
  },
  {
    _id: "6811f7c41cf6539f888ffc42",
    name: "Mystery",
    image: "",
    subCategoryId: "6811d81a1680f8a7d435cb7a",
  },
  {
    _id: "6811f7cbe9e4b1cd0fc13c52",
    name: "Poetry",
    image: "",
    subCategoryId: "6811d81a1680f8a7d435cb7a",
  },
  {
    _id: "6811f7d14048cda887d51fd3",
    name: "Realistic Fiction",
    image: "",
    subCategoryId: "6811d81a1680f8a7d435cb7a",
  },
  {
    _id: "6811f7d6a37dfd62aeb94284",
    name: "Romance",
    image: "",
    subCategoryId: "6811d81a1680f8a7d435cb7a",
  },
  {
    _id: "6811f7dd7a30a0863d363d81",
    name: "Science Fiction",
    image: "",
    subCategoryId: "6811d81a1680f8a7d435cb7a",
  },
  {
    _id: "6811f7e481a2a5f33140e6ef",
    name: "Thriller",
    image: "",
    subCategoryId: "6811d81a1680f8a7d435cb7a",
  },

  //   Non-Fiction
  {
    _id: "6811f7ea355390fe0b39e226",
    name: "Autobiography/Biography/Memoir",
    image: "",
    subCategoryId: "6811d821ebe38531a1fe3fee",
  },
  {
    _id: "6811f7f1706368e8a9a3b9c9",
    name: "Commentary",
    image: "",
    subCategoryId: "6811d821ebe38531a1fe3fee",
  },
  {
    _id: "6811f7f60767ab3294202a23",
    name: "Faith/Religion",
    image: "",
    subCategoryId: "6811d821ebe38531a1fe3fee",
  },
  {
    _id: "6811f7fffa2f2d9ed089e0a5",
    name: "History",
    image: "",
    subCategoryId: "6811d821ebe38531a1fe3fee",
  },
  {
    _id: "6811f8053fa67d9a1714c979",
    name: "How-To Guides",
    image: "",
    subCategoryId: "6811d821ebe38531a1fe3fee",
  },
  {
    _id: "6811f80ca8345b94386d98a9",
    name: "Humour",
    image: "",
    subCategoryId: "6811d821ebe38531a1fe3fee",
  },
  {
    _id: "6811f811a5d715a840a798de",
    name: "Journals",
    image: "",
    subCategoryId: "6811d821ebe38531a1fe3fee",
  },
  {
    _id: "6811f8178d7a690cc040afab",
    name: "Motivation",
    image: "",
    subCategoryId: "6811d821ebe38531a1fe3fee",
  },
  {
    _id: "6811f81c0408d9531ee79f87",
    name: "Philosophy and Insight",
    image: "",
    subCategoryId: "6811d821ebe38531a1fe3fee",
  },
  {
    _id: "6811f825caf83a5a3985c4c5",
    name: "Self-Help",
    image: "",
    subCategoryId: "6811d821ebe38531a1fe3fee",
  },
  {
    _id: "6811f82b9b08a8b589b169f6",
    name: "Skills and Hobbies",
    image: "",
    subCategoryId: "6811d821ebe38531a1fe3fee",
  },
  {
    _id: "6811f837d9d9d19ff075a60e",
    name: "Travel",
    image: "",
    subCategoryId: "6811d821ebe38531a1fe3fee",
  },
];

// Not giving the _id onwards as mongobd will create auto for this and we don't need to map anywhere
const subSubSubCategory = [
  // Preschool/Kindergarten/Basic
  {
    name: "Art",
    image: "",
    subSubCategoryId: "6811f56d0fc7ca9045096845",
  },
  {
    name: "Arithmetics/Mathematics",
    image: "",
    subSubCategoryId: "6811f56d0fc7ca9045096845",
  },
  {
    name: "Bible/Religious Education",
    image: "",
    subSubCategoryId: "6811f56d0fc7ca9045096845",
  },
  {
    name: "English/Language",
    image: "",
    subSubCategoryId: "6811f56d0fc7ca9045096845",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f56d0fc7ca9045096845",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f56d0fc7ca9045096845",
  },
  {
    name: "Reading",
    image: "",
    subSubCategoryId: "6811f56d0fc7ca9045096845",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f56d0fc7ca9045096845",
  },
  {
    name: "Spelling",
    image: "",
    subSubCategoryId: "6811f56d0fc7ca9045096845",
  },

  // Day Care Centres
  {
    name: "Art",
    image: "",
    subSubCategoryId: "6811f6666d543be2551da39d",
  },
  {
    name: "Arithmetics/Mathematics",
    image: "",
    subSubCategoryId: "6811f6666d543be2551da39d",
  },
  {
    name: "Bible/Religious Education",
    image: "",
    subSubCategoryId: "6811f6666d543be2551da39d",
  },
  {
    name: "English/Language",
    image: "",
    subSubCategoryId: "6811f6666d543be2551da39d",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f6666d543be2551da39d",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f6666d543be2551da39d",
  },
  {
    name: "Reading",
    image: "",
    subSubCategoryId: "6811f6666d543be2551da39d",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f6666d543be2551da39d",
  },
  {
    name: "Spelling",
    image: "",
    subSubCategoryId: "6811f6666d543be2551da39d",
  },

  // Nursery
  {
    name: "Art",
    image: "",
    subSubCategoryId: "6811f66fa3520de28310ef23",
  },
  {
    name: "Arithmetics/Mathematics",
    image: "",
    subSubCategoryId: "6811f66fa3520de28310ef23",
  },
  {
    name: "Bible/Religious Education",
    image: "",
    subSubCategoryId: "6811f66fa3520de28310ef23",
  },
  {
    name: "English/Language",
    image: "",
    subSubCategoryId: "6811f66fa3520de28310ef23",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f66fa3520de28310ef23",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f66fa3520de28310ef23",
  },
  {
    name: "Reading",
    image: "",
    subSubCategoryId: "6811f66fa3520de28310ef23",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f66fa3520de28310ef23",
  },
  {
    name: "Spelling",
    image: "",
    subSubCategoryId: "6811f66fa3520de28310ef23",
  },

  // K1
  {
    name: "Abilities (PEP)",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "Art",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "Arithmetics/Mathematics",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "Bible/Religious Education",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "Comprehension",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "English/Language",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "Integrated Studies (PEP)",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "Library Science",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "Reading",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "Science/Integrated Science",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },
  {
    name: "Spelling",
    image: "",
    subSubCategoryId: "6881ec93e746c52cc1228041",
  },

  // K2
  {
    name: "Abilities (PEP)",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "Art",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "Arithmetics/Mathematics",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "Bible/Religious Education",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "Comprehension",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "English/Language",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "Integrated Studies (PEP)",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "Library Science",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "Reading",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "Science/Integrated Science",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },
  {
    name: "Spelling",
    image: "",
    subSubCategoryId: "6881eca1c6fcb92fa81eb130",
  },

  // K3
  {
    name: "Abilities (PEP)",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "Art",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "Arithmetics/Mathematics",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "Bible/Religious Education",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "Comprehension",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "English/Language",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "Integrated Studies (PEP)",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "Library Science",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "Reading",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "Science/Integrated Science",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },
  {
    name: "Spelling",
    image: "",
    subSubCategoryId: "6881ecaf9a227b8d57d5a5a0",
  },

  // K4
  {
    name: "Abilities (PEP)",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "Art",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "Arithmetics/Mathematics",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "Bible/Religious Education",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "Comprehension",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "English/Language",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "Integrated Studies (PEP)",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "Library Science",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "Reading",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "Science/Integrated Science",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },
  {
    name: "Spelling",
    image: "",
    subSubCategoryId: "6881ecb8b668d81649fa7fdd",
  },

  // K5
  {
    name: "Abilities (PEP)",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "Art",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "Arithmetics/Mathematics",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "Bible/Religious Education",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "Comprehension",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "English/Language",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "Integrated Studies (PEP)",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "Library Science",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "Reading",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "Science/Integrated Science",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },
  {
    name: "Spelling",
    image: "",
    subSubCategoryId: "6881ecbe87f0265e9ba3ee70",
  },

  // Grade 1
  {
    name: "Abilities (PEP)",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "Art",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "Arithmetics/Mathematics",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "Bible/Religious Education",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "Comprehension",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "English/Language",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "Integrated Studies (PEP)",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "Library Science",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "Reading",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "Science/Integrated Science",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },
  {
    name: "Spelling",
    image: "",
    subSubCategoryId: "6811f67566ba209b701f6aa5",
  },

  // Grade 2
  {
    name: "Abilities (PEP)",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "Art",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "Arithmetics/Mathematics",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "Bible/Religious Education",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "Comprehension",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "English/Language",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "Integrated Studies (PEP)",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "Library Science",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "Reading",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "Science/Integrated Science",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },
  {
    name: "Spelling",
    image: "",
    subSubCategoryId: "6811f67c190507c103f5948b",
  },

  // Grade 3
  {
    name: "Abilities (PEP)",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "Art",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "Arithmetics/Mathematics",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "Bible/Religious Education",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "Comprehension",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "English/Language",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "Integrated Studies (PEP)",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "Library Science",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "Reading",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "Science/Integrated Science",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },
  {
    name: "Spelling",
    image: "",
    subSubCategoryId: "6811f683f169ee061710bc20",
  },

  // Grade 4
  {
    name: "Abilities (PEP)",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "Art",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "Arithmetics/Mathematics",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "Bible/Religious Education",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "Comprehension",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "English/Language",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "Integrated Studies (PEP)",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "Library Science",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "Reading",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "Science/Integrated Science",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },
  {
    name: "Spelling",
    image: "",
    subSubCategoryId: "6811f68cbb6f1bdbdc268489",
  },

  // Grade 5
  {
    name: "Abilities (PEP)",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "Art",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "Arithmetics/Mathematics",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "Bible/Religious Education",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "Comprehension",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "English/Language",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "Integrated Studies (PEP)",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "Library Science",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "Reading",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "Science/Integrated Science",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },
  {
    name: "Spelling",
    image: "",
    subSubCategoryId: "6811f696d36fdd117504f4a2",
  },

  // Grade 6
  {
    name: "Abilities (PEP)",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "Art",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "Arithmetics/Mathematics",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "Bible/Religious Education",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "Comprehension",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "English/Language",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "Integrated Studies (PEP)",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "Library Science",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "Reading",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "Science/Integrated Science",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },
  {
    name: "Spelling",
    image: "",
    subSubCategoryId: "6811f69e48287d8142a64a1a",
  },

  // Grade 7
  {
    name: "Accounting",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Additional Mathematics",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Agricultural Science",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Art/Art & Design",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Business",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Biology",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Chemistry",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Economics",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Electronic Document Preparation (EDPM)",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "English Language",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "English Literature",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Environmental Science",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Food and Nutrition",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Geography",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Home Economics Management",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Human and Social Biology",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "History/Caribbean History",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Integrated Science",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Mathematics",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Technical Drawing",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },
  {
    name: "Textiles and Clothing",
    image: "",
    subSubCategoryId: "6811f6a58ddb72d6d47d61f0",
  },

  // Grade 8
  {
    name: "Accounting",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Additional Mathematics",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Agricultural Science",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Art/Art & Design",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Business",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Biology",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Chemistry",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Economics",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Electronic Document Preparation (EDPM)",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "English Language",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "English Literature",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Environmental Science",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Food and Nutrition",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Geography",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Home Economics Management",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Human and Social Biology",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "History/Caribbean History",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Integrated Science",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Mathematics",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Technical Drawing",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },
  {
    name: "Textiles and Clothing",
    image: "",
    subSubCategoryId: "6811f6adecf3a47cbde82ce9",
  },

  // Grade 9
  {
    name: "Accounting",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Additional Mathematics",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Agricultural Science",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Art/Art & Design",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Business",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Biology",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Chemistry",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Economics",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Electronic Document Preparation (EDPM)",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "English Language",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "English Literature",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Environmental Science",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Food and Nutrition",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Geography",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Home Economics Management",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Human and Social Biology",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "History/Caribbean History",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Integrated Science",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Mathematics",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Technical Drawing",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },
  {
    name: "Textiles and Clothing",
    image: "",
    subSubCategoryId: "6811f6b5db90f4e18f1312ee",
  },

  // Grade 10
  {
    name: "Accounting",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Additional Mathematics",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Agricultural Science",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Art/Art & Design",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Business",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Biology",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Chemistry",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Economics",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Electronic Document Preparation (EDPM)",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "English Language",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "English Literature",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Environmental Science",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Food and Nutrition",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Geography",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Home Economics Management",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Human and Social Biology",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "History/Caribbean History",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Integrated Science",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Mathematics",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Technical Drawing",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },
  {
    name: "Textiles and Clothing",
    image: "",
    subSubCategoryId: "6811f6bd934080a15c4f3155",
  },

  // Grade 11
  {
    name: "Accounting",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Additional Mathematics",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Agricultural Science",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Art/Art & Design",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Business",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Biology",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Chemistry",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Economics",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Electronic Document Preparation (EDPM)",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "English Language",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "English Literature",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Environmental Science",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Food and Nutrition",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Geography",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Home Economics Management",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Human and Social Biology",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "History/Caribbean History",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Integrated Science",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Mathematics",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Technical Drawing",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },
  {
    name: "Textiles and Clothing",
    image: "",
    subSubCategoryId: "6811f6c504ef2d90f0b4824a",
  },

  // Grade 12
  {
    name: "Accounting",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Additional Mathematics",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Agricultural Science",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Art/Art & Design",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Business",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Biology",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Chemistry",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Economics",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Electronic Document Preparation (EDPM)",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "English Language",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "English Literature",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Environmental Science",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Food and Nutrition",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Geography",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Home Economics Management",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Human and Social Biology",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "History/Caribbean History",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Integrated Science",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Mathematics",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Technical Drawing",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },
  {
    name: "Textiles and Clothing",
    image: "",
    subSubCategoryId: "6811f6ccd4aa6c311911818e",
  },

  // Grade 13
  {
    name: "Accounting",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Additional Mathematics",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Agricultural Science",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Art/Art & Design",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Business",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Biology",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Chemistry",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Computer Science",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Economics",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Electronic Document Preparation (EDPM)",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "English Language",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "English Literature",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Environmental Science",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "French",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Food and Nutrition",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Geography",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Home Economics Management",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Human and Social Biology",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "History/Caribbean History",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Integrated Science",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Mathematics",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Music",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Physical Education",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Social Studies",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Spanish",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Technical Drawing",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },
  {
    name: "Textiles and Clothing",
    image: "",
    subSubCategoryId: "6811f6d484068337bde61d9b",
  },

  // Diploma
  {
    id: "6639e0c0b2e5d1a7f3a8b7c9",
    name: "Arts - Culinary",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7ca",
    name: "Arts - Performing",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7cb",
    name: "Arts - Visual",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7cc",
    name: "Design - Fashion Design",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7cd",
    name: "Design - Interior Design",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7ce",
    name: "Arts - Music",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7cf",
    name: "Early Childhood Education",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7d0",
    name: "Primary Level Education",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7d1",
    name: "Secondary Level Education",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7d2",
    name: "Tertiary Level Education",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7d3",
    name: "Languages - French",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7d4",
    name: "Languages - Latin",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7d5",
    name: "Languages - Spanish",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7d6",
    name: "Linguistics",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7d7",
    name: "Theology",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7d8",
    name: "Business - Accounting",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7d9",
    name: "Business - Administrative Management",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7da",
    name: "Business - Agricultural Entrepreneurship",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7db",
    name: "Business - Archives and Records Management",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7dc",
    name: "Business - Banking and Finance",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7dd",
    name: "Business - Business Administration",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7de",
    name: "Business - Business Analytics",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7df",
    name: "Business - Cost and Management Accounting",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7e0",
    name: "Business - Economics",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7e1",
    name: "Business - Entrepreneurship",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7e2",
    name: "Business - Financial Accounting",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7e3",
    name: "Business - Financial Management",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7e4",
    name: "Business - General Management",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7e5",
    name: "Business - Human Resource Management/Development",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7e6",
    name: "Business - Internal Auditing",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7e7",
    name: "Business - International Management",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7e8",
    name: "Business - Leadership",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7e9",
    name: "Business - Logistics Operations/Management",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7ea",
    name: "Business - Marketing",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7eb",
    name: "Business - Operations Management",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7ec",
    name: "Business - Production Management",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7ed",
    name: "Business - Project Management",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7ee",
    name: "Business - Public Sector Administration",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7ef",
    name: "Business - Sales",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7f0",
    name: "Medical Sciences - Medicine",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7f1",
    name: "Medical Sciences - Nursing",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7f2",
    name: "Medical Sciences - Physiotherapy",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7f3",
    name: "Medical Sciences - Physical Education",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7f4",
    name: "Medical Sciences - Sports Medicine",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7f5",
    name: "Science & Technology - Actuarial Science",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7f6",
    name: "Science & Technology - Agricultural Science",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7f7",
    name: "Science & Technology - Biochemistry",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7f8",
    name: "Science & Technology - Biology",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7f9",
    name: "Science & Technology - Biotechnology",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7fa",
    name: "Science & Technology - Chemistry",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7fb",
    name: "Science & Technology - Data Analytics",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7fc",
    name: "Science & Technology - Forensic Science",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7fd",
    name: "Science & Technology - Geography",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7fe",
    name: "Science & Technology - Geology",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b7ff",
    name: "Science & Technology - Information Technology",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b800",
    name: "Science & Technology - Mathematics",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b801",
    name: "Science & Technology - Physics",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b802",
    name: "Science & Technology - Technology",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b803",
    name: "Science & Technology - Zoology",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b804",
    name: "Law - Administrative Law",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b805",
    name: "Law - Criminology",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b806",
    name: "Law - Human Rights Law",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b807",
    name: "Law - Real Property Law",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b808",
    name: "Law - Maritime Law",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b809",
    name: "Law - Environmental Law",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b80a",
    name: "Civil Engineering",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b80b",
    name: "Biomedical Engineering",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b80c",
    name: "Electrical Power Engineering",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b80d",
    name: "Electronics and Computer Engineering",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b80e",
    name: "Mechanical Engineering",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b80f",
    name: "Architecture",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b810",
    name: "Environmental Sciences",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b811",
    name: "Communications",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b812",
    name: "Media",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b813",
    name: "Journalism",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b814",
    name: "Caribbean Political Thought",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b815",
    name: "Environmental Economics",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b816",
    name: "Game Theory",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b817",
    name: "Aviation",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b818",
    name: "Artificial Intelligence and Computer Science",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b819",
    name: "Cruise Shipping and Marine Tourism",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b81a",
    name: "Custom Processes, Freight Forwarding and Immigration",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b81b",
    name: "Customs Brokerage",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b81c",
    name: "Cyber Security and Digital Forensics",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b81d",
    name: "Engineering - Marine Engineering",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b81e",
    name: "Engineering - Chief Engineer & Second Engineer Officer",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b81f",
    name: "Engineering - Industrial Automation",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b820",
    name: "Engineering - Industrial Systems",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b821",
    name: "Engineering - Mechatronics",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b822",
    name: "Forensic Sciences",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b823",
    name: "Industrial Security Management",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b824",
    name: "Industrial Systems Operations and Maintenance",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b825",
    name: "International Shipping",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b826",
    name: "Logistics and Supply Chain Management",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b827",
    name: "Marine Transportation",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b828",
    name: "Maritime Biotechnology",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b829",
    name: "Master and Chief Mate",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b82a",
    name: "Police Sciences",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b82b",
    name: "Port Management",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b82c",
    name: "Security Administration and Management",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b82d",
    name: "Security Studies",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b82e",
    name: "Shipping and Logistics",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b82f",
    name: "Strategic Counter Terrorism",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b830",
    name: "International Relations",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b831",
    name: "Political Science",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b832",
    name: "Punishment and Corrections",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b833",
    name: "Psychology",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b834",
    name: "Security and Defense",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b835",
    name: "Sex, Gender and Society",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b836",
    name: "Social and Cultural Anthropology",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b837",
    name: "Social Work",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b838",
    name: "Food and Beverage",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b839",
    name: "Event Planning",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },
  {
    id: "6639e0c0b2e5d1a7f3a8b83a",
    name: "Tourism",
    image: "",
    subSubCategoryId: "6811f6db07fc20f19898aeb9",
  },

  // Asociates Degree
  {
    name: "Arts - Culinary",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Arts - Performing",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Arts - Visual",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Design - Fashion Design",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Design - Interior Design",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Arts - Music",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Early Childhood Education",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Primary Level Education",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Secondary Level Education",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Tertiary Level Education",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Languages - French",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Languages - Latin",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Languages - Spanish",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Linguistics",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Theology",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Accounting",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Administrative Management",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Agricultural Entrepreneurship",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Archives and Records Management",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Banking and Finance",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Business Administration",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Business Analytics",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Cost and Management Accounting",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Economics",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Entrepreneurship",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Financial Accounting",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Financial Management",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - General Management",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Human Resource Management/Development",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Internal Auditing",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - International Management",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Leadership",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Logistics Operations/Management",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Marketing",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Operations Management",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Production Management",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Project Management",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Public Sector Administration",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Business - Sales",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Medical Sciences - Medicine",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Medical Sciences - Nursing",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Medical Sciences - Physiotherapy",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Medical Sciences - Physical Education",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Medical Sciences - Sports Medicine",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Science & Technology - Actuarial Science",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Science & Technology - Agricultural Science",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Science & Technology - Biochemistry",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Science & Technology - Biology",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Science & Technology - Biotechnology",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Science & Technology - Chemistry",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Science & Technology - Data Analytics",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Science & Technology - Forensic Science",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Science & Technology - Geography",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Science & Technology - Geology",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Science & Technology - Information Technology",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Science & Technology - Mathematics",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Science & Technology - Physics",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Science & Technology - Technology",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Science & Technology - Zoology",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Law - Administrative Law",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Law - Criminology",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Law - Human Rights Law",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Law - Real Property Law",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Law - Maritime Law",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Law - Environmental Law",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Civil Engineering",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Biomedical Engineering",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Electrical Power Engineering",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Electronics and Computer Engineering",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Mechanical Engineering",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Architecture",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Environmental Sciences",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Communications",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Media",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Journalism",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Caribbean Political Thought",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Environmental Economics",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Game Theory",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Aviation",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Artificial Intelligence and Computer Science",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Cruise Shipping and Marine Tourism",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Custom Processes, Freight Forwarding and Immigration",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Customs Brokerage",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Cyber Security and Digital Forensics",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Engineering - Marine Engineering",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Engineering - Chief Engineer & Second Engineer Officer",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Engineering - Industrial Automation",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Engineering - Industrial Systems",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Engineering - Mechatronics",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Forensic Sciences",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Industrial Security Management",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Industrial Systems Operations and Maintenance",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "International Shipping",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Logistics and Supply Chain Management",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Marine Transportation",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Maritime Biotechnology",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Master and Chief Mate",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Police Sciences",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Port Management",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Security Administration and Management",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Security Studies",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Shipping and Logistics",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Strategic Counter Terrorism",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "International Relations",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Political Science",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Punishment and Corrections",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Psychology",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Security and Defense",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Sex, Gender and Society",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Social and Cultural Anthropology",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Social Work",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Food and Beverage",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Event Planning",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },
  {
    name: "Tourism",
    image: "",
    subSubCategoryId: "6811f6e577988201f319a66f",
  },

  // First Degree/ Bachelors Degree
  {
    name: "Arts - Culinary",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Arts - Performing",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Arts - Visual",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Design - Fashion Design",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Design - Interior Design",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Arts - Music",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Early Childhood Education",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Primary Level Education",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Secondary Level Education",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Tertiary Level Education",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Languages - French",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Languages - Latin",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Languages - Spanish",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Linguistics",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Theology",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Accounting",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Administrative Management",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Agricultural Entrepreneurship",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Archives and Records Management",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Banking and Finance",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Business Administration",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Business Analytics",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Cost and Management Accounting",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Economics",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Entrepreneurship",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Financial Accounting",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Financial Management",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - General Management",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Human Resource Management/Development",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Internal Auditing",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - International Management",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Leadership",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Logistics Operations/Management",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Marketing",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Operations Management",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Production Management",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Project Management",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Public Sector Administration",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Business - Sales",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Medical Sciences - Medicine",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Medical Sciences - Nursing",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Medical Sciences - Physiotherapy",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Medical Sciences - Physical Education",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Medical Sciences - Sports Medicine",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Science & Technology - Actuarial Science",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Science & Technology - Agricultural Science",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Science & Technology - Biochemistry",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Science & Technology - Biology",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Science & Technology - Biotechnology",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Science & Technology - Chemistry",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Science & Technology - Data Analytics",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Science & Technology - Forensic Science",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Science & Technology - Geography",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Science & Technology - Geology",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Science & Technology - Information Technology",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Science & Technology - Mathematics",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Science & Technology - Physics",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Science & Technology - Technology",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Science & Technology - Zoology",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Law - Administrative Law",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Law - Criminology",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Law - Human Rights Law",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Law - Real Property Law",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Law - Maritime Law",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Law - Environmental Law",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Civil Engineering",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Biomedical Engineering",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Electrical Power Engineering",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Electronics and Computer Engineering",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Mechanical Engineering",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Architecture",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Environmental Sciences",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Communications",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Media",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Journalism",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Caribbean Political Thought",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Environmental Economics",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Game Theory",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Aviation",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Artificial Intelligence and Computer Science",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Cruise Shipping and Marine Tourism",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Custom Processes, Freight Forwarding and Immigration",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Customs Brokerage",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Cyber Security and Digital Forensics",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Engineering - Marine Engineering",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Engineering - Chief Engineer & Second Engineer Officer",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Engineering - Industrial Automation",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Engineering - Industrial Systems",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Engineering - Mechatronics",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Forensic Sciences",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Industrial Security Management",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Industrial Systems Operations and Maintenance",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "International Shipping",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Logistics and Supply Chain Management",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Marine Transportation",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Maritime Biotechnology",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Master and Chief Mate",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Police Sciences",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Port Management",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Security Administration and Management",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Security Studies",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Shipping and Logistics",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Strategic Counter Terrorism",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "International Relations",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Political Science",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Punishment and Corrections",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Psychology",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Security and Defense",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Sex, Gender and Society",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Social and Cultural Anthropology",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Social Work",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Food and Beverage",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Event Planning",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },
  {
    name: "Tourism",
    image: "",
    subSubCategoryId: "6811f6ec6b13ae198f280f35",
  },

  // Graduate Degree/ Masters Degree
  {
    name: "Arts - Culinary",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Arts - Performing",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Arts - Visual",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Design - Fashion Design",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Design - Interior Design",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Arts - Music",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Early Childhood Education",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Primary Level Education",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Secondary Level Education",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Tertiary Level Education",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Languages - French",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Languages - Latin",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Languages - Spanish",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Linguistics",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Theology",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Accounting",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Administrative Management",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Agricultural Entrepreneurship",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Archives and Records Management",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Banking and Finance",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Business Administration",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Business Analytics",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Cost and Management Accounting",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Economics",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Entrepreneurship",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Financial Accounting",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Financial Management",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - General Management",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Human Resource Management/Development",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Internal Auditing",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - International Management",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Leadership",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Logistics Operations/Management",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Marketing",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Operations Management",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Production Management",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Project Management",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Public Sector Administration",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Business - Sales",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Medical Sciences - Medicine",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Medical Sciences - Nursing",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Medical Sciences - Physiotherapy",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Medical Sciences - Physical Education",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Medical Sciences - Sports Medicine",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Science & Technology - Actuarial Science",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Science & Technology - Agricultural Science",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Science & Technology - Biochemistry",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Science & Technology - Biology",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Science & Technology - Biotechnology",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Science & Technology - Chemistry",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Science & Technology - Data Analytics",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Science & Technology - Forensic Science",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Science & Technology - Geography",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Science & Technology - Geology",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Science & Technology - Information Technology",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Science & Technology - Mathematics",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Science & Technology - Physics",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Science & Technology - Technology",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Science & Technology - Zoology",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Law - Administrative Law",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Law - Criminology",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Law - Human Rights Law",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Law - Real Property Law",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Law - Maritime Law",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Law - Environmental Law",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Civil Engineering",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Biomedical Engineering",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Electrical Power Engineering",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Electronics and Computer Engineering",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Mechanical Engineering",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Architecture",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Environmental Sciences",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Communications",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Media",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Journalism",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Caribbean Political Thought",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Environmental Economics",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Game Theory",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Aviation",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Artificial Intelligence and Computer Science",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Cruise Shipping and Marine Tourism",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Custom Processes, Freight Forwarding and Immigration",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Customs Brokerage",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Cyber Security and Digital Forensics",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Engineering - Marine Engineering",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Engineering - Chief Engineer & Second Engineer Officer",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Engineering - Industrial Automation",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Engineering - Industrial Systems",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Engineering - Mechatronics",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Forensic Sciences",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Industrial Security Management",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Industrial Systems Operations and Maintenance",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "International Shipping",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Logistics and Supply Chain Management",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Marine Transportation",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Maritime Biotechnology",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Master and Chief Mate",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Police Sciences",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Port Management",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Security Administration and Management",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Security Studies",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Shipping and Logistics",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Strategic Counter Terrorism",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "International Relations",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Political Science",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Punishment and Corrections",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Psychology",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Security and Defense",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Sex, Gender and Society",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Social and Cultural Anthropology",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Social Work",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Food and Beverage",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Event Planning",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },
  {
    name: "Tourism",
    image: "",
    subSubCategoryId: "6811f6f3a1a7d838b6b6d8f4",
  },

  // Postgraduate Degree
  {
    name: "Arts - Culinary",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Arts - Performing",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Arts - Visual",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Design - Fashion Design",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Design - Interior Design",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Arts - Music",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Early Childhood Education",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Primary Level Education",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Secondary Level Education",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Tertiary Level Education",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Languages - French",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Languages - Latin",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Languages - Spanish",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Linguistics",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Theology",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Accounting",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Administrative Management",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Agricultural Entrepreneurship",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Archives and Records Management",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Banking and Finance",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Business Administration",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Business Analytics",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Cost and Management Accounting",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Economics",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Entrepreneurship",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Financial Accounting",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Financial Management",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - General Management",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Human Resource Management/Development",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Internal Auditing",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - International Management",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Leadership",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Logistics Operations/Management",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Marketing",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Operations Management",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Production Management",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Project Management",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Public Sector Administration",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Business - Sales",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Medical Sciences - Medicine",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Medical Sciences - Nursing",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Medical Sciences - Physiotherapy",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Medical Sciences - Physical Education",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Medical Sciences - Sports Medicine",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Science & Technology - Actuarial Science",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Science & Technology - Agricultural Science",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Science & Technology - Biochemistry",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Science & Technology - Biology",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Science & Technology - Biotechnology",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Science & Technology - Chemistry",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Science & Technology - Data Analytics",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Science & Technology - Forensic Science",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Science & Technology - Geography",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Science & Technology - Geology",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Science & Technology - Information Technology",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Science & Technology - Mathematics",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Science & Technology - Physics",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Science & Technology - Technology",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Science & Technology - Zoology",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Law - Administrative Law",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Law - Criminology",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Law - Human Rights Law",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Law - Real Property Law",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Law - Maritime Law",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Law - Environmental Law",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Civil Engineering",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Biomedical Engineering",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Electrical Power Engineering",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Electronics and Computer Engineering",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Mechanical Engineering",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Architecture",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Environmental Sciences",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Communications",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Media",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Journalism",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Caribbean Political Thought",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Environmental Economics",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Game Theory",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Aviation",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Artificial Intelligence and Computer Science",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Cruise Shipping and Marine Tourism",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Custom Processes, Freight Forwarding and Immigration",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Customs Brokerage",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Cyber Security and Digital Forensics",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Engineering - Marine Engineering",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Engineering - Chief Engineer & Second Engineer Officer",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Engineering - Industrial Automation",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Engineering - Industrial Systems",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Engineering - Mechatronics",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Forensic Sciences",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Industrial Security Management",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Industrial Systems Operations and Maintenance",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "International Shipping",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Logistics and Supply Chain Management",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Marine Transportation",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Maritime Biotechnology",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Master and Chief Mate",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Police Sciences",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Port Management",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Security Administration and Management",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Security Studies",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Shipping and Logistics",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Strategic Counter Terrorism",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "International Relations",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Political Science",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Punishment and Corrections",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Psychology",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Security and Defense",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Sex, Gender and Society",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Social and Cultural Anthropology",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Social Work",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Food and Beverage",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Event Planning",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
  {
    name: "Tourism",
    image: "",
    subSubCategoryId: "6811f6fac671752f8933696e",
  },
];

module.exports = {
  subCategory,
  subSubCategory,
  subSubSubCategory,
};
