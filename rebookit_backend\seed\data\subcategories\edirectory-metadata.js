const subCategory = [
  {
    _id: "68613660ba947189b202a829",
    name: "Schools",
    image: "",
    categoryId: "68612cc0ba947189b202a826",
  },
  {
    _id: "68613660ba947189b202a82a",
    name: "Tu<PERSON>",
    image: "",
    categoryId: "68612cc0ba947189b202a826",
  },
  {
    _id: "686759fe4c67abdc3020b820",
    name: "Extracurricular Clubs & Activities",
    image: "",
    categoryId: "68612cc0ba947189b202a826",
  },
];

const subSubCategory = [
  // SCHOOLS SUB-SUB-CATEGORY
  {
    _id: "68675a5ed9dec1e46261dc75",
    name: "Early Childhood",
    image: "",
    subCategoryId: "68613660ba947189b202a829",
  },
  {
    _id: "68675a68f03a62eaa8ff9d8e",
    name: "Primary/Preparatory School",
    image: "",
    subCategoryId: "68613660ba947189b202a829",
  },
  // {
  //   _id: "68675a6f295893bb872a8148",
  //   name: "Preparatory School ",
  //   image: "",
  //   subCategoryId: "68613660ba947189b202a829",
  // },
  {
    _id: "68675a7ef9011d18000e60ea",
    name: "Secondary/High School ",
    image: "",
    subCategoryId: "68613660ba947189b202a829",
  },
  // {
  //   _id: "68675a834d8c96e973fd6a97",
  //   name: "Exrtacurricular Clubs & Activities ",
  //   image: "",
  //   subCategoryId: "68613660ba947189b202a829",
  // },
  // {
  //   _id: "68675a89d8b00a970ae1b8f3",
  //   name: "High School",
  //   image: "",
  //   subCategoryId: "68613660ba947189b202a829",
  // },
  {
    _id: "68675a8ed1b91a867918b882",
    name: "Tertiary/College/University",
    image: "",
    subCategoryId: "68613660ba947189b202a829",
  },
  // {
  //   _id: "68675ab5c87c0c285823b4bb",
  //   name: "College",
  //   image: "",
  //   subCategoryId: "68613660ba947189b202a829",
  // },
  // {
  //   _id: "68675abb0d948036e086d9a7",
  //   name: "University",
  //   image: "",
  //   subCategoryId: "68613660ba947189b202a829",
  // },
  // {
  //   _id: "68675abf56f98ddebe00604b",
  //   name: "Professional Associations",
  //   image: "",
  //   subCategoryId: "68613660ba947189b202a829",
  // },
  {
    _id: "68675ac4e8d02f96be7e6730",
    name: "Professional Associations/Institutions",
    image: "",
    subCategoryId: "68613660ba947189b202a829",
  },
  {
    _id: "68675ac82ca69e7e62b07403",
    name: "Vocational and Technical Institutions",
    image: "",
    subCategoryId: "68613660ba947189b202a829",
  },



  // TUTORS SUB-SUB-CATEGORY
  {
    _id: "68675ace2677135b7bf54708",
    name: "Accounts",
    image: "",
    subCategoryId: "68613660ba947189b202a82a",
  },
  {
    _id: "68675adb48a07a2109cadbc5",
    name: "Biology",
    image: "",
    subCategoryId: "68613660ba947189b202a82a",
  },
  {
    _id: "68675adf64dbb35d1ac2bf6d",
    name: "Chemistry",
    image: "",
    subCategoryId: "68613660ba947189b202a82a",
  },
  {
    _id: "68675ae4c89ade6664bac2c7",
    name: "Economics",
    image: "",
    subCategoryId: "68613660ba947189b202a82a",
  },
  {
    _id: "68675ae823aa4543677c7b15",
    name: "English Language",
    image: "",
    subCategoryId: "68613660ba947189b202a82a",
  },
  {
    _id: "68675aecd11c618955146895",
    name: "English Literature",
    image: "",
    subCategoryId: "68613660ba947189b202a82a",
  },
  {
    _id: "68675af0774f759b33af908f",
    name: "French",
    image: "",
    subCategoryId: "68613660ba947189b202a82a",
  },
  {
    _id: "68675af9234c36a111aa096e",
    name: "Geography",
    image: "",
    subCategoryId: "68613660ba947189b202a82a",
  },
  {
    _id: "68675aff984282cc6c920510",
    name: "History",
    image: "",
    subCategoryId: "68613660ba947189b202a82a",
  },
  {
    _id: "68675b04c496567cbe700dbf",
    name: "Information Technology",
    image: "",
    subCategoryId: "68613660ba947189b202a82a",
  },
  {
    _id: "68675b080e3b9430195f4255",
    name: "Mathematics",
    image: "",
    subCategoryId: "68613660ba947189b202a82a",
  },
  {
    _id: "68675b0ca24afd006ab8a5b6",
    name: "Physics",
    image: "",
    subCategoryId: "68613660ba947189b202a82a",
  },
  {
    _id: "68675b102798d22f980388a1",
    name: "Reading & Spelling",
    image: "",
    subCategoryId: "68613660ba947189b202a82a",
  },
  {
    _id: "68675b15e945057ec407115d",
    name: "Spanish",
    image: "",
    subCategoryId: "68613660ba947189b202a82a",
  },




  // EXTRACURRICULAR CLUBS & ACTIVITIES SUB-SUB-CATEGORY
  {
    _id: "6867640642c3d01e0d4f15ee",
    name: "Voice, speech and song",
    image: "",
    subCategoryId: "686759fe4c67abdc3020b820",
  },
  {
    _id: "6867640df7070e6a64af0c63",
    name: "Drama",
    image: "",
    subCategoryId: "686759fe4c67abdc3020b820",
  },
  {
    _id: "686764135a3479cfd878772f",
    name: "Dance",
    image: "",
    subCategoryId: "686759fe4c67abdc3020b820",
  },
  {
    _id: "6867641f4129db0e195da081",
    name: "Sports",
    image: "",
    subCategoryId: "686759fe4c67abdc3020b820",
  },
  {
    _id: "68676425eed9aa1fda096b02",
    name: "Music",
    image: "",
    subCategoryId: "686759fe4c67abdc3020b820",
  },
  {
    _id: "6867642cabc797c55a68ee4e",
    name: "Visual Art",
    image: "",
    subCategoryId: "686759fe4c67abdc3020b820",
  },
  {
    _id: "686764330c4c02833e10ef24",
    name: "Technology",
    image: "",
    subCategoryId: "686759fe4c67abdc3020b820",
  },
  {
    _id: "686764388fbfba4330944127",
    name: "Culinary Arts",
    image: "",
    subCategoryId: "686759fe4c67abdc3020b820",
  },
  {
    _id: "68676443e208c08e37f114c3",
    name: "Design",
    image: "",
    subCategoryId: "686759fe4c67abdc3020b820",
  },
  {
    _id: "6867644872766e05d1c42bec",
    name: "Environment",
    image: "",
    subCategoryId: "686759fe4c67abdc3020b820",
  },
  {
    _id: "68676454b29d0389a01b2ffb",
    name: "Academics",
    image: "",
    subCategoryId: "686759fe4c67abdc3020b820",
  },
  {
    _id: "6867645a9c9a66cb39d04a72",
    name: "Leadership Development & Service Clubs",
    image: "",
    subCategoryId: "686759fe4c67abdc3020b820",
  },
  {
    _id: "68676461f15863987167cca5",
    name: "Finance",
    image: "",
    subCategoryId: "686759fe4c67abdc3020b820",
  },
];

const subSubSubCategory = [
  // VOICE, SPEECH AND SONG 
  {
    _id: "68676482c4af71bcfb34e30b",
    name: "choir/Singing",
    image: "",
    subSubCategoryId: "6867640642c3d01e0d4f15ee",
  },
  // {
  //   _id: "68676488be4f68a924192901",
  //   name: "Singing",
  //   image: "",
  //   subSubCategoryId: "6867640642c3d01e0d4f15ee",
  // },
  {
    _id: "686764922f499e43703c9b7f",
    name: "Voice Ciaching",
    image: "",
    subSubCategoryId: "6867640642c3d01e0d4f15ee",
  },
  {
    _id: "686764983efaa843a174f4aa",
    name: "Voice Acting",
    image: "",
    subSubCategoryId: "6867640642c3d01e0d4f15ee",
  },
  {
    _id: "686764a0f634b0103f42b0af",
    name: "Voice and Speech",
    image: "",
    subSubCategoryId: "6867640642c3d01e0d4f15ee",
  },


  
  // DRAMA
  {
    _id: "686764b46fa5dcb6bc8f485e",
    name: "Drama/Theatre Arts",
    image: "",
    subSubCategoryId: "6867640df7070e6a64af0c63",
  },



  // DANCE
  {
    _id: "686764cd5d2ff23958df7f43",
    name: "Ballet",
    image: "",
    subSubCategoryId: "686764135a3479cfd878772f",
  },
  {
    _id: "686764d50855bfb2872785b8",
    name: "Creative Dancing (Traditional, Modern, Folk)",
    image: "",
    subSubCategoryId: "686764135a3479cfd878772f",
  },



  // SPORTS
  {
    _id: "6867651ac82f2d7b98cf4665",
    name: "Badminton",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "686765233bef71a1c29f0d02",
    name: "Basketball",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "68676530ba6d26348e87a336",
    name: "Cheerleading & Dance",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "686765387f1a790a3cc1a900",
    name: "Chess",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "6867653f94f482ff031c9524",
    name: "Cricket",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "68676546c12c7e8ec2d7d326",
    name: "Fotball",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "6867654ebf952dcd3c40b094",
    name: "Go-Karting",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "68676555926bc3e9781b66a1",
    name: "Golf",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "6867655c74c6ce737a01ee01",
    name: "Gymnastics",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "68676563a1f6bf084d7900ab",
    name: "Hockey",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "6867656ad1729484d2bd29a4",
    name: "Lacrosse",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "68676572f4880b8340b10351",
    name: "Martial Arts",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "6867657825221df93a8bbfc5",
    name: "Netball",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "6867658058a3118c003c402a",
    name: "Polo",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "6867658701b7b030374d0b00",
    name: "Rugby",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "6867658f4edf6e2622068144",
    name: "Swimming",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "6867659a553a49441d11d1aa",
    name: "Synchronized Swimmimg",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "686765a0a1a6118f5f12a38f",
    name: "Tennis (Lawn)",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "686765a8f0c1dad7f9954f69",
    name: "Tennis (Table)",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "686765ae64a088358f6f0344",
    name: "Track and Field",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },
  {
    _id: "686765c98e1e542084f9d1a1",
    name: "Volleyball",
    image: "",
    subSubCategoryId: "6867641f4129db0e195da081",
  },



  // MUSIC
  {
    _id: "686765fd05844dc579ae4b3b",
    name: "Piano",
    image: "",
    subSubCategoryId: "68676425eed9aa1fda096b02",
  },
  {
    _id: "68676602163b58985173dea8",
    name: "Drumming (Traditional)",
    image: "",
    subSubCategoryId: "68676425eed9aa1fda096b02",
  },
  {
    _id: "68676608ecbb7bac3578da63",
    name: "Drumming (African)",
    image: "",
    subSubCategoryId: "68676425eed9aa1fda096b02",
  },
  {
    _id: "68676611ea4efb69c41ccbac",
    name: "Guitar",
    image: "",
    subSubCategoryId: "68676425eed9aa1fda096b02",
  },
  {
    _id: "68676618b1db71c280ef0841",
    name: "Violin",
    image: "",
    subSubCategoryId: "68676425eed9aa1fda096b02",
  },
  {
    _id: "68676621948ab413238cba9f",
    name: "Cello",
    image: "",
    subSubCategoryId: "68676425eed9aa1fda096b02",
  },
  {
    _id: "6867662743b138ef89dd940c",
    name: "Steel Pan",
    image: "",
    subSubCategoryId: "68676425eed9aa1fda096b02",
  },
  {
    _id: "686766306102e88214faf588",
    name: "Sexophone",
    image: "",
    subSubCategoryId: "68676425eed9aa1fda096b02",
  },
  {
    _id: "686766376ae2a59959826066",
    name: "Recorder",
    image: "",
    subSubCategoryId: "68676425eed9aa1fda096b02",
  },
  {
    _id: "6867663fa1e33e51d106a268",
    name: "Trumpet",
    image: "",
    subSubCategoryId: "68676425eed9aa1fda096b02",
  },
  {
    _id: "68676647d11ec4c3d333dab4",
    name: "Trombone",
    image: "",
    subSubCategoryId: "68676425eed9aa1fda096b02",
  },
  {
    _id: "6867664e2d784fc220cec1c4",
    name: "Flute",
    image: "",
    subSubCategoryId: "68676425eed9aa1fda096b02",
  },
  {
    _id: "6867665441b3c391330d9dca",
    name: "Harp",
    image: "",
    subSubCategoryId: "68676425eed9aa1fda096b02",
  },



  // VISUAL ART
  {
    _id: "6867675e1256e3a664159ec6",
    name: "Art & Craft",
    image: "",
    subSubCategoryId: "6867642cabc797c55a68ee4e",
  },
  {
    _id: "6867676f3343a706aa025ef8",
    name: "Caligraphy & Typography",
    image: "",
    subSubCategoryId: "6867642cabc797c55a68ee4e",
  },
  {
    _id: "6867677416c1a7e87649f271",
    name: "Ceramics",
    image: "",
    subSubCategoryId: "6867642cabc797c55a68ee4e",
  },
  {
    _id: "6867678d940950d42f3e235e",
    name: "Digital Art",
    image: "",
    subSubCategoryId: "6867642cabc797c55a68ee4e",
  },
  {
    _id: "6867679d2a199500351ca8c1",
    name: "Drawing",
    image: "",
    subSubCategoryId: "6867642cabc797c55a68ee4e",
  },
  {
    _id: "686767ac0112cc3427f6b1ad",
    name: "Film & Video",
    image: "",
    subSubCategoryId: "6867642cabc797c55a68ee4e",
  },
  {
    _id: "686767b3aab5752707834b94",
    name: "Floral Arrangement",
    image: "",
    subSubCategoryId: "6867642cabc797c55a68ee4e",
  },
  {
    _id: "686767bb265f874b2ebe6fd0",
    name: "Graphic Design",
    image: "",
    subSubCategoryId: "6867642cabc797c55a68ee4e",
  },
  {
    _id: "686767c5276719faea37f4aa",
    name: "Painting",
    image: "",
    subSubCategoryId: "6867642cabc797c55a68ee4e",
  },
  {
    _id: "686767d32d0711099c171fa4",
    name: "Photography",
    image: "",
    subSubCategoryId: "6867642cabc797c55a68ee4e",
  },
  {
    _id: "686767d93ea5b6336d99bd27",
    name: "Printmaking",
    image: "",
    subSubCategoryId: "6867642cabc797c55a68ee4e",
  },
  {
    _id: "686767f358c49c9977a66d8c",
    name: "Sculpting",
    image: "",
    subSubCategoryId: "6867642cabc797c55a68ee4e",
  },
  {
    _id: "686767fe183b61694500230c",
    name: "Textile Art",
    image: "",
    subSubCategoryId: "6867642cabc797c55a68ee4e",
  },
  {
    _id: "686768067fa11fbafa54773b",
    name: "Woodwork",
    image: "",
    subSubCategoryId: "6867642cabc797c55a68ee4e",
  },


  
  // TECHNOLOGY
  {
    _id: "6867683898d216532c0bafb2",
    name: "Programming",
    image: "",
    subSubCategoryId: "686764330c4c02833e10ef24",
  },
  {
    _id: "6867683eab1e64ec6ecdc482",
    name: "Coding",
    image: "",
    subSubCategoryId: "686764330c4c02833e10ef24",
  },
  {
    _id: "686768467833e2eeb0641843",
    name: "Robotics",
    image: "",
    subSubCategoryId: "686764330c4c02833e10ef24",
  },
  {
    _id: "68676850b8682812b80bd780",
    name: "STEM Club",
    image: "",
    subSubCategoryId: "686764330c4c02833e10ef24",
  },



  // CULINARY ARTS
  {
    _id: "6867686c3a2962792d27f0bf",
    name: "Baking and Pastry Making",
    image: "",
    subSubCategoryId: "686764388fbfba4330944127",
  },
  {
    _id: "686768731ea87554179df01d",
    name: "Cake Decorating",
    image: "",
    subSubCategoryId: "686764388fbfba4330944127",
  },
  {
    _id: "686768784d4f7058dca0ceae",
    name: "Confectionary Making",
    image: "",
    subSubCategoryId: "686764388fbfba4330944127",
  },
  {
    _id: "6867688013433b975314dc4b",
    name: "Cooking",
    image: "",
    subSubCategoryId: "686764388fbfba4330944127",
  },
  {
    _id: "6867688a8004005a48ca5092",
    name: "Food Sculpting",
    image: "",
    subSubCategoryId: "686764388fbfba4330944127",
  },



  // DESIGN
  {
    _id: "68676987632e2f3b9f11e10e",
    name: "Interior Design",
    image: "",
    subSubCategoryId: "68676443e208c08e37f114c3",
  },
  {
    _id: "6867698da3ed5c5e5e7c1333",
    name: "Fashion Design",
    image: "",
    subSubCategoryId: "68676443e208c08e37f114c3",
  },
  {
    _id: "68676997f6ba68ab80d2cc3d",
    name: "Jewellary Making",
    image: "",
    subSubCategoryId: "68676443e208c08e37f114c3",
  },



  // ENVIRONMENT
  {
    _id: "686769ea2061247bb4b37478",
    name: "Environmental Clubs",
    image: "",
    subSubCategoryId: "6867644872766e05d1c42bec",
  },
  {
    _id: "686769f26303f214588f5ec7",
    name: "Aggriculture and Gardening Clubs",
    image: "",
    subSubCategoryId: "6867644872766e05d1c42bec",
  },
  {
    _id: "686769f81b762c60a50bd2c8",
    name: "4-H Clubs",
    image: "",
    subSubCategoryId: "6867644872766e05d1c42bec",
  },
  {
    _id: "68676a020d6553fff7084254",
    name: "Earth Ambassadors",
    image: "",
    subSubCategoryId: "6867644872766e05d1c42bec",
  },
  {
    _id: "68676a0a51e5f25816559c4d",
    name: "Recycling Projects",
    image: "",
    subSubCategoryId: "6867644872766e05d1c42bec",
  },
  {
    _id: "68676a10a75c99496ae3333d",
    name: "Tree-Planting Initiatives",
    image: "",
    subSubCategoryId: "6867644872766e05d1c42bec",
  },



  // ACADEMICS
  {
    _id: "68676a4802e8ee81ffeeb424",
    name: "Debate",
    image: "",
    subSubCategoryId: "68676454b29d0389a01b2ffb",
  },
  {
    _id: "68676a4e8b6b56a998712a10",
    name: "Spelling Bee",
    image: "",
    subSubCategoryId: "68676454b29d0389a01b2ffb",
  },
  {
    _id: "68676a5625c86d9e427e4702",
    name: "Schools Challenge Quiz",
    image: "",
    subSubCategoryId: "68676454b29d0389a01b2ffb",
  },
  {
    _id: "68676a62d17d6da93a2a0d18",
    name: "Kangaroo Math",
    image: "",
    subSubCategoryId: "68676454b29d0389a01b2ffb",
  },
  // {
  //   _id: "68676a6c093631566f991d99",
  //   name: "Writers",
  //   image: "",
  //   subSubCategoryId: "68676454b29d0389a01b2ffb",
  // },
  {
    _id: "68676a736ac20272bdd53344",
    name: "Writers/Journalism Clubs",
    image: "",
    subSubCategoryId: "68676454b29d0389a01b2ffb",
  },



  // LEADERSHIP DEVELOPMENT & SERVICE CLUBS
  {
    _id: "68676a9a8c2a8f285cb549a7",
    name: "Boy Scouts/Cub Scouts",
    image: "",
    subSubCategoryId: "6867645a9c9a66cb39d04a72",
  },
  // {
  //   _id: "68676ab6df1a2c0030bc3767",
  //   name: "Cub Scouts",
  //   image: "",
  //   subSubCategoryId: "6867645a9c9a66cb39d04a72",
  // },
  {
    _id: "68676abe7f155327565bce06",
    name: "Girl Guides/Brownies",
    image: "",
    subSubCategoryId: "6867645a9c9a66cb39d04a72",
  },
  // {
  //   _id: "68676ac58180e063c6db044a",
  //   name: "Brownies",
  //   image: "",
  //   subSubCategoryId: "6867645a9c9a66cb39d04a72",
  // },
  {
    _id: "68676acc8e710b6487f65df6",
    name: "Builders Club",
    image: "",
    subSubCategoryId: "6867645a9c9a66cb39d04a72",
  },
  {
    _id: "68676ad33ac1aa62b390b7c8",
    name: "Cadets",
    image: "",
    subSubCategoryId: "6867645a9c9a66cb39d04a72",
  },
  {
    _id: "68676ada723820349511e674",
    name: "Junior Achievement",
    image: "",
    subSubCategoryId: "6867645a9c9a66cb39d04a72",
  },
  {
    _id: "68676ae79de5b451ca32a195",
    name: "K-Kids",
    image: "",
    subSubCategoryId: "6867645a9c9a66cb39d04a72",
  },
  {
    _id: "68676af2a114366cbf3f7778",
    name: "Key Club",
    image: "",
    subSubCategoryId: "6867645a9c9a66cb39d04a72",
  },




  // FINANCE
  {
    _id: "68676b116463f02ffd992c35",
    name: "Young Investors and Finance and Literacy Groups",
    image: "",
    subSubCategoryId: "68676461f15863987167cca5",
  },
]

module.exports = {
  subCategory,
  subSubCategory,
  subSubSubCategory,
};
