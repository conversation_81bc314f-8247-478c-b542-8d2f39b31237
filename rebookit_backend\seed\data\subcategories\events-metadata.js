const subCategory = [
    {
        _id: "6874d0393b8c7032cc6181c7",
        name: "Music",
        image: "",
        categoryId: "68612cc0ba947189b202a828"
    },
    {
        _id: "6874d14cf701db4d0019d150",
        name: "Visual Arts",
        image: "",
        categoryId: "68612cc0ba947189b202a828"
    },
    {
        _id: "6874d6ecf57d79c55b079ddf",
        name: "Performing Arts",
        image: "",
        categoryId: "68612cc0ba947189b202a828"
    },
    {
        _id: "6874d7b31abe7ca42c3c6c3e",
        name: "Film / Screenings",
        image: "",
        categoryId: "68612cc0ba947189b202a828"
    },
    {
        _id: "6874d8187bb54e584844efd2",
        name: "Lectures & Book Events",
        image: "",
        categoryId: "68612cc0ba947189b202a828"
    },
    {
        _id: "6874d8892e3fa32661ec20eb",
        name: "Fashion Shows",
        image: "",
        categoryId: "68612cc0ba947189b202a828"
    },
    {
        _id: "6874d8f39c3c4fc0aeebb7df",
        name: "Food & Drink Events",
        image: "",
        categoryId: "68612cc0ba947189b202a828"
    },
    {
        _id: "6874d946be0498ad02b635f1",
        name: "Festivals & Fairs",
        image: "",
        categoryId: "68612cc0ba947189b202a828"
    },
    {
        _id: "6874d9b68d8a281bd008b12b",
        name: "Charity / Fundraisers",
        image: "",
        categoryId: "68612cc0ba947189b202a828"
    },
    {
        _id: "6874da07868c6c012cdea602",
        name: "Sports & Active Life",
        image: "",
        categoryId: "68612cc0ba947189b202a828"
    },
    {
        _id: "6874da653e27f556ddbca33f",
        name: "Nightlife",
        image: "",
        categoryId: "68612cc0ba947189b202a828"
    },
    {
        _id: "6874dab0e872f29c259ab2fe",
        name: "Kids & Family Events",
        image: "",
        categoryId: "68612cc0ba947189b202a828"
    },
];



const subsubcategories = [
    // MUSIC
    {
        _id: "6874d0f6b0263cd37f32851a",
        name: "Concerts",
        image: "",
        subCategoryId: "6874d0393b8c7032cc6181c7"
    },
    {
        _id: "6874d0fdfd24821247a78f20",
        name: "DJ Nights",
        image: "",
        subCategoryId: "6874d0393b8c7032cc6181c7"
    },
    {
        _id: "6874d10c458101681aa6bc04",
        name: "Open Mic",
        image: "",
        subCategoryId: "6874d0393b8c7032cc6181c7"
    },
    {
        _id: "6874d11767f35b1ca2a25469",
        name: "Classical Music",
        image: "",
        subCategoryId: "6874d0393b8c7032cc6181c7"
    },
    {
        _id: "6874d122213ba5a6eaa82ef0",
        name: "Indie/Underground",
        image: "",
        subCategoryId: "6874d0393b8c7032cc6181c7"
    },
    {
        _id: "6874d12ed100d5afc21b698a",
        name: "Music Workshops",
        image: "",
        subCategoryId: "6874d0393b8c7032cc6181c7"
    },
    {
        _id: "6874d137850ad3ba97639921",
        name: "Album Launches",
        image: "",
        subCategoryId: "6874d0393b8c7032cc6181c7"
    },




    // VISUAL ARTS
    {
        _id: "6874d1fd91fc0e2096649cbd",
        name: "Art Exhibitions",
        image: "",
        subCategoryId: "6874d14cf701db4d0019d150"
    },
    {
        _id: "6874d205790eb394041ebc54",
        name: "Photography Shows",
        image: "",
        subCategoryId: "6874d14cf701db4d0019d150"
    },
    {
        _id: "6874d20e9cc66e8b75adc68c",
        name: "Sculpture / Installations",
        image: "",
        subCategoryId: "6874d14cf701db4d0019d150"
    },
    {
        _id: "6874d216aac0618cb0bb56ee",
        name: "Art Competitions",
        image: "",
        subCategoryId: "6874d14cf701db4d0019d150"
    },
    {
        _id: "6874d222180210f9e83df5cd",
        name: "Street Art Tours",
        image: "",
        subCategoryId: "6874d14cf701db4d0019d150"
    },
    {
        _id: "6874d22df0e04bad8e585334",
        name: "Gallery Openings",
        image: "",
        subCategoryId: "6874d14cf701db4d0019d150"
    },




    // PERFORMING ARTS
    {
        _id: "6874d7140af262239e750af4",
        name: "Theatre / Drama",
        image: "",
        subCategoryId: "6874d6ecf57d79c55b079ddf"
    },
    {
        _id: "6874d71fa70f8342ec21e4e6",
        name: "Stand-Up Comedy",
        image: "",
        subCategoryId: "6874d6ecf57d79c55b079ddf"
    },
    {
        _id: "6874d72adf3dffa27c4135e7",
        name: "Dance Performances",
        image: "",
        subCategoryId: "6874d6ecf57d79c55b079ddf"
    },
    {
        _id: "6874d78958a19acbcd16987b",
        name: "Improve Shows",
        image: "",
        subCategoryId: "6874d6ecf57d79c55b079ddf"
    },
    {
        _id: "6874d79c6785a295982ffe14",
        name: "Puppetry",
        image: "",
        subCategoryId: "6874d6ecf57d79c55b079ddf"
    },
    {
        _id: "6874d7a670c16812f77efc06",
        name: "Opera",
        image: "",
        subCategoryId: "6874d6ecf57d79c55b079ddf"
    },




    // FILM / SCREENINGS
    {
        _id: "6874d7cea1e805070c8743ba",
        name: "Movie Premieres",
        image: "",
        subCategoryId: "6874d7b31abe7ca42c3c6c3e"
    },
    {
        _id: "6874d7da936b6adbf04a2191",
        name: "Short Film Screenings",
        image: "",
        subCategoryId: "6874d7b31abe7ca42c3c6c3e"
    },
    {
        _id: "6874d7e2c880446d7a7be575",
        name: "Film Festivals",
        image: "",
        subCategoryId: "6874d7b31abe7ca42c3c6c3e"
    },
    {
        _id: "6874d7f254197c0afe97bd43",
        name: "Documentary Nights",
        image: "",
        subCategoryId: "6874d7b31abe7ca42c3c6c3e"
    },
    {
        _id: "6874d7fe9bcca307cb36126b",
        name: "Drive-in Movies",
        image: "",
        subCategoryId: "6874d7b31abe7ca42c3c6c3e"
    },


    // LECTURES & BOOK EVENTS
    {
        _id: "6874d840dbda1eb538c0e037",
        name: "Author Meet & Greet",
        image: "",
        subCategoryId: "6874d8187bb54e584844efd2"
    },
    {
        _id: "6874d8471cd717a2ae74ccca",
        name: "Book Launches",
        image: "",
        subCategoryId: "6874d8187bb54e584844efd2"
    },
    {
        _id: "6874d84d5788c6a3b84376c4",
        name: "Public Talks / TedX",
        image: "",
        subCategoryId: "6874d8187bb54e584844efd2"
    },
    {
        _id: "6874d8582f6ee8162eb2aeb4",
        name: "Literary Festivals",
        image: "",
        subCategoryId: "6874d8187bb54e584844efd2"
    },
    {
        _id: "6874d861236ed1f60c661c65",
        name: "Academic Lectures",
        image: "",
        subCategoryId: "6874d8187bb54e584844efd2"
    },
    {
        _id: "6874d873d4d6517f9c184d81",
        name: "Poetry Readings",
        image: "",
        subCategoryId: "6874d8187bb54e584844efd2"
    },



    // FASHION SHOWS
    {
        _id: "6874d8b4d335056996fcd3f9",
        name: "Runway Events",
        image: "",
        subCategoryId: "6874d8892e3fa32661ec20eb"
    },
    {
        _id: "6874d8c1ffa558be1972e3a1",
        name: "Fashion Week Events",
        image: "",
        subCategoryId: "6874d8892e3fa32661ec20eb"
    },
    {
        _id: "6874d8c8091518fe3cee361c",
        name: "Designer Showcases",
        image: "",
        subCategoryId: "6874d8892e3fa32661ec20eb"
    },
    {
        _id: "6874d8d889b3eb5d4eeee463",
        name: "Fashion Pop-ups",
        image: "",
        subCategoryId: "6874d8892e3fa32661ec20eb"
    },
    {
        _id: "6874d8e1a9d55ce7b137519b",
        name: "OperStyling Workshopsa",
        image: "",
        subCategoryId: "6874d8892e3fa32661ec20eb"
    },



    // FOOD & DRINK EVENTS
    {
        _id: "6874d911e5b1f37b51e295bd",
        name: "Food Festivals",
        image: "",
        subCategoryId: "6874d8f39c3c4fc0aeebb7df"
    },
    {
        _id: "6874d919380f5e05ede43afc",
        name: "Wine / Beer Tastings",
        image: "",
        subCategoryId: "6874d8f39c3c4fc0aeebb7df"
    },
    {
        _id: "6874d92040d1989328dc829e",
        name: "Cooking Classes",
        image: "",
        subCategoryId: "6874d8f39c3c4fc0aeebb7df"
    },
    {
        _id: "6874d92b6532c517054f0c20",
        name: "Chef’s Table / Pop-up Restaurants",
        image: "",
        subCategoryId: "6874d8f39c3c4fc0aeebb7df"
    },
    {
        _id: "6874d932d085314c4dd9338f",
        name: "Food Truck Gatherings",
        image: "",
        subCategoryId: "6874d8f39c3c4fc0aeebb7df"
    },



    // FESTIVALS & FAIRS
    {
        _id: "6874d96997734be5566a568d",
        name: "Cultural Festivals",
        image: "",
        subCategoryId: "6874d946be0498ad02b635f1"
    },
    {
        _id: "6874d97449e69b39520db2da",
        name: "Religious Festivals",
        image: "",
        subCategoryId: "6874d946be0498ad02b635f1"
    },
    {
        _id: "6874d97e5d50127939e3ab4b",
        name: "Craft Fairs",
        image: "",
        subCategoryId: "6874d946be0498ad02b635f1"
    },
    {
        _id: "6874d9923e456f754374c7e2",
        name: "Flea Markets",
        image: "",
        subCategoryId: "6874d946be0498ad02b635f1"
    },
    {
        _id: "6874d9a3674ea5b2c411a474",
        name: "Tech Fairs",
        image: "",
        subCategoryId: "6874d946be0498ad02b635f1"
    },
    {
        _id: "6874d9aae76217f23baf2b63",
        name: "Holi / Diwali / Christmas Events",
        image: "",
        subCategoryId: "6874d946be0498ad02b635f1"
    },




    // CHARITY / FUNDRAISERS
    {
        _id: "6874d9d085d69b7eb40e11a1",
        name: "Donation Drives",
        image: "",
        subCategoryId: "6874d9b68d8a281bd008b12b"
    },
    {
        _id: "6874d9e1c7321a9836d26e5e",
        name: "Walkathons / Marathons",
        image: "",
        subCategoryId: "6874d9b68d8a281bd008b12b"
    },
    {
        _id: "6874d9e93ab522b93e33447e",
        name: "Auction for Cause",
        image: "",
        subCategoryId: "6874d9b68d8a281bd008b12b"
    },
    {
        _id: "6874d9f2f2966ca1eb54ac40",
        name: "NGO Showcases",
        image: "",
        subCategoryId: "6874d9b68d8a281bd008b12b"
    },
    {
        _id: "6874d9f7bf4791df7fe13330",
        name: "Community Service Events",
        image: "",
        subCategoryId: "6874d9b68d8a281bd008b12b"
    },



    // SPORTS & ACTIVE LIFE
    {
        _id: "6874da1d6b8ca44767bdb922",
        name: "Marathons / Running Events",
        image: "",
        subCategoryId: "6874da07868c6c012cdea602"
    },
    {
        _id: "6874da235edad2da736f1101",
        name: "Yoga / Zumba Sessions",
        image: "",
        subCategoryId: "6874da07868c6c012cdea602"
    },
    {
        _id: "6874da312d82e6044e7ec21e",
        name: "Football / Cricket Matches",
        image: "",
        subCategoryId: "6874da07868c6c012cdea602"
    },
    {
        _id: "6874da3f8e2b777246338372",
        name: "Adventure Activities",
        image: "",
        subCategoryId: "6874da07868c6c012cdea602"
    },
    {
        _id: "6874da49b8a4b53522b40b80",
        name: "OpSports Campsra",
        image: "",
        subCategoryId: "6874da07868c6c012cdea602"
    },
    {
        _id: "6874da57a785bb3f26b41654",
        name: "Fitness Challenges",
        image: "",
        subCategoryId: "6874da07868c6c012cdea602"
    },



    // NIGHTLIFE

    {
        _id: "6874da772c811a914393bef0",
        name: "Club Events",
        image: "",
        subCategoryId: "6874da653e27f556ddbca33f"
    },
    {
        _id: "6874da7f280cd440cd4c5c30",
        name: "Bar Nights",
        image: "",
        subCategoryId: "6874da653e27f556ddbca33f"
    },
    {
        _id: "6874da8ba62ac6654071afbd",
        name: "Theme Parties",
        image: "",
        subCategoryId: "6874da653e27f556ddbca33f"
    },
    {
        _id: "6874da9415028cd419cc9f39",
        name: "Live DJs",
        image: "",
        subCategoryId: "6874da653e27f556ddbca33f"
    },
    {
        _id: "6874da9b58cfbc61a89e7c41",
        name: "Ladies’ Nights",
        image: "",
        subCategoryId: "6874da653e27f556ddbca33f"
    },
    {
        _id: "6874daa126da9dee6570737f",
        name: "Pub Crawls",
        image: "",
        subCategoryId: "6874da653e27f556ddbca33f"
    },



    // KIDS & FAMILY EVENTS
    {
        _id: "6874daca8e59fdf60639e9f7",
        name: "Magic Shows",
        image: "",
        subCategoryId: "6874dab0e872f29c259ab2fe"
    },
    {
        _id: "6874dad6570fb4b615d26a8c",
        name: "Storytelling",
        image: "",
        subCategoryId: "6874dab0e872f29c259ab2fe"
    },
    {
        _id: "6874dae2787f5fddbb7e0d2b",
        name: "Toy / Comic Conventions",
        image: "",
        subCategoryId: "6874dab0e872f29c259ab2fe"
    },
    {
        _id: "6874daea2f448100c8bfe4f9",
        name: "Family Picnics",
        image: "",
        subCategoryId: "6874dab0e872f29c259ab2fe"
    },
    {
        _id: "6874daf479b2c166fd1c3523",
        name: "Puppet Shows",
        image: "",
        subCategoryId: "6874dab0e872f29c259ab2fe"
    },
    {
        _id: "6874dafe1fbd6582916cce59",
        name: "Kids’ Workshops / STEM Events",
        image: "",
        subCategoryId: "6874dab0e872f29c259ab2fe"
    },
];

module.exports = {
    subCategory,
    subsubcategories
}