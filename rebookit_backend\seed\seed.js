const mongoose = require("mongoose");
mongoose.set("debug", true)
const bcrypt = require("bcrypt");
const dotenv = require("dotenv");
const bookModel = require('../tables/schema/books.js');
const fs = require('fs');

const roleModel = require("../tables/schema/roles");
const userModel = require("../tables/schema/user");
const subscriptionPlanModel = require("../tables/schema/subscription_plans");
const categoryModel = require("../tables/schema/category");
const subCategoryModel = require("../tables/schema/subCategory");
const subSubCategoryModel = require("../tables/schema/subSubCategory");
const subSubSubCategoryModel = require("../tables/schema/subSubSubCategory");

const { roles } = require("./data/roles");
const { subscriptionPlans } = require("./data/subscription-plan");
const { category } = require("./data/category");
const {
  subCategory: bookSubCategories,
  subSubCategory: bookSubSubCategories,
  subSubSubCategory: bookSubSubSubCategories
} = require("./data/subcategories/books-metadata");
const {
  subCategory: edDirectorySubCategories,
  subSubCategory: edDirectorySubSubCategories,
  subSubSubCategory: edDirectorySubSUbSubCategories
} = require("./data/subcategories/edirectory-metadata");

const {
  subCategory: eventSubCategories,
  subsubcategories: eventSubSubCategories,
} = require("./data/subcategories/events-metadata");

const {
  subCategory: scholarshipCategories
} = require("./data/subcategories/scholarship-metadata")


const allSubCategories = [...bookSubCategories, ...edDirectorySubCategories, ...eventSubCategories, ...scholarshipCategories];
const allSubSubCategories = [...bookSubSubCategories, ...edDirectorySubSubCategories, ...eventSubSubCategories];
const allSubSubSubCategories = [ ...bookSubSubSubCategories, ...edDirectorySubSUbSubCategories];

dotenv.config();

async function connect() {
  try {
    await mongoose.connect(process.env.DB_URL);
    console.log("Connected to MongoDB");
  } catch (error) {
    console.error("Error connecting to MongoDB:", error);
  }
}

// Connect to MongoDB and seed
const initFunction = async () => {
  try {
    await connect();
    // Insert new roles
    await roleModel.deleteMany({});
    await roleModel.insertMany(roles);
    console.log("Roles seeded successfully");

    // Insert new Subscription Plans
    await subscriptionPlanModel.deleteMany({});
    await subscriptionPlanModel.insertMany(subscriptionPlans);
    console.log("Subscription Plans created successfully");

    let adminRole = await roleModel.findOne({ roleName: "admin" });


    let salt = await bcrypt.genSalt(10);
    let adminPassword = await bcrypt.hash("Demo@1234", salt);
    const adminUser = {
      firstName: "Admin",
      lastName: "User",
      email: "<EMAIL>",
      isEmailVerified: true,
      isProfileCompleted: true,
      mobileNumber: "+************",
      password: adminPassword,
      roleId: adminRole._id,
      location: {
        type: "Point",
        coordinates: [77.5946, 12.9716], // [longitude, latitude] — Example for Bangalore, India
      },
    }
    await userModel.deleteMany();
    await userModel.create(adminUser);
    console.log("Admin created successfully");


    // Insert new Categories
    await categoryModel.deleteMany({});
    await categoryModel.insertMany(category);
    console.log("Categories created successfully");

    // Insert new Sub-Categories
    await subCategoryModel.deleteMany({});
    await subCategoryModel.insertMany(allSubCategories);
    console.log("Subcategories created successfully");

    //  // Insert new Sub- Sub Categories
    await subSubCategoryModel.deleteMany({});
    await subSubCategoryModel.insertMany(allSubSubCategories);
    console.log("SubSubcategories created successfully");

    // Insert new SubSubSub Category model
    await subSubSubCategoryModel.deleteMany({});
    await subSubSubCategoryModel.insertMany(allSubSubSubCategories);
    console.log("Sub Sub Sub created successfully");

    const books = JSON.parse(fs.readFileSync(require('path').join(__dirname, 'data', 'books.json')));

    let insertedCount = 0;

    for (const bookData of books) {
      const transformed = {
        isbn: bookData.isbn || '',
        title: bookData.title,
        author_name: Array.isArray(bookData.author_name) ? bookData.author_name : [],
        publisher: bookData.publisher || {},
        physicalAttributes: bookData.physicalAttributes || {},
        content: {
          description: bookData.content?.description || '',
          tableOfContents: bookData.content?.tableOfContents || [],
          languages: bookData.content?.language || [],
          genres: bookData.content?.genres || [],
          subjects: bookData.content?.subjects || [],
          keywords: bookData.content?.keywords || [],
          ageRange: bookData.content?.ageRange || undefined
        },
        series: bookData.series || {},
        media: bookData.media || {},
        cover_i: bookData.cover_i || '',
        pricing: bookData.pricing || {}
      };
      const book = new bookModel(transformed);
      await book.save();
      insertedCount++;
      const grade = transformed.content.keywords.find(k => k.startsWith("GRADE_"));
      console.log(`Added: ${transformed.title} (${grade || ''})`);
    }
    console.log(`Total books inserted: ${insertedCount}`);

    await mongoose.disconnect();
    console.log("database connection closed");
    process.exit(0); // Exit
  } catch (error) {
    await mongoose.disconnect();
    console.error("Seeding failed:", error);
    process.exit(1);
  }
};

initFunction();