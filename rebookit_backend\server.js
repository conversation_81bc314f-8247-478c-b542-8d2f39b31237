const express = require("express");
const dotenv = require("dotenv");
const swaggerUi = require("swagger-ui-express");
const cors = require("cors");
const { createServer } = require("http");
const LoggerFactory = require("./logger/logger-factory.js");
const requestLogger = require("./middleware/request-logger.js");

const swaggerDocument = require("./doc");
const { initQueue, receiveData } = require("./queue/configuration");
const path = require("path");
const { createConnection } = require("./db-config/direct_config");
const routes = require("./routes.js");
const { ConnectSocketServer } = require("./socket/server.io");
const { NotFoundError } = require("./common/customErrors.js");
const { QueueEventHandlers } = require("./queue/event-handlers.js");
const errorHandler = require("./middleware/error-handler.middleware.js");
const PaymentController = require("./controllers/payment.controller.js");
const wrapAsync = require("./common/wrapAsync.js");
require("./crons/cron");

dotenv.config();

const app = express();
const httpServer = createServer(app);

const logger = LoggerFactory.getLogger();

createConnection();

(async () => {
  await initQueue();
  receiveData((queueKey, message) => {
    QueueEventHandlers(queueKey, message);
  });
})();

app.use(cors());
app.use(requestLogger());

app.use("/api/payment/webhook", express.raw({ type: "application/json" }), wrapAsync(PaymentController.handleWebhook));

app.use(express.json());
app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerDocument));

app.use(express.static(path.resolve("public")));

app.get("/health-check", (req, res) => {
  res.status(200).json({ status: "active" });
});

routes.forEach((route) => {
  app.use(route.path, route.router);
});

app.use(function (req, res, next) {
  throw new NotFoundError("Route not found");
});

app.use(errorHandler);

ConnectSocketServer(httpServer);

const port = process.env.PORT;

httpServer.listen(port, (err) => {
  if (err) logger.error("Error occurred:", err);
  else logger.info(`App server started on port ${port} by pid ${process.pid}`);
});
