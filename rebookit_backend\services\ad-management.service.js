const { DateTime } = require("luxon");
const { BadRequestError, InternalServerError } = require("../common/customErrors");
const { AdResourceTypeEnum, AdPageTypeEnum, AdPositionTypeEnum, PaymentStatusEnum } = require("../common/Enums");
const AdModel = require("../tables/schema/ad");
const AdPaymentModel = require("../tables/schema/adPayment");
const PricingOverrideModel = require("../tables/schema/pricingOverride");
const PricingRuleModel = require("../tables/schema/pricingRule");
const ResourceModel = require("../tables/schema/resource");

const createAdResource = async (data) => {
  const { type, page, position, basePrice, pricingRules = [], overrideRules = [] } = data;
  const exitingResource = await ResourceModel.findOne({ type, page, position });
  if (exitingResource) {
    throw new BadRequestError("Resource already exist");
  }

  const basePriceRule = {
    name: "base price",
    price: basePrice,
    priority: 0,
    startDate: DateTime.utc().startOf("year").toJSDate(),
    endDate: DateTime.utc().endOf("year").toJSDate(),
    color: "none",
  };

  pricingRules.push(basePriceRule);

  const names = pricingRules.map((r) => r.name);
  if (names.length !== Array.from(new Set(names)).length) {
    throw new BadRequestError("Pricing rule names must be unique");
  }

  const resource = await ResourceModel.create({ type, page, position, pricingRules, overrideRules });

  if (!resource) {
    throw new InternalServerError("Something went wrong");
  }

  return { resource };
};

const updateAdResource = async (resourceId, data) => {
  const resource = await ResourceModel.findById(resourceId);
  if (!resource) {
    throw new BadRequestError("Resource does not Exist");
  }

  // const updatedResource = await ResourceModel.findByIdAndUpdate(
  //   resourceId,
  //   {
  //     $push: {
  //       pricingRules: { $each: data.pricingRules },
  //       pricingOverride: { $each: data.pricingOverride }
  //     },
  //   },
  //   { new: true, runValidators: true }
  // );

  // Update pricing rules
  if (data.pricingRules && data.pricingRules.length > 0) {
    for (const rule of data.pricingRules) {
      const { startDate, endDate, price, priority } = rule;
      // Find existing rule with same date range
      const existingRule = await ResourceModel.findOne({
        _id: resourceId,
        "pricingRules.startDate": startDate,
        "pricingRules.endDate": endDate,
      });

      if (existingRule) {
        // Update existing rule
        await ResourceModel.updateOne(
          {
            _id: resourceId,
            "pricingRules.startDate": startDate,
            "pricingRules.endDate": endDate,
          },
          {
            $set: {
              "pricingRules.$.price": price,
              "pricingRules.$.priority": priority,
            },
          },
          { runValidators: true }
        );
      } else {
        // Create new rule
        await ResourceModel.findByIdAndUpdate(
          resourceId,
          {
            $push: {
              pricingRules: {
                price,
                priority,
                startDate,
                endDate,
              },
            },
          },
          { runValidators: true }
        );
      }
    }
  }

  // Update override rules
  if (data.pricingOverride && data.pricingOverride.length > 0) {
    for (const override of data.pricingOverride) {
      const { date, price } = override;
      // Find existing override with same date
      const existingOverride = await ResourceModel.findOne({
        _id: resourceId,
        "pricingOverride.date": date,
      });

      if (existingOverride) {
        // Update existing override
        await ResourceModel.updateOne(
          {
            _id: resourceId,
            "pricingOverride.date": date,
          },
          {
            $set: {
              "pricingOverride.$.price": price,
            },
          },
          { runValidators: true }
        );
      } else {
        // Create new override
        await ResourceModel.findByIdAndUpdate(
          resourceId,
          {
            $push: {
              pricingOverride: {
                date,
                price,
              },
            },
          },
          { runValidators: true }
        );
      }
    }
  }

  const updatedResource = await ResourceModel.findById(resourceId);

  return { updatedResource };
};

const getAllAdResources = async () => {
  const resources = await ResourceModel.find({});
  return { resources };
};

const getAdResourcesById = async (resourceId) => {
  const resource = await ResourceModel.findById(resourceId);
  if (!resource) {
    throw new BadRequestError("Resource does not exist");
  }

  return { resource };
};

const deleteAdResource = async (resourceId) => {
  const resource = await ResourceModel.findById(resourceId);
  if (!resource) {
    throw new BadRequestError("Resource does not exist");
  }

  const deleted = await ResourceModel.findByIdAndDelete(resourceId);
  if (!deleted) {
    throw new InternalServerError("Failed to delete resource");
  }

  return { deleted, message: "Resource deleted successfully" };
};

const updateBasePrice = async (data) => {
  const { resourceId, baseId, price } = data;

  const resource = await ResourceModel.findById(resourceId);
  if (!resource) {
    throw new BadRequestError("Resource does not exist");
  }

  const updateBase = await ResourceModel.updateOne(
    { _id: resourceId, "pricingRules._id": baseId, "pricingRules.name": "base price" },
    {
      $set: {
        "pricingRules.$.price": price,
      },
    },
    {
      new: true,
      runValidators: true,
    }
  );
  if (!updateBase) throw new InternalServerError("Something went wrong");

  return updateBase;
};

const setPriceRule = async (data) => {
  const { resourceId, rules } = data;

  const resource = await ResourceModel.findById(resourceId);
  if (!resource) {
    throw new BadRequestError("Resource does not exist");
  }

  const rulesToPush = Array.isArray(rules) ? rules : [rules];

  const updated = await ResourceModel.findByIdAndUpdate(
    resourceId,
    {
      $push: {
        pricingRules: { $each: rulesToPush },
      },
    },
    { new: true, runValidators: true }
  );

  if (!updated) {
    throw new InternalServerError("Failed to update pricing rules");
  }

  return { resource: updated };
};

const updatePriceRule = async (data) => {
  const { resourceId, ruleId, pricingRules } = data;

  const updates = {};
  ["name", "price", "priority", "startDate", "endDate", "color", "isActive"].forEach((field) => {
    if (Object.prototype.hasOwnProperty.call(pricingRules, field)) {
      updates[`pricingRules.$.${field}`] = field.endsWith("Date") ? new Date(pricingRules[field]) : pricingRules[field];
    }
  });

  const updatedRule = await ResourceModel.updateOne(
    { _id: resourceId, "pricingRules._id": ruleId },
    {
      $set: updates,
    },
    {
      new: true,
      runValidators: true,
    }
  );
  if (!updatedRule) throw new InternalServerError("Something went wrong");

  return updatedRule;
};

const deletePriceRule = async (data) => {
  const { resourceId, ruleId } = data;

  const resource = await ResourceModel.findById(resourceId);
  if (!resource) {
    throw new BadRequestError("Resource does not exist");
  }

  const deleted = await ResourceModel.updateOne(
    { _id: resourceId },
    { $pull: { pricingRules: { _id: ruleId } } },
    { runValidators: true }
  );

  if (!deleted.modifiedCount) {
    throw new InternalServerError("Failed to delete pricing rule");
  }

  return { deleted };
};

const setOverrideRule = async (data) => {
  const { resourceId, overridePrices } = data;

  const resource = await ResourceModel.findById(resourceId);
  if (!resource) {
    throw new BadRequestError("Invalid Resouce");
  }

  const updated = await ResourceModel.findByIdAndUpdate(
    resourceId,
    {
      $push: {
        overrideRules: { $each: overridePrices },
      },
    },
    { new: true, runValidators: true }
  );

  if (!updated) {
    throw new InternalServerError("Failed to update pricing rules");
  }

  return { resource: updated };
};

const updateOverrideRule = async (data) => {
  const { resourceId, overrideId, overRidePrices } = data;

  const resource = await ResourceModel.findById(resourceId);
  if (!resource) {
    throw new BadRequestError("Resource does not exist");
  }

  const updates = {};
  ["price", "date"].forEach((field) => {
    if (Object.prototype.hasOwnProperty.call(overRidePrices, field)) {
      updates[`overRidePrices.$.${field}`] = field.endsWith("Date") ? new Date(overRidePrices[field]) : overRidePrices[field];
    }
  });

  const updatedRule = await ResourceModel.updateOne(
    { _id: resourceId, "overRidePrices._id": overrideId },
    {
      $set: updates,
    },
    {
      new: true,
      runValidators: true,
    }
  );
  if (!updatedRule) throw new InternalServerError("Something went wrong");

  return updatedRule;
};

const deleteOverride = async (data) => {
  const { resourceId, overrideId } = data;

  const resource = await ResourceModel.findById(resourceId);
  if (!resource) {
    throw new BadRequestError("Resource does not exist");
  }

  const deleted = await ResourceModel.updateOne(
    { _id: resourceId },
    { $pull: { overrideRules: { _id: overrideId } } },
    { runValidators: true }
  );

  if (!deleted.modifiedCount) {
    throw new InternalServerError("Failed to delete override rule");
  }

  return { deleted };
};

const getTodayPrice = async (resourceId, date) => {
  const resource = await ResourceModel.findById(resourceId);
  if (!resource) {
    throw new BadRequestError("Resouce does not exist");
  }

  // Check for overrides
  const override = await PricingOverrideModel.findOne({
    resourceId,
    dates: { $in: [new Date(date)] },
  });
  if (override) return { price: override.pricePerDay };

  // Check active pricing rules with highest priority, if priority are same then return the latest created
  const rules = await PricingRuleModel.find({
    resourceId,
    startDate: { $lte: new Date(date) },
    endDate: { $gt: new Date(date) },
  }).sort({ priority: -1, createdAt: -1 });

  if (rules.length > 0) return { price: rules[0].pricePerDay };

  // Fallback to base price
  return { price: resource.basePrice };
};

// User routes

// Calculate total price for ad dates
async function calculateAdPrice(resourceId, dates) {
  let total = 0;
  for (const date of dates) {
    const price = await getTodayPrice(resourceId, date);
    total += price;
  }
  return total;
}

const createAd = async (user, data) => {
  const { resourceId, startDate, endDate } = data;

  const resource = await ResourceModel.findById(resourceId);
  if (!resource) {
    throw new BadRequestError("Resource does not exist");
  }

  const ad = await AdModel.create({
    resourceId,
    startDate: new Date(startDate),
    endDate: new Date(endDate),
    userId: user._id,
    status: "pending",
    isActive: false,
    paymentStatus: "pending",
  });

  if (!ad) throw new BadRequestError("Soemthing went wrong");

  return { ad };
};

const createCompaign = async (data) => {
  const { resourceId, dates, userId } = data;

  const maxDate = new Date();
  maxDate.setFullYear(maxDate.getFullYear() + 1);
  const invalidDates = dates.filter((date) => new Date(date) > maxDate);
  if (invalidDates.length > 0) {
    throw new BadRequestError("Dates must be within 1 year");
  }

  const campaign = await AdModel.create({
    resourceId,
    dates: dates.map((d) => new Date(d)),
    userId,
    status: "pending",
    isActive: true,
  });

  return { campaign };
};

const updateAdStatus = async (id, body) => {
  const ad = await AdModel.findById(id);
  if (!ad) throw new BadRequestError("Ad not found");

  if(ad.paymentStatus !== PaymentStatusEnum.SUCCESS) {
    throw new BadRequestError("Payment is pending, cannot proceed without payment");
  }

  if (!["approved", "rejected"].includes(body.status)) {
    throw new BadRequestError("Invalid status");
  }

  ad.status = body.status;
  await ad.save();

  return { ad };
};

const createPaymentIntent = async (data) => {
  const { campaignId, userId } = data;

  const campaign = await AdModel.findById(campaignId);
  if (!campaign) return res.status(404).json({ error: "Campaign not found" });
  if (campaign.userId.toString() !== userId) {
    throw new BadRequestError("Unauthorized Access");
  }
  if (campaign.status !== "approved") {
    throw new BadRequestError("Ad must be approved before payment");
  }

  // Calculate total price
  const totalAmount = await calculateAdPrice(campaign.resourceId, campaign.dates);
  const amountInCents = Math.round(totalAmount * 100);

  // Create Stripe payment intent
  const paymentIntent = await stripe.paymentIntents.create({
    amount: amountInCents,
    currency: "usd",
    metadata: { campaignId: campaignId.toString(), userId: userId.toString() },
  });

  // Store payment record
  const payment = await AdPaymentModel.create({
    userId,
    campaignId,
    amount: totalAmount,
    currency: "jusd",
    status: "pending",
    paymentIntent: paymentIntent.id,
  });

  return {
    clientSecret: paymentIntent.client_secret,
    paymentId: payment._id,
  };
};

const getAdPlansService = async (type, position, page = 1, limit = 10) => {
  const query = { isActive: true }; // Only return active plans
  if (type) query.type = type;
  if (position) query.position = position;

  const skip = (parseInt(page) - 1) * parseInt(limit);

  const [adPlans, total] = await Promise.all([
    ResourceModel.find(query).skip(skip).limit(parseInt(limit)),
    ResourceModel.countDocuments(query)
  ]);

  return {
    adPlans,
    pagination: {
      total,
      page: parseInt(page),
      limit: parseInt(limit),
      totalPages: Math.ceil(total / limit)
    }
  };
};

const getAdPlan = async (id) => {
  const adPlan = await ResourceModel.findOne({ _id: id, isActive: true });
  return adPlan;
};

const webhook = async (req) => {
  const sig = req.headers["stripe-signature"];
  let event;

  try {
    event = stripe.webhooks.constructEvent(req.body, sig, process.env.STRIPE_WEBHOOK_SECRET);
  } catch (err) {
    return res.status(400).send(`Webhook Error: ${err.message}`);
  }

  if (event.type === "payment_intent.succeeded") {
    const paymentIntent = event.data.object;
    const payment = await AdPaymentModel.findOneAndUpdate(
      { paymentIntent: paymentIntent.id },
      { status: "success" },
      { new: true }
    );
    if (payment) {
      await AdModel.findByIdAndUpdate(payment.campaignId, { isActive: true });
    }
  }

  return { received: true };
};

module.exports = {
  createAdResource,
  updateAdResource,
  getAllAdResources,
  getAdResourcesById,
  deleteAdResource,
  updateBasePrice,
  setPriceRule,
  updatePriceRule,
  deletePriceRule,
  setOverrideRule,
  updateOverrideRule,
  deleteOverride,
  // getTodayPrice,
  createAd,
  createCompaign,
  updateAdStatus,
  createPaymentIntent,
  getAdPlansService,
  webhook,
  getAdPlan
};
