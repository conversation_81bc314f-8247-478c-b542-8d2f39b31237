const mongoose = require("mongoose");
const { INDEX_NAMES } = require("../common/collectionName");
const { BadRequestError, InternalServerError } = require("../common/customErrors");
const { ItemListStatusEnum, RoleEnum, PlanTypeEnum, PlanStatusEnum } = require("../common/Enums");
const { sendData } = require("../queue/configuration");
const { queueName, NotificationEventName, ElasticsearchEventName } = require("../queue/queueNames");
const bookmarkModel = require("../tables/schema/bookmark");
const { itemlistModel } = require("../tables/schema/itemList");
const paymentModel = require("../tables/schema/paymentDetails");
const roleModel = require("../tables/schema/roles");
const subscriptionModel = require("../tables/schema/subscription");
const subscriptionPlanModel = require("../tables/schema/subscription_plans");
const testimonialModel = require("../tables/schema/testimonial");
const userModel = require("../tables/schema/user");
const UserReviewModel = require("../tables/schema/reviewSchema");
const Conversation = require("../tables/schema/conversation");
const Message = require("../tables/schema/messages");
const categoryModel = require("../tables/schema/category");

const getAdminById = async (id) => {
  return await userModel.findById(id);
};

const addSubscriptionPlan = async (data) => {
  if (data.planType == PlanTypeEnum.FREE) {
    const existingFreePlan = await subscriptionPlanModel.findOne({
      planType: PlanTypeEnum.FREE,
    });
    if (existingFreePlan) {
      throw new BadRequestError("Free plan already exists, only 1 free plan is allowed");
    }

    if (data.price) throw new BadRequestError("Free plan can not have price.");
    data.price = 0;
  }

  const categories = data.listings.map((o) => o.category);
  const uniqueEntries = Array.from(new Set([...categories]));
  if (uniqueEntries.length != categories.length) throw new BadRequestError("Duplicate category found in categorywise listings");

  const foundCategories = await categoryModel.find({ _id: { $in: categories } });
  if (foundCategories.length < categories.length) throw new BadRequestError("Invalid category found");

  const subscriptionPlan = await subscriptionPlanModel.create(data);

  return {
    subscriptionPlan,
  };
};

const subscriptionPlansList = async () => {
  const subscriptionPlans = await subscriptionPlanModel.find({});
  return {
    subscriptionPlans,
  };
};

const updateSubscriptionPlan = async (planId, data) => {
  if (data.listings) {
    const categories = data.listings.map((o) => o.category);
    const uniqueEntries = Array.from(new Set([...categories]));
    if (uniqueEntries.length != categories.length) throw new BadRequestError("Duplicate category found in categorywise listings");

    const foundCategories = await categoryModel.find({ _id: { $in: categories } });
    if (foundCategories.length < categories.length) throw new BadRequestError("Invalid category found");
  }
  const updatedPlan = await subscriptionPlanModel.findByIdAndUpdate(planId, { $set: data }, { new: true, runValidators: true });
  return { updatedPlan };
};

const filterMembers = async (body, page = 1, pageSize = 10) => {
  const pageNo = Number(page) || 1;
  pageSize = Number(pageSize);
  const skip = (pageNo - 1) * pageSize;
  let sortObj = { createdAt: -1 };

  const result = await subscriptionModel.aggregate([
    {
      $lookup: {
        from: "subscription_plans",
        let: { planId: "$planId" },
        pipeline: [
          { $match: { $expr: { $eq: ["$_id", "$$planId"] } } },
          {
            $project: {
              planName: 1,
              planMonths: 1,
              planType: 1,
              price: 1,
              profileImage: 1,
            },
          },
        ],

        as: "subsriptionPlanDoc",
      },
    },
    { $unwind: "$subsriptionPlanDoc" },
    {
      $match: { ...body.filters },
    },
    {
      $facet: {
        count: [
          {
            $count: "count",
          },
        ],
        data: [
          {
            $sort: sortObj,
          },
          {
            $skip: skip,
          },
          {
            $limit: pageSize,
          },
          {
            $lookup: {
              from: "users",
              let: { userId: "$userId" },
              pipeline: [
                { $match: { $expr: { $eq: ["$_id", "$$userId"] } } },
                {
                  $project: {
                    firstName: 1,
                    lastName: 1,
                    email: 1,
                    mobileNumber: 1,
                    profileImage: 1,
                  },
                },
              ],

              as: "userDoc",
            },
          },
          {
            $unwind: "$userDoc",
          },
          {
            $project: {
              startDate: 1,
              endDate: 1,
              status: 1,
              subsriptionPlanDoc: 1,
              userDoc: 1,
              paymentId: 1,
            },
          },
        ],
      },
    },
  ]);

  const totalCount = result[0].count.length > 0 ? result[0].count[0].count : 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    page: pageNo,
    pageSize,
    totalPages,
    totalCount,
    data: result[0].data || [],
  };
};

const modifyItemStatus = async (itemId, data) => {
  const { status } = data;
  const reasonForReject = data.reason || "";
  let updateQuery = { status };

  if (status == ItemListStatusEnum.ACCEPTED) {
    updateQuery = { status, isActive: true, publishedAt: new Date() };
  }

  if (status == ItemListStatusEnum.REJECTED) {
    updateQuery = { status, reason: reasonForReject };
  }

  const foundItem = await itemlistModel.findById(itemId);
  if (!foundItem) throw new BadRequestError("Item not found.");
  if (foundItem.status == status) throw new BadRequestError("Old and new status can not be same");
  
  if (foundItem.status == ItemListStatusEnum.ACCEPTED && status == ItemListStatusEnum.REJECTED) {
    throw new BadRequestError("Approved items can not be rejected, they can be removed from listing");
  }
  if (foundItem.status == ItemListStatusEnum.REJECTED && status == ItemListStatusEnum.ACCEPTED) {
    throw new BadRequestError("Rejected items can not be approved, they can be removed from listing");
  }

  const itemStatusUpdated = await itemlistModel.findByIdAndUpdate(
    itemId,
    { $set: updateQuery },
    { new: true, runValidators: true }
  );

  if (!itemStatusUpdated) throw new InternalServerError("Something went wrong");

  if (itemStatusUpdated.status === ItemListStatusEnum.ACCEPTED) {
    const foundItem = await itemlistModel
      .findById(itemId)
      .populate({
        path: "categoryId",
        select: {
          name: 1,
        },
      })
      .populate({
        path: "subCategoryId",
        select: {
          name: 1,
        },
      })
      .populate({
        path: "createdBy",
        select: {
          firstName: 1,
          lastName: 1,
          email: 1,
        },
      });
    console.log("foundItem", foundItem);

    const activeSubscription = await subscriptionModel
      .findOne({
        userId: foundItem.createdBy._id,
        status: PlanStatusEnum.ACTIVE,
      })
      .populate("planId");

    await subscriptionModel.findOneAndUpdate(
      { _id: activeSubscription._id, "remainingListings.category": foundItem.categoryId._id },
      { $inc: { "remainingListings.$.noOfListing": -1 } }
    );

    sendData(queueName["NOTIFICATION_QUEUE"], {
      EventName: NotificationEventName.ITEM_APPROVED,
      data: {
        _id: itemStatusUpdated?._id,
        body: itemStatusUpdated,
      },
    });
  } else if (itemStatusUpdated.status === ItemListStatusEnum.HOLD) {
    sendData(queueName["NOTIFICATION_QUEUE"], {
      EventName: NotificationEventName.ITEM_HOLD,
      data: {
        index: INDEX_NAMES.ITEMS,
        _id: itemStatusUpdated?._id,
        body: itemStatusUpdated,
      },
    });
  } else if (itemStatusUpdated.status === ItemListStatusEnum.REJECTED) {
    sendData(queueName["NOTIFICATION_QUEUE"], {
      EventName: NotificationEventName.ITEM_REJECTED,
      data: {
        _id: itemStatusUpdated?._id,
        body: itemStatusUpdated,
        reason: reasonForReject,
      },
    });
  }

  return {
    item: itemStatusUpdated,
  };
};

const getItemById = async (itemId) => {
  const item = await itemlistModel
    .findById(itemId)
    .populate("createdBy", "_id firstName lastName email mobileNumber profileImage")
    .populate("categoryId")
    .populate("subCategoryId")
    .populate("subSubCategoryId")
    .populate("subSubSubCategoryId")
    .lean();
  if (!item) {
    throw new BadRequestError("Item does not exist");
  }

  const review = await UserReviewModel.findOne({ userId: item.createdBy._id }).select({
    averageRating: 1,
    totalReviews: 1,
  });
  return {
    item,
    review: review ? review : null,
  };
};
const deleteListedItem = async (itemId, hard) => {
  const item = await itemlistModel.findById(itemId).select("createdBy");
  if (!item) throw new BadRequestError("Invalid item id");

  if (hard) {
    await itemlistModel.findByIdAndDelete(itemId);

    await userModel.findByIdAndUpdate(item.createdBy, {
      $pull: { allListingItems: itemId },
    });

    const foundConversations = await Conversation.find({ sellerItemId: itemId });
    if (foundConversations.length > 0) {
      for (const conv of foundConversations) {
        await Message.deleteMany({ conversation: conv._id });
        await Conversation.findByIdAndDelete(conv._id);
      }
    }
    return {
      message: "Item parmanently deleted",
    };
  } else {
    if(item.status === ItemListStatusEnum.DELETED) throw new BadRequestError("Can't delete item, as this item is already removed by user")
    await itemlistModel.findByIdAndUpdate(itemId, { $set: { status: ItemListStatusEnum.ADMIN_DELETED, isActive: false } });
    return {
      message: "Item removed from listing",
    };
  }
};

const getUsers = async (body, page = 1, pageSize = 10) => {
  const pageNo = Number(page) || 1;
  pageSize = Number(pageSize);
  const skip = (pageNo - 1) * pageSize;
  const searchTerm = body?.searchTerm;
  let searchQuery = {};
  if (searchTerm) {
    const searchTerms = searchTerm.split(/\s+/); // Split into words
    let nameConditions = [];

    if (searchTerms.length >= 2) {
      const firstWord = searchTerms[0];
      const remainingWords = searchTerms.slice(1).join(" ");

      nameConditions.push({
        $and: [
          { firstName: { $regex: escapeRegex(firstWord), $options: "i" } },
          { lastName: { $regex: escapeRegex(remainingWords), $options: "i" } },
        ],
      });

      // Reverse case
      nameConditions.push({
        $and: [
          { firstName: { $regex: escapeRegex(remainingWords), $options: "i" } },
          { lastName: { $regex: escapeRegex(firstWord), $options: "i" } },
        ],
      });
    }

    const escapedTerm = escapeRegex(searchTerm);
    searchQuery = {
      $or: [
        ...nameConditions,
        { firstName: { $regex: escapedTerm, $options: "i" } },
        { lastName: { $regex: escapedTerm, $options: "i" } },
        { email: { $regex: escapedTerm, $options: "i" } },
      ],
    };
  }

  const userRole = await roleModel.findOne({ roleName: RoleEnum.USER });
  if (!userRole) return { count: 0, users: [] };

  const query = { ...searchQuery, roleId: userRole._id, isProfileCompleted: true };
  const users = await userModel.aggregate([
    { $match: query },
    { $sort: { createdAt: -1 } },
    { $skip: skip },
    { $limit: pageSize },
    {
      $lookup: {
        from: "subscriptions",
        let: { id: "$_id" },
        pipeline: [
          { $match: { status: PlanStatusEnum.ACTIVE, $expr: { $eq: ["$userId", "$$id"] } } },
          {
            $project: {
              startDate: 1,
              endDate: 1,
              planId: 1,
              status: 1,
              remainingListings: 1,
              remainingBoosts: 1,
            },
          },
        ],
        as: "subscriptionDoc",
      },
    },
    { $unwind: { path: "$subscriptionDoc", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "subscription_plans",
        foreignField: "_id",
        localField: "subscriptionDoc.planId",
        as: "planDoc",
      },
    },
    { $unwind: { path: "$planDoc", preserveNullAndEmptyArrays: true } },
    {
      $project: {
        email: 1,
        firstName: 1,
        lastName: 1,
        location: 1,
        aboutMe: 1,
        mobileNumber: 1,
        profileImage: 1,
        allListingItems: 1,
        status: 1,
        registeredAt: 1,
        subscriptionDoc: { $ifNull: ["$subscriptionDoc", null] },
        planDoc: { $ifNull: ["$planDoc", null] },
      },
    },
  ]);

  const totalCount = await userModel.countDocuments(query);
  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    page: pageNo,
    pageSize,
    totalPages,
    totalCount,
    data: users,
  };
};

const getUserById = async (userId) => {
  const user = await userModel.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(userId) } },
    {
      $lookup: {
        from: "itemlists",
        let: { id: "$_id" },
        pipeline: [
          { $match: { $expr: { $eq: ["$createdBy", "$$id"] } } },
          { $lookup: { from: "categories", foreignField: "_id", localField: "categoryId", as: "categoryDoc" } },
          { $unwind: "$categoryDoc" },
          {
            $project: {
              title: 1,
              description: 1,
              address: 1,
              status: 1,
              images: 1,
              categoryId: 1,
              categoryDoc: 1,
            },
          },
        ],
        as: "itemlistsDoc",
      },
    },
    {
      $lookup: {
        from: "payments",
        let: { id: "$_id" },
        pipeline: [
          { $match: { $expr: { $eq: ["$userId", "$$id"] } } },
          {
            $lookup: {
              from: "subscription_plans",
              foreignField: "_id",
              localField: "planId",
              as: "planDoc",
            },
          },
          { $unwind: "$planDoc" },
        ],
        as: "paymentsDoc",
      },
    },
    {
      $lookup: {
        from: "subscriptions",
        let: { id: "$_id" },
        pipeline: [
          { $match: { status: PlanStatusEnum.ACTIVE, $expr: { $eq: ["$userId", "$$id"] } } },
          {
            $project: {
              startDate: 1,
              endDate: 1,
              planId: 1,
              status: 1,
              remainingListings: 1,
              remainingBoosts: 1,
            },
          },
        ],
        as: "subscriptionDoc",
      },
    },
    { $unwind: { path: "$subscriptionDoc", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "subscription_plans",
        foreignField: "_id",
        localField: "subscriptionDoc.planId",
        as: "planDoc",
      },
    },
    { $unwind: { path: "$planDoc", preserveNullAndEmptyArrays: true } },
    {
      $project: {
        email: 1,
        firstName: 1,
        lastName: 1,
        location: 1,
        aboutMe: 1,
        mobileNumber: 1,
        profileImage: 1,
        itemlistsDoc: 1,
        paymentsDoc: 1,
        subscriptionDoc: { $ifNull: ["$subscriptionDoc", null] },
        planDoc: { $ifNull: ["$planDoc", null] },
      },
    },
  ]);

  if (user.length < 1) throw new BadRequestError("Invalid user");

  return {
    user: user[0],
  };
};

// Helper to escape regex special characters
const escapeRegex = (text) => {
  return text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&");
};

const getOverview = async () => {
  const userRole = await roleModel.findOne({ roleName: RoleEnum.USER });
  const now = new Date();
  const oneWeekAgo = new Date().setDate(now.getDate() - 7);

  const [totalTransactions, totalListedItems, totalUsers, newUser] = await Promise.all([
    paymentModel.countDocuments(),
    itemlistModel.countDocuments({ status: ItemListStatusEnum.ACCEPTED }),
    userModel.countDocuments({ roleId: userRole._id }),
    userModel.countDocuments({ roleId: userRole._id, createdAt: { $gte: oneWeekAgo } }),
  ]);

  return {
    totalTransactions,
    totalListedItems,
    totalUsers,
    newUser,
  };
};

// GET LAST MONTH AND LAST MONTH SUBSCRIBERS COUNT
const getLastMonthSubscription = async (thisMonthStartDate, thisMonthEndDate, lastMonthStartDate, lastMonthEndDate) => {
  // All params should be JS Date objects or ISO strings
  const currentStartDate = new Date(thisMonthStartDate);
  const currentEndDate = new Date(thisMonthEndDate);
  currentEndDate.setHours(23, 59, 59, 999);

  const lastStartDate = new Date(lastMonthStartDate);
  const lastEndDate = new Date(lastMonthEndDate);
  lastEndDate.setHours(23, 59, 59, 999);

  const currentRangeCount = await subscriptionModel.countDocuments({
    startDate: {
      $gte: currentStartDate,
      $lt: currentEndDate,
    },
  });

  const lastRangeCount = await subscriptionModel.countDocuments({
    startDate: {
      $gte: lastStartDate,
      $lt: lastEndDate,
    },
  });

  return {
    thisMonthSubscriptions: currentRangeCount,
    lastMonthSubscriptions: lastRangeCount,
  };
};

// GET TOTAL AD REQUESTS
const getTotalAdRequests = async (startDate, endDate) => {
  // startDate and endDate should be JS Date objects or ISO strings
  const start = new Date(startDate);
  const end = new Date(endDate);
  // Ensure end date includes the whole day
  end.setHours(23, 59, 59, 999);

  const totalBannerAd = await adModel.countDocuments({
    type: "banner",
    createdAt: { $gte: start, $lte: end },
  });
  const totalFeaturedAd = await adModel.countDocuments({
    type: "featured",
    createdAt: { $gte: start, $lte: end },
  });
  return { totalBannerAd, totalFeaturedAd };
};

// TESTIMONIAL SERVICES

// CREATE TESTIMONIAL
const createTestimonial = async (data) => {
  if (!data.name) throw new BadRequestError("Name is required");
  if (!data.title) throw new BadRequestError("Title is required");
  if (!data.content) throw new BadRequestError("Content is required");
  if (!data.image) throw new BadRequestError("Image is required");

  const testimonial = await testimonialModel.create(data);
  return testimonial;
};

// UPDATE TESTIMONIAL
const updateTestimonial = async (testimonialId, data) => {
  const existingTestimonial = await testimonialModel.findById(testimonialId);
  if (!existingTestimonial) throw new BadRequestError("Testimonial not found");

  const updatedTestimonial = await testimonialModel.findByIdAndUpdate(
    testimonialId,
    { $set: data },
    { new: true, runValidators: true }
  );
  if (!updatedTestimonial) throw new InternalServerError("Something went wrong");
  return {
    updatedTestimonial,
  };
};

// DELETE TESTIMONIAL
const deleteTestimonial = async (testimonialId) => {
  const deletedTestimonial = await testimonialModel.findByIdAndDelete(testimonialId);
  if (!deletedTestimonial) throw new BadRequestError("Testimonial not found");
  return deletedTestimonial;
};

// GET TESTIMONIALS
const getTestimonial = async () => {
  const testimonials = await testimonialModel.find({});
  return testimonials;
};

module.exports = {
  getAdminById,

  addSubscriptionPlan,
  subscriptionPlansList,
  updateSubscriptionPlan,
  filterMembers,

  modifyItemStatus,
  getItemById,
  deleteListedItem,
  getUsers,
  getUserById,
  getOverview,

  getLastMonthSubscription,
  getTotalAdRequests,
  createTestimonial,
  updateTestimonial,
  deleteTestimonial,
  getTestimonial,
};
