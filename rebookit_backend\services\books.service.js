const axios = require("axios")
const dotenv = require("dotenv");
const { BadRequestError } = require("../common/customErrors");
dotenv.config({})

const bookModel = require('../tables/schema/books'); // Add this import


const bookServiceInstance = axios.create({
  baseURL: process.env.BOOKS_BASE_URL ,
  // baseURL: "https://openlibrary.org" ,
  timeout: 15000, 
});

// const searchBooks = async (query) => {
//     if(!query) throw new BadRequestError("Query required with the key q")
//     const response = await bookServiceInstance.get(`/search.json?q=${(query).trim().replace(/\s+/g, '+')}`);
//     return response.data;
// };

const getBookByISBN = async (ISBN) => {
    const response = await bookServiceInstance.get(`/api/books?bibkeys=ISBN:${ISBN}&format=json&jscmd=data`);
    return response.data;
};


const searchBooks = async (query) => {
    if (!query) throw new BadRequestError("Query required with the key q");
    const normalizedQuery = query.toLowerCase().trim();

    // Text search first
    const textResults = await bookModel.find(
      { $text: { $search: normalizedQuery } },
      { score: { $meta: "textScore" } }
    ).sort({ score: { $meta: "textScore" } }).limit(50).lean();

    let customData = textResults;

    if (textResults.length < 5) {
      const regexResults = await bookModel.find({
        title: { $regex: normalizedQuery, $options: 'i' }
      }).limit(50).lean();
      const ids = new Set(textResults.map(b => b._id.toString()));
      customData = [...textResults, ...regexResults.filter(b => !ids.has(b._id.toString()))];
    }

    // Third-party API fetch
    const fetchedData = await (async () => {
      const response = await bookServiceInstance.get(`/search.json?q=${encodeURIComponent(query)}`);
      return response.data ? response.data : { docs: [] };
    })();

    // Merge local DB books at the top of docs
    // Try to avoid duplicates by ISBN or title+author_name
    const dbDocs = customData.map(b => ({ ...b, _source: 'local' }));
    const dbIsbns = new Set(dbDocs.map(b => b.isbn).filter(Boolean));
    const dbTitles = new Set(dbDocs.map(b => b.title && b.title.toLowerCase()));

    // Remove third-party docs that are already in our DB (by ISBN or title)
    const thirdPartyDocs = (fetchedData.docs || []).filter(doc => {
      if (doc.isbn && Array.isArray(doc.isbn)) {
        if (doc.isbn.some(isbn => dbIsbns.has(isbn))) return false;
      } else if (doc.isbn && dbIsbns.has(doc.isbn)) {
        return false;
      }
      if (doc.title && dbTitles.has(doc.title.toLowerCase())) return false;
      return true;
    });

    // Compose the merged docs array
    const mergedDocs = [
      ...dbDocs,
      ...thirdPartyDocs
    ];

    // Return the full third-party response, but with our merged docs array
    return {
      ...fetchedData,
      docs: mergedDocs,
      numFound: (dbDocs.length + thirdPartyDocs.length),
      num_found: (dbDocs.length + thirdPartyDocs.length),
      q: query
    };
};

module.exports = {
  searchBooks,
  getBookByISBN,
}
