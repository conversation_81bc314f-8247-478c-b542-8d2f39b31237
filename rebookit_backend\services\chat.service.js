const { BadRequestError } = require("../common/customErrors.js");
const { ONLINE_USERS_SET, redis } = require("../redis/configuration.js");
const Conversation = require("../tables/schema/conversation.js");
const Message = require("../tables/schema/messages.js");
const mongoose = require("mongoose");
const userModel = require("../tables/schema/user.js");
const { ItemListStatusEnum } = require("../common/Enums.js");

class socketService {}

socketService.registerUser = async function (socketId, user) {
  await redis.hset(
    ONLINE_USERS_SET,
    user._id,
    JSON.stringify({
      _id: socketId,
      user: {
        firstName: user?.firstName,
        lastName: user.lastName,
      },
    })
  ); // hset adds a member to a set
  return socketService.getAllOnlineUsers();
};

socketService.unregisterUser = async function (socket) {
  console.log("unregistered", socket.user);
  redis.hdel(ONLINE_USERS_SET, socket.id);

  await userModel.findByIdAndUpdate(socket.user._id, {
    $unset: {
      selectedChatFilters: "",
    },
  });
  return socketService.getAllOnlineUsers();
};

socketService.getAllOnlineUsers = async function () {
  const users = await redis.hgetall(ONLINE_USERS_SET); // return all data as object
  console.log("users", users);
  let parsedUsers = {};
  Object.keys(users).map((socketId) => {
    // Parse the stored JSON data
    console.log("socketId", socketId, JSON.parse(users[socketId]));
    const user = JSON.parse(users[socketId]);
    parsedUsers[socketId] = { socketId, user };
  });

  return parsedUsers;
};

socketService.getAllChats = async function (userId, body) {
  let chatTypeFilter = {};

  if (body.chatType === "sellers") {
    chatTypeFilter["item_doc.createdBy._id"] = { $ne: userId };
  } else if (body.chatType === "buyers") {
    chatTypeFilter["item_doc.createdBy._id"] = { $eq: userId };
  }

  console.log("body", body, chatTypeFilter);

  // Helper to escape regex special characters
  const escapeRegex = (text) => text.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, "\\$&");

  const pipeline = [
    {
      $match: {
        participants: {
          $in: [new mongoose.Types.ObjectId(userId)],
        },
        deletedBy: {
          $ne: userId,
        },
      },
    },
    {
      $project: {
        participantData: 1,
        participants: 1,
        status: 1,
        lastMessage: { $arrayElemAt: ["$messages", -1] },
        type: 1,
        updatedAt: 1,
        sellerItemId: 1,
        deletedBy: 1,
      },
    },
    {
      $lookup: {
        from: "users",
        let: { participantsIds: "$participants" },
        pipeline: [
          { $match: { $expr: { $in: ["$_id", "$$participantsIds"] } } },
          { $project: { _id: 1, firstName: 1, lastName: 1 } },
        ],
        as: "participantData",
      },
    },
    {
      $lookup: {
        from: "messages",
        foreignField: "_id",
        localField: "lastMessage",
        as: "lastMessage",
      },
    },
    // Unwind lastMessage to convert array to object
    { $unwind: { path: "$lastMessage", preserveNullAndEmptyArrays: true } },
    {
      $lookup: {
        from: "itemlists",
        let: { itemId: "$sellerItemId" },
        pipeline: [
          { $match: { $expr: { $eq: ["$_id", "$$itemId"] } } },
          {
            $project: {
              title: 1,
              description: 1,
              authors: 1,
              _id: 1,
              createdBy: 1,
              images: 1,
              status: 1,
              isActive: 1,
              price: 1,
            },
          },
          {
            $lookup: {
              from: "users",
              let: { createdBy: "$createdBy" },
              pipeline: [
                {
                  $match: {
                    $expr: {
                      $eq: ["$_id", "$$createdBy"],
                    },
                  },
                },
                {
                  $project: {
                    _id: 1,
                    firstName: 1,
                    lastName: 1,
                    profileImage: 1,
                    email: 1,
                    mobileNumber: 1,
                  },
                },
              ],
              as: "createdBy",
            },
          },
          // { $unwind: { path: "$createdBy", preserveNullAndEmptyArrays: true } }
        ],
        as: "item_doc",
      },
    },
    { $unwind: "$item_doc" },
    { $match: chatTypeFilter },
  ];

  // Add search if searchTerm exists
  if (body.searchTerm && body.searchTerm.trim()) {
    const escapedSearchTerm = escapeRegex(body.searchTerm.trim());
    const regexPattern = `.*${escapedSearchTerm}.*`;

    const searchFilter = {
      $expr: {
        $gt: [
          {
            $size: {
              $filter: {
                input: "$participantData",
                as: "participant",
                cond: {
                  $and: [
                    { $ne: ["$$participant._id", userId] },
                    {
                      $or: [
                        {
                          $regexMatch: {
                            input: "$$participant.firstName",
                            regex: regexPattern,
                            options: "i",
                          },
                        },
                        {
                          $regexMatch: {
                            input: "$$participant.lastName",
                            regex: regexPattern,
                            options: "i",
                          },
                        },
                      ],
                    },
                  ],
                },
              },
            },
          },
          0,
        ],
      },
    };
    pipeline.push({ $match: searchFilter });
  }

  pipeline.push({ $sort: { updatedAt: -1 } });

  const allChats = await Conversation.aggregate(pipeline);
  return allChats;
};

socketService.getChatById = async function (user, convId, page = 1) {
  const pageNo = page || 1;
  const pageSize = Number(process.env.PAGE_SIZE);
  const skip = (pageNo - 1) * pageSize;

  const conversation = await Conversation.aggregate([
    { $match: { _id: new mongoose.Types.ObjectId(convId) } },
    {
      $project: {
        participants: 1,
        status: 1,
        type: 1,
        createdAt: 1,
        updatedAt: 1,
        sellerItemId: 1,
        deletedBy: 1,
      },
    },
    {
      $lookup: {
        from: "users",
        foreignField: "_id",
        localField: "participants",
        as: "participants_docs",
      },
    },
    {
      $lookup: {
        from: "itemlists",
        foreignField: "_id",
        localField: "sellerItemId",
        as: "item",
      },
    },
  ]);
  // console.log("conver", conversation);
  if (conversation.length < 1) {
    throw new BadRequestError("Invalid converstaion");
  }

  const participants = conversation[0].participants.map((o) => o.toString());

  if (!participants.includes(user._id.toString())) {
    throw new BadRequestError("You're not part of this conversation");
  }

  const messages = await Message.find({
    conversation: convId,
    deletedBy: {
      $ne: user._id,
    },
  })
    .sort({
      createdAt: 1,
    })
    .skip(skip)
    .limit(pageSize);

  const count = await Message.countDocuments({ conversation: convId });
  return {
    count,
    conversation,
    messages,
  };
};

socketService.deleteChat = async function (user, conv) {
  const conversation = await Conversation.findById(conv);
  console.log("socketService.deleteChat conversation", conversation);
  if (!conversation) {
    throw new BadRequestError("Invalid conversation");
  }

  const participants = conversation.participants.map((o) => o.toString());

  if (!participants.includes(user._id.toString())) {
    throw new BadRequestError("You're not part of this conversation");
  }

  if (conversation.deletedBy?.find((userId) => userId.equals(user._id))) {
    throw new BadRequestError("Conversation is already deleted by you.");
  }

  const updatedChat = await Conversation.findByIdAndUpdate(
    conv,
    {
      $push: {
        deletedBy: user._id,
      },
    },
    {
      new: true,
      runValidators: true,
    }
  );
  await Message.updateMany(
    { conversation: conv },
    {
      $push: {
        deletedBy: user._id,
      },
    }
  );
  return {
    deletedConversation: updatedChat,
  };
};

socketService.getChatByItemId = async function (user, itemId, page = 1) {
  const foundConv = await Conversation.findOne({ participants: user._id, sellerItemId: new mongoose.Types.ObjectId(itemId) });

  if (!foundConv)
    return {
      count: 0,
      conversation: null,
      messages: [],
    };
  const chatDetails = await socketService.getChatById(user, foundConv._id, page);
  return chatDetails;
};

module.exports = {
  socketService,
};
