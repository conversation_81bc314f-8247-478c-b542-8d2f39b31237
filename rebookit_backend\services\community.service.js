const questionModel = require("../tables/schema/question");
const answerModel = require("../tables/schema/answer");
const { Types, default: mongoose } = require("mongoose");
const { sendData } = require("../queue/configuration");
const { queueName, NotificationEventName } = require("../queue/queueNames");
const categoryModel = require("../tables/schema/category");
const { BadRequestError } = require("../common/customErrors");
const userModel = require("../tables/schema/user");

const postQuestion = async (user, body) => {
  if (body.categoryId) {
    const foundCategory = await categoryModel.findById(body.categoryId);
    if (!foundCategory) throw new BadRequestError("Invalid category");
  }

  const newQuestion = await questionModel.create({
    ...body,
    askedBy: user._id,
  });
  return {
    question: newQuestion,
  };
};

const editAnswer = async (user, body, answerId) => {
  const foundAnswer = await answerModel.findById( answerId ).populate('questionId');
  if(!foundAnswer) throw new BadRequestError("Invalid answer id");
  if(foundAnswer.questionId.askedBy.toString() == user._id.toString()) {
    throw new BadRequestError("Invalid operation");
  }

  const updatedAnswer = await answerModel.findByIdAndUpdate( answerId, { 
    $set: body,
  }, { new : true, runValidators : true });

  return {
    updatedAnswer
  }

};

const responseToQuestion = async (user, data) => {
  const foundQuestion = await questionModel.findById(data.questionId);
  if (!foundQuestion) throw new BadRequestError("Invalid question");
  if (foundQuestion.askedBy.toString() == user._id.toString()) throw new BadRequestError("You can't answer your own question");

  const existingAnswer = await answerModel.findOne({
    questionId: data.questionId,
    answeredBy: user._id,
  });
  if (existingAnswer) throw new BadRequestError("You can't answer a question twice");

  const answer = await answerModel.create({ ...data, answeredBy: user._id });
  return {
    answer,
  };
};

const answerCountLookup = {
  $lookup: {
    from: "answers",
    let: { qId: "$_id" },
    pipeline: [{ $match: { $expr: { $eq: ["$questionId", "$$qId"] } } }],
    as: "answerDocs",
  },
};

const addTotalAnswers = {
  $addFields: {
    totalAnswers: {
      $size: "$answerDocs",
    },
  },
};

const latestAnswerLookup = {
  $lookup: {
    from: "answers",
    let: { qId: "$_id" },
    pipeline: [
      { $match: { $expr: { $eq: ["$questionId", "$$qId"] } } },
      { $sort: { createdAt: -1 } },
      { $limit: 1 },
      {
        $project: {
          _id: 1,
          answerText: 1,
          answeredBy: 1,
          createdAt: 1,
        },
      },
    ],
    as: "latestAnswer",
  },
};
const unwindLatest = { $unwind: { path: "$latestAnswer", preserveNullAndEmptyArrays: true } };

const userLookup = [
  {
    $lookup: {
      from: "users",
      let: { askedBy: "$askedBy" },
      pipeline: [{ $match: { $expr: { $eq: ["$_id", "$$askedBy"] } } }, { $project: { firstName: 1, lastName: 1, profileImage: 1 } }],
      as: "askedBy",
    },
  },
  { $unwind: "$askedBy" },
];

async function fetchQuestions({ user, askedBy, page = 1, pageSize = 10, onlyAnswered = false, categoryId, searchTerm, sort = "latest" }) {
  pageSize = Number(pageSize);
  const pageNo = Number(page) || 1;
  const skip = (pageNo - 1) * pageSize;

  const pipeline = [];
  let commonFilter = {};
  if (categoryId) commonFilter = { categoryId: new mongoose.Types.ObjectId(categoryId) };

  let useTextSort = false;
  if (searchTerm) {
    pipeline.push({ $match: { $text: { $search: searchTerm } } });
    // text score sorting
    pipeline.push({ $addFields: { score: { $meta: "textScore" } } });
    useTextSort = true;
  }

  if (askedBy) {
    pipeline.push({ $match: { askedBy, ...commonFilter } });
  }
  // in case of all questions remove current user questions
  else {
    if(user) pipeline.push({ $match: { askedBy: { $ne: user._id }, ...commonFilter } });
    else pipeline.push({ $match: { ...commonFilter } });
  }

  if (onlyAnswered) {
    pipeline.push(answerCountLookup, addTotalAnswers, { $match: { totalAnswers: { $gt: 0 } } });
  }

  let sortObj = { $sort: { createdAt: -1 } };
  if (useTextSort) {
    sortObj = { $sort: { score: -1 } };
  } else if (sort == "oldest") {
    sortObj = { $sort: { createdAt: 1 } };
  } else if (sort == "popularity") {
    sortObj = { $sort: { totalAnswers: -1 } };
  } else if (sort == "leastPopularity") {
    sortObj = { $sort: { totalAnswers: 1 } };
  }

  pipeline.push({
    $facet: {
      metadata: [{ $count: "Total" }],
      data: [
        // if not filtering answered earlier, do the lookup and addFields
        ...(onlyAnswered ? [] : [answerCountLookup, addTotalAnswers]),
        sortObj,
        { $skip: skip },
        { $limit: pageSize },
        ...userLookup,
        latestAnswerLookup,
        unwindLatest,
        ...(onlyAnswered ? [] : [{ $project: { answerDocs: 0 } }]),
      ],
    },
  });
  pipeline.push({
    $project: {
      totalCount: { $arrayElemAt: ["$metadata.Total", 0] },
      data: 1,
    },
  });

  const [result = {}] = await questionModel.aggregate(pipeline);
  const totalCount = result.totalCount || 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    page: pageNo,
    pageSize,
    totalPages,
    totalCount,
    data: result.data || [],
  };
}

const getUserQuestions = async (user, page, pageSize, searchTerm) => {
  return fetchQuestions({ user, askedBy: user._id, page, pageSize, onlyAnswered: false, searchTerm });
}
  

const getAllQuestions = async (userId, page, pageSize, categoryId, searchTerm, sort) => {
  let user = null;
  if( userId ) user = await userModel.findById(userId);
  return fetchQuestions({ user, page, pageSize, onlyAnswered: false, categoryId, searchTerm, sort });
}
 
const getUserQuestionsWithAnswers = (user, page, pageSize, searchTerm) =>
  fetchQuestions({ user, askedBy: user._id, page, pageSize, onlyAnswered: true, searchTerm });

const userAnswers = async (user, page = 1, pageSize = 10, searchTerm) => {
  page = Number(page);
  pageSize = Number(pageSize);

  const pageNo = Math.max(1, parseInt(page, 10) || 1);
  const skip = (pageNo - 1) * pageSize;
  let searchPipeline = [];
  if (searchTerm) searchPipeline.push({ $match: { $text: { $search: searchTerm } } });
  const result = await questionModel.aggregate([
    ...searchPipeline,
    {
      $lookup: {
        from: "answers",
        let: { qId: "$_id" },
        pipeline: [{ $match: { $expr: { $eq: ["$questionId", "$$qId"] }, answeredBy: user._id } }],
        as: "userAnswerDocs",
      },
    },
    {
      $unwind: {
        path: "$userAnswerDocs",
        preserveNullAndEmptyArrays: false,
      },
    },
    {
      $facet: {
        totalCount: [{ $count: "totalCount" }],
        answers: [{ $sort: { "userAnswerDocs.createdAt": -1 } }, { $skip: skip }, { $limit: pageSize }],
      },
    },
  ]);

  const totalCount = result[0].totalCount.length > 0 ? result[0].totalCount[0].totalCount : 0;
  const totalPages = Math.ceil(totalCount / pageSize);
  return {
    totalCount,
    totalPages,
    pageSize: pageSize,
    page: page,
    answers: result[0].answers,
  };
};

const retrieveQuestionAndResponses = async (quesId) => {
  const result = await questionModel.aggregate([
    {
      $match: {
        _id: new mongoose.Types.ObjectId(quesId),
      },
    },
    {
      $lookup: {
        from: "users",
        let: { askedBy: "$askedBy" },
        pipeline: [
          {
            $match: { $expr: { $eq: ["$_id", "$$askedBy"] } },
          },
          { $project: { firstName: 1, lastName: 1, profileImage: 1 } },
        ],
        as: "askedBy",
      },
    },
    {
      $unwind: "$askedBy",
    },
    {
      $lookup: {
        from: "answers",
        let: { id: "$_id" },
        pipeline: [
          {
            $match: {
              $expr: { $eq: ["$questionId", "$$id"] },
            },
          },
          {
            $lookup: {
              from: "users",
              let: { answeredBy: "$answeredBy" },
              pipeline: [
                {
                  $match: { $expr: { $eq: ["$_id", "$$answeredBy"] } },
                },
                { $project: { firstName: 1, lastName: 1, profileImage: 1 } },
              ],
              as: "answeredBy",
            },
          },
          {
            $unwind: "$answeredBy",
          },
        ],
        as: "answers",
      },
    },
  ]);
  if (result.length > 0) {
    return { question: result[0] };
  } else {
    throw new BadRequestError("Question does not exist");
  }
};

const removeQuestion = async (user, quesId) => {
  const deletedQuestion = await questionModel.findOneAndDelete({
    _id: quesId,
    askedBy: user._id,
  });
  if (!deletedQuestion) {
    throw new BadRequestError("Question not found or not yours");
  }
  await answerModel.deleteMany({ questionId: quesId });
  return {
    deletedQuestion,
  };
};

const removeAnswer = async (user, ansId) => {
  const deletedAns = await answerModel.findOneAndDelete({ _id: ansId, answeredBy: user._id });
  if (!deletedAns) {
    throw new BadRequestError("Answer not found or not yours");
  }
  return {
    deletedAns,
  };
};

module.exports = {
  postQuestion,
  responseToQuestion,
  getUserQuestions,
  getAllQuestions,
  getUserQuestionsWithAnswers,
  userAnswers,
  removeQuestion,
  removeAnswer,
  retrieveQuestionAndResponses,
  editAnswer
};
