const { BadRequestError } = require("../common/customErrors");
const faqModel = require("../tables/schema/faq");
const roleModel = require("../tables/schema/roles")

const addFaq = async (adminUser, body) => {
  const faq = new faqModel({
    ...body,
    createdBy: adminUser._id,
  });

  const savedFaq = await faq.save();
  return {
    savedFaq,
  };
};

const filterFaqs = async (user, filters = {}, page = 1, pageSize = 10) => {
  const { searchTerm, tags, category } = filters;
  const query = {};
  const adminRole = await roleModel.findOne( { roleName : "admin"})
  console.log("user", user);
  if (user.roleId.toString() != adminRole._id.toString()) {
    query.isActive = true;
  }
  if (searchTerm) {
    query.$text = { $search: searchTerm };
  }
  if (Array.isArray(tags) && tags.length) {
    query.tags = { $in: tags };
  }
  if (category) {
    query.category = category;
  }

  const skip = (page - 1) * pageSize;
  const projection = {};
  const sort = {};
  if (searchTerm) {
    projection.score = { $meta: "textScore" };
    sort.score = { $meta: "textScore" };
  } else {
    sort.order = 1;
    sort.createdAt = -1;
  }

  const totalCount = await faqModel.countDocuments(query);
  const data = await faqModel.find(query, projection).sort(sort).skip(skip).limit(pageSize);

  const totalPages = Math.ceil(totalCount / pageSize);
  return { page: Number( page ), pageSize: Number(pageSize), totalPages, totalCount, data };
};

const updateFaq = async (id, body) => {
  const faq = await faqModel.findById(id);
  if (!faq) {
    throw new BadRequestError("FAQ not found");
  }
  const updatedFaq = await faqModel.findByIdAndUpdate(id, { $set: body }, { new : true, runValidators : true });
  return {
    updatedFaq,
  };
};

const deleteFaq = async ( id) => {
  const deleted = await faqModel.findByIdAndDelete(id);
  if (!deleted) {
    throw new BadRequestError("FAQ not found");
  }
  return { deleted };
};

module.exports = {
  addFaq,
  filterFaqs,
  updateFaq,
  deleteFaq,
};
