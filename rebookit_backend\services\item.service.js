const dotenv = require("dotenv");
const {
  itemlistModel,
  bookItem,
  tutorItem,
  schoolItem,
  scholarshipAwardItem,
  eventItem,
  extracurricularActivityItem,
} = require("../tables/schema/itemList");
const { DateTime } = require("luxon");
const userModel = require("../tables/schema/user");
const bookmarkModel = require("../tables/schema/bookmark");
const { ItemListStatusEnum, PlanStatusEnum } = require("../common/Enums");
const { BadRequestError, InternalServerError, ForbiddenError } = require("../common/customErrors");
const categoryModel = require("../tables/schema/category");
const subCategoryModel = require("../tables/schema/subCategory");
dotenv.config({});
const UserReview = require("../tables/schema/reviewSchema");
const { default: mongoose } = require("mongoose");
const subscriptionModel = require("../tables/schema/subscription");
const otpModel = require("../tables/schema/otp");
const subSubCategoryModel = require("../tables/schema/subSubCategory");
const subSubSubCategoryModel = require("../tables/schema/subSubSubCategory");
dotenv.config({});

const listItem = async (user, data) => {
  // verify otp first for listing
  // const foundOtp = await otpModel
  //   .findOne({
  //     email: user.email,
  //     codeType: "verification_on_listing",
  //     isUsed: true,
  //   })
  //   .sort({ createdAt: -1 });

  // if (!foundOtp) throw new BadRequestError("Either otp was not sent or it has been expired");

  // if (data.otp != foundOtp.code && data.otp != "111111") {
  //   throw new BadRequestError("Incorrect Otp");
  // }

  const foundCategory = await categoryModel.findById(data.categoryId);
  if (!foundCategory) {
    throw new BadRequestError("Invalid category Id");
  }

  if (
    (foundCategory.name == "Book" && data.kind != "BookItem") ||
    (foundCategory.name == "E-directory" &&
      data.kind != "SchoolItem" &&
      data.kind != "TutorItem" &&
      data.kind != "ExtracurricularActivityItem") ||
    (foundCategory.name == "Scholarship & Awards" && data.kind != "ScholarshipAwardItem") ||
    (foundCategory.name == "Events" && data.kind != "EventItem")
  )
    throw new BadRequestError("Item type mistmatch with category");

  if (data.subCategoryId) {
    const foundSubCat = await subCategoryModel.findById(data.subCategoryId);
    if (!foundSubCat) {
      throw new BadRequestError("Invalid subcategory Id");
    }

    if (foundSubCat.categoryId.toString() != data.categoryId) {
      throw new BadRequestError("Sub category does not belong to this category");
    }
  }

  if (data.subSubCategoryId) {
    const foundSubSubCat = await subSubCategoryModel.findById(data.subSubCategoryId);

    if (!foundSubSubCat) throw new BadRequestError("Invalid subsub category id");

    if (foundSubSubCat.subCategoryId.toString() != data.subCategoryId) {
      throw new BadRequestError("Sub Sub category does not belong to this Sub category");
    }
  }

  if (data.subSubSubCategoryId) {
    const foundSubSubSubCat = await subSubSubCategoryModel.findById(data.subSubSubCategoryId);

    if (!foundSubSubSubCat) throw new BadRequestError("Invalid subsubsub category");

    if (foundSubSubSubCat.subSubCategoryId.toString() != data.subSubCategoryId) {
      throw new BadRequestError("Sub Sub Sub category does not belong to this Sub Sub category");
    }
  }

  const activeSubscription = await subscriptionModel
    .findOne({
      userId: user._id,
      status: PlanStatusEnum.ACTIVE,
    })
    .populate("planId");

  // Need to create free plan if there is no queued plan
  if (!activeSubscription) {
    throw new ForbiddenError("You don't have any active subscription plan");
  }

  if (!activeSubscription.remainingListings || activeSubscription.remainingListings.length < 1) {
    throw new ForbiddenError("Your plan does not include listings, please upgrade");
  }

  const foundCatInSubscriptionPlan = activeSubscription.remainingListings.find((o) => o.category.toString() == data.categoryId);
  if (!foundCatInSubscriptionPlan)
    throw new BadRequestError("You can't post items in " + foundCategory.name + " as, this does not includes in your plan");
  console.log("foundCatInSubscriptionPlan", foundCatInSubscriptionPlan);
  if (foundCatInSubscriptionPlan.noOfListing < 1) throw new BadRequestError("You've used all your quota");

  let selectedItemModel;
  switch (data.kind) {
    case "BookItem": {
      selectedItemModel = bookItem;
      break;
    }
    case "TutorItem": {
      selectedItemModel = tutorItem;
      break;
    }
    case "SchoolItem": {
      selectedItemModel = schoolItem;
      break;
    }
    case "ExtracurricularActivityItem": {
      selectedItemModel = extracurricularActivityItem;
      break;
    }
    case "EventItem": {
      selectedItemModel = eventItem;
      break;
    }
    case "ScholarshipAwardItem": {
      selectedItemModel = scholarshipAwardItem;
      break;
    }
    default: {
      throw new BadRequestError("Invalid item type");
    }
  }

  const plan = activeSubscription.planId;
  const listingCategory = plan.listings.find((o) => o.category.toString() == data.categoryId);

  const now = DateTime.now();
  const expireAt = now.plus({ days: listingCategory.listingValidityDays });

  data.createdBy = user._id;
  data.updatedBy = user._id;
  data.expireAt = expireAt;

  const item = await selectedItemModel.create(data);

  await userModel.findByIdAndUpdate(
    user._id,
    {
      $push: { allListingItems: item._id },
    },
    { new: true }
  );

  return { item };
};

const getAutoCompleteItems = async (page, pageSize = 10, searchTerm) => {
  const pageNo = Number(page) || 1;
  pageSize = Number(pageSize);
  const skip = (pageNo - 1) * pageSize;
  let query = {};

  if (searchTerm) {
    query = {
      $or: [
        { title: { $regex: searchTerm, $options: "i" } },
        { description: { $regex: searchTerm, $options: "i" } },
        { authors: { $regex: searchTerm, $options: "i" } },
      ],
    };
  }

  const result = await itemlistModel.aggregate([
    {
      $match: {
        ...query,
        isActive: true,
      },
    },
    {
      $facet: {
        count: [
          {
            $count: "count",
          },
        ],

        data: [
          {
            $skip: skip,
          },
          {
            $limit: pageSize,
          },
          {
            $project: {
              title: 1,
              description: 1,
              images: 1,
              condition: 1,
              price: 1,
            },
          },
        ],
      },
    },
  ]);

  const totalCount = result[0].count.length > 0 ? result[0].count[0].count : 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    page: pageNo,
    pageSize,
    totalPages,
    totalCount,
    data: result[0].data || [],
  };
};

const searchItems = async (user, payload, page, pageSize = 10, userId) => {
  const pageNo = Number(page) || 1;
  pageSize = Number(pageSize);
  const skip = (pageNo - 1) * pageSize;

  const filters = payload.filters;
  let sortQuery = user ? { createdAt: -1 } : { boostedAt: -1 };

  if (payload.sort && Object.keys(payload.sort).length > 0) {
    sortQuery = payload.sort;
  }

  let query = {};

  const earthRadiusKm = 6378.1;

  if (filters.coordinates) {
    const radiusInRadians = (filters.maxDistanceInKm ? filters.maxDistanceInKm : 100) / earthRadiusKm;
    query["address.geometry.location"] = {
      $geoWithin: {
        $centerSphere: [payload.filters.coordinates, radiusInRadians],
      },
    };
  } 
  // else if (userId) {
  //   const foundUser = await userModel.findById(userId);
  //   if (!foundUser) throw new BadRequestError("Invalid userId");
  //   if (foundUser.location) {
  //     const radiusInRadians = 100 / earthRadiusKm;
  //     query["address.geometry.location"] = {
  //       $geoWithin: {
  //         $centerSphere: [user.location.coordinates, radiusInRadians],
  //       },
  //     };
  //   }
  // }

  if (filters.keyword) {
    query = {
      $text: {
        $search: filters.keyword,
      },
    };
  }

  if (filters.minPrice != null || filters.maxPrice != null) {
    query.price = {};

    if (filters.minPrice != null) {
      query.price.$gte = filters.minPrice;
    }
    if (filters.maxPrice != null) {
      query.price.$lte = filters.maxPrice;
    }
  }

  if (filters.category?.length > 0) {
    query.categoryId = {
      $in: filters.category.map((cat) => new mongoose.Types.ObjectId(cat)),
    };
  }

  if (filters.subCategory?.length > 0) {
    query.subCategoryId = {
      $in: filters.subCategory.map((subCat) => new mongoose.Types.ObjectId(subCat)),
    };
  }

  if (filters.subSubCategory?.length > 0) {
    query.subSubCategoryId = {
      $in: filters.subSubCategory.map((subSubCat) => new mongoose.Types.ObjectId(subSubCat)),
    };
  }

  if (filters.subSubSubCategory?.length > 0) {
    query.subSubSubCategoryId = {
      $in: filters.subSubSubCategory.map((subSubSubCat) => new mongoose.Types.ObjectId(subSubSubCat)),
    };
  }

  if (filters.authors?.length > 0) {
    query.authors = {
      $in: filters.authors,
    };
  }

  if (filters.parish) {
    query["address.parish"] = {
      $in: filters.parish,
    };
  }

  let matchQuery;
  if (user?.roleId?.roleName == "admin") matchQuery = query;
  else if (user) matchQuery = { ...query, createdBy: user._id, status: { $ne: ItemListStatusEnum.DELETED } };
  else matchQuery = { ...query, isActive: true };

  const result = await itemlistModel.aggregate([
    {
      $match: matchQuery,
    },
    {
      $facet: {
        count: [
          {
            $count: "count",
          },
        ],

        data: [
          {
            $sort: sortQuery,
          },
          {
            $skip: skip,
          },
          {
            $limit: pageSize,
          },
          {
            $lookup: {
              from: "categories",
              let: { categoryId: "$categoryId" },
              pipeline: [
                { $match: { $expr: { $eq: ["$_id", "$$categoryId"] } } },
                {
                  $project: {
                    name: 1,
                  },
                },
              ],

              as: "categoryDoc",
            },
          },
          { $unwind: { path: "$categoryDoc", preserveNullAndEmptyArrays: true } },

          {
            $lookup: {
              from: "subcategories",
              let: { subCategoryId: "$subCategoryId" },
              pipeline: [
                { $match: { $expr: { $eq: ["$_id", "$$subCategoryId"] } } },
                {
                  $project: {
                    name: 1,
                  },
                },
              ],

              as: "subcategoryDoc",
            },
          },
          { $unwind: { path: "$subcategoryDoc", preserveNullAndEmptyArrays: true } },

          {
            $lookup: {
              from: "subsubcategories",
              let: { subSubCategoryId: "$subSubCategoryId" },
              pipeline: [
                { $match: { $expr: { $eq: ["$_id", "$$subSubCategoryId"] } } },
                {
                  $project: {
                    name: 1,
                  },
                },
              ],

              as: "subsubcategoryDoc",
            },
          },
          { $unwind: { path: "$subsubcategoryDoc", preserveNullAndEmptyArrays: true } },

          {
            $lookup: {
              from: "subsubsubcategories",
              let: { subSubSubCategoryId: "$subSubSubCategoryId" },
              pipeline: [
                { $match: { $expr: { $eq: ["$_id", "$$subSubSubCategoryId"] } } },
                {
                  $project: {
                    name: 1,
                  },
                },
              ],

              as: "subsubsubcategoryDoc",
            },
          },
          { $unwind: { path: "$subsubsubcategoryDoc", preserveNullAndEmptyArrays: true } },

          {
            $lookup: {
              from: "users",
              let: { createdBy: "$createdBy" },
              pipeline: [
                { $match: { $expr: { $eq: ["$_id", "$$createdBy"] } } },
                {
                  $project: {
                    firstName: 1,
                    lastName: 1,
                    email: 1,
                  },
                },
              ],

              as: "createdByDoc",
            },
          },
          {
            $unwind: {
              path: "$createdByDoc",
              preserveNullAndEmptyArrays: true, // IT WAS SET TO FALSE, I CHANGED IT INTO TRUE TO KEEP THE DATA
            },
          },
          {
            $lookup: {
              from: "userreviews",
              let: { createdBy: "$createdBy" },
              pipeline: [
                { $match: { $expr: { $eq: ["$user", "$$createdBy"] } } },
                { $unwind: "$reviews" },
                {
                  $sort: { "reviews.createdAt": -1 },
                },
                { $limit: 1 },

                {
                  $project: {
                    averageRating: 1,
                    totalReviews: 1,
                    reviews: 1,
                  },
                },
              ],
              as: "reviewDoc",
            },
          },
          ...(userId
            ? [
                {
                  $lookup: {
                    from: "bookmarks",
                    let: { itemId: "$_id" },
                    pipeline: [
                      {
                        $match: {
                          $expr: {
                            $and: [
                              { $eq: ["$itemId", "$$itemId"] },
                              {
                                $eq: ["$bookmarkedBy", new mongoose.Types.ObjectId(userId)],
                              },
                            ],
                          },
                        },
                      },
                      { $limit: 1 },
                    ],
                    as: "bookmarkDoc",
                  },
                },
                {
                  $addFields: {
                    hasBookmarked: { $gt: [{ $size: "$bookmarkDoc" }, 0] },
                  },
                },
              ]
            : []),
        ],
      },
    },
  ]);

  const totalCount = result[0].count.length > 0 ? result[0].count[0].count : 0;
  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    page: pageNo,
    pageSize,
    totalPages,
    totalCount,
    data: result[0].data || [],
  };
};

const updateItem = async (user, itemId, updateItemData) => {
  // Find the item with necessary fields including type and sub-schemas
  const item = await itemlistModel.findById(itemId).select("createdBy status");

  if (!item) throw new BadRequestError("Item not found");
  if (item.createdBy.toString() !== user._id.toString()) {
    throw new BadRequestError("Can't update, item does not belong to you");
  }

  if (item.status === ItemListStatusEnum.ADMIN_DELETED) {
    throw new BadRequestError("Can't update or delete, item has been removed by admin");
  }

  // Determine item type based on fields present
  switch (updateItemData.kind) {
    case "BookItem": {
      selectedItemModel = bookItem;
      break;
    }
    case "TutorItem": {
      selectedItemModel = tutorItem;
      break;
    }
    case "SchoolItem": {
      selectedItemModel = schoolItem;
      break;
    }
    case "ExtracurricularActivityItem": {
      selectedItemModel = extracurricularActivityItem;
      break;
    }
    case "EventItem": {
      selectedItemModel = eventItem;
      break;
    }
    case "ScholarshipAwardItem": {
      selectedItemModel = scholarshipAwardItem;
      break;
    }
    default: {
      throw new BadRequestError("Invalid item type");
    }
  }

  // Handle status changes
  if (selectedItemModel === bookItem && updateItemData.status === ItemListStatusEnum.MARKED_AS_SOLD) {
    if (item.status !== ItemListStatusEnum.ACCEPTED) {
      throw new BadRequestError("You can only mark as sold when listed item is approved");
    }
    updateItemData.isActive = false;
  }

  if (updateItemData.status === "deleted") {
    updateItemData.isActive = false;
  }

  // after updating it should go in pending state automatically for approval
  if (!updateItemData.status) {
    updateItemData.status = ItemListStatusEnum.PENDING;
    updateItemData.isActive = false;
  }

  const updatedItem = await selectedItemModel.findByIdAndUpdate(
    itemId,
    { $set: updateItemData },
    { new: true, runValidators: true }
  );

  if (!updatedItem) throw new InternalServerError("Something went wrong");

  return { updatedItem };
};

const boostItem = async (user, itemId) => {
  const foundItem = await itemlistModel.findById(itemId);

  if (!foundItem) throw new BadRequestError("Invalid item");
  if (foundItem.createdBy.toString() != user._id.toString()) throw new BadRequestError("This item does not belong to you");
  if (!foundItem.isActive) throw new BadRequestError("Can not boost items, which are not active");

  const foundSubscription = await subscriptionModel.findOne({
    status: PlanStatusEnum.ACTIVE,
    userId: user._id,
  });

  if (!foundSubscription) {
    throw new BadRequestError("You don't have active subscription plan, please wait for few hours and try again");
  }

  if (!("remainingBoosts" in foundSubscription)) {
    throw new BadRequestError("Your plan does not include boosts, please upgrade");
  }

  if (foundSubscription.remainingBoosts === 0) {
    throw new BadRequestError("You've used all your boosts, please renew your subscription");
  }
  await subscriptionModel.findOneAndUpdate(
    {
      status: PlanStatusEnum.ACTIVE,
      userId: user._id,
    },
    {
      $inc: { remainingBoosts: -1 },
    }
  );

  const boostedItem = await itemlistModel.findByIdAndUpdate(
    itemId,
    {
      $set: {
        boostedAt: new Date(),
      },
    },
    {
      new: true,
      runValidators: true,
    }
  );

  return {
    boostedItem,
  };
};

const getListedItemById = async (itemId, userId) => {
  const item = await itemlistModel
    .findById(itemId)
    .populate("createdBy", "_id firstName lastName email mobileNumber profileImage location")
    .populate("categoryId")
    .populate("subCategoryId")
    .populate("subSubCategoryId")
    .populate("subSubSubCategoryId")
    .lean();

  if (!item) throw new BadRequestError("Item not found");
  if (item.status == ItemListStatusEnum.DELETED) throw new BadRequestError("This item has been deleted");
  if (userId) {
    const bookmark = await bookmarkModel.findOne({
      itemId,
      bookmarkedBy: userId,
    });
    if (bookmark) item.bookmarkDoc = bookmark;
    item.isBookmarked = !!bookmark;
  }

  return item;
};

const bookmarkAnItem = async (user, data) => {
  const foundItem = await itemlistModel.findById(data.itemId);
  if (!foundItem) throw new BadRequestError("Invalid item Id");

  let foundBook_inBookMarked = await bookmarkModel.findOne({ bookmarkedBy: user._id, itemId: data.itemId }, {});

  if (foundBook_inBookMarked) throw new BadRequestError("Item Already Added");
  const createdBookmark = await bookmarkModel.create({
    bookmarkedBy: user._id,
    ...data,
  });
  if (!createdBookmark) {
    throw new InternalServerError("bookmark could not be created");
  }
  return createdBookmark;
};

const listOfBookmarks = async (userId) => {
  console.log("userId", userId);
  return await bookmarkModel.find({ bookmarkedBy: userId }).populate({
    path: "itemId",
    populate: {
      path: "createdBy",
      select: "firstName lastName profileImage",
    },
    select: "title address price images createdBy",
  });
};

const bookmarkRemoved = async (userId, bookmarkId) => {
  return await bookmarkModel.findOneAndDelete({
    _id: bookmarkId,
    bookmarkedBy: userId,
  });
};

const addReview_service = async (user, body) => {
  const review = {
    reviewer: user._id,
    rating: body.rating,
    comment: body.comment,
  };

  let userAlreadyExist = await UserReview.findOne({
    user: body.userId,
  });

  if (!userAlreadyExist) {
    userAlreadyExist = new UserReview({
      user: body.userId,
      reviews: [review],
    });
  } else {
    let alreadyReviewd = userAlreadyExist.reviews.find((item) => item.reviewer.toString() == review.reviewer.toString());
    if (alreadyReviewd) {
      throw new BadRequestError("You have already reviewed This user");
    }
    userAlreadyExist.reviews.push(review);
  }

  let savedData = await userAlreadyExist.save();

  return {
    message: "Review Saved Succesfully",
    status: true,
    data: savedData,
  };
};

const getUserReviews = async (userId) => {
  const userReview = await UserReview.findOne({ user: userId }).populate("reviews.reviewer", "firstName email profileImage");
  if (!userReview) throw new BadRequestError("No reviews found for this user");
  return userReview;
};

const getNearbyTutors = async (userId) => {
  const data = await itemlistModel.find({
    $geoNear: {
      near: {
        type: "Point",
        coordinates: [-76.8099041, 18.0178743], // your [lon, lat]
      },
      distanceField: "distanceInMeters", // the field MongoDB will attach
      maxDistance: 10_000, // 10 km in meters
      spherical: true,
    },
  });
  const count = await itemlistModel.countDocuments();
  return {
    count,
    data,
  };
};

module.exports = {
  listItem,
  searchItems,
  updateItem,
  bookmarkAnItem,
  listOfBookmarks,
  bookmarkRemoved,
  getListedItemById,
  addReview_service,
  getUserReviews,
  getAutoCompleteItems,
  boostItem,

  getNearbyTutors,
};
