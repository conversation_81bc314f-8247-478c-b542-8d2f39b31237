const { BadRequestError, InternalServerError } = require("../common/customErrors");
const categoryModel = require("../tables/schema/category");
const { itemlistModel } = require("../tables/schema/itemList");
const subCategoryModel = require("../tables/schema/subCategory");
const subSubCategoryModel = require("../tables/schema/subSubCategory");
const subSubSubCategoryModel = require("../tables/schema/subSubSubCategory");

const addCategory = async (user, data) => {
  const createdCategory = await categoryModel.create(data);

  if (!createdCategory) throw new BadRequestError("Category could not be created.");

  return createdCategory;
};

const addSubCategory = async (user, data) => {
  const foundCategory = await categoryModel.findById(data.categoryId);
  if (!foundCategory) throw new BadRequestError("Invalid category id");

  const createdSubCategory = await subCategoryModel.create(data);
  if (!createdSubCategory) throw new BadRequestError("Subcategory could not be created");
  return createdSubCategory;
};

// CHANGE SUBJECT TO SUB-SUB-CATEGORY IN ADD
const addSubSubCategory = async (user, data) => {
  const foundSubCategory = await subCategoryModel.findById(data.subCategoryId);
  if (!foundSubCategory) {
    throw new BadRequestError("Invalid subcategory id");
  }

  const createdSubSubCategory = await subSubCategoryModel.create(data);
  if (!createdSubSubCategory) {
    throw new BadRequestError("sub-sub-category could not be created");
  }
  return createdSubSubCategory;
};

// ADD SUB-SUB-SUB-CATEGORY
const addSubSubSubCategory = async (user, data) => {
  const foundSubSubCategory = await subSubCategoryModel.findById(data.subSubCategoryId);
  if (!foundSubSubCategory) {
    throw new BadRequestError("Invalid subSubCategory id");
  }

  const createdSubSubSubCategory = await subSubSubCategoryModel.create(data);
  if (!createdSubSubSubCategory) {
    throw new BadRequestError("sub-sub-sub-category could not be created");
  }
  return createdSubSubSubCategory;
};

const getCategories = async () => {
  const KIND_MAP = {
    Book: "BookItem",
    Events: "EventItem",
    "Scholarship & Awards": "ScholarshipAwardItem",
  };

  const categories = await categoryModel.find().lean();

  const categoriesWithKind = categories.map((cat) => ({
    ...cat,
    kind: KIND_MAP[cat.name] || null,
  }));

  return {
    count: categories.length,
    categories: categoriesWithKind,
  };
};

const getSubCategories = async (categoryId) => {
  const foundCategory = await categoryModel.findById(categoryId);
  if (!foundCategory) throw new BadRequestError("Invalid category id");
  const KIND_MAP = {
    Schools: "SchoolItem",
    Tutors: "TutorItem",
    "Extracurricular Clubs & Activities": "ExtracurricularActivityItem",
  };
  const subCategories = await subCategoryModel.find({ categoryId }).lean();

  const subCategoriesWithKind = subCategories.map((cat) => ({
    ...cat,
    kind: KIND_MAP[cat.name] || null,
  }));
  return {
    count: subCategories.length,
    subCategories: subCategoriesWithKind,
  };
};

// CHANGE SUBJECT TO SUB-SUB-CATEGORY IN GET
const getSubSubCategories = async (subCategoryId) => {
  const foundSubCategory = await subCategoryModel.findById(subCategoryId);
  if (!foundSubCategory) throw new BadRequestError("Invalid subcategory id");

  const subSubCategories = await subSubCategoryModel.find({ subCategoryId });
  return {
    count: subSubCategories.length,
    subSubCategories,
  };
};

// GET SUB-SUB-SUB-CATEGORY
const getSubSubSubCategories = async (subSubCategoryId) => {
  const foundSubSubCategory = await subSubCategoryModel.findById(subSubCategoryId);
  if (!foundSubSubCategory) throw new BadRequestError("Invalid subsubcategory id");

  const subSubSubCategories = await subSubSubCategoryModel.find({ subSubCategoryId });
  return {
    count: subSubSubCategories.length,
    subSubSubCategories,
  };
};

const editCategory = async (id, body) => {
  const foundCategory = await categoryModel.findById(id);
  if (!foundCategory) throw new BadRequestError("Category not found");

  const updatedCategory = await categoryModel.findByIdAndUpdate(id, { $set: body }, { new: true, runValidators: true });

  if (!updatedCategory) throw new InternalServerError("Something went wrong while updating category");
  return { updatedCategory };
};

const editSubCategory = async (id, body) => {
  const foundSubCategory = await subCategoryModel.findById(id);
  if (!foundSubCategory) throw new BadRequestError("SubCategory not found");

  if (body.categoryId) {
    const foundCategory = await categoryModel.findById(body.categoryId);
    if (!foundCategory) throw new BadRequestError("Invalid Category Id");
  }

  const updatedSubCategory = await subCategoryModel.findByIdAndUpdate(id, { $set: body }, { new: true, runValidators: true });

  if (!updatedSubCategory) throw new InternalServerError("Something went wrong while updating subcategory");
  return { updatedSubCategory };
};

const editSubSubCategory = async (id, body) => {
  const found = await subSubCategoryModel.findById(id);
  if (!found) throw new BadRequestError("SubSubCategory not found");

  if (body.subCategoryId) {
    const subCat = await subCategoryModel.findById(body.subCategoryId);
    if (!subCat) throw new BadRequestError("Invalid SubCategory Id");
  }

  const updated = await subSubCategoryModel.findByIdAndUpdate(id, { $set: body }, { new: true, runValidators: true });

  if (!updated) throw new InternalServerError("Something went wrong while updating sub-sub-category");
  return { updatedSubSubCategory: updated };
};

const editSubSubSubCategory = async (id, body) => {
  const found = await subSubSubCategoryModel.findById(id);
  if (!found) throw new BadRequestError("SubSubSubCategory not found");

  if (body.subSubCategoryId) {
    const subSubCat = await subSubCategoryModel.findById(body.subSubCategoryId);
    if (!subSubCat) throw new BadRequestError("Invalid SubSubCategory Id");
  }

  const updated = await subSubSubCategoryModel.findByIdAndUpdate(id, { $set: body }, { new: true, runValidators: true });

  if (!updated) throw new InternalServerError("Something went wrong while updating sub-sub-sub-category");
  return { updatedSubSubSubCategory: updated };
};

const deleteCategory = async (id) => {
  const found = await categoryModel.findById(id);
  if (!found) throw new BadRequestError("Category not found");

  const hasSubCategories = await subCategoryModel.exists({ categoryId: id });
  if (hasSubCategories) throw new BadRequestError("Cannot delete category: subcategories exist");

  const itemRef = await itemlistModel.exists({ categoryId: id });
  if (itemRef) throw new BadRequestError("Cannot delete category: items reference this category");

  const deleted = await categoryModel.findByIdAndDelete(id);
  if (!deleted) throw new InternalServerError("Something went wrong while deleting category");
  return { deletedCategory: deleted };
};

const deleteSubCategory = async (id) => {
  const found = await subCategoryModel.findById(id);
  if (!found) throw new BadRequestError("SubCategory not found");

  const hasSubSub = await subSubCategoryModel.exists({ subCategoryId: id });
  if (hasSubSub) throw new BadRequestError("Cannot delete subcategory: sub-sub-categories exist");

  const itemRef = await itemlistModel.exists({ subCategoryId: id });
  if (itemRef) throw new BadRequestError("Cannot delete subcategory: items reference this subcategory");

  const deleted = await subCategoryModel.findByIdAndDelete(id);
  if (!deleted) throw new InternalServerError("Something went wrong while deleting subcategory");
  return { deletedSubCategory: deleted };
};

const deleteSubSubCategory = async (id) => {
  const found = await subSubCategoryModel.findById(id);
  if (!found) throw new BadRequestError("SubSubCategory not found");

  const hasSubSubSub = await subSubSubCategoryModel.exists({ subSubCategoryId: id });
  if (hasSubSubSub) throw new BadRequestError("Cannot delete sub-sub-category: sub-sub-sub-categories exist");

  const itemRef = await itemlistModel.exists({ subSubCategoryId: id });
  if (itemRef) throw new BadRequestError("Cannot delete sub-sub-category: items reference this sub-sub-category");

  const deleted = await subSubCategoryModel.findByIdAndDelete(id);
  if (!deleted) throw new InternalServerError("Something went wrong while deleting sub-sub-category");
  return { deletedSubSubCategory: deleted };
};

const deleteSubSubSubCategory = async (id) => {
  const found = await subSubSubCategoryModel.findById(id);
  if (!found) throw new BadRequestError("SubSubSubCategory not found");

  const itemRef = await itemlistModel.exists({ subSubSubCategoryId: id });
  if (itemRef) throw new BadRequestError("Cannot delete sub-sub-sub-category: items reference this sub-sub-sub-category");

  const deleted = await subSubSubCategoryModel.findByIdAndDelete(id);
  if (!deleted) throw new InternalServerError("Something went wrong while deleting sub-sub-sub-category");
  return { deletedSubSubSubCategory: deleted };
};

module.exports = {
  addCategory,
  addSubCategory,
  addSubSubCategory,
  addSubSubSubCategory,

  getCategories,
  getSubCategories,
  getSubSubCategories,
  getSubSubSubCategories,

  editCategory,
  editSubCategory,
  editSubSubCategory,
  editSubSubSubCategory,

  deleteCategory,
  deleteSubCategory,
  deleteSubSubCategory,
  deleteSubSubSubCategory,
};
