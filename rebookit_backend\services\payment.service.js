const { BadRequestError } = require("../common/customErrors");
const { PaymentStatusEnum, PlanMonthsTypeEnum, PlanStatusEnum, PlanTypeEnum } = require("../common/Enums");
const statusCodes = require("../common/statusCodes");
const { sendData } = require("../queue/configuration");
const { queueName, NotificationEventName } = require("../queue/queueNames");
const paymentModel = require("../tables/schema/paymentDetails");
const subscriptionModel = require("../tables/schema/subscription");
const subscriptionPlanModel = require("../tables/schema/subscription_plans");
const userModel = require("../tables/schema/user");
const moment = require("moment-timezone");

const stripe = require("stripe")(process.env.STRIPE_API_KEY);

const createPaymentIntent = async (user, body) => {
  const foundPlan = await subscriptionPlanModel.findById(body.planId);
  if (!foundPlan) {
    throw new BadRequestError("Invalid plan id");
  }

  const existingPlan = await subscriptionModel.findOne({ userId: user._id, status: PlanStatusEnum.ACTIVE }).populate("planId");
  if (existingPlan && existingPlan.planId.price > foundPlan.price) {
    const foundQueuedPlan = await subscriptionModel.findOne({
      userId: user._id,
      status: PlanStatusEnum.QUEUED,
    });
    if (foundQueuedPlan)
      throw new BadRequestError("You can not purchase another downgraded plan, as you do have one queued downgraded plan.");
  }

  const transaction = await paymentModel.create({
    userId: user._id,
    planId: foundPlan._id,
    amount: foundPlan.price,
  });

  const amountInCents = foundPlan.price * 100;

  let paymentIntent;
  try {
    paymentIntent = await stripe.paymentIntents.create({
      amount: amountInCents,
      currency: "jmd",
      automatic_payment_methods: { enabled: true },
      metadata: {
        transactionId: transaction._id.toString()
      },
    });
  } catch (err) {
    await paymentModel.findByIdAndDelete(transaction._id);
    console.log(err)
    throw new Error("Failed to create payment intent");
  }

  transaction.paymentIntent = paymentIntent.id;
  await transaction.save();

  return {
    paymentIntent,
    transaction,
  };
};

const handleWebhook = async (headers, body) => {
  const sig = headers["stripe-signature"];
  const webhookSecret = process.env.STRIPE_WEBHOOK_SECRET;
  console.log("req.headers new ", headers);
  console.log("req.body new ", body);

  let event;

  try {
    event = stripe.webhooks.constructEvent(body, sig, webhookSecret);
  } catch (err) {
    console.error("webhook signature verification failed:", err.message);
    throw new BadRequestError(err.message);
  }

  console.log("received Stripe Event:", event.type);

  try {
    // console.log("body", body)
    // const data = body;
    const paymentIntent = event.data.object;
    const { transactionId } =  paymentIntent.metadata;
    const transaction = await paymentModel.findById(transactionId).populate("planId").populate("userId");
    switch (event.type) {
      case "payment_intent.succeeded": {

        let planStatus = null;
        const monthCount = calculateMonthCount(transaction.planId.planMonths);
        let startDate = moment().toDate();
        let endDate = moment().add(monthCount, "M").toDate();

        const existingPlan = await subscriptionModel
          .findOne({
            userId: transaction.userId._id,
            status: PlanStatusEnum.ACTIVE,
          })
          .populate("planId");
        
        if (existingPlan?.planId.planType === PlanTypeEnum.PAID) {
          const existingPlanMonthCount = calculateMonthCount(existingPlan.planId.planMonths);
          // comparison in monthly price
          const oldPrice = existingPlan.planId.price / existingPlanMonthCount;
          const newPrice = transaction.planId.price / monthCount;
          if (newPrice >= oldPrice) {
            // immediately upgrade in case upgraded plan or user trying to same plan again
            planStatus = PlanStatusEnum.ACTIVE;
          } else if (newPrice < oldPrice) {
            planStatus = PlanStatusEnum.QUEUED;
            // in case of plan downgrade start new plan only after existingPlan
            startDate = moment(existingPlan.endDate).add(1, "second").toDate();
            endDate = moment(startDate).add(monthCount, "M").toDate();
          }
        } else {
          planStatus = PlanStatusEnum.ACTIVE;
        }

        if (planStatus != PlanStatusEnum.QUEUED) {
          //vanish all active or queued plans in case of plan upgrade
          await subscriptionModel.findOneAndUpdate(
            {
              userId: transaction.userId._id,
              status: {
                $in: [PlanStatusEnum.ACTIVE, PlanStatusEnum.QUEUED],
              },
            },
            {
              $set: {
                endDate: new Date(),
                status: PlanStatusEnum.UPGRADED,
              },
            }
          );
        }

        let features = {};
        if(transaction.planId.listings) features.remainingListings = transaction.planId.listings
       if(transaction.planId.boosts) features.remainingBoosts = transaction.planId.boosts;

        const notificationsTypes = JSON.parse(process.env.SUBSCRIPTION_NOTIFICATIONS);
        const notifications = [];
        for (const notificationDays of notificationsTypes) {
          const sendAtDate = moment(endDate).subtract(notificationDays, "minutes").toDate();
          const newNotification = {
            sendAt: sendAtDate,
            dayBefore: Number(notificationDays),
          };
          notifications.push(newNotification);
        }
        await subscriptionModel.create({
          userId: transaction.userId._id,
          planId: transaction.planId,
          startDate: startDate,
          endDate: endDate,
          status: planStatus,
          paymentId: transactionId,
          ...features,
          notifications,
        });

        await paymentModel.findByIdAndUpdate(transactionId, {
          $set: {
            status: PaymentStatusEnum.SUCCESS,
          },
        });
        sendData(queueName.NOTIFICATION_QUEUE, {
          EventName: NotificationEventName.SUBSCRIPTON_PURCHASE_SUCCESSFULL,
          data: {
            to: transaction.userId.email,
            userName: transaction.userId.firstName,
            planName: transaction.planId.planName,
            amount: transaction.amount,
            transactionId: transaction._id,
          },
        });
        console.log("payment success");
        break;
      }

      case "payment_intent.payment_failed": {
        await paymentModel.findByIdAndUpdate(transactionId, {
          $set: {
            status: PaymentStatusEnum.FAILED,
          },
        });
        sendData(queueName.NOTIFICATION_QUEUE, {
          EventName: NotificationEventName.SUBSCRIPTON_PURCHASE_FAILED,
          data: {
            to: transaction.userId.email,
            userName: transaction.userId.firstName,
            planName: transaction.planId.planName,
            amount: transaction.amount,
            transactionId: transaction._id,
          },
        });
        break;
      }
      default:
        console.log(`Unhandled event type: ${event.type}`);
    }

    return { received: true };
  } catch (err) {
    console.error("❌ Error processing webhook:", err);
    throw new BadRequestError(err.message);
  }
};

const calculateMonthCount = (planType) => {
  console.log("plan Type", planType);
  let months = null;
  switch (planType) {
    case PlanMonthsTypeEnum.MONTHLY:
      months = 1;
      break;
    case PlanMonthsTypeEnum.QUARTERLY:
      currentPlanMonths = 3;
      break;
    case PlanMonthsTypeEnum.HALF_YEARLY:
      months = 6;
      break;
    case PlanMonthsTypeEnum.YEARLY:
      months = 12;
      break;
    default:
      throw new Error("Invalid plan type");
  }
  return months;
};

const getPaymentHistory = async (user) => {
  // fetch only paid plans
  const subscriptions = await subscriptionModel
    .find({ userId: user._id, paymentId: { $exists: true } })
    .sort({ createdAt: -1 })
    .populate({
      path: "paymentId",
      select: {
        amount: 1,
        currency: 1,
        status: 1,
      },
    })
    .populate({
      path: "planId",
      select: {
        planName: 1,
        price: 1,
      },
    });
  return {
    count: subscriptions.length,
    subscriptions,
  };
};

const queryTranscations = async (query, page = 1, pageSize = 10) => {

  const pageNo = Number(page) || 1;
  pageSize = Number(pageSize);
  const skip = (pageNo - 1) * pageSize;

  let sort = { createdAt: -1 };
  if (query.sortBy) {
    sort = {
      [query.sortBy]: query.order === 'desc' ? -1 : 1
    };
  }

  let matchQuery = {};
  if (query.fromDate) {
    matchQuery.createdAt = { $gte: new Date(query.fromDate) };
  }
  if (query.toDate) {
    matchQuery.createdAt = {
      ...matchQuery.createdAt,
      $lte: new Date(query.toDate)
    };
  }


  if (query.status) {
    matchQuery.status = query.status
  }

  if (query.minAmt || query.maxAmt) {
    matchQuery.amount = {};
    if (query.minAmt) matchQuery.amount.$gte = Number(query.minAmt);
    if (query.maxAmt) matchQuery.amount.$lte = Number(query.maxAmt);
  }

  const data = await paymentModel.find(matchQuery).sort(sort).skip(skip).limit(pageSize).populate('userId', 'firstName lastName email mobileNumber profileImage');
  const totalCount = await paymentModel.countDocuments(matchQuery);

  const totalPages = Math.ceil(totalCount / pageSize);

  return {
    page: pageNo,
    pageSize,
    totalPages,
    totalCount,
    data
  };



}
module.exports = {
  createPaymentIntent,
  handleWebhook,
  getPaymentHistory,
  calculateMonthCount,
  queryTranscations
};
