const { sendData } = require("../queue/configuration");
const { queueName, NotificationEventName, ElasticsearchEventName } = require("../queue/queueNames");
const moment = require("moment-timezone");
const bcrypt = require("bcrypt");

const otpModel = require("../tables/schema/otp");
const roleModel = require("../tables/schema/roles");
const userModel = require("../tables/schema/user");
const UserReviewModel = require("../tables/schema/reviewSchema");

const { generateOTP, generateToken } = require("../utils/utils");
const { Schema } = require("mongoose");
const { BadRequestError, InternalServerError } = require("../common/customErrors");
const subscriptionPlanModel = require("../tables/schema/subscription_plans");
const { calculateMonthCount } = require("./payment.service");
const subscriptionModel = require("../tables/schema/subscription");
const { PlanStatusEnum, PlanTypeEnum, UserStatusEnum } = require("../common/Enums");
const itemlistModel = require("../tables/schema/itemList");
const Conversation = require("../tables/schema/conversation");
const testimonialModel = require("../tables/schema/testimonial");
const { sendOtpEmailVerification, sendSupportMail } = require("../utils/mail");
const supportMailModel = require("../tables/schema/support");

const UserInfo = async (userId) => {
  return await userModel.findById(userId).populate("allListingItems");
};

const SendUserOtp = async ({ email, mobileNumber, codeType }) => {
  if (codeType == "verification") {
    // If user exists, delete and start fresh
    const existingUser = await userModel.findOne({ email, isEmailVerified: true, isProfileCompleted: true });
    if (existingUser) throw new BadRequestError("Duplicate email");
  }

  if (codeType == "verification_on_listing") {
    const existingUser = await userModel.findOne({ email, isEmailVerified: true, isProfileCompleted: true });
    if (!existingUser) {
      throw new BadRequestError("Invalid user of listing verification");
    }
  }

  const foundOTP = await otpModel.find({ email, codeType, isUsed: false }).sort({ createdAt: -1 });
  if (foundOTP.length >= 1) {
    if (foundOTP.length >= 3) {
      throw new BadRequestError("Sending otp limit exceeded, please try again after 15 mins");
    }
    const latestOTP = foundOTP[0];
    const diffInMs = Date.now() - latestOTP.createdAt.getTime();
    if (diffInMs < 60 * 1000) {
      throw new BadRequestError("Please wait for 1 min before resending otp");
    }
  }

  let otp = generateOTP();

  let otpPayload = { code: otp };
  if (email) otpPayload.email = email;
  otpPayload.codeType = codeType;

  let otpTable = new otpModel(otpPayload);
  await otpTable.save();

  const { messageId } = await sendOtpEmailVerification({ to: email, Otp: otp });

  return {
    success: true,
    messageId,
    message: "Otp is send to your registered Email/Mobile",
  };
};

const VerifyUserOTP = async ({ otp, email, codeType }) => {
  const foundOtp = await otpModel.find({ email, codeType, isUsed: false }).sort({ createdAt: -1 }).limit(1);
  if (foundOtp.length < 1) {
    throw new BadRequestError("Otp does not exist, please send otp first");
  }

  if (process.env.NODE_ENV === "production") {
    if (foundOtp[0].code != otp) {
      throw new BadRequestError("Incorrect otp");
    }
  } else {
    if (foundOtp[0].code != otp && otp != "111111") {
      throw new BadRequestError("Incorrect otp");
    }
  }

  await otpModel.findByIdAndUpdate(foundOtp[0]._id, { isUsed: true });
  let user = await userModel.findOne({ email });
  if (codeType == "verification") {
    if (user) await userModel.findOneAndUpdate({ email }, { $set: { isEmailVerified: true } });
    else {
      user = await userModel.create({ email, isEmailVerified: true, isProfileCompleted: false });
    }
  } else {
    user = await userModel.findOne({ email });
  }
  return {
    isOtpVerified: true,
    codeType,
    user: user,
  };
};

const UserRegistration = async (userData) => {
  // check if user already exists
  const userExists = await userModel.findOne({ email: userData.email });
  if (!userExists) throw new BadRequestError("Invalid email");
  if (!userExists.isEmailVerified) throw new BadRequestError("Email not verified");

  const { email } = userData;

  // CASE 1: CHECK FOR THE OTP, IF AVAILBALE WITH THE USER EMAIL THEN USER CAN REGISTER. IT WILL SOLVE THE ISSUE OF USING ANOTHERS EMAILS AFTER 15 MINUTES
  const foundOtp = await otpModel.find({ email, isUsed: true }).sort({ createdAt: -1 }).limit(1);
  if (foundOtp.length < 1) {
    throw new BadRequestError("Otp expired, please send otp first");
  }

  // CASE 2: IF THE TWO EMAIL IS VERIFIED BUT NOT CREATED YET BUT USER TRY TO SOMEONE ELSE'S USER EMAIL WHICH IS ALREADY CREATED THEN IT WILL THROW AN ERROR
  if (userExists.isProfileCompleted) {
    throw new BadRequestError("User already registered with this email");
  }

  const freeSubscriptionPlan = await subscriptionPlanModel.findOne({
    planType: PlanTypeEnum.FREE,
  });

  if (!freeSubscriptionPlan) throw new InternalServerError("Free Plan does not exist");

  // CASE 3 TOKEN FOR VERIFY RIGHT USER

  // get the role id
  const filterRole = await roleModel.findOne({ roleName: "user" });

  let salt = await bcrypt.genSalt(10);
  userData.password = await bcrypt.hash(userData.password, salt);

  const updatedUser = await userModel.findByIdAndUpdate(
    userExists._id,
    {
      $set: {
        ...userData,
        roleId: filterRole._id,
        loggedIn: new Date(),
        registeredAt: new Date(),
        isProfileCompleted: true,
        status: UserStatusEnum.ACTIVE,
      },
    },
    { new: true }
  );

  let token = generateToken(updatedUser._id);

  const monthCount = calculateMonthCount(freeSubscriptionPlan.planMonths);
  const endDate = moment().add(monthCount, "M").toDate();

  const newSubscription = {
    userId: updatedUser._id,
    planId: freeSubscriptionPlan._id,
    startDate: moment().toDate(),
    endDate: endDate,
    status: PlanStatusEnum.ACTIVE,
    remainingListings: freeSubscriptionPlan.listings,
    remainingBoosts: freeSubscriptionPlan.boosts,
  };

  const createdSubscription = await subscriptionModel.create(newSubscription);

  // call the notification service to send greeting on mail and phone through queue
  sendData(queueName["NOTIFICATION_QUEUE"], {
    EventName: NotificationEventName.USER_REGISTERED,
    data: { _id: updatedUser?._id },
  });

  return { user: updatedUser, token, subscription: createdSubscription._id };
};

const UserLogin = async ({ email, password }) => {
  let user = await userModel.findOne({ email }).select("+password");
  if (!user) throw new BadRequestError("Invalid User");
  if (!user.isEmailVerified) throw new BadRequestError("Invalid email");
  if (!user.isProfileCompleted) throw new BadRequestError("Invalid email");
  if (user.status === UserStatusEnum.SUSPENDED)
    throw new BadRequestError("Your account has been suspended, please contact admin");

  if (await user.compare(password)) {
    let token = generateToken(user._id);

    return { success: true, token, userData: user };
  } else {
    throw new BadRequestError("Password not match");
  }
};

const PasswordForgot = async ({ email }) => {
  // CHECK WITH THE EMAIL WHOSE ISPROFILE IS FALSE
  const existingUser = await userModel.findOne({ email, isProfileCompleted: true });
  if (!existingUser) throw new BadRequestError("User not exist, Please create account first");

  const foundOTP = await otpModel.find({ email, codeType: "forgot_pass", isUsed: false }).sort({ createdAt: -1 });
  if (foundOTP.length >= 1) {
    if (foundOTP.length >= 3) {
      throw new BadRequestError("Sending forgot password otp limit exceeded, please try again later");
    }
    const latestOTP = foundOTP[0];
    const diffInMs = Date.now() - latestOTP.createdAt.getTime();
    if (diffInMs < 60 * 1000) {
      throw new BadRequestError("Please wait for 1 min before resending otp");
    }
  }

  let otpNumber = generateOTP();
  let otpPayload = {
    email,
    code: otpNumber,
    codeType: "forgot_pass",
  };

  let otpTable = new otpModel(otpPayload);
  await otpTable.save();

  // call the notification service to send otp on mail and phone through queue
  await sendData(queueName["NOTIFICATION_QUEUE"], {
    EventName: NotificationEventName.SEND_OTP_FORGOT_PASS,
    data: { email, otpNumber },
  });

  return {
    success: true,
    message: "Otp is send to your registered Email",
  };
};

const verifyForgotPasswordOtp = async ({ email, otp }) => {
  const foundUser = await userModel.findOne({ email, isEmailVerified: true });
  if (!foundUser) throw new BadRequestError("Invalid email");

  const foundOtp = await otpModel.find({ email, codeType: "forgot_pass", isUsed: false }).sort({ createdAt: -1 });
  if (foundOtp.length < 1) throw new BadRequestError("Otp does not exist, please send otp first.");

  if (otp == foundOtp[0].code) {
    await otpModel.findByIdAndUpdate(foundOtp[0]._id, { $set: { isUsed: true } });
    return {
      success: true,
      isOtpVerified: true,
    };
  } else {
    throw new BadRequestError("Incorrect Otp");
  }
};

const resetPassword = async ({ email, otp, newPassword }) => {
  //currently one otp can be used multiple times for the same user, need to be fixed later
  const foundUser = await userModel.findOne({ email, isEmailVerified: true });
  if (!foundUser) throw new BadRequestError("Invalid email");

  const foundOtp = await otpModel.find({ email, codeType: "forgot_pass", isUsed: true }).sort({ createdAt: -1 });
  if (foundOtp.length < 1) throw new BadRequestError("Otp does not exist, please send otp first.");

  if (otp == foundOtp[0].code) {
    let salt = await bcrypt.genSalt(10);
    hashedPassword = await bcrypt.hash(newPassword, salt);

    const updatedUser = await userModel.findByIdAndUpdate(foundUser._id, { $set: { password: hashedPassword } }, { new: true });

    return { success: true, user: updatedUser };
  } else {
    throw new BadRequestError("Incorrect Otp");
  }
};

const UpdateProfile = async ({ user, body }) => {
  if (body.coordinates) {
    body.location = {
      type: "Point",
      coordinates: body.coordinates,
    };
  }

  const updatedProfile = await userModel
    .findByIdAndUpdate(
      user._id,
      {
        $set: body,
      },
      { new: true }
    )
    .lean();
  const { password, allListingItems, ...otherDetails } = updatedProfile;
  return { success: true, message: "Profile has been updated successfully", updatedProfile: otherDetails };
};

const VerifyUserData = async ({ user, otp, email, mobileNumber, verifyType }) => {
  let otpQuery = {
    $or: [],
    codeType: verifyType,
  };

  if (email) {
    otpQuery.$or.push({ code: otp, email });
  }

  if (mobileNumber) {
    otpQuery.$or.push({ code: otp, mobileNumber });
  }

  console.log("otp query", JSON.stringify(otpQuery));
  let otpTable = await otpModel.findOne(otpQuery);
  console.log("otpTable", otpTable);
  if (otpTable) {
    if (mobileNumber && userData.mobileNumber != mobileNumber) {
      throw new BadRequestError("Invalid mobile number");
    }
    if (email && userData.email != email) {
      throw new BadRequestError("Invalid email");
    }

    return { success: true, message: "Verified Successfully" };
  }

  throw new BadRequestError("Otp Not Found Or Incorrect");
};

const getPlans = async (user) => {
  const currentSubscription = await subscriptionModel
    .findOne({
      userId: user._id,
      status: PlanStatusEnum.ACTIVE,
    })
    .populate({
      path: "planId",
      populate: {
        path: "listings.category",
      },
    })
    .populate({
      path: "planId",
      populate: {
        path: "listings.category",
      },
    })
    .populate("remainingListings.category");
  const queuedPlanCount = await subscriptionModel.countDocuments({
    userId: user._id,
    status: PlanStatusEnum.QUEUED,
  });

  const plans = await subscriptionPlanModel
    .find({ active: true, planType: PlanTypeEnum.PAID })
    .populate("listings.category")
    .lean();
  plans.sort((a, b) => {
    const aMonthCount = calculateMonthCount(a.planMonths);
    const bMonthCount = calculateMonthCount(b.planMonths);
    const planAPrice = a.price / aMonthCount;
    const planBPrice = b.price / bMonthCount;
    return planAPrice - planBPrice;
  });

  const currentPlanIndex = plans.findIndex((plan) => plan._id.toString() == currentSubscription?.planId._id.toString());
  if (currentPlanIndex >= 0) {
    plans[currentPlanIndex].currentPlan = true;
  }

  return {
    currentSubscription,
    queuedPlanCount,
    plans,
  };
};

const RebookerOfTheMonth = async () => {
  const startOfMonth = new Date(new Date().getFullYear(), new Date().getMonth(), 1);
  const endOfMonth = new Date();

  const rankings = await userModel.aggregate([
    // MATCH ITEM OF THE USER
    {
      $lookup: {
        from: "itemlists",
        localField: "_id",
        foreignField: "createdBy",
        as: "items",
      },
    },
    // MATCH REVIEWS OF THE USER
    {
      $lookup: {
        from: "userreviews",
        localField: "_id",
        foreignField: "user",
        as: "reviews",
      },
    },
    // MATCH CONVERSATIONS IN WHICH USER IS PRESENT
    {
      $lookup: {
        from: "conversations",
        localField: "_id",
        foreignField: "participants",
        as: "conversations",
      },
    },

    // FILTER OUT THE FIELDS WHICH IS REQUIRED
    {
      $addFields: {
        itemCount: {
          $size: {
            $filter: {
              input: "$items",
              as: "item",
              cond: {
                $and: [{ $gte: ["$$item.publishedAt", startOfMonth] }, { $lt: ["$$item.publishedAt", endOfMonth] }],
              },
            },
          },
        },
        averageRating: {
          $ifNull: [{ $avg: "$reviews.averageRating" }, 0], // Handle null values explicitly
        },
        uniqueChats: {
          $size: {
            $reduce: {
              input: "$conversations",
              initialValue: [],
              in: {
                $setUnion: ["$$value", "$$this.participants"],
              },
            },
          },
        },
      },
    },

    // FILTER USERS WITH CURRENT MONTH ACTIVITY
    {
      $match: {
        $or: [{ itemCount: { $gt: 0 } }, { averageRating: { $gt: 0 } }, { uniqueChats: { $gt: 0 } }],
      },
    },

    // SORT RANKING
    {
      $sort: {
        itemCount: -1,
        averageRating: -1,
        uniqueChats: -1,
      },
    },

    // PROJECT FINAL OUTPUT
    {
      $project: {
        _id: 1,
        itemCount: 1,
        averageRating: 1,
        uniqueChats: 1,
      },
    },
  ]);

  return rankings;
};

const GetTestimonials = async () => {
  const testimonials = await testimonialModel.find({}).select("name title content image").sort({ createdAt: -1 }).limit(10);

  return testimonials;
};

const updateUser = async (user, id, body) => {
  const foundUser = await userModel.findById(id);
  if (!foundUser) throw new BadRequestError("User does not exist");
  const updatedUser = await userModel
    .findByIdAndUpdate(
      id,
      {
        $set: body,
      },
      { new: true, runValidators: true }
    )
    .lean();
  if (!updatedUser) throw new InternalServerError("User could not be updated");
  const { password, ...otherDetails } = updatedUser;
  return {
    updatedUser: otherDetails,
  };
};
const SupportRequest = async (body) => {
  let { email, name, subject, message } = body;
  let to = "<EMAIL>" || process.env.HELPDESK_EMAIL;
  let support = new supportMailModel({ content: message, subject: subject, name: name, email: email })
  let savedData = await support.save()
  sendSupportMail({ to, email, name, subject, message });
  return {
    message: "Thanks, For the feedback",
    data: savedData
  };
  // const foundUser = await userModel.findById(id);
  // if (!foundUser) throw new BadRequestError("User does not exist");
  // const updatedUser = await userModel
  //   .findByIdAndUpdate(
  //     id,
  //     {
  //       $set: body,
  //     },
  //     { new: true, runValidators: true }
  //   )
  //   .lean();
  // if (!updatedUser) throw new InternalServerError("User could not be updated");
  // const { password, ...otherDetails } = updatedUser;

  return {
    // updatedUser: otherDetails,
  };
};
const getAllSupportRequest = async (body, queryPayload) => {
  // console.log("params",param)
  let payload = {
    name: body.name,
    subject: body.subject,
    page: queryPayload.page || 1,
    pageSize: queryPayload.pageSize || 10
  }
  console.log("payload", payload)
  let query = {
    name: { $regex: body.name || "", $options: "i" },
    subject: { $regex: body.subject || "", $options: "i" }
  };
  console.log("(payload.page-1)*payload.pageSize", (payload.page - 1) * payload.pageSize)
  console.log("query", query)
  let supportData = await supportMailModel.find(query).skip((payload.page - 1) * payload.pageSize).limit(payload.pageSize)
  // console.log("supportData", supportData)
  let totalCount = await supportMailModel.countDocuments(query)
  return {
    data: supportData,
    page: payload.page,
    pageSize: payload.pageSize,
    totalCount: totalCount,
    totalPages: Math.ceil(totalCount / payload.pageSize) || 1
  }
}


const updateSupportRequest = async (body, query) => {
  let updatedResponse = await supportMailModel.findByIdAndUpdate(query.id, { status: body.status })
  console.log("updatedResponse", updatedResponse)
  return {
    message: "Updated Successfull"
  }
}

module.exports = {
  UserInfo,
  UserRegistration,
  UserLogin,
  SendUserOtp,
  VerifyUserOTP,
  VerifyUserData,
  PasswordForgot,
  verifyForgotPasswordOtp,
  resetPassword,
  UpdateProfile,
  getPlans,
  RebookerOfTheMonth,
  GetTestimonials,
  updateUser,
  SupportRequest,
  getAllSupportRequest,
  updateSupportRequest
};
