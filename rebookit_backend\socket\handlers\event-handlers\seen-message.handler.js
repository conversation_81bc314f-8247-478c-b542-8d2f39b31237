const { DB_NAME, TABLE_NAME } = require("../../../common/collectionName.js");
const { BadRequestError, InternalServerError } = require("../../../common/customErrors.js");
const { ONLINE_USERS_SET, redis } = require("../../../redis/configuration.js");
const { socketService } = require("../../../services/chat.service.js");
const Conversation = require("../../../tables/schema/conversation.js");
const Message = require("../../../tables/schema/messages.js");
const userModel = require("../../../tables/schema/user.js");

const messageSeenHandler = async function (socket, socketServer, { conv }) {
  const user = socket.user;
  console.log("users", user);

  const foundConversation = await Conversation.findById(conv);
  if (foundConversation) {
    if (!foundConversation.participants.find((userId) => userId.equals(user._id))) {
      throw new BadRequestError("You're not part of this conversation");
    }
    await Message.updateMany(
      {
        conversation: conv,
        to: user._id,
      },
      {
        $set: {
          isSeen: true,
        },
      }
    );
  } else {
    throw new BadRequestError("Invalid conversation");
  }

  const recipientId = foundConversation.participants.find((userId) => userId.toString() !== user._id.toString());

  if (!recipientId) {
    throw new InternalServerError("recipient id not found in conversation");
  }

  let recipient = await redis.hget(ONLINE_USERS_SET, recipientId);
  if (recipient) recipient = JSON.parse(recipient);
  console.log("recipient", recipient);

  if (recipient) {
    const foundRecipient = await userModel.findById(recipientId);

    let chatTypeFilters = {};
    if (foundRecipient.selectedChatFilters) {
      chatTypeFilters.chatType = foundRecipient.selectedChatFilters.chatType;
    }

    const recipientChats = await socketService.getAllChats(recipientId, {
      ...chatTypeFilters,
    });
    
    socketServer.to(recipient._id).emit("on-message-seen", {
      conversation: conv,
      chats: recipientChats,
    });
  }
};

module.exports = { messageSeenHandler };
