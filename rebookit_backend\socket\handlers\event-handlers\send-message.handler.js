const { ONLINE_USERS_SET, redis } = require("../../../redis/configuration.js");
const { socketService } = require("../../../services/chat.service.js");
const Conversation = require("../../../tables/schema/conversation.js");
const Message = require("../../../tables/schema/messages.js");
const mongoose = require("mongoose");
const userModel = require("../../../tables/schema/user.js");
const { itemlistModel } = require("../../../tables/schema/itemList.js");
const { BadRequestError } = require("../../../common/customErrors.js");
const { ItemListStatusEnum } = require("../../../common/Enums.js");

const sendMessageHandler = async function (socket, socketServer, { recipientId, message, sellerItemId }) {
  console.log("message sendMessageHandler", message, "recipientId", recipientId, "sellerItemId", sellerItemId);
  const senderId = socket.id;
  let recipient = await redis.hget(ONLINE_USERS_SET, recipientId);
  if (recipient) recipient = JSON.parse(recipient);

  const user = socket.user;
  console.log("users", user);
  const foundUser = await userModel.findById(recipientId);
  if (!foundUser) throw new BadRequestError("Invalid participant id")
  const foundItem = await itemlistModel.findById(sellerItemId, "status");
  if (!foundItem) throw new BadRequestError("Invalid item Id");
  if (foundItem.status == ItemListStatusEnum.MARKED_AS_SOLD) throw new BadRequestError("This Item has already been sold.");
  if (foundItem.status == ItemListStatusEnum.DELETED) throw new BadRequestError("This Item has been deleted.");
  if (foundItem.status == ItemListStatusEnum.ADMIN_DELETED) throw new BadRequestError("This Item has been removed by admin.");
  if (foundItem.status != ItemListStatusEnum.ACCEPTED) throw new BadRequestError("You can not chat on this item, as it is not approved")

  const updatedChat = await Conversation.findOneAndUpdate(
    {
      participants: {
        $all: [{ $elemMatch: { $eq: user._id } }, { $elemMatch: { $eq: new mongoose.Types.ObjectId(recipientId) } }],
      },
      sellerItemId,
    },
    {
      $setOnInsert: {
        participants: [user._id, new mongoose.Types.ObjectId(recipientId)],
        sellerItemId,
      },
    },
    { upsert: true, new: true }
  );
  console.log("updatedChat", updatedChat);
  const newMessage = new Message({
    conversation: updatedChat._id,
    ...message,
    to: recipientId,
    sender: user._id,
  });

  const savedMessage = await newMessage.save();
  console.log("savedMessage", savedMessage);
  await Conversation.findByIdAndUpdate(updatedChat._id, {
    $push: { messages: savedMessage._id },
    $pull: { deletedBy: recipientId },
  });

  let chatTypeFilters = {};
  if (user.selectedChatFilters) {
    chatTypeFilters.chatType = user.selectedChatFilters.chatType;
  }

  const senderChats = await socketService.getAllChats(user._id, {
    ...chatTypeFilters,
  });

  socketServer.to(senderId).emit("on-message", {
    message: savedMessage,
    chats: senderChats,
  });

  console.log("recipient", recipient);

  if (recipient) {
    const foundRecipient = await userModel.findById(recipientId);

    let chatTypeFilters = {};
    if (foundRecipient.selectedChatFilters) {
      chatTypeFilters.chatType = foundRecipient.selectedChatFilters.chatType;
    }

    const recipientChats = await socketService.getAllChats(recipientId, {
      ...chatTypeFilters,
    });

    console.log("recipient._id", recipient._id);

    socketServer.to(recipient._id).emit("on-message", {
      message: savedMessage,
      chats: recipientChats,
    });
  }
};

module.exports = { sendMessageHandler };
