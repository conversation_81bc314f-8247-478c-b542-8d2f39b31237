const { wrapAsync } = require("../../common/wrapAsync.socket.js");
const { messageSeenHandler } = require("./event-handlers/seen-message.handler.js");
const { sendMessageHandler } = require("./event-handlers/send-message.handler.js");

function HandleSocketRequest(socket, chatIO) {
  console.log("HandleSocketRequest function");

  wrapAsync(socket, "send-message", async (data) => {
    console.log("private msg", data.message.text);
    console.log("private msg to", data.recipientId);
    console.log("item id", data.sellerItemId);
    console.log("private msg from", socket.id);
    await sendMessage<PERSON>andler(socket, chatIO, data);
  });

  wrapAsync(socket, "message-seen", async (data) => {
    console.log("conversation Id", data.conv);
    await messageSeenHandler(socket, chatIO, data);
  });
}

module.exports = { HandleSocketRequest };
