const { Server } = require("socket.io");
const { setSocketServerInstance } = require("./socketStore.js");
const { socketService } = require("../services/chat.service.js");
const { verifySocketConnection } = require("../middleware/middleware.js");
const { HandleSocketRequest } = require("./handlers/socket-handler.js");
const { createAdapter } = require("@socket.io/redis-adapter");
const { Redis } = require("ioredis");

async function ConnectSocketServer(server) {
  console.log("Socket server connected");

  const pubClient = new Redis({ url: process.env.REDIS_HOST, port: process.env.REDIS_PORT });
  const subClient = pubClient.duplicate();

  const io = new Server(server, {
    adapter: createAdapter(pubClient, subClient),
    cors: {
      origin: "*",
      methods: ["GET", "POST", "PUT", "DELETE"],
      credentials: false,
    },
  });

  setSocketServerInstance(io);

  const ChatIo = io.of("/");

  ChatIo.use(verifySocketConnection);
  ChatIo.on("connection", async (socket) => {
    const onlineUsers = await socketService.registerUser(socket.id, socket.user);
    console.log("emitting register event");
    ChatIo.emit("onlineUser-list", onlineUsers);

    HandleSocketRequest(socket, ChatIo);

    socket.on("disconnect", async () => {
      console.log("user disconnected " + socket.user.id);
      const users = await socketService.unregisterUser(socket);
      ChatIo.emit("onlineUser-list", users);
    });
  });
}

module.exports = { ConnectSocketServer };
