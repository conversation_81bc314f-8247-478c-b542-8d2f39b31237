const mongoose = require('mongoose');
const { RebookerListTypeEnum } = require('../../common/Enums');

// Define the conversation schema
const rebookerListSchema = new mongoose.Schema(
    {

        userId: {
            type: mongoose.Schema.Types.ObjectId,
            ref: "user"
        },

        type: {
            type: String,
            enum: Object.values(RebookerListTypeEnum)
        },

        month: {
            type: Date,
            default: Date.now,
        },

        calculatedAt: {
            type: Date,
            default: Date.now
        }

    },
    {
        timestamps: true,
        versionKey: false
    }
);



const rebookerListModel = mongoose.model('rebookerlist', rebookerListSchema);


module.exports = rebookerListModel;
