const mongoose = require("mongoose");

const PaymentSchema = new mongoose.Schema(
  {
    userId: { 
        type: mongoose.Schema.Types.ObjectId,
        ref: "user",
        required: true 
    },
    adId: { 
        type: mongoose.Schema.Types.ObjectId, 
        ref: "Ad", 
        required: true 
    },
    amount: { 
        type: Number, 
        required: true 
    },
    currency: { 
        type: String, 
        default: "jusd" 
    },
    status: { 
        type: String, 
        enum: ["pending", "success", "failed"], 
        default: "pending" 
    },
    paymentIntent: { 
        type: String, 
        required: true 
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

const AdPaymentModel = mongoose.model("Payment", PaymentSchema);

module.exports = AdPaymentModel;
