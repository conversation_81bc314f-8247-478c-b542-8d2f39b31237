const mongoose = require("mongoose");
const userModel = require("./user");

const answerSchema = new mongoose.Schema(
    {

        questionId: {
            type: mongoose.Schema.Types.ObjectId,
            ref:'question',
            required: true,
            index: true
        },

        answerText: {
            type: String,
            required: true
        },

        answeredBy: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'user',
            index: true
        },

    },
    {
        timestamps: true,
        versionKey: false
    }
)

const answerModel = mongoose.model("answer", answerSchema);

module.exports = answerModel;