const mongoose = require("mongoose");

const bookmarkSchema = new mongoose.Schema(
    {

        itemId: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
            ref: "itemlists"
        },

        bookmarkedBy: {
            type: mongoose.Schema.Types.ObjectId,
            required: true,
            ref: "user"
        },


    },
    {
        timestamps: true,
        versionKey: false
    }
)

const bookmarkModel = mongoose.model("bookmark", bookmarkSchema);

module.exports = bookmarkModel;