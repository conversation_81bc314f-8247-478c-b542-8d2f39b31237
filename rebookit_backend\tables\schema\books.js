const mongoose = require("mongoose");

const bookSchema = new mongoose.Schema(
  {
    isbn: { type: String, index: true, sparse: true },
    title: { type: String, required: true, index: true },
    author_name: [{ type: String, required: true, index: true }],
    publisher: {
      name: { type: String, index: true },
      imprint: String,
      location: String,
      date: { type: Date, index: true },
      edition: String,
    },
    physicalAttributes: {
      format: {
        type: String,
        index: true,
        enum: ["hardcover", "paperback", "ebook", "audiobook", "spiral", "other", "ebook and print"],
      },
      pageCount: { type: Number },
      dimensions: {
        height: Number,
        width: Number,
        thickness: Number,
        unit: { type: String, enum: ["cm", "in"] },
      },
      weight: {
        value: Number,
        unit: { type: String, enum: ["g", "oz", "lb", "kg"] },
      },
    },
    content: {
      description: String,
      tableOfContents: [String],
      languages: [{ type: String }],
      genres: [{ type: String }],
      subjects: [{ type: String }],
      keywords: [{ type: String }],
      ageRange: {
        min: Number,
        max: Number,
      },
    },

    series: {
      name: { type: String },
      position: { type: Number },
      total: Number,
    },

    media: {
      coverImage: String,
      images: [String],
      samplePages: [String],
      previewLink: String,
      audioSample: String,
    },
    cover_i: String,
    pricing: {
      amount: { type: Number },
      currency: { type: String, enum: ["USD", "EUR", "GBP", "INR", "JPY"] },
      discount: Number,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

bookSchema.index({
  title: "text",
  "content.keywords": "text",
  "content.subjects": "text",
  authors: "text",
});

const bookModel = mongoose.model("books", bookSchema);

module.exports = bookModel;
