const mongoose = require("mongoose");
// const { DirectoryTypeEnum } = require("../../common/Enums");

const categorySchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      unique: true,
    },
    // directoryType: {
    //   type: String,
    //   enum: DirectoryTypeEnum,
    //   required: true,
    // },
    image: {
      type: String,
      // required: true
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

const categoryModel = mongoose.model("category", categorySchema);

module.exports = categoryModel;
