const mongoose = require("mongoose");

// Define the conversation schema
const conversationSchema = new mongoose.Schema(
  {
    participants: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "user",
        required: true,
      },
    ],
    messages: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "message",
      },
    ],
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "user",
    },

    deletedBy: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "user",
      },
    ],

    status: {
      type: String,
      enum: ["active", "archived", "deleted"],
      default: "active",
    },

    type: {
      type: String,
      enum: ["single", "group"],
      default: "single",
    },
    sellerItemId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "itemlists",
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

const Conversation = mongoose.model("Conversation", conversationSchema);

module.exports = Conversation;
