const mongoose = require("mongoose");
const { Schema, model, Types } = mongoose;

const faqSchema = new Schema(
  {
    question: {
      type: String,
      required: [true, "FAQ question is required"],
      trim: true,
      minlength: [5, "Question must be at least 5 characters"],
      maxlength: [200, "Question cannot exceed 200 characters"],
      index: true,
    },
    answer: {
      type: String,
      required: [true, "FAQ answer is required"],
      trim: true,
      minlength: [5, "Answer must be at least 5 characters"],
    },
    category: {
      type: String,
      enum: ["General", "Membership", "Billing", "Technical", "Other"],
      default: "General",
      index: true,
    },
    order: {
      // used for custom ordering of Faq in UI
      type: Number,
      default: 0,
    },
    tags: {
      type: [String],
      default: [],
      index: true,
    },
    createdBy: {
      type: Types.ObjectId,
      ref: "user",
      required: true,
    },
    isActive: {
      type: Boolean,
      default: true,
      index: true,
    },
  },
  {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
    versionKey: false
  }
);

faqSchema.index({ question: "text", answer: "text", tags: "text" });

faqSchema.pre("save", function (next) {
  if (this.order < 0) {
    this.order = 0;
  }
  next();
});

const Faq = model("Faq", faqSchema);
module.exports = Faq;
