const mongoose = require("mongoose");
const { Schema } = mongoose;
const {
  ItemListStatusEnum,
  BookConditionEnum,
  SchoolTypeEnum,
  ActivityTypeEnum,
  FrequencyEnum,
  EventModeEnum,
  QualificationTypeEnum,
  TargetClassTypeEnum,
  ExperienceTypeEnum,
  ClassesOffered,
  ParishesListEnum,
  UnitEnum,
} = require("../../common/Enums");

const options = { versionKey: false, timestamps: true };

// Define embedded address schemas (without timestamps or _id)
const EmbeddedGeometrySchema = new Schema(
  {
    location: {
      type: {
        type: String,
        enum: ["Point"],
        default: "Point",
      },
      coordinates: {
        type: [Number],
        required: true,
        validate: {
          // Keep existing validation
          validator: function (value) {
            return value.length === 2;
          },
          message: "Coordinates must be an array of two numbers [longitude, latitude].",
        },
      },
    },
  },
  { _id: false }
);

const EmbeddedAddressSchema = new Schema(
  {
    formatted_address: {
      type: String,
      required: true,
    },
    geometry: {
      type: EmbeddedGeometrySchema,
      required: true,
    },
    country: { type: String, required: true },
    country_code: String,
    postal_code: { type: String },
    parish: { type: String, enum: Object.values(ParishesListEnum), required: true },
    administrative_area_level_1: { type: String }, // State/Region (e.g., California) OR  indicates a first-order civil entity below the country level.
    administrative_area_level_2: String, // indicates a second-order civil entity below the country level.
    administrative_area_level_3: String, // indicates a third-order civil entity below the country level.
    administrative_area_level_4: String, // indicates a fourth-order civil entity below the country level.
    administrative_area_level_5: String, // indicates a fifth-order civil entity below the country level.
    administrative_area_level_6: String, // indicates a sixth-order civil entity below the country level.
    administrative_area_level_7: String, // indicates a seventh-order civil entity below the country level.
    locality: String, // city/town
    political: String, // Neighborhood name
    political_short: String, // Short form of the political name
    sublocality: String, // SUBLOCALITY indicates a first-order civil entity below a locality.
    sublocality_level_1: String, // indicates a first-order civil entity below a sublocality.
    sublocality_level_2: String, // indicates a second-order civil entity below a sublocality.
    sublocality_level_3: String, // indicates a third-order civil entity below a sublocality.
    sublocality_level_4: String, // indicates a fourth-order civil entity below a sublocality.
    sublocality_level_5: String, // indicates a fifth-order civil entity below a sublocality.
    colloquial_area: String, // Popular place name / informal region
    airport: String, // AIRPORT indicates an airport.
    bus_station: String, // BUS_STATION indicates a bus station.
    park: String, // PARK indicates a park.
    university: String, // UNIVERSITY indicates a university.
    intersection: String, // e.g., "Main St & 1st Ave"
    route: String, // street name
    street_number: String, // street number
    natural_feature: String, // Natural landmarks (rivers, peaks, etc.)
    premise: String, // Building or property name
    subpremise: String, // Unit, apartment, or suite number
  },
  { _id: false, versionKey: false }
);

const itemListSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
    },
    description: {
      type: String,
    },
    categoryId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "category",
      required: true,
    },
    subCategoryId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "subCategory",
    },
    subSubCategoryId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "subSubCategory",
    },
    subSubSubCategoryId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "subSubSubCategory",
    },
    address: {
      type: EmbeddedAddressSchema,
      required: true,
    },

    images: {
      type: [String],
    },
    tags: {
      type: [String],
    },
    price: {
      type: Number,
      // required: true,
    },
    unit:{
      type: String,
      enum: Object.values(UnitEnum)
    },
    status: {
      type: String,
      enum: Object.values(ItemListStatusEnum),
      default: ItemListStatusEnum.PENDING,
    },
    reason: {
      type: String,
    },
    publishedAt: {
      type: Date,
    },
    expireAt: {
      type: Date,
    },
    boostedAt: {
      type: Date,
      default: Date.now,
    },
    isActive: {
      type: Boolean,
      default: false,
    },
    createdBy: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "user",
    },
    updatedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "user",
    },
    verifiedBy: {
      type: Schema.Types.ObjectId,
      ref: "user",
    },
  },
  options
);

const bookSchema = new mongoose.Schema({
  isbn_number: {
    type: String,
  },
  authors: {
    type: [String],
    validate: {
      validator: (arr) => arr.length > 0,
      message: "At least one author is required.",
    },
  },
  condition: {
    type: String,
    enum: Object.values(BookConditionEnum),
    required: true,
  },
});

const tutorSchema = new mongoose.Schema({
  highestQualification: {
    type: String,
    enum: Object.values(QualificationTypeEnum),
    required: true,
  },
  targetClasses: {
    type: String,
    enum: Object.values(TargetClassTypeEnum),
    reuiered: true,
  },
  experience: {
    type: String,
    enum: Object.values(ExperienceTypeEnum),
    required: true,
  },
  website: {
    type: String,
  },
});

const schoolSchema = new mongoose.Schema({
  schoolType: {
    type: String,
    enum: Object.values(SchoolTypeEnum),
    required: true,
  },
  classesOffered: {
    type: String,
    enum: Object.values(ClassesOffered),
    required: true,
  },
  website: {
    type: String,
  },
});

const extraCurricularActivitySchema = new mongoose.Schema({
  activityType: {
    type: String,
    enum: Object.values(ActivityTypeEnum),
    required: true,
  },
  frequency: {
    type: String,
    enum: Object.values(FrequencyEnum),
    required: true,
  },
  targetStudents: {
    type: String,
    required: true,
  },
});

const eventSchema = new mongoose.Schema({
  eventStartDate: {
    type: Date,
    required: true,
  },
  eventEndDate: {
    type: Date,
    required: true,
  },
  eventMode: {
    type: String,
    enum: Object.values(EventModeEnum),
    required: true,
  },
  website: {
    type: String,
  },
});

const scholarshipAwardSchema = new mongoose.Schema({
  scholarshipType: { type: String, required: true },
  eligibilityCriteria: {
    type: String,
    required: true,
  },
  website: {
    type: String,
  },
});

itemListSchema.index({ title: "text", description: "text", tags: "text" });
itemListSchema.index({ "address.geometry.location": "2dsphere" });
itemListSchema.index({ status: 1, isActive: 1 });
itemListSchema.index({ price: 1 });

const itemlistModel = mongoose.model("itemlists", itemListSchema);
const bookItem = itemlistModel.discriminator("BookItem", bookSchema, options);
const tutorItem = itemlistModel.discriminator("TutorItem", tutorSchema, options);
const eventItem = itemlistModel.discriminator("EventItem", eventSchema, options);
const schoolItem = itemlistModel.discriminator("SchoolItem", schoolSchema, options);
const extracurricularActivityItem = itemlistModel.discriminator(
  "ExtracurricularActivityItem",
  extraCurricularActivitySchema,
  options
);
const scholarshipAwardItem = itemlistModel.discriminator("ScholarshipAwardItem", scholarshipAwardSchema, options);

module.exports = {
  itemlistModel,
  bookItem,
  tutorItem,
  schoolItem,
  extracurricularActivityItem,
  eventItem,
  scholarshipAwardItem,
};
