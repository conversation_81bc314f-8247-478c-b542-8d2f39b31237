const mongoose = require('mongoose');
// Define the conversation schema
const messageSchema = new mongoose.Schema(
  {
    conversation: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "conversations",
    },
    text: {
      type: String,
    },
    isMedia: {
      type: Boolean,
      default: false,
    },
    location: {
      type: {
        lat: { type: Number },
        lng: { type: Number }
      }
    },
    mediaType: {
      type: String,
      enum: ["audio", "video", "image", "location"],
    },
    mediaUrl: {
      type: String,
    },
    isSeen: {
      type: Boolean,
      default: false
    },
    deletedBy: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "user",
      },
    ],
    sender: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "user",
      required: true,
    },
    to: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "user",
      required: true,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

const Message = mongoose.model("message", messageSchema);

module.exports = Message;
