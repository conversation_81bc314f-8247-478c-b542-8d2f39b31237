const mongoose = require("mongoose");
const { Schema } = mongoose;
const dotenv = require("dotenv");

dotenv.config({});

// Define the conversation schema
const otpSchema = new mongoose.Schema(
  {
    code: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      lowercase: true,
      match: [
        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        "enter a valid email",
      ],
    },
    //we're not using mobile as of now, we kept it for future
    mobileNumber: {
      type: Number,
    },
    codeType: {
      type: String,
      enum: ["forgot_pass", "verification", "verification_on_listing", "verification_on_update"],
      required: true,
      default: "verification",
    },
    expireAt: {
      type: Date,
      default: () => new Date(Date.now() + 15 * 60 * 1000), // 15 minutes from now
      index: { expires: 0 }, // TTL index will expire at the exact time in expireAt
    },
    isUsed: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

otpSchema.path("email").required(function () {
  return !this.mobileNumber;
}, "Either email is required");

otpSchema.index({ email: 1, codeType: 1, isUsed: 1 });
otpSchema.index({ mobileNumber: 1, codeType: 1, isUsed: 1 });

const otpModel = mongoose.model("otp", otpSchema);

module.exports = otpModel;
