const mongoose = require("mongoose");
const { PaymentStatusEnum } = require("../../common/Enums");

const paymentSchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref:"user",
      required: true,

    },
    planId: {
      type: mongoose.Schema.Types.ObjectId,
      ref:"subscription_plans",
      required: true,
    },

    amount: {
      type: Number,
      required: true,
    },
    currency: {
      type: String,
      required: true,
      default: "jmd",
    },
    paymentIntent: {
      type: String
    },
    status: {
      type: String,
      required: true,
      default: "pending",
      enum: Object.values(PaymentStatusEnum),
      index: true
    },
  },
  {
    timestamps: true,
    versionKey: false,
    strict: false,
  }
);

const paymentModel = mongoose.model("payments", paymentSchema);

module.exports = paymentModel;
