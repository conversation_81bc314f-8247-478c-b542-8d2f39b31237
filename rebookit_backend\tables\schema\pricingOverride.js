const mongoose = require("mongoose");


const PricingOverrideSchema = new mongoose.Schema(
 {
   resourceId: {
     type: mongoose.Schema.Types.ObjectId,
     ref: "resource",
     required: true,
   },
   dates: [ //  to be ask
     {
       type: Date,
       required: true,
     },
   ],
   pricePerDay: {
     type: Number,
     required: true,
   },
 },
 {
   timestamps: true,
   versionKey: false,
 }
);


const PricingOverrideModel = mongoose.model("pricingoverride", PricingOverrideSchema);


module.exports = PricingOverrideModel;