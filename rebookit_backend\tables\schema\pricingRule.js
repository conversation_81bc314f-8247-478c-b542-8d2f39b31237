const mongoose = require("mongoose");


const PricingRuleSchema = new mongoose.Schema(
 {
   resourceId: {
     type: mongoose.Schema.Types.ObjectId,
     ref: "resource",
     required: true,
   },
   startDate: {
     type: Date,
     required: true,
   },
   endDate: {
     type: Date,
     required: true,
   },
   pricePerDay: {
     type: Number,
     required: true,
   },
   priority: {
     type: Number,
     required: true,
   },
 },
 {
   timestamps: true,
   versionKey: false,
 }
);


const PricingRuleModel = mongoose.model("pricingrule", PricingRuleSchema);


module.exports = PricingRuleModel;