const mongoose = require("mongoose");

const questionSchema = new mongoose.Schema(
  {
    title: {
      type: String,
      required: true,
    },

    askedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "user",
    },

    categoryId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "category",
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);
questionSchema.index( { title: "text" })
questionSchema.index( { askedBy : 1 }) 

const questionModel = mongoose.model("question", questionSchema);

module.exports = questionModel;
