const mongoose = require("mongoose");
const { AdResourceTypeEnum, AdPageTypeEnum, AdPositionTypeEnum } = require("../../common/Enums");

const PricingRuleSchema = new mongoose.Schema(
  {
    name: { type: String, required: true },
    price: { type: Number, required: true },
    priority: { type: Number },
    startDate: { type: Date, required: true },
    endDate: { type: Date, required: true },
    isActive: { type: Boolean, required: true, default: true },
    color: { type: String, required: true }
  },
  { timestamps: true }
);

const PricingOverrideSchema = new mongoose.Schema(
  {
    date: { type: Date, required: true },
    price: { type: Number, required: true },
  },
  { timestamps: true }
);

const ResourceSchema = new mongoose.Schema(
  {
    type: {
      type: String,
      enum: Object.values(AdResourceTypeEnum),
      required: true,
    },
    page: {
      type: String,
      enum: Object.values(AdPageTypeEnum),
      required: true,
    },
    position: {
      type: String,
      enum: Object.values(AdPositionTypeEnum),
      required: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    // basePrice: { type: Number, required: true },
    pricingRules: [PricingRuleSchema],
    overrideRules: [PricingOverrideSchema],
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

const ResourceModel = mongoose.model("resource", ResourceSchema);

module.exports = ResourceModel;
