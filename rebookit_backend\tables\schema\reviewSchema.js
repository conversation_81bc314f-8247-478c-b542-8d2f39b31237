const mongoose = require('mongoose');
const { Schema } = mongoose;

const ReviewSchema = new Schema({
    reviewer: {
        type: Schema.Types.ObjectId,
        ref: 'user',
        required: true
    },
    rating: {
        type: Number,
        required: true,
        min: 1,
        max: 5
    },
    comment: {
        type: String,
        trim: true
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
});


const UserReviewSchema = new Schema({
    user: {
        type: Schema.Types.ObjectId,
        ref: 'user',
        required: true,
        unique: true
    },
    reviews: [ReviewSchema],
    averageRating: {
        type: Number,
        default: 0
    },
    totalReviews: {
        type: Number,
        default: 0
    }
}, {
    timestamps: true,
    versionKey: false
});


UserReviewSchema.pre('save', function (next) {
    if (this.reviews.length > 0) {
        this.totalReviews = this.reviews.length;
        this.averageRating = this.reviews.reduce((acc, curr) => acc + curr.rating, 0) / this.reviews.length;
    } else {
        this.totalReviews = 0;
        this.averageRating = 0;
    }
    next();
});



const UserReview = mongoose.model('UserReview', UserReviewSchema);


module.exports = UserReview