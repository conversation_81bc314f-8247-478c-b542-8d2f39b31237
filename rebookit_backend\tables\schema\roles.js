const mongoose = require("mongoose");
const { Schema } = mongoose;
const dotenv = require("dotenv");
const { RoleEnum } = require("../../common/Enums");

// Define the role schema
const roleSchema = new Schema(
  {
    roleName: {
      type: String,
      required: true,
      unique: true,
      enum: Object.values(RoleEnum),
    },

    description: {
      type: String,
      default: "",
      trim: true,
    },

    permissions: {
      canRead: {
        type: Boolean,
        default: false,
      },
      canWrite: {
        type: Boolean,
        default: false,
      },
      canDelete: {
        type: Boolean,
        default: false,
      },
    },
  },
  {
    timestamps: true,
    versionKey: false
  }
);

// Create the model
const roleModel = mongoose.model("roles", roleSchema);

module.exports = roleModel;
