const mongoose = require("mongoose");

const subSubCategorySchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    image: {
      type: String,
      //   required: true,
    },
    subCategoryId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "subCategory",
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

const subSubCategoryModel = mongoose.model("subSubCategory", subSubCategorySchema);

module.exports = subSubCategoryModel;
