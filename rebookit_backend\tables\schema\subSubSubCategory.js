const mongoose = require("mongoose");

const subSubSubCategorySchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    image: {
      type: String,
      //   required: true,
    },
    subSubCategoryId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: "subSubCategory",
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

const subSubSubCategoryModel = mongoose.model("subSubSubCategory", subSubSubCategorySchema);

module.exports = subSubSubCategoryModel;
