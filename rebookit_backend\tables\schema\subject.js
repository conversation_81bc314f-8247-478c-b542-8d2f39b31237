const mongoose = require("mongoose");
const subCategoryModel = require("./subCategory");

const subjectSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
    },
    image: {
      type: String,
    },
    subCategoryId: {
      type: mongoose.Schema.Types.ObjectId,
      required: true,
      ref: 'subCategory',
    },
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

const subjectModel = mongoose.model("subject", subjectSchema);

module.exports = subjectModel;
