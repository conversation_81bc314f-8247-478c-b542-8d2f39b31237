const mongoose = require("mongoose");
const { PlanStatusEnum, NotificationTypesEnum } = require("../../common/Enums");
const { Schema } = mongoose;

const listingSchema = new mongoose.Schema({
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "category",
    required: true,
  },
  noOfListing: {
    type: Number,
    required: true,
  },
});

const notificationSchema = new mongoose.Schema({
  notificationType: {
    type: String,
    enum: NotificationTypesEnum,
    default: NotificationTypesEnum.EMAIL,
  },
  sendAt: Date,
  dayBefore: Number,
  isSent: {
    type: Boolean,
    default: false,
  },
});
const subscriptionSchema = new mongoose.Schema(
  {
    userId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "user",
    },

    startDate: {
      type: Date,
      required: true,
      index: true,
    },

    endDate: {
      type: Date,
      required: true,
      index: true,
    },
    status: {
      type: String,
      enum: Object.values(PlanStatusEnum),
      index: true,
    },
    paymentId: {
      type: Schema.Types.ObjectId,
      ref: "payments",
    },
    planId: {
      type: Schema.Types.ObjectId,
      required: true,
      ref: "subscription_plans",
    },
    remainingListings: [listingSchema],
    remainingBoosts: {
      type: Number
    },
    notifications: [notificationSchema],
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

const subscriptionModel = mongoose.model("subscriptions", subscriptionSchema);

module.exports = subscriptionModel;
