const mongoose = require("mongoose");
const { PlanTypeEnum } = require("../../common/Enums");

const listingSchema = new mongoose.Schema({
  category: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'category'
  },
  noOfListing: {
    type: Number
  },
  listingValidityDays: Number,

});


const subscriptionPlanSchema = new mongoose.Schema(
  {
    planName: {
      type: String,
      required: true,
    },
    planMonths: {
      type: String,
      required: true,
    },

    planType: {
      type: String,
      enum: Object.values(PlanTypeEnum),
      default: PlanTypeEnum.FREE,
    },
    price: {
      type: Number,
      required: true,
    },
    boosts: {
      type: Number,
    },
    listings: [listingSchema],
    active: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true, versionKey: false }
);

const subscriptionPlanModel = mongoose.model("subscription_plans", subscriptionPlanSchema);

module.exports = subscriptionPlanModel;
