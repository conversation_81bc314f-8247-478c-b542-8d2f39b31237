const mongoose = require("mongoose");
const { Schema } = mongoose;
const supportMail = new Schema(
  {
    name: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      required: true,
    },
    content: {
      type: String,
      required: true,
    },
    subject: {
      type: String,
      required: true,
    },
    status:{
      type:Boolean,
      require:true,
      default:false
    }
      
  },
  { timestamps: true }
);

const supportMailModel = mongoose.model("supportMail", supportMail);
module.exports = supportMailModel;