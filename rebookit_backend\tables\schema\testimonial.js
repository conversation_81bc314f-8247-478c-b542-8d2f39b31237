const mongoose = require("mongoose");
const { Schema } = mongoose;
const testimonialSchema = new Schema(
  {
    name: {
      type: String,
      required: true,
    },
    title: {
      type: String,
      required: true,
    },
    content: {
      type: String,
      required: true,
    },
    image: {
      type: String,
      required: true,
    },
  },
  { timestamps: true }
);

const testimonialModel = mongoose.model("testimonials", testimonialSchema);
module.exports = testimonialModel;