const mongoose = require("mongoose");
const { Schema } = mongoose;
const dotenv = require("dotenv");
const bcrypt = require("bcrypt");
const { ChatTypeEnum, UserStatusEnum } = require("../../common/Enums");

dotenv.config({});

const chatFilterSchema = new mongoose.Schema({
  chatType: {
    type: String,
    enum: Object.values(ChatTypeEnum),
    default: "buyers",
  },
});

const userSchema = new mongoose.Schema(
  {
    firstName: {
      type: String,
    },

    lastName: {
      type: String,
    },

    email: {
      type: String,
      required: [true, "please enter valid email"],
      lowercase: true,
      unique: true,
      match: [
        /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
        "enter a valid email",
      ],
    },
    isEmailVerified: {
      type: Boolean,
      default: false,
    },
    isProfileCompleted: {
      type: Boolean,
      default: false,
    },
    mobileNumber: {
      type: String,
      unique: true,
      trim: true,
      sparse: true,
      validate: {
        validator: function (v) {
          //E.164 check: "+" then 6–15 digits
          return /^\+\d{6,15}$/.test(v);
        },
        message: (props) => `${props.value} is not a valid E.164 phone number!`,
      },
    },
    isdCode: {
      type: String,
    },
    password: {
      type: String,
    },

    profileImage: {
      type: String,
    },

    allListingItems: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "itemlists",
      },
    ],

    roleId: {
      type: Schema.Types.ObjectId,
      ref: "roles",
    },

    address: {
      type: String,
    },
    location: {
      type: {
        type: String,
        enum: ["Point"],
      },
      coordinates: {
        type: [Number], // [longitude, latitude]
        validate: {
          validator: function (value) {
            return value.length === 2;
          },
          message: "Coordinates must be an array of two numbers [longitude, latitude].",
        },
      },
    },
    aboutMe: {
      type: String,
    },
    registeredAt: {
      type: Date,
    },
    loggedIn: {
      type: Date,
    },
    status: {
      type: String,
      default: UserStatusEnum.PENDING,
      enum: Object.values(UserStatusEnum),
    },
    selectedChatFilters: chatFilterSchema,
  },
  {
    timestamps: true,
    versionKey: false,
  }
);

userSchema.index({ location: "2dsphere" });
userSchema.methods.compare = async function (password) {
  let bool = await bcrypt.compare(password, this.password);
  return bool;
};

const userModel = mongoose.model("user", userSchema);

module.exports = userModel;
