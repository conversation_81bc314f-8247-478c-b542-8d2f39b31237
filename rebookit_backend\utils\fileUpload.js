const { S3Client, PutObjectCommand } = require("@aws-sdk/client-s3");
const multer = require("multer");
const path = require("path");
const uuid = require("uuid");
const { BadRequestError } = require("../common/customErrors");

const s3 = new S3Client({
    region: process.env.S3_BUCKET_REGION.trim(),
    credentials: {
        accessKeyId: process.env.S3_ACCESS_KEY.trim(),
        secretAccessKey: process.env.S3_SECRET_KEY.trim(),
    }
});

const AWS_S3_BUCKET_NAME = process.env.BUCKET_NAME;

// Multer: store file in memory (RAM) instead of disk
const storage = multer.memoryStorage();

function checkFileType(req, file, cb) {
    const filetypes = /jpeg|jpg|png|webp|gif/;
    const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = filetypes.test(file.mimetype);

    if (mimetype && extname) {
        return cb(null, true);
    } else {
        cb(new BadRequestError('Error: Images only! (jpeg, jpg, png, webp, gif)'));
    }
}

const upload = multer({
    storage,
    limits: { fileSize: parseInt(process.env.FILE_SIZE) * 1024 * 1024 },
    fileFilter: checkFileType
});

// Upload a single file buffer to S3
const uploadFileToS3 = async (file) => {
    const fileExt = path.extname(file.originalname);
    const s3Key = `uploads/${uuid.v4()}${fileExt}`;

    const uploadParams = {
        Bucket: AWS_S3_BUCKET_NAME,
        Key: s3Key,
        Body: file.buffer,
        ContentType: file.mimetype
    };

    await s3.send(new PutObjectCommand(uploadParams));

    return { url: `${process.env.SERVER_BASE_URL}${s3Key}` };
};

// Upload multiple files buffer to S3
const uploadMultipleFileToS3 = async (files) => {
    const uploadedFilesArr = []

    console.log("files", files)

    for (let file of files) {

        const fileExt = path.extname(file.originalname);
        const s3Key = `uploads/${uuid.v4()}${fileExt}`;

        const uploadParams = {
            Bucket: AWS_S3_BUCKET_NAME,
            Key: s3Key,
            Body: file.buffer,
            ContentType: file.mimetype
        };

        await s3.send(new PutObjectCommand(uploadParams));

        uploadedFilesArr.push({ url: `${process.env.SERVER_BASE_URL}${s3Key}` })
    }

    return uploadedFilesArr;

};

module.exports = { upload, uploadFileToS3, uploadMultipleFileToS3 };
