// mailer/mailer.js
// const nodemailer = require("nodemailer");
const dotenv = require("dotenv");
const sgMail = require("@sendgrid/mail");
dotenv.config();
sgMail.setApiKey(process.env.SENDGRID_API_KEY);

// const transporter = nodemailer.createTransport({
//   host: "smtp.gmail.com",
//   port: 465,
//   secure: true,
//   auth: {
//     user: process.env.EMAIL_USER,
//     pass: process.env.EMAIL_PASS,
//   },
// });

const sendEmail = async ({ to, subject, text, html }) => {
  const msg = {
    to,
    from: process.env.EMAIL_USER,
    subject,
    text,
    html,
  };

  // const mailOptions = {
  //   from: `" <${process.env.EMAIL_USER}>`,
  //   to,
  //   subject: subject,
  //   text,
  //   html,
  // };

  try {
    const info = await sgMail.send(msg);
    console.log(`Email sent: ${info}`);
    return info;
  } catch (error) {
    console.error(`Error sending email:`, error);
    throw error;
  }
};

const sendOtpEmailVerification = async ({ to, Otp }) => {
  const html = `<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Email Verification OTP – Rebookit</title>
  <style>
    .preheader { display:none!important;visibility:hidden;mso-hide:all;font-size:1px;line-height:1px;max-height:0;opacity:0;overflow:hidden; }
    body { margin:0;padding:0;background:#f4f4f4;font-family:Arial,sans-serif;-webkit-text-size-adjust:none;-ms-text-size-adjust:none; }
    .container { max-width:600px;margin:0 auto;background:#ffffff;border:1px solid #dddddd;border-radius:8px;overflow:hidden; }
    .header { text-align:center;padding:20px;background:#0052cc; }
    .header img { max-width:120px; }
    .content { padding:30px;color:#333333;line-height:1.5; }
    h1 { margin:0 0 20px;font-size:24px;color:#222222; }
    p { margin:0 0 15px; }
    .otp-box { display:block;text-align:center;background:#eef4ff;padding:20px;margin:20px auto;border-radius:5px;font-size:32px;font-weight:bold;letter-spacing:4px;color:#0052cc; }
    .button-link { display:inline-block;margin:20px auto;padding:12px 25px;background:#0052cc;color:#ffffff!important;text-decoration:none;font-size:16px;border-radius:5px; }
    .footer { text-align:center;padding:20px;font-size:12px;color:#999999;border-top:1px solid #eeeeee; }
    @media (prefers-color-scheme: dark) {
      body { background:#121212;color:#dddddd; }
      .container { background:#1e1e1e;border-color:#333333; }
      .content { color:#cccccc; }
      .otp-box { background:#2a2a2a;color:#66aaff; }
      .button-link { background:#66aaff; }
      .footer { color:#777777;border-top-color:#333333; }
    }
  </style>
</head>
<body>
  <div class="preheader">Your Rebookit email verification OTP—valid for 15 minutes.</div>
  <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
    <tr>
      <td align="center" style="padding:40px 0;">
        <div class="container">
          
          <!-- Header -->
          <div class="header">
            <img src="https://rebook-it.s3.us-east-1.amazonaws.com/uploads/a74c0477-a154-4493-bfb9-c78530f869c2.png" alt="Rebookit Logo"/>
          </div>
          
          <!-- Content -->
          <div class="content">
            <h1>Email Verification</h1>
            <p>Please use the One-Time Password (OTP) below to verify your email address. This code expires in <strong>15 minutes</strong>.</p>
            
            <!-- OTP Box -->
            <div class="otp-box">${Otp}</div>

            <p>If you did not initiate this request, you can safely ignore this email. If you believe someone else is trying to use your email, please <a href="https://rebookit.club/contact-us" style="color:#0052cc;">contact support</a>.</p>

          </div>
          
          <!-- Footer -->
          <div class="footer">
            &copy; 2025 Rebookit. All rights reserved.<br/>
           <a href="https://rebookit.club/privacy-policy" style="color:#999999;">Privacy Policy</a> | 
            <a href="https://rebookit.club/terms-and-conditions" style="color:#999999;">Terms of Service</a>
          </div>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>
`;
  return await sendEmail({ to, subject: "Email Verification OTP - Rebookit", html });
};

const sendOtpForgotPassword = async ({ to, Otp }) => {
  const html = `<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Password Reset OTP - Rebookit</title>
  <style>
    .preheader { display:none!important;visibility:hidden;mso-hide:all;font-size:1px;line-height:1px;max-height:0;opacity:0;overflow:hidden; }
    body { margin:0;padding:0;background:#f4f4f4;font-family:Arial,sans-serif;-webkit-text-size-adjust:none;-ms-text-size-adjust:none; }
    .container { max-width:600px;margin:0 auto;background:#ffffff;border:1px solid #dddddd;border-radius:8px;overflow:hidden; }
    .header { text-align:center;padding:20px;background:#0052cc; } /* primary blue */
    .header img { max-width:120px; }
    .content { padding:30px;color:#333333;line-height:1.5; }
    h1 { margin:0 0 20px;font-size:24px;color:#222222; }
    p { margin:0 0 15px; }
    .otp-box { display:block;text-align:center;background:#eef4ff;padding:20px;margin:20px auto;border-radius:5px;font-size:32px;font-weight:bold;letter-spacing:4px;color:#0052cc; } /* light blue bg */
    .button-link { display:inline-block;margin:20px auto;padding:12px 25px;background:#0052cc;color:#ffffff!important;text-decoration:none;font-size:16px;border-radius:5px; }
    .footer { text-align:center;padding:20px;font-size:12px;color:#999999;border-top:1px solid #eeeeee; }
    @media (prefers-color-scheme: dark) {
      body { background:#121212;color:#dddddd; }
      .container { background:#1e1e1e;border-color:#333333; }
      .content { color:#cccccc; }
      .otp-box { background:#2a2a2a;color:#66aaff; }
      .button-link { background:#66aaff; }
      .footer { color:#777777;border-top-color:#333333; }
    }
  </style>
</head>
<body>
  <div class="preheader">Your Rebookit OTP for password reset—valid for 15 minutes.</div>
  <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
    <tr>
      <td align="center" style="padding:40px 0;">
        <div class="container">
          
          <!-- Header -->
          <div class="header">
            <img src="https://rebook-it.s3.us-east-1.amazonaws.com/uploads/a74c0477-a154-4493-bfb9-c78530f869c2.png" alt="Rebookit Logo"/>
          </div>
          
          <!-- Content -->
          <div class="content">
            <h1>Password Reset Request</h1>
            <p>We received a request to reset your Rebookit password. Enter the One-Time Password (OTP) below to proceed. It expires in <strong>15 minutes</strong>.</p>
            
            <!-- OTP Box -->
            <div class="otp-box">${Otp}</div>
          
            
            <p>If you didn’t request this, please ignore this email or <a href="https://rebookit.club/contact-us" style="color:#0052cc;">contact support</a>.</p>
          </div>
          
          <!-- Footer -->
          <div class="footer">
            &copy; 2025 Rebookit. All rights reserved.<br/>
            <a href="https://rebookit.club/privacy-policy" style="color:#999999;">Privacy Policy</a> | 
            <a href="https://rebookit.club/terms-and-conditions" style="color:#999999;">Terms of Service</a>
          </div>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>
`;
  await sendEmail({ to, subject: "Password reset Otp - Rebookit", html });
};

const sendWelcomeEmail = async ({ to }) => {
  const html = `<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Welcome to Rebookit</title>
  <style>
    .preheader { display:none!important;visibility:hidden;mso-hide:all;font-size:1px;line-height:1px;max-height:0;opacity:0;overflow:hidden; }
    body { margin:0;padding:0;background:#f4f4f4;font-family:Arial,sans-serif;-webkit-text-size-adjust:none;-ms-text-size-adjust:none; }
    .container { max-width:600px;margin:0 auto;background:#ffffff;border:1px solid #dddddd;border-radius:8px;overflow:hidden; }
    .header { text-align:center;padding:20px;background:#0052cc; }
    .header img { max-width:120px; }
    .content { padding:30px;color:#333333;line-height:1.5; }
    h1 { margin:0 0 20px;font-size:24px;color:#222222; }
    p { margin:0 0 15px; }
    .button-link { display:inline-block;margin:20px auto;padding:12px 25px;background:#0052cc;color:#ffffff!important;text-decoration:none;font-size:16px;border-radius:5px; }
    .feature-list { padding-left:20px; margin:0 0 20px; }
    .feature-list li { margin-bottom:10px; }
    .footer { text-align:center;padding:20px;font-size:12px;color:#999999;border-top:1px solid #eeeeee; }
    @media (prefers-color-scheme: dark) {
      body { background:#121212;color:#dddddd; }
      .container { background:#1e1e1e;border-color:#333333; }
      .content { color:#cccccc; }
      .button-link { background:#66aaff; }
      .footer { color:#777777;border-top-color:#333333; }
    }
  </style>
</head>
<body>
  <div class="preheader">Welcome to Rebookit – your community for sustainable, cost-effective textbook exchange.</div>
  <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
    <tr>
      <td align="center" style="padding:40px 0;">
        <div class="container">
          
          <!-- Header -->
          <div class="header">
            <img src="https://rebook-it.s3.us-east-1.amazonaws.com/uploads/a74c0477-a154-4493-bfb9-c78530f869c2.png" alt="ReBookIt.Club Logo"/>
          </div>
          
          <!-- Content -->
          <div class="content">
            <h1>Welcome, ${to}!</h1>
            <p>Thanks for joining <strong>ReBookIt.Club</strong>, where sustainable meets savings.</p>
            
            <p>ReBookIt.Club connects local buyers and sellers across all academic levels to:</p>
            <ul class="feature-list">
              <li>Give your used textbooks a new home—and recoup part of your purchase cost</li>
              <li>Save valuable space at home by letting go of books you no longer need</li>
              <li>Source quality textbooks at a fraction of the cost for your next term or year</li>
              <li>Join a community where many sellers are buyers too—putting money back in your pocket both ways</li>
            </ul>
            
            <p>Ready to get started? Post your first listing or browse available textbooks now:</p>
            <p style="text-align:center;">
              <a href="https://rebookit.club" class="button-link">Go to Dashboard</a>
            </p>
            
            <p>If you have any questions, our support team is here to help: <a href="https://rebookit.club/contact-us" style="color:#0052cc;">Contact Support</a>.</p>
          </div>
          
          <!-- Footer -->
          <div class="footer">
            &copy; 2025 rebookit.club. All rights reserved.<br/>
            <a href="https://rebookit.club/privacy-policy" style="color:#999999;">Privacy Policy</a> | 
            <a href="https://rebookit.club/terms-and-conditions" style="color:#999999;">Terms of Service</a>
          </div>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>
`;
  await sendEmail({ to, subject: "Welcome to Rebookit", html });
};

// 7‑day renewal reminder
const send7DayReminderEmail = async ({ to, userName = "", planName, endDate }) => {
  const formattedDate = new Date(endDate).toDateString();
  const html = `<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Your plan expires soon</title>
  <style>
    .preheader { display:none!important;visibility:hidden;mso-hide:all;font-size:1px;line-height:1px;max-height:0;opacity:0;overflow:hidden; }
    body { margin:0;padding:0;background:#f4f4f4;font-family:Arial,sans-serif;-webkit-text-size-adjust:none;-ms-text-size-adjust:none; }
    .container { max-width:600px;margin:0 auto;background:#ffffff;border:1px solid #dddddd;border-radius:8px;overflow:hidden; }
    .header { text-align:center;padding:20px;background:#0052cc; }
    .header img { max-width:120px; }
    .content { padding:30px;color:#333333;line-height:1.5; }
    h1 { margin:0 0 20px;font-size:24px;color:#222222; }
    p { margin:0 0 15px; }
    .button-link { display:inline-block;margin:20px auto;padding:12px 25px;background:#0052cc;color:#ffffff!important;text-decoration:none;font-size:16px;border-radius:5px; }
    .footer { text-align:center;padding:20px;font-size:12px;color:#999999;border-top:1px solid #eeeeee; }
    @media (prefers-color-scheme: dark) {
      body { background:#121212;color:#dddddd; }
      .container { background:#1e1e1e;border-color:#333333; }
      .content { color:#cccccc; }
      .button-link { background:#66aaff; }
      .footer { color:#777777;border-top-color:#333333; }
    }
  </style>
</head>
<body>
  <div class="preheader">Your ${planName} plan expires on ${formattedDate}. Renew to keep enjoying ReBookIt.Club.</div>
  <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
    <tr>
      <td align="center" style="padding:40px 0;">
        <div class="container">
          
          <!-- Header -->
          <div class="header">
            <img src="https://rebook-it.s3.us-east-1.amazonaws.com/uploads/a74c0477-a154-4493-bfb9-c78530f869c2.png" alt="ReBookIt.Club Logo"/>
          </div>
          
          <!-- Content -->
          <div class="content">
            <h1>Hey ${userName},</h1>
            <p>Just a friendly reminder that your subscription plan <strong>“${planName}”</strong> is set to expire in 7 days, on <strong>${formattedDate}</strong>.</p>
            <p>To avoid any interruption in service, please renew your plan now:</p>
            <p style="text-align:center;">
              <a href="https://rebookit.club/renew" class="button-link">Renew Your Plan</a>
            </p>
            <p>If you have questions or need help, our support team is always here for you: <a href="https://rebookit.club/contact-us" style="color:#0052cc;">Contact Support</a>.</p>
          </div>
          
          <!-- Footer -->
          <div class="footer">
            &copy; 2025 ReBookIt.Club. All rights reserved.<br/>
            <a href="https://rebookit.club/privacy-policy" style="color:#999999;">Privacy Policy</a> | 
            <a href="https://rebookit.club/terms-and-conditions" style="color:#999999;">Terms of Service</a>
          </div>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>
`;
  await sendEmail({
    to,
    subject: "Reminder: Your plan expires in 7 days",
    html,
  });
};

// 3‑day renewal reminder
const send3DayReminderEmail = async ({ to, userName = "", planName, endDate }) => {
  const formattedDate = new Date(endDate).toDateString();
  const html = `<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Your plan expires soon</title>
  <style>
    .preheader { display:none!important;visibility:hidden;mso-hide:all;font-size:1px;line-height:1px;max-height:0;opacity:0;overflow:hidden; }
    body { margin:0;padding:0;background:#f4f4f4;font-family:Arial,sans-serif;-webkit-text-size-adjust:none;-ms-text-size-adjust:none; }
    .container { max-width:600px;margin:0 auto;background:#ffffff;border:1px solid #dddddd;border-radius:8px;overflow:hidden; }
    .header { text-align:center;padding:20px;background:#0052cc; }
    .header img { max-width:120px; }
    .content { padding:30px;color:#333333;line-height:1.5; }
    h1 { margin:0 0 20px;font-size:24px;color:#222222; }
    p { margin:0 0 15px; }
    .button-link { display:inline-block;margin:20px auto;padding:12px 25px;background:#0052cc;color:#ffffff!important;text-decoration:none;font-size:16px;border-radius:5px; }
    .footer { text-align:center;padding:20px;font-size:12px;color:#999999;border-top:1px solid #eeeeee; }
    @media (prefers-color-scheme: dark) {
      body { background:#121212;color:#dddddd; }
      .container { background:#1e1e1e;border-color:#333333; }
      .content { color:#cccccc; }
      .button-link { background:#66aaff; }
      .footer { color:#777777;border-top-color:#333333; }
    }
  </style>
</head>
<body>
  <div class="preheader">Your ${planName} plan expires on ${formattedDate}. Renew now to stay uninterrupted.</div>
  <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
    <tr>
      <td align="center" style="padding:40px 0;">
        <div class="container">
          
          <!-- Header -->
          <div class="header">
            <img src="https://rebook-it.s3.us-east-1.amazonaws.com/uploads/a74c0477-a154-4493-bfb9-c78530f869c2.png" alt="ReBookIt.Club Logo"/>
          </div>
          
          <!-- Content -->
          <div class="content">
            <h1>Hi ${userName},</h1>
            <p>Just a quick nudge: your subscription plan <strong>“${planName}”</strong> will expire in 3 days, on <strong>${formattedDate}</strong>.</p>
            <p>Please renew now to continue enjoying uninterrupted service:</p>
            <p style="text-align:center;">
              <a href="https://rebookit.club/renew" class="button-link">Renew Your Plan</a>
            </p>
            <p>You’re almost there—let’s keep you on board! If you need help, reach out: <a href="https://rebookit.club/contact-us" style="color:#0052cc;">Contact Support</a>.</p>
          </div>
          
          <!-- Footer -->
          <div class="footer">
            &copy; 2025 ReBookIt.Club. All rights reserved.<br/>
           <a href="https://rebookit.club/privacy-policy" style="color:#999999;">Privacy Policy</a> | 
            <a href="https://rebookit.club/terms-and-conditions" style="color:#999999;">Terms of Service</a>
          </div>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>
`;
  await sendEmail({
    to,
    subject: "Urgent: Your plan expires in 3 days",
    html,
  });
};

// 1‑day final renewal reminder
const send1DayReminderEmail = async ({ to, userName = "", planName, endDate }) => {
  const formattedDate = new Date(endDate).toDateString();
  const html = `<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Final Notice: Plan Expires Tomorrow</title>
  <style>
    .preheader { display:none!important;visibility:hidden;mso-hide:all;font-size:1px;line-height:1px;max-height:0;opacity:0;overflow:hidden; }
    body { margin:0;padding:0;background:#f4f4f4;font-family:Arial,sans-serif;-webkit-text-size-adjust:none;-ms-text-size-adjust:none; }
    .container { max-width:600px;margin:0 auto;background:#ffffff;border:1px solid #dddddd;border-radius:8px;overflow:hidden; }
    .header { text-align:center;padding:20px;background:#0052cc; }
    .header img { max-width:120px; }
    .content { padding:30px;color:#333333;line-height:1.5; }
    h1 { margin:0 0 20px;font-size:24px;color:#222222; }
    p { margin:0 0 15px; }
    .button-link { display:inline-block;margin:20px auto;padding:12px 25px;background:#0052cc;color:#ffffff!important;text-decoration:none;font-size:16px;border-radius:5px; }
    .footer { text-align:center;padding:20px;font-size:12px;color:#999999;border-top:1px solid #eeeeee; }
    @media (prefers-color-scheme: dark) {
      body { background:#121212;color:#dddddd; }
      .container { background:#1e1e1e;border-color:#333333; }
      .content { color:#cccccc; }
      .button-link { background:#66aaff; }
      .footer { color:#777777;border-top-color:#333333; }
    }
  </style>
</head>
<body>
  <div class="preheader">Your ${planName} plan expires on ${formattedDate}.</div>
  <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
    <tr>
      <td align="center" style="padding:40px 0;">
        <div class="container">
          
          <!-- Header -->
          <div class="header">
            <img src="https://rebook-it.s3.us-east-1.amazonaws.com/uploads/a74c0477-a154-4493-bfb9-c78530f869c2.png" alt="ReBookIt.Club Logo"/>
          </div>
          
          <!-- Content -->
          <div class="content">
            <h1>Hi ${userName},</h1>
            <p><strong>Final reminder:</strong> your subscription plan <strong>“${planName}”</strong> expires tomorrow, <strong>${formattedDate}</strong>.</p>
            <p>Renew now to ensure zero downtime and uninterrupted access:</p>
            <p style="text-align:center;">
              <a href="https://rebookit.club/renew" class="button-link">Renew Now</a>
            </p>
            <p>This is your last chance—don’t miss out! If you need help, <a href="https://rebookit.club/contact-us" style="color:#0052cc;">Contact Support</a>.</p>
          </div>
          
          <!-- Footer -->
          <div class="footer">
            &copy; 2025 ReBookIt.Club. All rights reserved.<br/>
             <a href="https://rebookit.club/privacy-policy" style="color:#999999;">Privacy Policy</a> | 
            <a href="https://rebookit.club/terms-and-conditions" style="color:#999999;">Terms of Service</a>
          </div>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>
`;
  await sendEmail({
    to,
    subject: "Final Notice: Your plan expires tomorrow",
    html,
  });
};

const sendPlanPaymentSuccessEmail = async ({ to, userName = "", planName, amount, transactionId }) => {
  const html = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Payment Successful</title>
  <style>
    .preheader { display:none!important;visibility:hidden;mso-hide:all;font-size:1px;line-height:1px;max-height:0;opacity:0;overflow:hidden; }
    body { margin:0;padding:0;background:#f4f4f4;font-family:Arial,sans-serif;-webkit-text-size-adjust:none;-ms-text-size-adjust:none; }
    .container { max-width:600px;margin:0 auto;background:#ffffff;border:1px solid #dddddd;border-radius:8px;overflow:hidden; }
    .header { text-align:center;padding:20px;background:#28a745; }
    .header img { max-width:120px; }
    .content { padding:30px;color:#333333;line-height:1.5; }
    h1 { margin:0 0 20px;font-size:24px;color:#222222; }
    p { margin:0 0 15px; }
    .button-link { display:inline-block;margin:20px auto;padding:12px 25px;background:#28a745;color:#ffffff!important;text-decoration:none;font-size:16px;border-radius:5px; }
    .footer { text-align:center;padding:20px;font-size:12px;color:#999999;border-top:1px solid #eeeeee; }
    @media (prefers-color-scheme: dark) {
      body { background:#121212;color:#dddddd; }
      .container { background:#1e1e1e;border-color:#333333; }
      .content { color:#cccccc; }
      .button-link { background:#55dd88; }
      .footer { color:#777777;border-top-color:#333333; }
    }
  </style>
</head>
<body>
  <div class="preheader">Your payment for ${planName} was successful.</div>
  <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
    <tr>
      <td align="center" style="padding:40px 0;">
        <div class="container">
          <div class="header">
            <img src="https://rebook-it.s3.us-east-1.amazonaws.com/uploads/a74c0477-a154-4493-bfb9-c78530f869c2.png" alt="ReBookIt.Club Logo"/>
          </div>
          <div class="content">
            <h1>Payment Successful!</h1>
            <p>Hi ${userName},</p>
            <p>Thank you for your payment of <strong>$${amount}</strong> for the <strong>“${planName}”</strong> plan.</p>
            <p><strong>Transaction ID:</strong> ${transactionId}</p>
            <p>Your subscription is now active. You can manage your plan anytime in your account.</p>
            <p style="text-align:center;">
              <a href="https://rebookit.club" class="button-link">Go to Dashboard</a>
            </p>
            <p>Need help? <a href="https://rebookit.club/contact-us" style="color:#28a745;">Contact Support</a>.</p>
          </div>
          <div class="footer">
            &copy; 2025 ReBookIt.Club. All rights reserved.<br/>
           <a href="https://rebookit.club/privacy-policy" style="color:#999999;">Privacy Policy</a> | 
            <a href="https://rebookit.club/terms-and-conditions" style="color:#999999;">Terms of Service</a>
          </div>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>`;
  await sendEmail({
    to,
    subject: "Payment Successful – Thank You!",
    html,
  });
};

const sendPlanPaymentFailureEmail = async ({ to, userName = "", planName }) => {
  const html = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Payment Failed</title>
  <style>
    .preheader { display:none!important;visibility:hidden;mso-hide:all;font-size:1px;line-height:1px;max-height:0;opacity:0;overflow:hidden; }
    body { margin:0;padding:0;background:#f4f4f4;font-family:Arial,sans-serif;-webkit-text-size-adjust:none;-ms-text-size-adjust:none; }
    .container { max-width:600px;margin:0 auto;background:#ffffff;border:1px solid #dddddd;border-radius:8px;overflow:hidden; }
    .header { text-align:center;padding:20px;background:#dc3545; }
    .header img { max-width:120px; }
    .content { padding:30px;color:#333333;line-height:1.5; }
    h1 { margin:0 0 20px;font-size:24px;color:#222222; }
    p { margin:0 0 15px; }
    .button-link { display:inline-block;margin:20px auto;padding:12px 25px;background:#dc3545;color:#ffffff!important;text-decoration:none;font-size:16px;border-radius:5px; }
    .footer { text-align:center;padding:20px;font-size:12px;color:#999999;border-top:1px solid #eeeeee; }
    @media (prefers-color-scheme: dark) {
      body { background:#121212;color:#dddddd; }
      .container { background:#1e1e1e;border-color:#333333; }
      .content { color:#cccccc; }
      .button-link { background:#ff6f6f; }
      .footer { color:#777777;border-top-color:#333333; }
    }
  </style>
</head>
<body>
  <div class="preheader">Your payment for ${planName} failed. Please try again.</div>
  <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
    <tr>
      <td align="center" style="padding:40px 0;">
        <div class="container">
          <div class="header">
            <img src="https://rebook-it.s3.us-east-1.amazonaws.com/uploads/a74c0477-a154-4493-bfb9-c78530f869c2.png" alt="ReBookIt.Club Logo"/>
          </div>
          <div class="content">
            <h1>Payment Failed</h1>
            <p>Hi ${userName},</p>
            <p>Unfortunately, your payment for the <strong>“${planName}”</strong> plan was not successful.</p>
            <p>Please try again to continue enjoying our services without interruption.</p>
            <p style="text-align:center;">
              <a href="https://rebookit.club/renew" class="button-link">Retry Payment</a>
            </p>
            <p>If the problem persists, <a href="https://rebookit.club/contact-us" style="color:#dc3545;">Contact Support</a> for help.</p>
          </div>
          <div class="footer">
            &copy; 2025 ReBookIt.Club. All rights reserved.<br/>
           <a href="https://rebookit.club/privacy-policy" style="color:#999999;">Privacy Policy</a> | 
            <a href="https://rebookit.club/terms-and-conditions" style="color:#999999;">Terms of Service</a>
          </div>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>`;
  await sendEmail({
    to,
    subject: "Payment Failed – Action Required",
    html,
  });
};

// const sendPlanActiveEmail = async ({ to, planName }) => {
//   const html = `Hi There,
//   Great news—your subscription plan (“${planName}”)
//   that you pre-purchased has now kicked in as of ${new Date().toDateString()}.

//   You can log in and start using your benefits immediately:
//   http://rebookit.com/dashboard

//   Thank you for planning ahead!

//   — The Team`;
//   await sendEmail({ to, subject: "Your subscription is now active!", html });
// };

const sendPlanActiveEmail = async ({ to, userName = "", planName }) => {
  const html = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Subscription Activated</title>
  <style>
    .preheader { display:none!important;visibility:hidden;mso-hide:all;font-size:1px;line-height:1px;max-height:0;opacity:0;overflow:hidden; }
    body { margin:0;padding:0;background:#f4f4f4;font-family:Arial,sans-serif;-webkit-text-size-adjust:none;-ms-text-size-adjust:none; }
    .container { max-width:600px;margin:0 auto;background:#ffffff;border:1px solid #dddddd;border-radius:8px;overflow:hidden; }
    .header { text-align:center;padding:20px;background:#28a745; }
    .header img { max-width:120px; }
    .content { padding:30px;color:#333333;line-height:1.5; }
    h1 { margin:0 0 20px;font-size:24px;color:#222222; }
    p { margin:0 0 15px; }
    .button-link { display:inline-block;margin:20px auto;padding:12px 25px;background:#28a745;color:#ffffff!important;text-decoration:none;font-size:16px;border-radius:5px; }
    .footer { text-align:center;padding:20px;font-size:12px;color:#999999;border-top:1px solid #eeeeee; }
    @media (prefers-color-scheme: dark) {
      body { background:#121212;color:#dddddd; }
      .container { background:#1e1e1e;border-color:#333333; }
      .content { color:#cccccc; }
      .button-link { background:#55dd88; }
      .footer { color:#777777;border-top-color:#333333; }
    }
  </style>
</head>
<body>
  <div class="preheader">Your subscription plan (“${planName}”) is now active.</div>
  <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
    <tr>
      <td align="center" style="padding:40px 0;">
        <div class="container">
          <div class="header">
            <img src="https://rebook-it.s3.us-east-1.amazonaws.com/uploads/a74c0477-a154-4493-bfb9-c78530f869c2.png" alt="ReBookIt.Club Logo"/>
          </div>
          <div class="content">
            <h1>Subscription Activated!</h1>
            <p>Hi ${userName},</p>
            <p>Great news—your subscription plan <strong>“${planName}”</strong> that you pre-purchased has now kicked in as of <strong>${new Date().toDateString()}</strong>.</p>
            <p>You can log in and start using your benefits immediately:</p>
            <p style="text-align:center;">
              <a href="https://rebookit.club/" class="button-link">Go to Dashboard</a>
            </p>
            <p>Thank you for planning ahead!</p>
          </div>
          <div class="footer">
            &copy; 2025 ReBookIt.Club. All rights reserved.<br/>
           <a href="https://rebookit.club/privacy-policy" style="color:#999999;">Privacy Policy</a> | 
            <a href="https://rebookit.club/terms-and-conditions" style="color:#999999;">Terms of Service</a>
          </div>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>`;
  await sendEmail({
    to,
    subject: "Your subscription is now active!",
    html,
  });
};

// ITEM APPROVED BY ADMIN
const sendItemApprovedEmail = async ({ to, itemName }) => {
  const html = `<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Item Approved – Rebookit</title>
  <style>
    .preheader {
      display:none!important;
      visibility:hidden;
      mso-hide:all;
      font-size:1px;
      line-height:1px;
      max-height:0;
      opacity:0;
      overflow:hidden;
    }
    body {
      margin:0;
      padding:0;
      background:#f4f4f4;
      font-family:Arial,sans-serif;
      -webkit-text-size-adjust:none;
      -ms-text-size-adjust:none;
    }
    .container {
      max-width:600px;
      margin:0 auto;
      background:#ffffff;
      border:1px solid #dddddd;
      border-radius:8px;
      overflow:hidden;
    }
    .header {
      text-align:center;
      padding:20px;
      background:#0052cc;
    }
    .header img {
      max-width:120px;
    }
    .content {
      padding:30px;
      color:#333333;
      line-height:1.5;
    }
    h1 {
      margin:0 0 20px;
      font-size:24px;
      color:#222222;
      text-align:center;
    }
    .status-box {
      display:block;
      text-align:center;
      background:#e8f5e9;
      padding:20px;
      margin:20px auto;
      border-radius:5px;
      font-size:24px;
      font-weight:bold;
      color:#2e7d32;
    }
    .button-link {
      display:inline-block;
      margin:20px auto;
      padding:12px 25px;
      background:#0052cc;
      color:#ffffff!important;
      text-decoration:none;
      font-size:16px;
      border-radius:5px;
      text-align:center;
    }
    .footer {
      text-align:center;
      padding:20px;
      font-size:12px;
      color:#999999;
      border-top:1px solid #eeeeee;
    }
    @media (prefers-color-scheme: dark) {
      body {
        background:#121212;
        color:#dddddd;
      }
      .container {
        background:#1e1e1e;
        border-color:#333333;
      }
      .content {
        color:#cccccc;
      }
      .status-box {
        background:#2a2a2a;
        color:#4caf50;
      }
      .button-link {
        background:#66aaff;
      }
      .footer {
        color:#777777;
        border-top-color:#333333;
      }
    }
  </style>
</head>
<body>
  <div class="preheader">Your item "${itemName}" has been approved on Rebookit.</div>
  <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
    <tr>
      <td align="center" style="padding:40px 0;">
        <div class="container">
          <!-- Header -->
          <div class="header">
            <img src="https://rebook-it.s3.us-east-1.amazonaws.com/uploads/a74c0477-a154-4493-bfb9-c78530f869c2.png" alt="Rebookit Logo"/>
          </div>
          
          <!-- Content -->
          <div class="content">
            <h1>Item Approved</h1>
            <p>Hi there,</p>
            <p>We're excited to inform you that your item has been approved and is now live on Rebookit!</p>
            
            <!-- Status Box -->
            <div class="status-box">
              ${itemName} is now active!
            </div>

            <p>Your item is now visible to our community. You can manage it directly from your dashboard.</p>
            
            <div style="text-align:center;">
              <a href="https://rebookit.club/profile/mybooks" class="button-link">Go to Dashboard</a>
            </div>

            <p>Thank you for contributing to Rebookit. If you have any questions, our support team is here to help.</p>
          </div>
          
          <!-- Footer -->
          <div class="footer">
            &copy; 2025 Rebookit. All rights reserved.<br/>
            <a href="https://rebookit.club/privacy-policy" style="color:#999999;">Privacy Policy</a> | 
            <a href="https://rebookit.club/terms-and-conditions" style="color:#999999;">Terms of Service</a>
          </div>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>`;
  await sendEmail({ to, subject: "Your Item Has Been Approved - Rebookit", html });
};

// ITEM REJECTED BY ADMIN
const sendItemRejectedEmail = async ({ to, itemName, reason }) => {
  const html = `<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Item Rejected – Rebookit</title>
  <style>
    .preheader {
      display:none!important;
      visibility:hidden;
      mso-hide:all;
      font-size:1px;
      line-height:1px;
      max-height:0;
      opacity:0;
      overflow:hidden;
    }
    body {
      margin:0;
      padding:0;
      background:#f4f4f4;
      font-family:Arial,sans-serif;
      -webkit-text-size-adjust:none;
      -ms-text-size-adjust:none;
    }
    .container {
      max-width:600px;
      margin:0 auto;
      background:#ffffff;
      border:1px solid #dddddd;
      border-radius:8px;
      overflow:hidden;
    }
    .header {
      text-align:center;
      padding:20px;
      background:#d32f2f;
    }
    .header img {
      max-width:120px;
    }
    .content {
      padding:30px;
      color:#333333;
      line-height:1.5;
    }
    h1 {
      margin:0 0 20px;
      font-size:24px;
      color:#222222;
      text-align:center;
    }
    .status-box {
      display:block;
      text-align:center;
      background:#ffebee;
      padding:20px;
      margin:20px auto;
      border-radius:5px;
      font-size:24px;
      font-weight:bold;
      color:#c62828;
    }
    .reason-box {
      background:#fff8e1;
      padding:15px;
      border-radius:5px;
      margin:20px 0;
      border-left:4px solid #ffc107;
    }
    .button-link {
      display:inline-block;
      margin:20px auto;
      padding:12px 25px;
      background:#d32f2f;
      color:#ffffff!important;
      text-decoration:none;
      font-size:16px;
      border-radius:5px;
      text-align:center;
    }
    .footer {
      text-align:center;
      padding:20px;
      font-size:12px;
      color:#999999;
      border-top:1px solid #eeeeee;
    }
    @media (prefers-color-scheme: dark) {
      body {
        background:#121212;
        color:#dddddd;
      }
      .container {
        background:#1e1e1e;
        border-color:#333333;
      }
      .content {
        color:#cccccc;
      }
      .status-box {
        background:#2a2a2a;
        color:#f44336;
      }
      .reason-box {
        background:#2a2a2a;
        border-left-color:#ffc107;
      }
      .button-link {
        background:#f44336;
      }
      .footer {
        color:#777777;
        border-top-color:#333333;
      }
    }
  </style>
</head>
<body>
  <div class="preheader">Your item "${itemName}" was not approved on Rebookit.</div>
  <table role="presentation" width="100%" cellpadding="0" cellspacing="0">
    <tr>
      <td align="center" style="padding:40px 0;">
        <div class="container">
          <!-- Header -->
          <div class="header">
            <img src="https://rebook-it.s3.us-east-1.amazonaws.com/uploads/a74c0477-a154-4493-bfb9-c78530f869c2.png" alt="Rebookit Logo"/>
          </div>
          
          <!-- Content -->
          <div class="content">
            <h1>Item Not Approved</h1>
            <p>Hi there,</p>
            <p>We regret to inform you that your item did not meet our guidelines and has not been approved.</p>
            
            <!-- Status Box -->
            <div class="status-box">
              ${itemName} was rejected
            </div>

            ${
              reason
                ? `
              <p><strong>Reason for rejection:</strong></p>
              <div class="reason-box">
              ${reason}
            </div>
            `
                : ""
            }

            <p>Please review our guidelines and resubmit your item with the necessary changes.</p>
            
            <div style="text-align:center;">
              <a href="https://rebookit.club/contact-us" class="button-link">Contact Support</a>
            </div>

            <p>We appreciate your understanding and look forward to your revised submission.</p>
          </div>
          
          <!-- Footer -->
          <div class="footer">
            &copy; 2025 Rebookit. All rights reserved.<br/>
           <a href="https://rebookit.club/privacy-policy" style="color:#999999;">Privacy Policy</a> | 
            <a href="https://rebookit.club/terms-and-conditions" style="color:#999999;">Terms of Service</a>
          </div>
        </div>
      </td>
    </tr>
  </table>
</body>
</html>`;
  await sendEmail({ to, subject: "Your Item Requires Changes - Rebookit", html });
};

const sendSupportMail = async ({ to, name = "", email = "", subject = "", message = "" }) => {
  const html = `<!DOCTYPE html>
<html lang="en" xmlns="http://www.w3.org/1999/xhtml">
<head>
  <meta charset="UTF-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
  <title>Item Rejected – Rebookit</title>
  <style>
    .preheader {
      display:none!important;
      visibility:hidden;
      mso-hide:all;
      font-size:1px;
      line-height:1px;
      max-height:0;
      opacity:0;
      overflow:hidden;
    }
    body {
      margin:0;
      padding:0;
      background:#f4f4f4;
      font-family:Arial,sans-serif;
      -webkit-text-size-adjust:none;
      -ms-text-size-adjust:none;
    }
    .container {
      max-width:600px;
      margin:0 auto;
      background:#ffffff;
      border:1px solid #dddddd;
      border-radius:8px;
      overflow:hidden;
    }
    .header {
      text-align:center;
      padding:20px;
      background:#d32f2f;
    }
    .header img {
      max-width:120px;
    }
    .content {
      padding:30px;
      color:#333333;
      line-height:1.5;
    }
    h1 {
      margin:0 0 20px;
      font-size:24px;
      color:#222222;
      text-align:center;
    }
    .status-box {
      display:block;
      text-align:center;
      background:#ffebee;
      padding:20px;
      margin:20px auto;
      border-radius:5px;
      font-size:24px;
      font-weight:bold;
      color:#c62828;
    }
    .reason-box {
      background:#fff8e1;
      padding:15px;
      border-radius:5px;
      margin:20px 0;
      border-left:4px solid #ffc107;
    }
    .button-link {
      display:inline-block;
      margin:20px auto;
      padding:12px 25px;
      background:#d32f2f;
      color:#ffffff!important;
      text-decoration:none;
      font-size:16px;
      border-radius:5px;
      text-align:center;
    }
    .footer {
      text-align:center;
      padding:20px;
      font-size:12px;
      color:#999999;
      border-top:1px solid #eeeeee;
    }
    @media (prefers-color-scheme: dark) {
      body {
        background:#121212;
        color:#dddddd;
      }
      .container {
        background:#1e1e1e;
        border-color:#333333;
      }
      .content {
        color:#cccccc;
      }
      .status-box {
        background:#2a2a2a;
        color:#f44336;
      }
      .reason-box {
        background:#2a2a2a;
        border-left-color:#ffc107;
      }
      .button-link {
        background:#f44336;
      }
      .footer {
        color:#777777;
        border-top-color:#333333;
      }
    }
  </style>
</head>
<body>
  <div class="preheader">Suport Request </div>
  <div> Name--${name}</div>
  <div> Email--${email}</div>
  <div> Subject--${subject}</div>
  <div> Message--${message}</div>
</body>
</html>`;
  await sendEmail({ to, subject: "Support Request -Rebookit", html });
};

module.exports = {
  sendEmail,
  sendOtpEmailVerification,
  sendOtpForgotPassword,
  sendWelcomeEmail,
  send7DayReminderEmail,
  send3DayReminderEmail,
  send1DayReminderEmail,
  sendPlanActiveEmail,
  sendPlanPaymentSuccessEmail,
  sendPlanPaymentFailureEmail,
  sendItemApprovedEmail,
  sendItemRejectedEmail,
  sendSupportMail,
};
