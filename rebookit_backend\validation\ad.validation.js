const Joi = require("joi");
const { objectIdRule, baseStringRule } = require("./rules");
const { AdResourceTypeEnum, AdPageTypeEnum, AdPositionTypeEnum } = require("../common/Enums");

const CreateAdResourcesSchema = Joi.object({
  type: baseStringRule.valid(...Object.values(AdResourceTypeEnum)).required(),
  page: baseStringRule.valid(...Object.values(AdPageTypeEnum)).required(),
  position: baseStringRule.valid(...Object.values(AdPositionTypeEnum)).required(),
  basePrice: Joi.number().required(),
  pricingRules: Joi.array()
    .items(
      Joi.object({
        name: baseStringRule.required(),
        price: Joi.number().positive().required(),
        priority: Joi.number().integer().min(10),
        startDate: Joi.date().required(),
        endDate: Joi.date().required(),
        color: baseStringRule.required(),
      })
    )
    .required(),
  overrideRules: Joi.array()
    .items(
      Joi.object({
        date: Joi.date().required(),
        price: Joi.number().positive().required(),
      })
    )
    .required(),
})
  .required()
  .unknown(false);

const UpdateAdResourcesSchema = Joi.object({
  pricingRules: Joi.array()
    .items(
      Joi.object({
        name: Joi.string().required(),
        price: Joi.number().positive().required(),
        priority: Joi.number().integer().min(10).required(),
        startDate: Joi.date().required(),
        endDate: Joi.date().required(),
      })
    )
    .required(),
  overrideRules: Joi.array()
    .items(
      Joi.object({
        date: Joi.date().required(),
        price: Joi.number().positive().required(),
      })
    )
    .required(),
})
  .required()
  .unknown(false);

const idParamSchema = Joi.object({
  id: objectIdRule.required(),
});

const updateBasePriceSchema = Joi.object({
  resourceId: objectIdRule,
  baseId: objectIdRule,
  price: Joi.number().positive(),
});

const setPriceRuleAd = Joi.object({
  resourceId: objectIdRule.required(),
  rules: Joi.object({
    name: baseStringRule.required(),
    price: Joi.number().positive().required(),
    priority: Joi.alternatives().try(Joi.number().integer().min(10), Joi.string().allow("")),
    startDate: Joi.date().required(),
    endDate: Joi.date().required(),
    color: baseStringRule.required(),
  }),
});

const updatePriceRuleAd = Joi.object({
  resourceId: objectIdRule,
  ruleId: objectIdRule,
  pricingRules: Joi.object({
    name: baseStringRule,
    price: Joi.number().positive(),
    priority: Joi.alternatives().try(Joi.number().integer().min(10), Joi.string().allow("")),
    startDate: Joi.date(),
    endDate: Joi.date(),
    color: baseStringRule,
  }),
});

const setOverridePriceRuleAd = Joi.object({
  resourceId: objectIdRule.required(),
  overridePrices: Joi.array()
    .items(
      Joi.object({
        date: Joi.date().required(),
        price: Joi.number().positive().required(),
      })
    )
    .required(),
});

const UpdateOverridePriceRuleAd = Joi.object({
  resourceId: objectIdRule,
  overrideId: objectIdRule,
  overrideRules: Joi.object({
    date: Joi.date(),
    price: Joi.number(),
  }),
});

module.exports = {
  CreateAdResourcesSchema,
  UpdateAdResourcesSchema,
  idParamSchema,
  updateBasePriceSchema,
  setPriceRuleAd,
  updatePriceRuleAd,
  setOverridePriceRuleAd,
  UpdateOverridePriceRuleAd,
};
