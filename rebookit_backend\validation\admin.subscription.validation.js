const Joi = require("joi");
const { PlanMonthsTypeEnum, PlanTypeEnum, PlanStatusEnum } = require("../common/Enums");
const { nameRequiredRule, boolRequiredRule, baseMinZeroRule, baseMinOneRule, objectIdRule, baseStringRule } = require("./rules");

const listingSchema = Joi.object().keys({
  category: objectIdRule.required(),
  noOfListing: Joi.number().min(1),
  listingValidityDays: Joi.number().min(1).max(365),
}).unknown(false);

const subscriptionPlanSchema = Joi.object({
  planName: nameRequiredRule,
  planType: Joi.string().valid(PlanTypeEnum.FREE, PlanTypeEnum.PAID).required(),
  planMonths: Joi.string().valid(...Object.values(PlanMonthsTypeEnum)).required(),
  price: baseMinOneRule.when("planType", { is: PlanTypeEnum.PAID, then: Joi.required(), }),
  listings: Joi.array().items(listingSchema).required(),
  boosts: baseMinZeroRule,
  active: boolRequiredRule
})
  .required()
  .unknown(false);

const updateSubscriptionPlanSchema = Joi.object({
  planName: Joi.string().min(2).max(50),
  planMonths: Joi.string().valid(...Object.values(PlanMonthsTypeEnum)),
  price: baseMinOneRule,
  listings: Joi.array().items(listingSchema),
  boosts: baseMinZeroRule,
  active: Joi.boolean()
}).unknown(false);


const filterSearchMemberSchema = Joi.object({
  // UPDATE VALIDATION AS FILTERS OBJECT IS REQUIRED IN BODY
  filters: Joi.object({
    keyword: baseStringRule.optional(),
    minPrice: baseMinZeroRule.optional(),
    maxPrice: baseMinZeroRule.optional(),
    status: Joi.string().valid(...Object.values(PlanStatusEnum)),
  }).optional()
    .unknown(false),
  planId: objectIdRule
}).unknown(false)

module.exports = {
  subscriptionPlanSchema,
  updateSubscriptionPlanSchema,
  filterSearchMemberSchema
};
