const Joi = require("joi");
const { objectIdRule } = require("./rules");

const idParamSchema = Joi.object({
  id: objectIdRule.required(),
});

const createTestimonialSchema = Joi.object({
  name: Joi.string().required(),
  title: Joi.string().required(),
  content: Joi.string().required(),
  image: Joi.string().required(),
})
  .required()
  .unknown(false);

const updateTestimonialSchema = Joi.object({
  name: Joi.string().optional(),
  title: Joi.string().optional(),
  content: Joi.string().optional(),
  image: Joi.string().optional(),
})
  .required()
  .unknown(false);

module.exports = {
  idParamSchema,
  createTestimonialSchema,
  updateTestimonialSchema,
};
