const Joi = require("joi");
const { pageSizeRule, pageNoRule, objectIdRule, searchTermRule, baseIdRule } = require("./rules");

const questionSchema = Joi.object({
  title: Joi.string().required(),
  categoryId: baseIdRule.optional(),
})
.required()
.unknown(false);

const answerSchema = Joi.object({
  questionId: baseIdRule.required(),
  answerText: Joi.string().required(),
})
.required()
.unknown(false);

const editAnswerBodySchema = Joi.object({
  answerText: Joi.string().required(),
})
.required()
.unknown(false)
;

const editAnswerQuerySchema = Joi.object({
  answerId: baseIdRule.required(),
})
.required()
.unknown(false)


const idParamSchema = Joi.object({
  id: baseIdRule.required(),
})
.required()
.unknown(false);

const allQuestionsQuerySchema = Joi.object({
  pageSize: pageSizeRule,
  page: pageNoRule,
  categoryId: objectIdRule,
  searchTerm: searchTermRule,
  sort: Joi.string().valid("popularity", "oldest", "leastPopularity"),
  userId: objectIdRule,
}).unknown(false);

const userQuestionsQuerySchema = Joi.object({
  pageSize: pageSizeRule,
  page: pageNoRule,
  searchTerm: searchTermRule,
}).unknown(false);

module.exports = {
  questionSchema,
  answerSchema,
  editAnswerBodySchema,
  idParamSchema,
  allQuestionsQuerySchema,
  userQuestionsQuerySchema,
  editAnswerQuerySchema
};
