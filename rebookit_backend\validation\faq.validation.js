const Joi = require("joi");
const { baseIdRule, boolOptionalRule, trimStringRule } = require("./rules");

const faqIdParam = Joi.object({
  id: baseIdRule.required().messages({
    "string.base": "FAQ id must be a string",
    "string.hex": "FAQ id must be a valid hex string",
    "string.length": "FAQ id must be 24 characters",
    "any.required": "FAQ id is required",
  }),
})
  .required()
  .unknown(false);

const createFaqSchema = Joi.object({
  question: Joi.string().trim().min(5).max(200).required().messages({
    "string.base": "Question must be a string",
    "string.empty": "Question cannot be empty",
    "string.min": "Question must be at least {#limit} characters",
    "string.max": "Question cannot exceed {#limit} characters",
    "any.required": "Question is required",
  }),

  answer: Joi.string().trim().min(5).required().messages({
    "string.base": "Answer must be a string",
    "string.empty": "Answer cannot be empty",
    "string.min": "Answer must be at least {#limit} characters",
    "any.required": "Answer is required",
  }),

  category: Joi.string().valid("General", "Membership", "Billing", "Technical", "Other").default("General").messages({
    "string.base": "Category must be a string",
    "any.only": "Category must be one of {#valids}",
  }),

  order: Joi.number().integer().min(0).default(0).messages({
    "number.base": "Order must be a number",
    "number.integer": "Order must be an integer",
    "number.min": "Order cannot be negative",
  }),

  tags: Joi.array()
    .items(
      trimStringRule.min(1).max(50).messages({
        "string.base": "Each tag must be a string",
        "string.empty": "Tags cannot be empty",
        "string.min": "Each tag must be at least {#limit} characters",
        "string.max": "Each tag cannot exceed {#limit} characters",
      })
    )
    .default([])
    .messages({
      "array.base": "Tags must be an array of strings",
    }),

  isActive: Joi.boolean().default(false).messages({
    "boolean.base": "isActive must be true or false",
  }),
})
  .required()
  .unknown(false);

// schema for filtering and paginating FAQs via query
const filterFaqsSchema = Joi.object({
  searchTerm: trimStringRule.min(1).max(100).optional().messages({
    "string.base": "searchTerm must be a string",
    "string.empty": "searchTerm cannot be empty",
    "string.max": "searchTerm cannot exceed {#limit} characters",
  }),

  tags: Joi.array()
    .items(
      trimStringRule.min(1).max(50).messages({
        "string.base": "Each tag must be a string",
        "string.empty": "Tags cannot be empty",
        "string.min": "Each tag must be at least {#limit} characters",
        "string.max": "Each tag cannot exceed {#limit} characters",
      })
    )
    .optional()
    .messages({
      "array.base": "Tags must be an array of strings",
    }),

  category: Joi.string().valid("General", "Membership", "Billing", "Technical", "Other").optional().messages({
    "string.base": "category must be a string",
    "any.only": "category must be one of {#valids}",
  }),
}).unknown(false);

const filterFaqsQuerySchema = Joi.object({
  pageSize: Joi.number().integer().min(1).max(100).default(10).messages({
    "number.base": "pageSize must be a number",
    "number.integer": "pageSize must be an integer",
    "number.min": "pageSize must be at least {#limit}",
    "number.max": "pageSize cannot exceed {#limit}",
  }),

  page: Joi.number().integer().min(1).default(1).messages({
    "number.base": "page must be a number",
    "number.integer": "page must be an integer",
    "number.min": "page must be at least {#limit}",
  }),
});

const updateFaqSchema = Joi.object({
  question: Joi.string().trim().min(5).max(200).optional().messages({
    "string.base": "Question must be a string",
    "string.min": "Question must be at least {#limit} characters",
    "string.max": "Question cannot exceed {#limit} characters",
  }),

  answer: Joi.string().trim().min(5).optional().messages({
    "string.base": "Answer must be a string",
    "string.min": "Answer must be at least {#limit} characters",
  }),

  category: Joi.string().valid("General", "Membership", "Billing", "Technical", "Other").optional().messages({
    "string.base": "Category must be a string",
    "any.only": "Category must be one of {#valids}",
  }),

  order: Joi.number().integer().min(0).optional().messages({
    "number.base": "Order must be a number",
    "number.integer": "Order must be an integer",
    "number.min": "Order cannot be negative",
  }),

  tags: Joi.array().items(trimStringRule.min(1).max(50)).optional().messages({
    "array.base": "Tags must be an array of strings",
  }),

  isActive: boolOptionalRule.messages({
    "boolean.base": "isActive must be true or false",
  }),
}).unknown(false);

module.exports = {
  faqIdParam,
  createFaqSchema,
  filterFaqsSchema,
  filterFaqsQuerySchema,
  updateFaqSchema,
};
