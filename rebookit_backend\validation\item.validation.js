const Joi = require("joi");
const {
  BookConditionEnum,
  ItemListStatusEnum,
  itemListTypeEnum,
  QualificationTypeEnum,
  TargetClassTypeEnum,
  ExperienceTypeEnum,
  EventModeEnum,
  SchoolTypeEnum,
  FrequencyEnum,
  ActivityTypeEnum,
  ClassesOffered,
  KindEnum,
  ParishesListEnum,
  UnitEnum,
} = require("../common/Enums");
const {
  pageSizeRule,
  pageNoRule,
  objectIdRule,
  baseIdRule,
  baseStringRule,
  baseMinZeroRule,
  baseMinOneRule,
} = require("./rules");
const { paginationQuerySchema } = require("./common-schema");
const otpRule = Joi.number().integer().min(100000).max(999999).required();

const createItemSchema = Joi.object({
  // otp: otpRule,
  kind: baseStringRule.valid(...Object.values(KindEnum)).required(),
  title: baseStringRule.max(500).required(),
  description: baseStringRule.allow(null, "").max(5000),
  categoryId: baseIdRule.required(),
  subCategoryId: baseIdRule,
  subSubCategoryId: baseIdRule,
  subSubSubCategoryId: baseIdRule,
  images: Joi.array().items(Joi.string().uri()).min(1).required(),
  tags: Joi.array().items(baseStringRule),
  price: baseMinOneRule,
  unit: baseStringRule.valid(...Object.values(UnitEnum)),
  address: Joi.object({
    formatted_address: baseStringRule.required(),
    geometry: Joi.object({
      location: Joi.object({
        type: baseStringRule.valid("Point").required(),
        coordinates: Joi.array()
          .ordered(
            Joi.number().min(-180).max(180).required(), // longitude
            Joi.number().min(-90).max(90).required() // latitude
          )
          .length(2)
          .required(),
      }).required(),
    }).required(),

    country: baseStringRule.required(),
    country_code: baseStringRule,
    postal_code: baseStringRule,
    parish: baseStringRule.valid(...Object.values(ParishesListEnum)).required(),
    administrative_area_level_1: baseStringRule.required(),
    administrative_area_level_2: baseStringRule,
    administrative_area_level_3: baseStringRule,
    administrative_area_level_4: baseStringRule,
    administrative_area_level_5: baseStringRule,
    administrative_area_level_6: baseStringRule,
    administrative_area_level_7: baseStringRule,
    locality: baseStringRule,
    political: baseStringRule,
    political_short: baseStringRule,
    sublocality: baseStringRule,
    sublocality_level_1: baseStringRule,
    sublocality_level_2: baseStringRule,
    sublocality_level_3: baseStringRule,
    sublocality_level_4: baseStringRule,
    sublocality_level_5: baseStringRule,
    colloquial_area: baseStringRule,
    airport: baseStringRule,
    bus_station: baseStringRule,
    park: baseStringRule,
    university: baseStringRule,
    intersection: baseStringRule,
    route: baseStringRule,
    street_number: baseStringRule,
    natural_feature: baseStringRule,
    neighborhood: baseStringRule,
    premise: baseStringRule,
    subpremise: baseStringRule,
    administrative_area_level_2: baseStringRule,
    administrative_area_level_3: baseStringRule,
    administrative_area_level_4: baseStringRule,
    administrative_area_level_5: baseStringRule,
    administrative_area_level_6: baseStringRule,
    administrative_area_level_7: baseStringRule,
    locality: baseStringRule,
    political: baseStringRule,
    political_short: baseStringRule,
    sublocality: baseStringRule,
    sublocality_level_1: baseStringRule,
    sublocality_level_2: baseStringRule,
    sublocality_level_3: baseStringRule,
    sublocality_level_4: baseStringRule,
    sublocality_level_5: baseStringRule,
    colloquial_area: baseStringRule,
    airport: baseStringRule,
    bus_station: baseStringRule,
    park: baseStringRule,
    university: baseStringRule,
    intersection: baseStringRule,
    route: baseStringRule,
    street_number: baseStringRule,
    natural_feature: baseStringRule,
    neighborhood: baseStringRule,
    premise: baseStringRule,
    subpremise: baseStringRule,
  }).required(),
})
  .when(Joi.object({ kind: "BookItem" }).unknown(), {
    then: Joi.object({
      isbn_number: baseStringRule.required(),
      authors: Joi.array().items(baseStringRule).min(1).required(),
      condition: baseStringRule.valid(...Object.values(BookConditionEnum)).required(),
    }),
  })
  .when(Joi.object({ kind: "TutorItem" }).unknown(), {
    then: Joi.object({
      highestQualification: baseStringRule.valid(...Object.values(QualificationTypeEnum)).required(),
      targetClasses: baseStringRule.valid(...Object.values(TargetClassTypeEnum)).required(),
      experience: baseStringRule.valid(...Object.values(ExperienceTypeEnum)).required(),
      website: baseStringRule.allow("", null).optional(),
    }),
  })
  .when(Joi.object({ kind: "EventItem" }).unknown(), {
    then: Joi.object({
      eventStartDate: Joi.date().required(),
      eventEndDate: Joi.date().required(),
      eventMode: baseStringRule.valid(...Object.values(EventModeEnum)).required(),
      website: baseStringRule.optional(),
    }),
  })
  .when(Joi.object({ kind: "SchoolItem" }).unknown(), {
    then: Joi.object({
      schoolType: baseStringRule.valid(...Object.values(SchoolTypeEnum)).required(),
      classesOffered: baseStringRule.valid(...Object.values(ClassesOffered)).required(),
      website: baseStringRule.optional(),
    }),
  })
  .when(Joi.object({ kind: "ExtracurricularActivityItem" }).unknown(), {
    then: Joi.object({
      activityType: baseStringRule.valid(...Object.values(ActivityTypeEnum)).required(),
      frequency: baseStringRule.valid(...Object.values(FrequencyEnum)).required(),
      targetStudents: baseStringRule.min(1).required(),
    }),
  })
  .when(Joi.object({ kind: "ScholarshipAwardItem" }).unknown(), {
    then: Joi.object({
      scholarshipType: baseStringRule.min(1).required(),
      eligibilityCriteria: baseStringRule.min(1).required(),
      website: baseStringRule.optional(),
    }),
  })
  .unknown(false);

const idParamSchema = Joi.object({
  id: objectIdRule.required(),
});

const updateItemSchema = Joi.object({
  kind: baseStringRule.valid(...Object.values(KindEnum)).required(),
  description: baseStringRule.allow(null, "").max(5000),
  price: baseMinOneRule,
  unit: baseStringRule.valid(...Object.values(UnitEnum)),

  address: Joi.object({
    formatted_address: baseStringRule.required(),
    geometry: Joi.object({
      location: Joi.object({
        type: baseStringRule.valid("Point").required(),
        coordinates: Joi.array()
          .ordered(
            Joi.number().min(-180).max(180).required(), // longitude
            Joi.number().min(-90).max(90).required() // latitude
          )
          .length(2)
          .required(),
      }).required(),
    }).required(),

    country: baseStringRule.required(),
    country_code: baseStringRule,
    postal_code: baseStringRule,
    parish: baseStringRule.valid(...Object.values(ParishesListEnum)),
    administrative_area_level_1: baseStringRule.required(),
    administrative_area_level_2: baseStringRule,
    administrative_area_level_3: baseStringRule,
    administrative_area_level_4: baseStringRule,
    administrative_area_level_5: baseStringRule,
    administrative_area_level_6: baseStringRule,
    administrative_area_level_7: baseStringRule,
    locality: baseStringRule,
    political: baseStringRule,
    political_short: baseStringRule,
    sublocality: baseStringRule,
    sublocality_level_1: baseStringRule,
    sublocality_level_2: baseStringRule,
    sublocality_level_3: baseStringRule,
    sublocality_level_4: baseStringRule,
    sublocality_level_5: baseStringRule,
    colloquial_area: baseStringRule,
    airport: baseStringRule,
    bus_station: baseStringRule,
    park: baseStringRule,
    university: baseStringRule,
    intersection: baseStringRule,
    route: baseStringRule,
    street_number: baseStringRule,
    natural_feature: baseStringRule,
    neighborhood: baseStringRule,
    premise: baseStringRule,
    subpremise: baseStringRule,
  }),

  images: Joi.array().items(baseStringRule.uri()),
  tags: Joi.array().items(baseStringRule),
  isActive: Joi.boolean(),
  status: baseStringRule.valid("deleted"),
})
  .when(Joi.object({ kind: "BookItem" }).unknown(), {
    then: Joi.object({
      isbn_number: baseStringRule,
      authors: Joi.array().items(baseStringRule).min(1),
      condition: baseStringRule.valid(...Object.values(BookConditionEnum)),
      status: baseStringRule.valid("marked as sold", "deleted"),
    }),
  })
  .when(Joi.object({ kind: "TutorItem" }).unknown(), {
    then: Joi.object({
      highestQualification: baseStringRule.valid(...Object.values(QualificationTypeEnum)),
      targetClasses: baseStringRule.valid(...Object.values(TargetClassTypeEnum)),
      experience: baseStringRule.valid(...Object.values(ExperienceTypeEnum)),
      website: baseStringRule.allow("", null).optional(),
    }),
  })
  .when(Joi.object({ kind: "EventItem" }).unknown(), {
    then: Joi.object({
      eventStartDate: Joi.date(),
      eventEndDate: Joi.date(),
      eventMode: baseStringRule.valid(...Object.values(EventModeEnum)),
      website: baseStringRule.optional(),
    }),
  })
  .when(Joi.object({ kind: "SchoolItem" }).unknown(), {
    then: Joi.object({
      schoolType: baseStringRule.valid(...Object.values(SchoolTypeEnum)),
      classesOffered: baseStringRule.valid(...Object.values(ClassesOffered)),
      website: baseStringRule.optional(),
    }),
  })
  .when(Joi.object({ kind: "ExtracurricularActivityItem" }).unknown(), {
    then: Joi.object({
      activityType: baseStringRule.valid(...Object.values(ActivityTypeEnum)),
      frequency: baseStringRule.valid(...Object.values(FrequencyEnum)),
      targetStudents: baseStringRule.min(1),
    }),
  })
  .when(Joi.object({ kind: "ScholarshipAwardItem" }).unknown(), {
    then: Joi.object({
      scholarshipType: baseStringRule,
      eligibilityCriteria: baseStringRule.min(1),
      website: baseStringRule,
    }),
  })
  .required()
  .unknown(false);

const itemBookmarkSchema = Joi.object({
  itemId: baseStringRule.required(),
})
  .required()
  .unknown(false);

const addReviewValidate = Joi.object({
  userId: baseStringRule.required(),
  comment: baseStringRule.required(),
  rating: Joi.number().required(),
})
  .required()
  .unknown(false);

const searchBooksSchema = Joi.object({
  filters: Joi.object({
    keyword: baseStringRule.optional(),
    minPrice: baseMinZeroRule.optional(),
    maxPrice: baseMinZeroRule.optional(),
    category: Joi.array().items(baseStringRule).optional(),
    subCategory: Joi.array().items(baseIdRule).optional(),
    subSubCategory: Joi.array().items(baseIdRule).optional(),
    subSubSubCategory: Joi.array().items(baseIdRule).optional(),
    authors: Joi.array().items(baseStringRule).optional(),
    parish: Joi.array().items(Joi.string()).optional(),
    coordinates: Joi.array()
      .ordered(
        Joi.number().min(-180).max(180).required(), // longitude
        Joi.number().min(-90).max(90).required() // latitude
      )
      .length(2)
      .optional(),
    maxDistanceInKm: Joi.number()
      .min(1)
      .max(10 * 1000)
      .optional(),
  })
    .required()
    .unknown(false),

  sort: Joi.object({
    createdAt: baseStringRule.valid(1, -1),
    price: baseStringRule.valid(1, -1),
  })
    .required()
    .max(1)
    .unknown(false),
})
  .required()
  .unknown(false);

const searchBookQuerySchemaPublic = paginationQuerySchema
  .keys({
    userId: objectIdRule,
  })
  .unknown(false);

const getAutoCompleteItemsQuerySchema = Joi.object({
  pageSize: pageSizeRule,
  page: pageNoRule,
  searchTerm: baseStringRule.required(),
}).unknown(false);

const userIdParam = Joi.object({
  userId: baseIdRule.required(),
})
  .required()
  .unknown(false);

module.exports = {
  createItemSchema,
  updateItemSchema,
  idParamSchema,
  itemBookmarkSchema,
  addReviewValidate,
  searchBooksSchema,
  searchBookQuerySchemaPublic,
  userIdParam,
  getAutoCompleteItemsQuerySchema,
};
