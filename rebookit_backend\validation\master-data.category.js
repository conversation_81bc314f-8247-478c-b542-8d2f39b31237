const Joi = require("joi");
const { baseIdRule } = require("./rules");
const categoryModel = require("../tables/schema/category");
const { BadRequestError } = require("../common/customErrors");

const categorySchema = Joi.object({
  name: Joi.string().required(),
  image: Joi.string(),
})
  .required()
  .unknown(false);

const subCategorySchema = Joi.object({
  name: Joi.string().required(),
  image: Joi.string(),
  categoryId: baseIdRule,
})
  .required()
  .unknown(false);

const subSubCategorySchema = Joi.object({
  name: Joi.string().required(),
  image: Joi.string(),
  subCategoryId: baseIdRule,
})
  .required()
  .unknown(false);

const subSubSubCategorySchema = Joi.object({
  name: Joi.string().required(),
  image: Joi.string(),
  subSubCategoryId: baseIdRule,
})
  .required()
  .unknown(false);

const editCategorySchema = Joi.object({
  category: baseIdRule,
  name: Joi.string().optional(),
  image: Joi.string().optional(),
})
  .required()
  .unknown(false);

const editSubCategorySchema = Joi.object({
  categoryId: baseIdRule.optional(),
  name: Joi.string().optional(),
  image: Joi.string().optional(),
})
  .required()
  .unknown(false);

const editSubSubCategorySchema = Joi.object({
  subCategoryId: baseIdRule.optional(),
  name: Joi.string().optional(),
  image: Joi.string().optional(),
})
  .required()
  .unknown(false);

const editSubSubSubCategorySchema = Joi.object({
  subSubCategoryId: baseIdRule.optional(),
  name: Joi.string().optional(),
  image: Joi.string().optional(),
})
  .required()
  .unknown(false);

module.exports = {
  categorySchema,
  subCategorySchema,
  subSubCategorySchema,
  subSubSubCategorySchema,

  editCategorySchema,
  editSubCategorySchema,
  editSubSubCategorySchema,
  editSubSubSubCategorySchema
};
