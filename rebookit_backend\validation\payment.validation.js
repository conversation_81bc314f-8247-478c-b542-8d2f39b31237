const Joi = require("joi");
const { PaymentStatusEnum } = require("../common/Enums");
const { paginationQuerySchema } = require("./common-schema");

const paymentIntentSchema = Joi.object({
  planId: Joi.string().length(24).hex().message(`"planId" must be a valid MongoDB ObjectId`).required()})
  .required()
  .unknown(false);

const queryPaymentsQuerySchema = paginationQuerySchema.keys({
  minAmt: Joi.number().min(1).max(100000000000),
  maxAmt:Joi.number().min(1).max(100000000000),
  fromDate: Joi.date(),
  toDate: Joi.date(),
  status: Joi.string().valid( ...Object.values( PaymentStatusEnum )),
  sortBy: Joi.string().valid("amount","status","createdAt"),
  order:Joi.string().valid("asc","desc")
}).unknown(false);

module.exports = {
  paymentIntentSchema,
  queryPaymentsQuerySchema
};
