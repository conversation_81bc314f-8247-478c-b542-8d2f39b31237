const Joi = require("joi");

const otpRule = Joi.number().integer().required();
const objectIdRule = Joi.string().hex().length(24);
const pageSizeRule = Joi.number().integer().min(1).max(50).default(10).messages({
    "number.base": `"pageSize" must be a number`,
    "number.integer": `"pageSize" must be an integer`,
    "number.min": `"pageSize" must be at least {#limit}`,
    "number.max": `"pageSize" cannot exceed {#limit}`,
  })
const pageNoRule = Joi.number().integer().min(1).default(1).messages({
    "number.base": `"page" must be a number`,
    "number.integer": `"page" must be an integer`,
    "number.min": `"page" must be at least {#limit}`,
  });
const searchTermRule =  Joi.string().trim().min(1).max(100).optional().messages({
    "string.base": `"searchTerm" must be a string`,
    "string.empty": `"searchTerm" cannot be empty`,
    "string.max": `"searchTerm" cannot exceed {#limit} characters`,
  });

// DRY Rules

const emailRule = Joi.string().email().min(3).max(50).required();
const passwordRule = Joi.string()
  .min(8)
  .max(30)
  .pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/)
  .required()
  .messages({
    "string.empty": "Password cannot be empty.",
    "string.min": "Password must be at least 8 characters long.",
    "string.max": "Password must not exceed 30 characters.",
    "string.pattern.base":
      "Password must include at least one uppercase letter, one lowercase letter, one number, and one special character.",
    "any.required": "Password is required.",
  });

const baseIdRule = Joi.string().hex().length(24);
const baseStringRule = Joi.string();
const commentRule = Joi.string().max(1000).optional();
const codeTypeRule = Joi.string().valid("verification", "verification_on_listing", "verification_on_update").required();
const nameRequiredRule = Joi.string().min(2).max(50).required();
const baseMinZeroRule = Joi.number().min(0);
const baseMinOneRule = Joi.number();
const boolOptionalRule = Joi.boolean().optional();
const boolRequiredRule = Joi.boolean().required();
const trimStringRule = Joi.string().trim();

module.exports = {
  otpRule,
  objectIdRule,
  pageSizeRule,
  pageNoRule,
  searchTermRule,
  emailRule,
  passwordRule,
  baseIdRule,
  baseStringRule,
  commentRule,
  codeTypeRule,
  nameRequiredRule,
  baseMinZeroRule,
  boolOptionalRule,
  boolRequiredRule,
  trimStringRule,
  baseMinOneRule
};
