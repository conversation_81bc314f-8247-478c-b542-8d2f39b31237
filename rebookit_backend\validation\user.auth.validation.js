const Joi = require("joi");
const {
  otpRule,
  emailRule,
  passwordRule,
  baseStringRule,
  baseIdRule,
  commentRule,
  codeTypeRule,
  nameRequiredRule,
  baseMinOneRule,
} = require("./rules");
const { UserStatusEnum } = require("../common/Enums");

const registerUserSchema = Joi.object({
  firstName: nameRequiredRule,
  lastName: Joi.string().max(50).allow("", null),
  email: emailRule,
  password: passwordRule,
  location: Joi.object({
    type: baseStringRule.required(),
    coordinates: Joi.array().min(2).max(2).items(Joi.number()),
  }).required(),
})
  .required()
  .unknown(false);

const loginSchema = Joi.object({
  email: emailRule,
  password: Joi.string().min(8).max(30).required(),
})
  .required()
  .unknown(false);

const forgotPasswordOtpSchema = Joi.object({
  email: emailRule,
})
  .required()
  .unknown(false);

const verifyForgotPasswordOtpSchema = Joi.object({
  otp: otpRule,
  email: emailRule.required(),
})
  .required()
  .unknown(false);

const sendOtpSchema = Joi.object({
  email: emailRule.required(),
  codeType: codeTypeRule,
})
  .required()
  .unknown(false);

const verifyOtpSchema = Joi.object({
  otp: otpRule,
  email: emailRule.required(),
  codeType: codeTypeRule,
})
  .required()
  .unknown(false);

const verifyAndCreatePassword = Joi.object({
  email: emailRule,
  otp: otpRule,
  newPassword: passwordRule,
})
  .required()
  .unknown(false);

// user edit profile
const editProfile = Joi.object({
  firstName: baseStringRule.optional(),
  lastName: baseStringRule.optional(),
  profileImage: Joi.string().uri().optional(),
  coordinates: Joi.array()
    .ordered(
      Joi.number().min(-180).max(180).required(), // longitude
      Joi.number().min(-90).max(90).required() // latitude
    )
    .length(2)
    .optional(),

  aboutMe: baseStringRule.optional(),
})
  .required()
  .unknown(false);

const updateReview = Joi.object({
  userId: baseIdRule.required(),
  reviewId: baseIdRule.required(),
})
  .required()
  .unknown(false);

const addReviewSchema = Joi.object({
  reviewer: baseIdRule.required(),
  rating: baseMinOneRule.max(5).required(),
  comment: commentRule,
})
  .required()
  .unknown(false);

const updateReviewSchema = Joi.object({
  rating: baseMinOneRule.max(5).optional(),
  comment: commentRule,
}).or("rating", "comment"); // At least one must be present

const updateUserSchema = Joi.object({
  status: baseStringRule.valid(UserStatusEnum.SUSPENDED, UserStatusEnum.ACTIVE),
}).unknown(false);

const supportMailSchema = Joi.object({
  email: emailRule,
  message: Joi.string().required(),
  subject: Joi.string().required(),
  name: Joi.string().required(),
});
const getAllsupportMailSchema = Joi.object({
  subject: Joi.string().optional(),
  name: Joi.string().optional(),
}).required().unknown(false);

const updateSupportMailSchema = Joi.object({
  status: Joi.boolean().required(),
}).required().unknown(false);
const updateSupportMailSchemaQuery = Joi.object({
  id: Joi.string().required(),
}).unknown(false);

module.exports = {
  registerUserSchema,
  loginSchema,
  forgotPasswordOtpSchema,
  verifyForgotPasswordOtpSchema,
  sendOtpSchema,
  verifyOtpSchema,
  verifyAndCreatePassword,
  editProfile,
  addReviewSchema,
  updateReviewSchema,
  updateReview,
  updateUserSchema,
  supportMailSchema,
  getAllsupportMailSchema,
  updateSupportMailSchema,
  updateSupportMailSchemaQuery
};
