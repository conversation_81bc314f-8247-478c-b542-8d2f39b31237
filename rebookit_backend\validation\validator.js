const validate = (schema, requestPart = "body") => {
  return async (req, res, next) => {
    try {
      // console.log(req[requestPart])
      const response = await schema.validateAsync(req[requestPart], {
        abortEarly: false,
        allowUnknown: false,
        stripUnknown: {
          objects: true,
        },
      });
      if (response) {
        const { error, value } = response;
        if (error) {
          throw error;
        }
      }

      next();
    } catch (error) {
      next(error);
    }
  };
};

module.exports = validate;
