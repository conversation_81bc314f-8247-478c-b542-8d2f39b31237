.aboutContainer {

    .searchContainer {
        padding: 10px;
        background: linear-gradient(to right, rgba(0, 0, 0, 1) 2%, #00000099 50%), url('/landingPage.png');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
        min-height: 43vh;


        .mainHeading {
            margin-top: 15px;
            left: 10px;
            top: 162px;
            font-style: normal;
            font-weight: 700;
            font-size: 31.504px;
            line-height: 32px;
            letter-spacing: -0.04em;
            color: #FFC72C;

            span {
                color: #fff;
            }

            p:last-child {
                font-family: 'Playfair';
                // font-size: 31px;

            }

        }
    }

    .headingWithGradient {
        background: linear-gradient(268deg, #211F54 11.09%, #0161AB 98.55%);
        background-clip: text;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }

    .image_section {
        width: 109px;
        height: 100%;


        img {
            width: 100%;
            height: 100%;
        }
    }

}


@media screen and (min-width: 425px) {
    .aboutContainer {

        .searchContainer {
            padding: 0 50px;
            // min-height: 80vh;
        }


        .image_section {
            width: 30%;
            height: 100%;
        }
    }

}

@media screen and (min-width: 768px) {
    .aboutContainer {

        .searchContainer {
            padding: 0 100px;
            min-height: 80vh;
        }

        .image_section {
            width: 100%;
            min-height: 60%;
            max-height: 60%;
            height: 100%;
        }
    }
}

@media screen and (min-width: 1024px) {
    .aboutContainer {

        .searchContainer {
            padding: 0 100px;
            min-height: 80vh;
        }

        .image_section {
            width: 100%;
            min-height: 58%;
            max-height: 58%;
            height: 100%;
        }
    }
}

@media screen and (min-width: 1440px) {
    .aboutContainer {

        .searchContainer {
            padding: 0 100px;
            min-height: 80vh;
        }

        .image_section {
            width: 100%;
            min-height: 65%;
            max-height: 65%;
            height: 100%;
        }
    }
}