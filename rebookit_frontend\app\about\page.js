import React from "react";
import aboutComponentCss from "./about.module.scss";
import dynamic from "next/dynamic";
import Image from "next/image";

const ImageSlider = dynamic(() =>
  import("../components/about/imageSlider", {
    ssr: false,
    loading: () => <div>Loading slider...</div>,
  })
);
const BuyAndSellComponent = dynamic(() =>
  import("../components/about/BuyAndSellComponent", {
    ssr: false,
  })
);

export const metadata = {
  title: "About Us",
  description: "Know About Us",
};

const benefitSection = [
  {
    title: "Students",
    text: "Looking to resell textbooks and study guides.",
    image: "/images/image_1.jpg",
  },
  {
    title: "Parents",
    text: "Whose children have outgrown early reading.",
    image: "/images/image_2.jpg",
  },
  {
    title: "Edu-preneurs",
    text: "Anyone offering services in book and Education.",
    image: "/images/image_3.jpg",
  },
  {
    title: "Book collectors",
    text: "Wishing to downsize or swap titles.",
    image: "/images/image_4.jpg",
  },
];

const aboutContinuousSection = [
  {
    title: "Quick Upload Process",
    text: "Add your book’s title, condition, price, and photo—quick, simple, and great for beginners.",
    titleColor: "#222054",
    classes: "text-[#222054] after:border-[#222054]",
  },
  {
    title: "No Hidden Charges",
    text: "No hidden fees or commissions—what you earn from selling is completely yours to keep.",
    titleColor: "#0161AB",
    classes: "text-[#0161AB] after:border-[#0161AB] lg:w-[90%]",
  },
  {
    title: "Support for All Genres",
    text: "Sell any genre—textbooks, novels, kids’ books. Every book finds its perfect reader on ReBookIt.",
    titleColor: "#C70007",
    classes: "text-[#C70007] after:border-[#C70007]",
  },
  {
    title: "Track Your Listings",
    text: "Track views, buyer chats, and sales instantly. Stay informed and in control of your listings.",
    classes: "text-[#457E42] after:border-[#457E42]",
  },
  {
    title: "Earn & Save Together",
    text: "Empowering users to earn and save money year after year with every book they list or buy.",
    classes: "text-[#222054] after:border-[#222054]",
  },
  {
    title: "Go Green with Every Read",
    text: "Promoting sustainability through book reuse because every reused book helps save resources and the planet.",
    classes: "text-[#0161AB] after:border-[#0161AB]",
  },
  {
    title: "A Learning Community",
    text: "Creating a vibrant hub for learners, readers, and educators to connect, share, and grow together.",
    classes: "text-[#C70007] after:border-[#C70007]",
  },
  {
    title: "Transparent Feedback Culture",
    small_title: "Honest reviews build trust.",
    text: "Every voice counts—your feedback shapes a better ReBookIt for all.",
    classes: "text-[#457E42] after:border-[#457E42]",
  },
];

const carouselImages = [
  "/images/carousel_img_1.jpg",
  "/images/carousel_img_2.jpg",
  "/images/carousel_img_3.jpg",
  "/images/carousel_img_4.jpg",
];

function About() {
  return (
    <div className={`${aboutComponentCss.aboutContainer} `}>
      <div className={`${aboutComponentCss.searchContainer} flex items-center`}>
        <section className="container-wrapper">
          <header className={`my-9 ${aboutComponentCss.mainHeading}`}>
            <h1 className="md:text-5xl md:font-semibold leading-normal lg:text-6xl">
              About Us
            </h1>
            <p className="mt-[10px] text-[31px] text-white md:text-4xl lg:leading-14 lg:text-5xl lg:font-bold lg:w-[75%]">
              ReBookIt connects readers through sustainable book sharing, making
              stories live again.
            </p>
          </header>
        </section>
      </div>

      <section className="mt-10 mb-5 px-2.5 smd:px-[50px] md:text-center md:px-[100px] md:my-[70px] md:text-lg lg:text-[22px] ">
        <section className="container-wrapper">
          <article>
            <p>
              At ReBookIt, we’re more than just a book-selling platform—we’re a
              movement built around sustainability, accessibility, and a love
              for reading. Whether it's a school textbook, a competitive exam
              guide, a timeless novel, or a forgotten children's storybook,
              every book deserves another chapter in someone else’s hands.
            </p>
          </article>
        </section>
      </section>

      <section className="px-2.5 mb-10 md:text-center md:px-[100px] md:mb-[70px]">
        <section className="container-wrapper">
          <h2
            className={`mb-2.5 text-[22px] font-semibold ${aboutComponentCss.headingWithGradient} md:text-3xl lg:text-4xl lg:mb-[30px]`}
          >
            Why ReBookIt Was Created?
          </h2>
          <article>
            <p className="md:text-lg lg:text-[22px]">
              We noticed a common problem: people often had stacks of unused
              books at home, and others were searching for affordable options to
              buy the same titles. ReBookIt was born out of this simple idea—to
              connect book lovers in a meaningful, easy, and eco-conscious way.
            </p>
          </article>
        </section>
      </section>

      <section className="bg-[#211F54] px-[11px] pt-7 pb-12 md:px-[100px] md:py-[50px] ">
        <section className="container-wrapper">
          <h2 className="text-[22px] leading-normal text-white font-semibold lg:leading-[61px] md:text-center md:text-3xl lg:text-[44px]">
            Who Benefits?
          </h2>

          <div className="flex flex-col gap-2.5 md:gap-6 mt-5 lg:mt-6 md:flex-row md:flex-wrap lg:flex-nowrap md:justify-center">
            {benefitSection.map((item, idx) => (
              <article
                key={item.title + idx}
                className="bg-white flex rounded-[10px] p-3.5 lg:pt-0 gap-[15px] lg:gap-3 xl:justify-between items-center h-[137px] md:flex-col md:p-0 md:w-[47%] xl:w-1/4 md:h-[300px] lg:h-[350px]"
              >
                <div className={`${aboutComponentCss.image_section}`}>
                  <Image
                    src={item.image}
                    alt={item.title}
                    className="w-full h-full object-cover rounded-[10px]"
                    width={100}
                    height={100}
                  />
                </div>
                <div className="flex flex-col gap-2 md:px-6 md:pb-3 md:h-[40%] w-[65%] md:w-[100%]">
                  <h3 className="text-lg font-semibold uppercase">
                    {item.title}
                  </h3>
                  <p className="leading-normal lg:leading-6 md:text-[15px]">
                    {item.text}
                  </p>
                </div>
              </article>
            ))}
          </div>
        </section>
      </section>

      <section className="py-10 px-2.5 md:px-[50px] md:py-[35px] lg:px-[100px] lg:py-[70px]">
        <section className="container-wrapper">
          <h2
            className={`text-[22px] font-semibold uppercase ${aboutComponentCss.headingWithGradient} mb-5 md:mb-[50px] md:text-center md:text-3xl lg:text-[44px]`}
          >
            Our Continuous Value Chain
          </h2>

          <div className="grid grid-col gap-5 md:gap-10 lg:gap-y-12 lg:gap-x-[26px] md:grid-cols-3 lg:grid-cols-4">
            {aboutContinuousSection.map((item, idx) => (
              <article
                key={item.title + idx}
                className='flex flex-col gap-2.5 relative after:content-[""] after:absolute after:left-[1px] md:after:left-[2px] lg:after:left-[2.5px] after:h-[99%] after:border-l after:border-black after:top-1/2 after:translate-y-[-50%] after:z-10'
              >
                <h3
                  className={`text-lg font-semibold uppercase pl-5 lg:pl-3.5 leading-[23px] relative after:content-[""] after:absolute after:left-0 after:h-full after:border-l-3 md:after:border-l-[5px] lg:after:border-l-6 after:top-1/2 after:translate-y-[-50%] after:z-20 md:text-xl lg:text-[26px] lg:leading-[42px] ${item.classes}`}
                >
                  {item.title}
                </h3>
                <p className="pl-5 lg:pl-[18px] md:text-[16px] lg:text-lg leading-normal lg:leading-8 md:text-[#606060]">
                  {item.small_title && (
                    <>
                      <span
                        className={`${aboutComponentCss.headingWithGradient}`}
                      >
                        {item.small_title}
                      </span>
                      <br />
                    </>
                  )}
                  {item.text}
                </p>
              </article>
            ))}
          </div>
        </section>
      </section>

      <section className="px-2.5 mb-16 lg:mb-[70px] w-full rounded-[20px]">
        <section className="container-wrapper">
          <ImageSlider imagesList={carouselImages} />
        </section>
      </section>

      <section className="px-2.5 pb-10 md:px-[50px] lg:px-[100px] lg:pb-[50px]">
        <section className="container-wrapper">
          <BuyAndSellComponent />
        </section>
      </section>
    </div>
  );
}

export default About;
