import Select, { StylesConfig } from 'react-select';
import { HiStar } from 'react-icons/hi';
import { IoMdStar } from 'react-icons/io';
import { MdArrowOutward, MdStar } from "react-icons/md";
import { FaBold } from "react-icons/fa6";
import { FaItalic } from "react-icons/fa6";
import { IoLink } from "react-icons/io5";
import { addReviewForSeller } from '../services/profile';
import { toast } from 'react-toastify';
import { useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { submitAnswer } from '../services/community';
import { userDataFromLocal } from '../utils/utils';
import { createInitialsAvatar } from '../components/InitialAvatar/CreateInitialAvatar';



export default function AddResponse({getAllanswer}) {
    const [answerdraft, setanswerdraft] = useState("")
    const [rating, setrating] = useState(null)
    const [loadingCommentBtn, setloadingCommentBtn] = useState(false)
    const router= useRouter()
    const userData = userDataFromLocal()
    console.log("userData", userData)
        const query = useSearchParams();
    
    const giveAnswer = async (e) => {
        let payload = {
            "questionId": query.get("id"),
            "answerText": answerdraft
        }
        let answerResponse = await submitAnswer(payload)
        // router.push("/")
        if(answerResponse.status==200){
            toast.success("Answer submitted Successfully")
            getAllanswer()
            setanswerdraft("")
        }
    }


    // console.log("rating", rating)
    // console.log("comment.", comment)

    return <div>
        <h1 className='md:text-[34px] font-bold md:text-3xl sm:text-[24px]'>Add Response</h1>
        <div className='border rounded-lg p-3 border-[1.5px] border-[#211F54] mt-6'>
            <div className='flex items-center'>
                <img className='w-[50px] h-[50px] rounded-full' src={userData.profileImage || createInitialsAvatar(`${userData?.firstName}  ${userData?.lastName||""}`, {
                                                                                                          bgColor: "#3f51b5",
                                                                                                          textColor: "#ffffff",
                                                                                                        }) } />
                <span className='ml-2'>{userData?.firstName}, {userData?.lastName}</span>
            </div>

            <textarea placeholder='enter commnet' value={answerdraft} onChange={(e) => {
                setanswerdraft(e.target.value)
            }} className='w-full border-b outline-none mt-3'>

            </textarea>
            <div className='flex justify-end items-end'>
                {/* <div className='flex items-center'><FaBold className='mx-2' size={20} /><FaItalic className='mx-2' size={20} /> <IoLink className='mx-2' size={30} /></div> */}
                <button
                    className="flex gap-2 items-center py-2 px-4 w-[120px] rounded-full text-white justify-center text-xs bg-[#1E2858] disabled:opacity-60"
                    onClick={giveAnswer}

                    disabled={loadingCommentBtn}
                >
                    {loadingCommentBtn ? (
                        <>
                            <svg
                                className="w-4 h-4 animate-spin text-white"
                                fill="none"
                                viewBox="0 0 24 24"
                            >
                                <circle
                                    className="opacity-25"
                                    cx="12"
                                    cy="12"
                                    r="10"
                                    stroke="currentColor"
                                    strokeWidth="4"
                                ></circle>
                                <path
                                    className="opacity-75"
                                    fill="currentColor"
                                    d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                                ></path>
                            </svg>
                            Loading...
                        </>
                    ) : (
                        'Submit'
                    )}
                </button>
            </div>
        </div>


    </div>
}