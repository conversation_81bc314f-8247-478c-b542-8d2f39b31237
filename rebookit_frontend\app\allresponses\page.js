
"use client"
import React, { useEffect, useState } from 'react'
import contactModule from "./allresponses.module.scss"
import dynamic from 'next/dynamic'
import Image from 'next/image'
import { MdOutlineMail } from "react-icons/md";
import { IoIosCall } from "react-icons/io";
import { IoLocationOutline } from "react-icons/io5";
import { FaTwitter, FaFacebook, FaYoutube, FaInstagram } from "react-icons/fa";
import AddResponse from './AddResponse';
import { useParams, useSearchParams } from 'next/navigation'
import { allAnswerOfQuestion } from '../services/community'
import moment from 'moment'
import { createInitialsAvatar } from '../components/InitialAvatar/CreateInitialAvatar'

const ImageSlider = dynamic(() => import("../components/about/imageSlider"), {
    // ssr: false,
    loading: () => <div>Loading slider...</div>
})

const BuyAndSellComponent = dynamic(() => import("../components/about/BuyAndSellComponent"), {
    // ssr: false,
})

// export const metadata = {
//     title: "About Us",
//     description: "Know About Us",
// };

function AllResponse() {

    const query = useSearchParams();
    const [allAnswers, getallAnswers] = useState([])
    const [question, setquestion] = useState("")

    const getAllanswer = async () => {
        
        console.log("query id", query.get("id"))
        let answers = await allAnswerOfQuestion(query.get("id"))
        if (answers.status == 200) {
            // allAnswerOfQuestion(query.get("id"))
            getallAnswers(answers.data.question.answers)
            setquestion(answers.data.question.title)
        }
        console.log("answers", answers)
    }
    useEffect(() => {
        getAllanswer()

    }, [])

    console.log("allAnswers", allAnswers)

    return (
        <div className={`${contactModule?.aboutContainer}`}>
            <div className={`${contactModule?.searchContainer} flex items-start justify-start`}>
                <section className='my-auto container-wrapper w-full'>
                    <header className={`my-9 ${contactModule?.mainHeading}`}>
                        <h1 className='text-3xl md:text-5xl font-semibold leading-normal lg:text-6xl'>Responses</h1>
                        <p className='mt-2 text-xl md:text-3xl lg:text-4xl lg:font-bold text-white lg:w-3/4'>
                            ReBookIt connects readers through sustainable book sharing, making stories live again.
                        </p>
                    </header>
                </section>
            </div>

            <section className='px-4 pb-10 md:px-12 lg:px-24 mt-[50px]'>
                <div className='container-wrapper'>
                    <div className='my-4 w-full'>
                        <p className='text-[26px] font-semibold'>Question</p>
                        <p className='text-[20px] font-semibold break-words'>"{question}"</p>
                    </div>
                    <p className='text-[26px] font-semibold'>All Responses</p>


                    {allAnswers.length > 0 ? <div className='my-5'>
                        {allAnswers.map((item, index) => {
                            return <div className={`my-5 ${index == allAnswers.length - 1 ? "" : "border-b border-[#DDDDDD]"} `}>
                                <p className='text-[18px] font-normal text-justify'> "{item.answerText}"</p>
                                <p className='text-[#818B9C] text-[16px]'>{moment(item.createAt).format("MMMM D, YYYY hh:mm A")}</p>
                                <div className='flex items-center my-3'>
                                    <img className='w-[50px] h-[50px] rounded-full' src={item?.askedBy?.profileImage || createInitialsAvatar(`${item?.askedBy?.firstName}  ${item?.askedBy?.lastName||""}`, {
                                                                                          bgColor: "#3f51b5",
                                                                                          textColor: "#ffffff",
                                                                                        }) } />
                                    <p className='font-medium ml-3'>{item.answeredBy.firstName} {item.answeredBy.lastName}</p>
                                </div>
                            </div>
                        })}
                    </div> : <h1 className='text-[16px] ml-2'>No Response Yet</h1>}
                </div>
            </section>

            <hr className='border-[#DDDDDD]'></hr>

            <section className='px-4 pb-10 md:px-12 lg:px-24 my-[50px]'>
                <div className='container-wrapper'>
                    <AddResponse getAllanswer={getAllanswer} />
                </div>
            </section>


            <section className='px-4 pb-10 md:px-12 lg:px-24'>
                <div className='container-wrapper'>
                    <BuyAndSellComponent />
                </div>
            </section>
        </div>
    )
}

export default AllResponse;
