.container {
  margin: 20px auto;
  padding: 10px;
  min-height: 50vh;

  hr {
    margin-top: 80px;
    background: #f2f2f2;
    opacity: 0.1;
  }

  .borderContainer {
    padding: 32px 16px;
    border-radius: 11.134px;
    border: 1px solid #eff0f6;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(9.278438568115234px);
    margin-bottom: 40px;
  }

  @media (min-width: 769px) {
    padding: 0px 100px;
    margin: 80px auto;

    .borderContainer {
      padding: 32px 16px;
      padding: 50px 60px;
    }
  }
  @media (max-width: 1129px) {
    padding: 0px;
    margin: 80px auto;

    .borderContainer {
      padding: 32px 16px;
      padding: 50px 50px;
    }
  }
  @media (max-width: 456px) {
    padding: 0px;
    margin: 0 auto;

    .borderContainer {
      padding: 32px 16px;
      //   padding: 20px 15px;
    }
  }
}
