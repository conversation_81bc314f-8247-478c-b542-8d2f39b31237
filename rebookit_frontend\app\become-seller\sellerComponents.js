"use client";

import React, {useEffect, useState} from "react";

const Stepper = dynamic(() => import("@/app/components/seller/stepper"));
const Categories = dynamic(() => import("@/app/components/seller/categories"));
const SubCategories = dynamic(() =>
  import("@/app/components/seller/subCategories")
);
const ReviewListing = dynamic(() =>
  import("@/app/components/seller/reviewListing")
);
const Celebration = dynamic(() =>
  import("@/app/components/seller/celebration")
);
const ListingForm = dynamic(() => import("@/app/components/seller/form"));
const BuyAndSellComponent = dynamic(() =>
  import("@/app/components/about/BuyAndSellComponent")
);

import sellerCss from "./seller.module.scss";
import dynamic from "next/dynamic";
import {getToken} from "../utils/utils";
import {useRouter} from "next/navigation";
import {useSelector} from "react-redux";

export default function SellerComponent() {
  const router = useRouter();

  useEffect(() => {
    const token = getToken();
    if (!token) {
      router.replace("/login"); // redirect to login if no token
    }
  }, []);

  const userList = useSelector((x) => x?.storeData?.userListing);

  // Reset scroll position to top whenever the step changes
  useEffect(() => {
    if (typeof window !== "undefined") {
      window.scrollTo({top: 0, left: 0, behavior: "auto"});
    }
  }, [userList?.currentStep]);

  return (
    <section className={`${sellerCss.container}`}>
      <section className="container-wrapper">
        <section className={`${sellerCss.borderContainer}`}>
          {userList.currentStep != 4 && (
            <>
              <div className="flex justify-center items-center w-full m-auto md:w-[70%]">
                <Stepper
                  current={userList.currentStep}
                  stepsName={[
                    "Category",
                    "Sub-Category",
                    "Fill Detail",
                    "Publish",
                  ]}
                />
              </div>

              <hr />
            </>
          )}

          {userList.currentStep == 0 && <Categories />}
          {userList.currentStep == 1 && <SubCategories s />}
          {userList.currentStep == 2 && <ListingForm />}
          {userList.currentStep == 3 && <ReviewListing />}
          {userList.currentStep == 4 && <Celebration />}
        </section>

        <section className="my-16 md:my-0 md:w-12/12">
          <BuyAndSellComponent />
        </section>
      </section>
    </section>
  );
}
