
import { useRef, useState } from "react";
import { HiStar } from "react-icons/hi";
import { IoMdStar, IoIosStarOutline } from "react-icons/io";
import { MdStar, MdStarHalf, MdStarOutline } from "react-icons/md";
import { FaBold } from "react-icons/fa6";
import { FaItalic } from "react-icons/fa6";
import { IoLink } from "react-icons/io5";
import { addReviewForSeller } from "../services/profile";
import { toast } from "react-toastify";
import { userDataFromLocal } from "../utils/utils";
import { createInitialsAvatar } from "../components/InitialAvatar/CreateInitialAvatar";

export default function AddRating({ bookState, getReviews }) {
  const [comment, setComment] = useState("");
  const [rating, setRating] = useState(0);
  const [hoverRating, setHoverRating] = useState(0);
  const [loadingCommentBtn, setLoadingCommentBtn] = useState(false);
  const userData = userDataFromLocal();

  const reviewAdd = async (e) => {
    if (!bookState?.createdBy?._id) {
      toast.error("User is not available to review");
      return;
    }
    if (comment.length < 1) {
      toast.error("Please provide a comment");
      return;
    }
    if (rating === 0) {
      toast.error("Please provide a rating");
      return;
    }

    setLoadingCommentBtn(true);
    let payload = {
      userId: bookState?.createdBy?._id,
      comment: comment,
      rating: rating,
    };

    try {
      let response = await addReviewForSeller(payload);
      if (response.status === 200) {
        toast.success("Review Added Successfully");
        setComment("");
        setRating(0);
        getReviews(bookState);
      } else {
        toast.error(response.data.message || "Something went wrong");
      }
    } catch (error) {
      toast.error("An error occurred while adding the review");
    } finally {
      setLoadingCommentBtn(false);
    }
  };

  // Create star rating component
  const renderStars = () => {
    return (
      <div className="flex items-center">
        <span className="mr-3">Rate Your Experience:</span>
        <div className="flex items-center">
          {[1, 2, 3, 4, 5].map((star) => (
            <button
              key={star}
              type="button"
              className="focus:outline-none"
              onClick={() => setRating(star)}
              onMouseEnter={() => setHoverRating(star)}
              onMouseLeave={() => setHoverRating(0)}
              aria-label={`Rate ${star} star${star !== 1 ? "s" : ""}`}
            >
              {hoverRating >= star || (!hoverRating && rating >= star) ? (
                <MdStar
                  className="text-yellow-400 transition-all duration-150"
                  size={25}
                />
              ) : (
                <MdStarOutline
                  className="text-gray-300 hover:text-yellow-300 transition-all duration-150"
                  size={25}
                />
              )}
            </button>
          ))}
        </div>
        {/* <div className="ml-3 text-lg font-semibold">
          {rating > 0 && `${rating}/5`}
        </div> */}
      </div>
    );
  };

  return (
    <div>
      <h1 className="md:text-[34px] font-bold md:text-3xl sm:text-[24px]">
        Add Review
      </h1>
      <div className="border rounded-lg p-3 border-[1.5px] border-[global_linear_gradiant] mt-3">
        <div className="flex items-center">
          <img
            className="w-[50px] h-[50px] rounded-full"
            src={
              userData?.profileImage ||
              createInitialsAvatar(`${userData?.firstName}  ${userData?.lastName}`, {
                                                      bgColor: "#3f51b5",
                                                      textColor: "#ffffff",
                                                    }) 
            }
            alt="User profile"
          />
          <span className="ml-2">
            {userData?.firstName}, {userData?.lastName}
          </span>
        </div>

        {/* Star rating component */}
        <div className="flex items-center my-4">{renderStars()}</div>

        <textarea
          placeholder="Enter your review here..."
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          className="w-full min-h-[120px] p-3 border-none rounded-md mt-4 focus:outline-none"
        />

        <div className="flex justify-end items-center mt-4">
          <button
            className="flex gap-2 items-center py-3 px-6 rounded-full text-white justify-center global_linear_gradient  transition-colors duration-200 disabled:opacity-60 disabled:cursor-not-allowed"
            onClick={reviewAdd}
            disabled={loadingCommentBtn}
          >
            {loadingCommentBtn ? (
              <div className="flex items-center">
                <svg
                  className="w-5 h-5 animate-spin text-white mr-2"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                  ></path>
                </svg>
                Submitting...
              </div>
            ) : (
              "Submit Review"
            )}
          </button>
        </div>
      </div>
    </div>
  );
}
