import React from "react";

const CircularRating = ({ rating = 4.5, maxRating = 5, size = 80 }) => {
  const strokeWidth = 8;
  const radius = (size - strokeWidth) / 2;
  const circumference = 2 * Math.PI * radius;

  // Calculate the progress as a percentage of circumference
  const progress = (rating / maxRating) * circumference;
  const progressPercent = (rating / maxRating) * 100;

  return (
    <div style={{ width: size, height: size }} className="relative">
      <svg
        width={size}
        height={size}
        className="transform -rotate-90"
      >
        {/* Background circle */}
        <circle
          stroke="#e5e7eb" // Tailwind gray-200
          fill="transparent"
          strokeWidth={strokeWidth}
          r={radius}
          cx={size / 2}
          cy={size / 2}
        />
        {/* Progress circle */}
        <circle
          stroke="#fbbf24" // Tailwind yellow-400
          fill="transparent"
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeDasharray={circumference}
          strokeDashoffset={circumference - progress}
          r={radius}
          cx={size / 2}
          cy={size / 2}
        />
      </svg>

      {/* Rating text in center */}
      <div className="absolute inset-0 flex items-center justify-center font-semibold text-xl text-yellow-600 select-none">
        {rating.toFixed(1)}
      </div>
    </div>
  );
};

export default CircularRating;
