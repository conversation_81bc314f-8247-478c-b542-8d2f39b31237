import React from "react";
import {ItemKindEnum, itemToKind, labelItemToKind} from "../config/constant";
import moment from "moment";

export default function ItemRespectiveDetails({bookState}) {
  if (bookState.__t == ItemKindEnum.BookItem) {
    return (
      <div>
        <p className="text-xs mb-5 text-[16px] md:text-[21px] leading-[27px] truncate">
          Author:{" "}
          <span className="font-medium">
            {bookState?.authors?.map((a) => a).join(", ")}
          </span>
        </p>
        <p className="text-xs mb-5 text-[16px] md:text-[21px] leading-[27px] truncate">
          ISBN: <span className="font-medium">{bookState?.isbn_number}</span>
        </p>
        <p className="text-xs  text-[16px] md:text-[21px] leading-[27px] truncate">
          Condition: <span className="font-medium">{bookState?.condition}</span>
        </p>
      </div>
    );
  } else if (bookState.__t == ItemKindEnum.TutorItem) {
    return (
      <div>
        <p className="text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]">
          {[itemToKind.TutorItem.experience]}:{" "}
          <span className="font-medium">{bookState?.experience}</span>
        </p>
        <p className="text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]">
          {[itemToKind.TutorItem.highestQualification]}:{" "}
          <span className="font-medium">{bookState?.highestQualification}</span>
        </p>
        <p className="text-xs mb-5  text-[16px] md:text-[21px] leading-[27px]">
          {[itemToKind.TutorItem.targetClasses]}:{" "}
          <span className="font-medium">{bookState?.targetClasses}</span>
        </p>
        <p className="text-xs  text-[16px] md:text-[21px] leading-[27px]">
          {[itemToKind.TutorItem.website]}:{" "}
          <span className="font-medium"><a href={bookState?.website} target="_blank">{bookState?.website}</a></span>
        </p>
      </div>
    );
  } else if (bookState.__t == ItemKindEnum.EventItem) {
    return (
      <div>
        <p className="text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]">
          {[labelItemToKind.EventItem.eventStartDate]}:{" "}
          <span className="font-medium">
            {moment(bookState?.eventStartDate).format("DD-MMM-YYYY HH:MM")}
          </span>
        </p>
        <p className="text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]">
          {[labelItemToKind.EventItem.eventEndDate]}:{" "}
          <span className="font-medium">
            {moment(bookState?.eventEndDate).format("DD-MMM-YYYY HH:MM")}
          </span>
        </p>
        <p className="text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]">
          {[labelItemToKind.EventItem.eventMode]}:{" "}
          <span className="font-medium">{bookState?.eventMode}</span>
        </p>
        <p className="text-xs  text-[16px] md:text-[21px] leading-[27px]">
          {[itemToKind.EventItem.website]}:{" "}
          <span className="font-medium"><a href={bookState?.website} target="_blank">{bookState?.website}</a></span>
        </p>
      </div>
    );
  } else if (bookState.__t == ItemKindEnum.SchoolItem) {
    return (
      <div>
        <p className="text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]">
          {[itemToKind.SchoolItem.classesOffered]}:{" "}
          <span className="font-medium">{bookState?.classesOffered}</span>
        </p>
        <p className="text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]">
          {[itemToKind.SchoolItem.schoolType]}:{" "}
          <span className="font-medium">{bookState?.schoolType}</span>
        </p>
        <p className="text-xs  text-[16px] md:text-[21px] leading-[27px]">
          {[itemToKind.SchoolItem.website]}:{" "}
          <span className="font-medium"><a href={bookState?.website} target="_blank">{bookState?.website}</a></span>
        </p>
      </div>
    );
  } else if (bookState.__t == ItemKindEnum.ScholarshipAwardItem) {
    return (
      <div>
        <p className="text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]">
          {[itemToKind.ScholarshipAwardItem.eligibilityCriteria]}:{" "}
          <span className="font-medium">{bookState?.eligibilityCriteria}</span>
        </p>
        <p className="text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]">
          {[itemToKind.ScholarshipAwardItem.scholarshipType]}:{" "}
          <span className="font-medium">{bookState?.scholarshipType}</span>
        </p>
        <p className="text-xs  text-[16px] md:text-[21px] leading-[27px]">
          {[itemToKind.ScholarshipAwardItem.website]}:{" "}
          <span className="font-medium"><a href={bookState?.website} target="_blank">{bookState?.website}</a></span>
        </p>
      </div>
    );
  } else if (bookState.__t == ItemKindEnum.ExtracurricularActivityItem) {
    return (
      <div>
        <p className="text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]">
          {[itemToKind.ExtracurricularActivityItem.activityType]}:{" "}
          <span className="font-medium">{bookState?.activityType}</span>
        </p>
        <p className="text-xs mb-5 text-[16px] md:text-[21px] leading-[27px]">
          {[itemToKind.ExtracurricularActivityItem.frequency]}:{" "}
          <span className="font-medium">{bookState?.frequency}</span>
        </p>
        <p className="text-xs  text-[16px] md:text-[21px] leading-[27px]">
          {[itemToKind.ExtracurricularActivityItem.targetStudents]}:{" "}
          <span className="font-medium">{bookState?.targetStudents}</span>
        </p>
      </div>
    );
  }
}
