"use client"
import React, { useEffect, useState } from 'react'
import communityScss from "./community.module.scss"
import dynamic from 'next/dynamic';
import { allQuestion } from '../services/community';
import { getCategories } from '../services/profile';
import ShareCard from '../components/common/ShareCard';

const BuyAndSellComponent = dynamic(() => import("@/app/components/about/BuyAndSellComponent"))
const QuestionsList = dynamic(() => import("@/app/components/community/questionsList"))

// export const metadata = {
//     title: "Community Page",
//     description: "A thriving community for our Members to engage, inspire, and make second-hand reading a shared experience.",
// };

function Community() {
    const [question, setQuestion] = useState([])
    const [isLoading, setisLoading] = useState(false)
    const [categoryState, setcategoryState] = useState([])
    const [selectedCategory, setSelectedCategory] = useState("all") // "all" is default and highlighted
    const [oldest, setoldest] = useState(false)
    const [searchText, setsearchText] = useState("")

    // Fetch questions, filter by category if not "all"
    const getQuestionFunc = async (searchedText, categoryId = selectedCategory) => {
        setisLoading(true)
        
        let searchQuery = ""
        if (searchText) {
            searchQuery += `&searchTerm=${searchText}`
        }
        // Only add categoryId if not "all" and not empty
        if (categoryId && categoryId !== "all") {
            searchQuery += `&categoryId=${categoryId}`
        }
        if (oldest) {
            searchQuery += `&sort=popularity`
        } else {
            searchQuery += `&sort=leastPopularity`
        }
        let questions = await allQuestion(searchQuery)
        if (questions.status == 200) {
            setQuestion(questions.data.data)
        }
        setisLoading(false)
    }

    // Fetch categories and add "All" at the start
    const fetchMasterCategory = async () => {
        try {
            let getCateogriesData = await getCategories()
            if (getCateogriesData?.status == 200) {
                if (getCateogriesData?.data?.categories) {
                    let state = [{ name: "Other", _id: "all" }, ...getCateogriesData?.data?.categories]
                    setcategoryState(state)
                }
            }
        }
        catch (err) {
            console.log("Err fetchMasterCategory", err)
        }
    }

    useEffect(() => {
        fetchMasterCategory()
    }, [])

    // Fetch questions when selectedCategory or oldest changes
    useEffect(() => {
        getQuestionFunc()
    }, [selectedCategory, oldest])

    // Handler for category click
    const handleCategoryClick = (catId) => {
        setSelectedCategory(catId)
    }

    console.log("selectedCategory", selectedCategory)

    return (
        <div className={`${communityScss.communityContainer}`}>

            {/* Header */}
            <div className={`${communityScss.communityHeader} flex items-center px-2.5 min-h-[38vh] md:!min-h-[82vh] md:px-[50px] lg:px-[100px]`}>
                <header className={`my-[30px]  ${communityScss.communityMainHeading}`}>
                    <h1 className='text-[22px] w-[98%] leading-normal font-semibold md:w-fit md:text-[40px] lg:text-6xl'>Our Community</h1>
                    <p className='mt-[5px] text-[30px] font-semibold leading-normal text-white md:text-4xl md:w-[90%] lg:text-[46px] lg:leading-14 lg:font-bold lg:w-[75%]'>
                        A thriving community for our Members to engage, inspire, and make second-hand reading a shared experience.
                    </p>
                </header>
            </div>

            {/* Guide Section */}
            <section className='mt-[40px] mb-[26px] px-2.5 md:px-[50px] md:mt-[50px] md:mb-[47px] lg:px-[100px] flex flex-col gap-2.5 md:justify-center md:items-center md:gap-[25px] lg:gap-[35px]'>
                <h3 className='text-[22px] font-medium capitalize leading-normal global_text_linear_gradient md:text-center md:text-[44px] lg:leading-[62px]'>Find Book Clubs, Discussion <br className='md:hidden' />
                    Topics & Helpful Guides</h3>
                <p className='text-sm leading-normal md:w-[75%] md:text-base md:leading-[26px] md:text-center'>Whether you’re a student, a bookworm, a casual reader, or a seller, our community hub is designed to connect you with like-minded people. Share your love for second-hand books, get tips, ask questions, or just explore the world of books in a whole new way.</p>
            </section>

            {/* Questions and answers list */}
            <QuestionsList
                question={question}
                isLoading={isLoading}
                setisLoading={setisLoading}
                getQuestionFunc={getQuestionFunc}
                categoryState={categoryState}
                setoldest={setoldest}
                oldest={oldest}
                setsearchText={setsearchText}
                searchText={searchText}
            />

            {/* Buy And Sell */}
            <section className='mt-[40px] mb-[40px] px-2.5 md:px-[50px] lg:px-[100px]'>
                <BuyAndSellComponent />
            </section>
        </div>
    )
}

export default Community
