export function createInitialsAvatar(name, options = {}) {
  // Default options
  const {
    size = 100,
    bgColor = "#cccccc",
    textColor = "#000000",
    shape = "circle",
  } = options;

  // Get initials from name
  const getInitials = (name) => {
    if (!name || typeof name !== "string") return "";

    const words = name
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0);
    if (words.length === 0) return "";

    if (words.length === 1) {
      return words[0].charAt(0).toUpperCase();
    }
    return `${words[0].charAt(0)}${words[words.length - 1].charAt(
      0
    )}`.toUpperCase();
  };

  const initials = getInitials(name);
  const fontSize = size * 0.4;

  // Create SVG based on shape
  let svgContent;
  if (shape === "circle") {
    const radius = size / 2;
    svgContent = `
      <circle cx="${radius}" cy="${radius}" r="${radius}" fill="${bgColor}" />
      <text x="50%" y="50%" dy="0.35em" text-anchor="middle" 
            font-family="Arial" font-size="${fontSize}" 
            fill="${textColor}" font-weight="bold">
        ${initials}
      </text>
    `;
  } else {
    svgContent = `
      <rect width="100%" height="100%" fill="${bgColor}" />
      <text x="50%" y="50%" dy="0.35em" text-anchor="middle" 
            font-family="Arial" font-size="${fontSize}" 
            fill="${textColor}" font-weight="bold">
        ${initials}
      </text>
    `;
  }

  // Create full SVG
  const svg = `
    <svg xmlns="http://www.w3.org/2000/svg" 
         width="${size}" 
         height="${size}" 
         viewBox="0 0 ${size} ${size}">
      ${svgContent}
    </svg>
  `;

  // Convert to data URL
  return `data:image/svg+xml,${encodeURIComponent(svg)}`;
}
