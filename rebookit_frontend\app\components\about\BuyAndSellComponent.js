import Link from "next/link";
import React from "react";

function BuyAndSellComponent() {
  return (
    <section
      className="rounded-[20px] px-5 pt-[31px] pb-7 md:py-3 md:px-12 lg:px-16 flex flex-col md:flex-row md:items-center justify-between"
      style={{
        background: "linear-gradient(268deg, #211F54 11.09%, #0161AB 98.55%)",
      }}
    >
      <div>
        <h3 className="mb-2.5 text-white text-[22px] md:text-[34px] font-semibold  leading-normal lg:leading-[33px]">
          Buy and sell your books in your community
        </h3>
        <p className="mb-5 font-light text-white text-[14px]">
          Connect with readers and sellers around you.
        </p>
        <Link href={"/become-seller"}>
          <button
            type="button"
            aria-label="Sell your book through ReBookIt community platform"
            className="cursor-pointer w-[150px] text-[12.5px] py-2.5 px-8 text-center font-semibold bg-white text-black rounded-full mb-7 md:mb-0"
          >
            Sell book
          </button>
        </Link>
      </div>

      <figure className="flex justify-center items-center h-[250px]">
        <img
          src={"/images/book_img.png"}
          alt="Illustration of an open book for community book trading"
          className="h-full"
        />
      </figure>
    </section>
  );
}

export default BuyAndSellComponent;
