"use client";

import React from 'react';
import { useElementWidth } from "../common/WidthChecker";
import dynamic from 'next/dynamic';

const Slider = dynamic(() => import("../../components/common/Slider"), {
    ssr: false,
    loading: () => <div>Loading slider...</div>
});

const Marquee = dynamic(() => import("../../components/common/Marquee"), {
    ssr: false,
    loading: () => <div>Loading slider...</div>
});

function ImageSlider(props) {
    const { imagesList = [] } = props;
    const [ref, width] = useElementWidth();

    const settings = {
        dots: true,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 5000,
        appendDots: dots => {
            const activeIndex = dots.findIndex(dot => dot.props.className.includes('slick-active'));
            let visibleDots = [];

            if (dots.length <= 3) {
                visibleDots = dots;
            } else {
                const start = Math.max(0, Math.min(activeIndex - 1, dots.length - 3));
                visibleDots = dots.slice(start, start + 3);
            }

            return (
                <div className="mt-4 w-full">
                    <ul className="flex justify-center gap-5">{visibleDots}</ul>
                </div>
            );
        },
        customPaging: i => (
            <div className="w-[40px] h-2 bg-gray-500 rounded-full opacity-60 hover:opacity-100 transition" />
        ),
    };

    return (
        <div ref={ref} className="w-full mx-auto">
            {width < 425 ? (
                <div className="w-full px-4">
                    <Slider sliderSettings={settings} className="custom_slider_dots">
                        {imagesList.map((image, index) => (
                            <div key={index} className="h-[370px] rounded-[20px] overflow-hidden">
                                <img
                                    src={image}
                                    alt="carousel images"
                                    className="rounded-[20px] object-cover w-full h-full"
                                />
                            </div>
                        ))}
                    </Slider>
                </div>
            ) : (
                <div className="overflow-hidden w-full">
                    <Marquee>
                        {imagesList.map((image, index) => (
                            <div
                                key={index}
                                className="h-[360px] w-[360px] rounded-[20px] mx-3.5 flex-shrink-0"
                            >
                                <img
                                    src={image}
                                    alt="carousel images"
                                    className="rounded-[20px] object-cover w-full h-full"
                                />
                            </div>
                        ))}
                    </Marquee>
                </div>
            )}
        </div>
    );
}

export default ImageSlider;
