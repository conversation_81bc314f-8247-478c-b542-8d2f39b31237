"use client"

import Link from "next/link"
import { useState } from "react";
import { HiOutlineChatBubbleLeftRight } from "react-icons/hi2";
import { SlShareAlt } from "react-icons/sl";
import { toast } from "react-toastify";

const CommunityComponent = () => {
    const [active,setactive]=useState(0)
    const handledebondSearchItem = () => {

    }
    let tabs=["All","Responses","Answer","Questions"]
    return (
        <div>
            <div className="flex items-center justify-between flex-wrap gap-4 py-4">
                <h1 className="text-xl font-semibold text-[#211F54]">Ask Community</h1>

                <div className="flex items-center gap-4 w-[40%]">
                    {/* Search Box */}
                    <div className="flex items-center w-full bg-white rounded-full border border-[#211F54] px-2 py-1 h-[45px]">
                        <input
                            type="text"
                            placeholder="Search..."
                            autoComplete="off"
                            onChange={(e) => handledebondSearchItem(e.target.value)}
                            className="flex-grow px-4 py-1 outline-none bg-transparent text-gray-700 w-full"
                        />
                        <button
                            className="px-4  py-1 text-white text-sm  font-medium rounded-full"
                            style={{
                                backgroundImage: 'linear-gradient(to right, #211F54, #0161AB)',
                            }}
                        >
                            Search
                        </button>
                    </div>

                    {/* Add Book Button SVG */}
                    <div className="w-[130px] h-[45px] flex items-center justify-center">
                        <Link href="/become-seller" aria-label="View all book categories">
                            <svg
                                width="130"
                                height="45"
                                viewBox="0 0 164 52"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path d="M101.203 5.00005L27.0963 5.00004C14.8929 5.00004 5.00021 14.5064 5.00021 26.2331" stroke="#211F54" strokeWidth="8.34158" />
                                <path d="M159.299 26.2332C159.299 14.5064 149.441 5.00005 137.281 5.00005L63.436 5.00004" stroke="#0161AB" strokeWidth="8.34158" />
                                <path d="M5 26.2332C5 37.9599 14.8927 47.4663 27.096 47.4663H101.203" stroke="#EFDC2A" strokeWidth="8.34158" />
                                <path d="M63.4355 47.4663H137.28C149.441 47.4663 159.298 37.9599 159.298 26.2332" stroke="#0161AB" strokeWidth="8.34158" />
                                <path d="M121 47.4663L46.8933 47.4663" stroke="#4A8B40" strokeWidth="8.34158" />
                                <path d="M121 5.00001L46.8933 5" stroke="#FF0009" strokeWidth="8.34158" />
                                <rect x="9" y="7" width="110" height="39" rx="19.5" fill="white" />
                                <text x="50%" y="50%" dominantBaseline="middle" textAnchor="middle" fontSize="13" fill="#211F54" fontFamily="Poppins, sans-serif">
                                    Add book
                                </text>
                            </svg>
                        </Link>
                    </div>
                </div>
            </div>

            <div className="flex  text-[18px]  border-b border-gray-300">
                {tabs.map((item,index)=>{
                    return <div onClick={()=>{
                        setactive(index)
                    }} className={`${active==index?"bg-clip-text text-transparent bg-gradient-to-r from-[#211F54] to-[#0161AB] border-b-[2px] border-[#211F54] mr-[60px]":"mr-[60px]"} `}>{item}</div>
                })}
            </div>
            <div>
               {Array.from({length:4}).map((item)=>{
                return <div className=" my-3 grid grid-cols-1 md:grid-cols-5 gap-4 p-4 bg-[#EFEFEF] p-5  rounded-[20px]">
                    {/* Left Section */}
                    <div className="md:col-span-3">
                        <h3 className="text-[20px] font-bold">What is Rebookit?</h3>
                        <p className="my-[10px] text-[16px]">
                            The ultimate online community for connecting local sellers with buyers for the sustainable disposal of used textbooks at all academic levels...
                        </p>
                        <div className="text-[14px] text-[#7E7E7E] space-y-1">
                            <div>Posted By: Kashish Akansha</div>
                            <div>Date: 18/July/2024</div>
                            <div>Responses: 15</div>
                        </div>
                    </div>

                    {/* Right Section */}
                    <div className="md:col-span-2  p-4 flex flex-col items-center justify-center">
                        <button className="w-full md:w-[250px] flex gap-2 items-center py-2 px-4 global_linear_gradient rounded-full text-white justify-center text-[18px] md:text-[24px]" onClick={()=>toast.success("Work in progress")}>
                            <HiOutlineChatBubbleLeftRight />
                            <span>All Responses</span>
                        </button>
                        <button className="w-full md:w-[250px] border mt-3 flex gap-2 items-center py-2 px-4 rounded-full justify-center text-[18px] md:text-[24px]"  onClick={()=>toast.success("Work in progress")}>
                            <span className="flex items-center">
                                <SlShareAlt className="mr-2" /> Share
                            </span>
                        </button>
                    </div>
                </div>
               })} 


            </div>
        </div>)
}
export default CommunityComponent