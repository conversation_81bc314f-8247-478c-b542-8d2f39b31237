"use client"
import { updateItemId, updateProfileComponentIndex } from "@/app/redux/slices/storeSlice";
import { get_bookMarkItems } from "@/app/services/profile";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import { FaArrowDownLong } from "react-icons/fa6";
import { FaArrowDown } from "react-icons/fa6";
import moment from "moment";
import { subscriptionPlans } from "@/app/services/membership";


export default function Billing() {
    const dispatch = useDispatch()
    const [bookmarkData, setbookmarkData] = useState([])
    const [currentPlan,setcurrentPlan]=useState({})
    const [billingData, setbillingData] = useState([
        { invoiceId: "#12323", date: new Date(), status: "paid", price: "99", plan: "Feature Ad" },
        { invoiceId: "#456356", date: new Date(), status: "paid", price: "45", plan: "Book listing" },
        { invoiceId: "#12323", date: new Date(), status: "paid", price: "101", plan: "Feature Ad" },
        { invoiceId: "#12323", date: new Date(), status: "paid", price: "345", plan: "Membership" },
        { invoiceId: "#12323", date: new Date(), status: "paid", price: "25", plan: "Feature Ad" },
        { invoiceId: "#12323", date: new Date(), status: "cancelled", price: "76", plan: "Feature Ad" }
    ])
    const [isLoading, setisLoading] = useState(false)
    let dataTable = [
        { name: "lala Land", seller: "warisAhmad", location: "Kiston london", price: "99", image: "sample" },
        { name: "lala Land", seller: "warisAhmad", location: "Kiston london", price: "99", image: "sample" },
        { name: "lala Land", seller: "warisAhmad", location: "Kiston london", price: "99", image: "sample" },
        { name: "lala Land", seller: "warisAhmad", location: "Kiston london", price: "99", image: "sample" },
        { name: "lala Land", seller: "warisAhmad", location: "Kiston london", price: "99", image: "sample" }
    ]
    const chatNavigationHandler = (e, itemId) => {
        e.stopPropagation();
        const profileIndex = 3;
        dispatch(updateProfileComponentIndex(profileIndex))
        dispatch(updateItemIdd(itemId))
        router.push("/profile");
    }

    const fetchBookMarkItems = async () => {
        try {
            setisLoading(true)
            let data = await get_bookMarkItems()
            console.log("fetchBookMarkItems data", data)
            if (data.data) {
                setbookmarkData(data.data)
                setisLoading(false)
            }

        } catch (err) {
            setisLoading(false)
            console.log("error in fetchBookMarkItems", err)
        }
    }

    async function getSubscriptionData() {
        try {
            let subscriptionData = await subscriptionPlans()
            // let subscriptionData = await getsubscriptionPlans()
            if (subscriptionData.status == 200) {
                // setSubscriptionList(subscriptionData?.data.plans)
                setcurrentPlan(subscriptionData?.data?.currentSubscription)
            } else {
                toast.error("Something went wrong")
            }
        } catch (err) {
            toast.error("Something went wrong")
        }
    }
    console.log("currentPlan in billing",currentPlan)
    console.log("bookmarkData", bookmarkData)
    useEffect(() => {
        fetchBookMarkItems()
        getSubscriptionData()
    }, [])

    const paidCompo = (text) => {
        const isPaid = text === "paid";
        const textColor = isPaid ? "#1E9609" : "#A70909";
        const bgColor = isPaid ? "#A5FF9647" : "#FFD6D6";

        return (
            <div
                className="inline-flex items-center px-3 py-1 rounded-full"
                style={{ backgroundColor: bgColor, color: textColor }}
            >
                <div
                    className="w-[8px] h-[8px] rounded-full mr-2"
                    style={{ backgroundColor: textColor }}
                ></div>
                {text}
            </div>
        );
    };
    let logoMater = "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/38fee772-1b93-41b7-a047-140df06dd338.png"
    const Card = () => {
        return (
            <div className="bg-[#4A8B40] text-[#ECECEC] rounded-xl p-6 w-full max-w-2xl shadow-md mt-6">
                <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
                    {/* Left: MasterCard Logo */}
                    <div className="flex-shrink-0">
                        <img src={logoMater} alt="MasterCard" className="w-14 h-auto object-contain" />
                    </div>

                    {/* Middle: Card Details */}
                    <div className="flex-1 w-full">
                        <div className="text-lg font-semibold mb-1">Johnathan Doe</div>
                        <div className="text-xl tracking-widest font-mono mb-1">•••• •••• •••• 1234</div>
                        <div className="text-sm mb-1 flex">
                            <span className="font-semibold block uppercase text-[#ECECEC]/80">Expires on</span>
                            <span className="font-semibold ml-3">09/27</span>
                        </div>
                        <div className="flex items-center text-sm text-[#ECECEC]/90 mt-2">
                            <svg xmlns="http://www.w3.org/2000/svg" className="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
                                <path strokeLinecap="round" strokeLinejoin="round" d="M16 12H8m0 0l-4-4m4 4l-4 4m16-4H8" />
                            </svg>
                            <EMAIL>
                        </div>
                    </div>

                    {/* Right: Change Button */}
                    <div className="w-full md:w-auto flex justify-end">
                        <button className="bg-white text-[#4A8B40] font-semibold px-5 py-2 rounded shadow-sm w-full md:w-auto">
                            Change
                        </button>
                    </div>
                </div>
            </div>
        );
    };
    return (
        <div className="p-4 md:p-8">
            <h1 className="font-bold text-[20px] md:text-[24px] mb-4">Billing</h1>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Payment Method */}
                <div className="border border-gray-300 rounded-md overflow-hidden">
                    <div className="bg-gray-200 p-3 flex justify-between items-center text-[16px] md:text-[18px]">
                        <span className="whitespace-nowrap font-medium">Payment Method</span>
                        <button className="flex items-center gap-2 px-4 py-2 w-fit sm:w-[115px] text-sm md:text-[12px] global_linear_gradient rounded-full text-white justify-center">
                            <span>Add Card</span>
                        </button>
                    </div>
                    <div className="px-3">
                        <Card />
                    </div>
                </div>

                {/* Plan Summary */}
                <div className="border border-gray-300 rounded-md overflow-hidden">
                    <div className="bg-gray-200 p-3 flex justify-between items-center text-[16px] md:text-[18px]">
                        <span className="font-medium">Current Plan Summary</span>
                        <button className="flex items-center gap-2 px-4 py-2 w-fit sm:w-[115px] text-sm md:text-[12px] bg-[#5B89FF] rounded text-white justify-center">
                            <span>Upgrade</span>
                        </button>
                    </div>
                    <div className="p-3">
                        <div className="grid grid-cols-3 gap-4 mb-5">
                            <div>
                                <label className="block text-sm mb-1">Plan Name</label>
                                <div className="font-bold text-[18px] md:text-[22px]">{currentPlan?.planId?.planName}</div>
                            </div>
                            <div>
                                <label className="block text-sm mb-1">Billing Cycle</label>
                                <div className="font-bold text-[18px] md:text-[22px]">{currentPlan?.planId?.planMonths}</div>
                            </div>
                            <div>
                                <label className="block text-sm mb-1">Plan Cost</label>
                                <div className="font-bold text-[18px] md:text-[22px]">${currentPlan?.planId?.price}</div>
                            </div>
                        </div>

                        <div className="mt-5">
                            <div className="text-sm">Book Uploaded</div>
                            <div className="font-bold">5 out of 10</div>
                            <div className="mt-3">
                                <div className="relative h-[30px] w-full bg-gray-200 rounded-md overflow-hidden">
                                    <div className="absolute left-0 top-0 h-full w-1/2 bg-gradient-to-r from-[#0161AB] to-[#211F54]"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Invoices Table */}
            <div className="bg-white p-6 rounded-lg shadow border border-gray-200 overflow-x-auto mt-6">
                <table className="min-w-[600px] w-full text-sm">
                    <thead className="bg-gray-200 text-black">
                        <tr>
                            {["Invoice", "Billing Date", "Status", "Amount", "Plan"].map((head, idx) => (
                                <th key={idx} className="px-6 py-4 text-left font-bold">
                                    <div className="flex items-center">
                                        <span>{head}</span>
                                        <FaArrowDown className="ml-2" />
                                    </div>
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className="bg-white ">
                        {!isLoading ? (
                            billingData.length > 0 ? (
                                billingData.map((item, i) => (
                                    <tr key={i} className="border-b border-gray-200 animate-fade-in-down">
                                        <td className="px-6 py-4 whitespace-nowrap">{item.invoiceId}</td>
                                        <td className="px-6 py-4 whitespace-nowrap">{moment(item.date).format("MMM D, YYYY")}</td>
                                        <td className="px-6 py-4 whitespace-nowrap">{paidCompo(item.status)}</td>
                                        <td className="px-6 py-4 whitespace-nowrap">${item.price}</td>
                                        <td className="px-6 py-4 whitespace-nowrap">{item.plan}</td>
                                    </tr>
                                ))
                            ) : (
                                <tr>
                                    <td colSpan={5} className="text-center py-4">No Data Available</td>
                                </tr>
                            )
                        ) : (
                            <tr>
                                <td colSpan={5} className="text-center py-4">Loading...</td>
                            </tr>
                        )}
                    </tbody>
                </table>
            </div>
        </div>
    );
}