"use client";

import React, { useEffect, useState, useCallback, useRef } from "react";
import { AdvancedMarker, APIProvider, Map } from "@vis.gl/react-google-maps";
import "./mapview.scss";

import Skeleton from "react-loading-skeleton";
import "react-loading-skeleton/dist/skeleton.css";
import { ItemKindEnum } from "@/app/config/constant";
import { useRouter } from "next/navigation";
import { useDispatch, useSelector } from "react-redux";
import { updateUserCoordinates } from "@/app/redux/slices/storeSlice";

const JAMAICA_CENTER = { lat: 17.9714, lng: -76.7931 };
const JAMAICA_ZOOM = 12;

const googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_NEW_API_KEY;
const Mapview = ({ data, width, height, center ,searchCoordinates}) => {
  const [userLocation, setUserLocation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [zoom, setZoom] = useState(JAMAICA_ZOOM);
  const [locationAllowed, setLocationAllowed] = useState(false);
  const router = useRouter();
  const mapRef = useRef(null);
  const dispatch= useDispatch()
  let storeData= useSelector(state=>state.storeData)
  const locations = Array.isArray(data) ? data : [data];
console.log("storeData in map",storeData)
  const toShoInlocation = (text) => {
    if (!text) return "";
    if (text.__t == ItemKindEnum.BookItem) {
      return "Book";
    } else if (text.__t == ItemKindEnum.EventItem) {
      return "Event";
    } else if (text.__t == ItemKindEnum.ExtracurricularActivityItem) {
      return "Activity";
    } else if (text.__t == ItemKindEnum.ScholarshipAwardItem) {
      return "Award";
    } else if (text.__t == ItemKindEnum.SchoolItem) {
      return "School";
    } else if (text.__t == ItemKindEnum.TutorItem) {
      return "Tutor";
    } else {
      return "";
    }
  };

  useEffect(() => {
    
    if (navigator.geolocation) {
      
      navigator.geolocation.getCurrentPosition(
        (position) => {
          if(data?.address?.geometry){
            console.log("inside geometry")
             setUserLocation({lat:data.address.geometry.location.coordinates[1],lng:data.address.geometry.location.coordinates[0]})
          }else{
             dispatch(updateUserCoordinates({lat: position.coords.latitude,lng: position.coords.longitude}))
            setUserLocation({
              lat: position.coords.latitude,
              lng: position.coords.longitude,
            });
          }
          if(searchCoordinates){
            
            setUserLocation(searchCoordinates)
          }
          setZoom(10); // Zoom in closer to user
          setLocationAllowed(true);
          setLoading(false);
          // setUserLocation(JAMAICA_CENTER);

        },
        (error) => {
          setUserLocation(JAMAICA_CENTER);
          setZoom(JAMAICA_ZOOM);
          setLocationAllowed(false);
          setLoading(false);
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 0,
        }
      );
    } else {
      setUserLocation(JAMAICA_CENTER);
      setZoom(JAMAICA_ZOOM);
      setLocationAllowed(false);
      setLoading(false);
    }
      // if(data?.address?.geometry){
      //   setUserLocation({lat:data.address.geometry.location.coordinates[1],lng:data.address.geometry.location.coordinates[0]})
      // }else{
      //   // setUserLocation(JAMAICA_CENTER)
      // }
  }, [data?.address?.geometry,searchCoordinates]);


  if (loading) {
    return (
      <div className="loader-container">
        <Skeleton height={height} />
      </div>
    );
  }

  // useEffect(() => {
  //     if(data?.address?.geometry){
  //       setUserLocation({lat:data.address.geometry.location.coordinates[1],lng:data.address.geometry.location.coordinates[0]})
  //     }
  // }, [])
  
  console.log("userLocation",userLocation)


  return (
    <APIProvider apiKey={googleMapsApiKey}>
      
      <Map
        defaultCenter={userLocation}
        // center={searchCoordinates||storeData.userCoordinates}
        defaultZoom={zoom}
        style={{
          width: width,
          height: height,
          borderRadius: "15px",
        }}
        mapId="DEMO_MAP_ID"
        gestureHandling={"greedy"}
        options={{
          draggable: true,
          scrollwheel: true,
          clickableIcons: true,
          zoomControl: true,
          disableDoubleClickZoom: false,
        }}
        disableDefaultUI={false}
      >
        {locationAllowed && (
          <AdvancedMarker
            key="user-location"
            position={userLocation}
            title="Your Location"
          >
            <div className="custom-marker p-2 bg-blue-500 text-white rounded-full border-2 border-white shadow-lg">
              <span role="img" aria-label="You">📍</span>
            </div>
          </AdvancedMarker>
        )}

        {locations.map((list, index) => {
          const coords = list?.address?.geometry?.location?.coordinates;
          if (!coords || coords.length < 2) return null;

          return (
            <AdvancedMarker
              key={index}
              position={{
                lat: coords[1],
                lng: coords[0],
              }}
              title={list?.createdByDoc?.firstName}
            >
              <div
                className="custom-marker relative z-10 hover:z-[9999] p-3 bg-white rounded shadow-lg"
                onClick={() => router.push(`/book-detail?id=${list._id}`)}
              >
                <div className="w-fit mx-auto">
                  <img
                    className="border aspect-[3/4] mx-auto w-[50px]"
                    src={list.images[0]}
                    alt={list.title}
                  />
                </div>
                <div className="text-md mt-2">{toShoInlocation(list)}</div>
                <div className="extra-content text-md mt-2 max-w-[100px]">{list.title}</div>
                <div className="text-md mt-2">J${list?.price}</div>
                <div className="triangle absolute bottom-[-15px] left-[30%]"></div>
              </div>
            </AdvancedMarker>

          );
        })}
      </Map>
    </APIProvider>
  );
};

export default Mapview;
