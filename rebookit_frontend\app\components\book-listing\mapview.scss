.mapWrapper {
    height: 700px !important;

    .gm-style {

        .gmnoprint {
            // display: none !important;
            // top: 0;
        }

        .gm-style-mtc-bbw {
            display: none !important;
        }

        .gm-fullscreen-control {
            right: auto;
            left: 0;

            img {
                // display: none !important;
                // background: url("/public/logo.png") !important;
            }
        }


        .gm-svpc {
            display: none;
        }
    }
}

@media screen and (max-width: 426px) {
    .mapWrapper {
        height: 308px !important;
    }
}

.triangle {
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-top: 15px solid gray;
    /* triangle color */
    ;
}

// .custom-marker {
//   transition: transform 0.3s ease, box-shadow 0.3s ease;
//   transform: scale(1);
//   cursor: pointer;
//   border-radius: 12px;
// }

// .custom-marker:hover {
//   transform: scale(2.2);
//   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
//   z-index: 10;
// }

.custom-marker{
  transition: z-index 0.2s ease, transform 0.2s ease;

}
.custom-marker:hover{
    // transition: transform 0.3 ease ;
    z-index: 1000;
    transform: scale(1.4);
    img{
        max-width: fit-content !important;
        max-height: fit-content !important;
        margin: auto;
    // font-size: 1.2rem;
        
        // width: 70px;
        // object-fit: contain;

    }
}

.custom-marker .extra-content{
    opacity: 0;
    display: none;
}

.custom-marker:hover .extra-content{
    // transition: transform 0.3 ease;
    // transform: scale(2.2);
    display: block;
    opacity: 1;
    // font-size: 1.2rem;
}

