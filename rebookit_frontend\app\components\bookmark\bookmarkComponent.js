"use client"
import { updateItemId, updateProfileComponentIndex } from "@/app/redux/slices/storeSlice";
import { delete_bookMarkItem, get_bookMarkItems } from "@/app/services/profile";
import { userDataFromLocal } from "@/app/utils/utils";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";


export default function Bookmark() {
    const dispatch = useDispatch()
    const [bookmarkData, setbookmarkData] = useState([])
    const [isLoading, setisLoading] = useState(false)
    const router = useRouter()
    const [isImageLoaded, setisImageLoaded] = useState(false)
    let dataTable = [
        { name: "lala Land", seller: "warisAhmad", location: "Kiston london", price: "99", image: "sample" },
        { name: "lala Land", seller: "warisAhmad", location: "Kiston london", price: "99", image: "sample" },
        { name: "lala Land", seller: "warisAhmad", location: "Kiston london", price: "99", image: "sample" },
        { name: "lala Land", seller: "warisAhmad", location: "Kiston london", price: "99", image: "sample" },
        { name: "lala Land", seller: "warisAhmad", location: "Kiston london", price: "99", image: "sample" }
    ]
    const chatNavigationHandler = (e, itemId) => {
        e.stopPropagation();
        const profileIndex = 3;
        dispatch(updateProfileComponentIndex(profileIndex))
        dispatch(updateItemId(itemId))
        router.push("/profile/messages");
    }

    const fetchBookMarkItems = async () => {
        try {
            setisLoading(true)
            let data = await get_bookMarkItems()
            console.log("fetchBookMarkItems data", data)
            if (data.data) {
                setbookmarkData(data.data)
                setisLoading(false)
            }

        } catch (err) {
            setisLoading(false)
            console.log("error in fetchBookMarkItems", err)
        }
    }
    console.log("bookmarkData", bookmarkData)
    useEffect(() => {
        fetchBookMarkItems()
    }, [])

    async function deleteItemById(id) {
        let deleteItem = await delete_bookMarkItem(id)
        console.log("deleteItem", deleteItem)
        if (deleteItem.status == 200) {
            fetchBookMarkItems()
            toast.success("Deleted Successfully")
        } else {

        }
    }
    return (<div>
        <h1 className="font-bold text-[24px]">Bookmark</h1>
        <div>
            <div class="bg-white p-6 rounded-lg shadow border border-gray-200 overflow-x-auto">
                <table class=" w-full overflow-x-auto">
                    <thead class="bg-gray-200 text-black rounded-lg ">
                        <tr className="w-full text-[14px]">
                            <td class="px-6 py-4 text-left ">Books</td>
                            <td class="px-6 py-4 text-left ">Seller</td>
                            <td class="px-6 py-4 text-left ">Location</td>
                            <td class="px-6 py-4 text-left  whitespace-nowrap ">Quoted Price</td>
                            <td class="px-6 py-4 text-left ">Action</td>
                        </tr>
                    </thead>
                    {!isLoading ? <tbody class="bg-white">
                        {bookmarkData.length > 0 ? bookmarkData.map((item) => {
                            return <tr class="border-b border-gray-200 text-[14px]">
                                <td class="px-6 py-4 flex whitespace-nowrap"><img src={item.itemId.coverImage}
                                    className={`w-8 h-8 rounded-t-[10px] object-cover  transition-opacity duration-300 ${isImageLoaded ? "opacity-100" : "opacity-0"
                                        }`}
                                    onLoad={() => setisImageLoaded(true)}

                                /> <span className="ml-3">{item.itemId.itemName}</span></td>
                                <td class="px-6 py-4 whitespace-nowrap">{item.itemId.createdBy.firstName}</td>
                                <td class="px-6 py-4 whitespace-nowrap">{item.location || ""}</td>
                                <td class="px-6 py-4 whitespace-nowrap">${item.itemId.price}</td>
                                <td class="px-6 py-4  flex w-full">
                                    <button className="flex  items-center py-1 mr-2 w-[90px] global_linear_gradient rounded-full text-white justify-center md:text-[12px]" onClick={(e) => {
                                        e.stopPropagation()
                                        let userData = userDataFromLocal()
                                        userData = JSON.parse(userData)
                                        console.log("userData", userData)
                                        if (item._source.createdBy._id == userData._id) {
                                            toast.error("You can not send message to your self")
                                        } else {
                                            chatNavigationHandler(e, item?._id)
                                        }
                                        // if (item.itemId?.createdBy?._id == userData?._id) {
                                        //     console.log("itself user")
                                        //     // toast.success("You can not send message to your self")
                                        // } else {
                                        //     chatNavigationHandler(e,item.itemId._id )
                                        // }
                                    }}>
                                        <span>Chat</span>
                                    </button>
                                    {/* <button className=" items-center py-1 w-[90px] global_linear_gradient rounded-full text-white justify-center md:text-[12px]" onClick={(e) => { deleteItemById(item._id) }}>
                                        <span>Delete</span>
                                    </button> */}
                                </td>
                            </tr>
                        }) : <tr className="text-center"><td colSpan={5}>No Data Available</td></tr>}

                    </tbody> : <tbody><tr><td colSpan={5} className=" text-center">. . .Loading</td></tr></tbody>}
                </table>
            </div>



        </div>
    </div>)
}