
"use client";

import React, {useEffect, useState} from "react";
import Slider from "react-slick";

import bannerCss from "./banner.module.scss";

// import testImage from "@/public/test.jpeg"
// import AdBannerImage from "@/public/images/ad_banner_image.png"
import AdBannerImage from "@/public/landing/adLady.svg";
import Image from "next/image";
import Link from "next/link";
import banner1 from "../../../../public/landing/banner1.png"
import banner2 from "../../../../public/landing/banner2.png"
import banner3 from "../../../../public/landing/banner3.png"


function BannerAd({className}) {
  const [adsImages, setadsImages] = useState([]);

  useEffect(() => {
    setadsImages([AdBannerImage, AdBannerImage, AdBannerImage]);
  }, []);

  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    cssEase: "cubic-bezier(0.645, 0.045, 0.355, 1)", // Smooth transition
  };

  return (
    <div className={`${bannerCss.bannerComponent} ${className}  `}>
      <Link href="/contact-us" className="w-full h-full">
        <div className="container-wrapper h-full overflow-hidden border border-gray-200 rounded-lg">
          <div className="slider-container rounded-[20.57px] overflow-hidden h-[113px] xl:h-[262px] md:h-[180px]">
          <Slider {...settings}>
            {[banner1,banner2,banner3].map((adsImage, index) => (
              <Image
                src={adsImage}
                alt="ads image"
                sizes="20w"
                height={20}
                objectFit="cover"
                // className="rounded-md"
              />
            ))}
          </Slider>
        </div>

          
        </div>
      </Link>
    </div>
  );
}

export default BannerAd;
