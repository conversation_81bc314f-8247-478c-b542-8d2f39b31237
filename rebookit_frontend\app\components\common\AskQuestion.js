import { addQuestion } from '@/app/services/community'
import { getCategories } from '@/app/services/profile'
import React, { useEffect, useState } from 'react'
import { toast } from 'react-toastify'
import SubmitButton from './SubmitButton'
import { getToken, RedirectToLoginIfNot, userDataFromLocal } from '@/app/utils/utils'
import { useRouter } from 'next/navigation'
import { createInitialsAvatar } from '../InitialAvatar/CreateInitialAvatar'

export default function AskQuestion({ modelForAnswer, setmodelForAnswer, userAnswerForEdit, functionCallAfterSubmit }) {
    const [addQuestionInput, setaddQuestionInput] = useState("")
    const [categoryState, setcategoryState] = useState([])
    const [selectedCategory, setselectedCategory] = useState("")
    const [isLoading, setisLoading] = useState(false)
    console.log("userAnswerForEdit in model", userAnswerForEdit)
    const router= useRouter()
    let userData = userDataFromLocal()
    console.log("selectedCategory", selectedCategory)
    console.log("addQuestionInput in model", addQuestionInput)
    console.log("functionCallAfterSubmit",functionCallAfterSubmit)
    const fetchMasterCategory = async () => {
        try {
            let getCateogriesData = await getCategories()
            console.log("getCateogriesData", getCateogriesData)
            if (getCateogriesData?.status == 200) {
                if (getCateogriesData?.data?.categories) {
                    let state = [{ name: "Other", _id: "" }, ...getCateogriesData?.data?.categories]
                    setcategoryState(state)
                }
            }
            // setisLoading(false)
        }
        catch (err) {
            // setisLoading(false)
            console.log("Err fetchMasterCategory", err)
        }
    }
    // useEffect(() => {
    //   if(userAnswerForEdit){
    //     setaddQuestionInput(userAnswerForEdit)
    //   }
    //   return()=>{
    //     setaddQuestionInput("")
    //   }
    // }, [userAnswerForEdit])


    const submitQuestion = async () => {
        if(!getToken()){
            // RedirectToLoginIfNot("/",router)
            router.push("/login")
            RedirectToLoginIfNot("/community",router)
            return
        }
        setisLoading(true)
        if (modelForAnswer) {

        } else {
            let payload = { title: addQuestionInput }
            if (selectedCategory) {
                payload.categoryId = selectedCategory
            }
            let submitResponse = await addQuestion(payload)
            if (submitResponse.status == 200) {
                toast.success("Question Added Successfully")
                let docElement = document.getElementById('myModal').classList.add("hidden")
                setaddQuestionInput("")
                setselectedCategory("")
                functionCallAfterSubmit &&functionCallAfterSubmit()
                // setisLoading(false)
            }
        }
        setisLoading(false)
    }
    useEffect(() => {
        fetchMasterCategory()

        return () => {
            if (modelForAnswer) {
                setmodelForAnswer(false)
            }
        }
    }, [])

    const InnerDiv = () => {
        
        return <div onClick={()=>{
            
        }}>Submit</div>
    }
    return (
        <div>
            <div id="myModal" className=" text-[black] fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden bg-[#EAEAEA]">
                <div className="bg-[#fcfcfc]  rounded-lg w-full max-w-lg shadow-lg relative">


                    <div className="flex w-[30px] cursor-pointer  shadow shadow-lg h-[30px] justify-center absolute top-[-10px] right-[-10px] rounded-full bg-[white] " onClick={() => {
                        let docElement = document.getElementById('myModal').classList.add("hidden")
                        console.log("docElement", docElement)
                        setaddQuestionInput("")
                        setmodelForAnswer(false)
                        setisLoading(false)
                    }}>
                        <button className="text-gray-500 hover:text-red-600 text-xl font-bold">&times;</button>
                    </div>
                    <div className="py-3 bg-white rounded-lg ">
                        <h2 className="text-xl font-semibold mb-4  border-b w-fit mx-auto">{modelForAnswer ? "Answer" : "Add Question"}</h2>
                    </div>
                    {/* <!-- Three Textareas --> */}
                    <div className="flex mx-4">
                        <div><img className="w-[20px] h-[20px] rounded-full" src={userData?.profileImage||createInitialsAvatar(`${userData.firstName}  ${userData?.lastName}`, {
                                        bgColor: "#3f51b5",
                                        textColor: "#ffffff",
                                      }) } /></div>
                        <div className="ml-3">{modelForAnswer ? "Reply To Question" : "Ask  Question"}</div>
                    </div>
                    <div class=" mx-4 mt-3">
                        <textarea rows={8} maxLength={250} value={addQuestionInput} onChange={(e) => setaddQuestionInput(e.target.value)} placeholder="Type here..." className="w-full bg-[#F4F4F4] rounded-lg p-2  outline-none"></textarea>
                        {!modelForAnswer && <p className="text-bold">Select Category</p>}
                        {!modelForAnswer && <select className="w-full p-3 border rounded-lg border-[#EAEAEA] outline-none" value={selectedCategory} onChange={(e) => setselectedCategory(e.target.value)}>
                            {categoryState.map((item) => {
                                return <option value={item._id}>{item.name}</option>
                            })}
                        </select>}
                    </div>


                    {/* <!-- Action Button --> */}
                    <div class="my-2 flex justify-start mx-4">
                        {/* <div className='flex gap-3.5 mt-3 items-center justify-center md:flex-col md:justify-center md:h-full md:w-fit md:items-start md:gap-2.5'>
                            <button className='py-[7.5px] px-[30px] text-[13px] font-semibold leading-[18px] text-center text-white rounded-full global_linear_gradient md:order-2 md:text-base md:leading-[22px]'
                                onClick={submitQuestion}
                            >Submit</button>

                        </div> */}
                        <div className="max-w-[300px] mx-auto">
                            <SubmitButton isLoading={isLoading} InnerDiv={InnerDiv} type={"button"} btnAction={submitQuestion}/>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
