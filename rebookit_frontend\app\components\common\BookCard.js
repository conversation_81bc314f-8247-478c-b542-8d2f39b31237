'use client'

import { useEffect, useState } from "react"
// import featuredCss from "./featurebook.module.scss"
import testBook from "@/public/landing/testbook.png"
import Image from "next/image"

import { MdArrowOutward } from "react-icons/md";
import { LuHeart } from "react-icons/lu";
import Link from "next/link"


export default function FeaturedBooks() {
    const [book,setBooks]=useState({name:"Psycology of moeny",image:testBook,price:500})

    const tempData = [
        {
            image: testBook,
            name: "Level 3 Geography Grade 3 2022",
            price: "3500",
            link: ""
        }
        // ,
        // {
        //     image: testBook,
        //     name: "Level 3 Geography Grade 3 2022",
        //     price: "3500",
        //     link: ""
        // },
        // {
        //     image: testBook,
        //     name: "Level 3 Geography Grade 3 2022",
        //     price: "3500",
        //     link: ""
        // },
        // {
        //     image: testBook,
        //     name: "Level 3 Geography Grade 3 2022",
        //     price: "3500",
        //     link: ""
        // },

    ]

    const [featuredBooks, setFeaturedBooks] = useState([])
    useEffect(() => {
        setFeaturedBooks(tempData)
    }, [])


    return (
        <section className={` px-2.5 md:px-[50px] lg:px-[100px]`}>

            <div className="container-wrapper">
                {/* <section className="md:flex md:justify-between md:items-start"> */}
                    {/* <header>
                        <h2 className="text-[22px] leading-normal uppercase font-semibold md:text-[36px] lg:text-[48px]">Featured Books</h2>
                        <p className="mt-2.5 font-light text-xs leading-[18px] md:text-[18px] md:leading-[27px] md:w-9/12">
                            Explore stories, knowledge, and imagination with <strong>ReBookIt.</strong> Find academic essentials, timeless novels, and rare gems—all in one place.
                        </p>
                    </header> */}
{/* 
                    <footer className="hidden md:flex justify-center my-5">
                        <Link href="/" aria-label="View all book categories">
                            <svg width="178" height="72" viewBox="0 0 178 72" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M171.629 35.7285C171.629 19.31 157.778 6.00018 140.692 6.00018L36.9361 6.00017C19.8503 6.00017 5.99954 19.31 5.99954 35.7285" stroke="#211F54" strokeWidth="11.679" />
                                <path d="M171.629 35.7285C171.629 19.31 157.827 6.00018 140.802 6.00018L37.412 6.00017" stroke="#0161AB" strokeWidth="11.679" />
                                <path d="M6 35.7285C6 52.147 19.8507 65.4569 36.9365 65.4569H140.693C157.779 65.4569 171.629 52.147 171.629 35.7285" stroke="#EFDC2A" strokeWidth="11.679" />
                                <path d="M37.4121 65.4569H140.802C157.827 65.4569 171.629 52.147 171.629 35.7285" stroke="#0161AB" strokeWidth="11.679" />
                                <path d="M140.693 6L36.937 5.99999" stroke="#FF0009" strokeWidth="11.679" />
                                <path d="M140.693 65.457L36.937 65.457" stroke="#4A8B40" strokeWidth="11.679" />
                                <rect x="11.6016" y="7.93848" width="154.01" height="54.6036" rx="27.3018" fill="white" />
                                <text x="50%" y="50%" dominantBaseline="middle" textAnchor="middle" fontSize="20" fill="#211F54" fontFamily="Poppins, sans-serif">
                                    View All
                                </text>
                            </svg>
                        </Link>
                    </footer>
                </section> */}

                <div className="my-5 grid grid-cols-2 lg:grid-cols-4 flex-wrap gap-2.5 md:my-14 md:gap-8 md:justify-between lg:mt-[30px]">
                    
                        <article  className="border border-[#80808017] pt-1 px-[3px] pb-[15px] lg:border-0">
                            <div className="relative flex justify-center items-center py-1.5 bg-[#f1f1f1] lg:h-[224px] lg:py-2.5 lg:px-2.5">
                                <Image src={book.image} alt="book image" objectFit="cover" sizes="50w" className="w-1/2" />
                                <div className="h-7 w-7 p-1 rounded-full border border-gray-200 bg-white flex justify-center items-center absolute top-1 right-1 cursor-pointer">
                                    <LuHeart size={17} color="#000" className="opacity-90" />
                                </div>
                            </div>

                            <div className="pt-2 pl-2 pr-2.5 md:text-[20px] lg:pt-5 lg:pb-[28px]">
                                <p className="text-sm leading-[19px] font-semibold lg:text-[20px] lg:leading-[26px] lg:w-11/12">{book.name}</p>

                                <div className="flex justify-between items-center my-2.5">
                                    <p className="font-bold self-end">
                                        <span className="text-[#4D7906] lg:text-[20px] lg:leading-[22px]">JMD </span>${book.price}
                                    </p>
                                    <div className="cursor-pointer global_linear_gradient p-2 rounded-full flex justify-center items-center lg:h-[46px] lg:w-[46px]">
                                        <MdArrowOutward className="h-[12px] w-[12px] lg:w-[16px] lg:h-[16px]" color="#fff" />
                                    </div>
                                </div>
                            </div>
                        </article>
                    
                </div>

                {/* <footer className="flex md:hidden justify-center mb-8">
                    <nav aria-label="View all books">
                        <Link href="/" aria-label="View all book categories">
                            <svg className="cursor-pointer" width="101" height="41" viewBox="0 0 101 41" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M97.5791 20.1641C97.5791 10.8877 89.7535 3.36779 80.1002 3.36779L21.4787 3.36779C11.8254 3.36779 3.99986 10.8877 3.99986 20.1641" stroke="#211F54" strokeWidth="6.59854" />
                                <path d="M97.5791 20.1641C97.5791 10.8877 89.7812 3.36779 80.162 3.36779L21.7476 3.36779" stroke="#0161AB" strokeWidth="6.59854" />
                                <path d="M4.00049 20.1641C4.00049 29.4404 11.826 36.9603 21.4794 36.9603H80.1009C89.7542 36.9603 97.5797 29.4404 97.5797 20.1641" stroke="#EFDC2A" strokeWidth="6.59854" />
                                <path d="M21.748 36.9603H80.1624C89.7816 36.9603 97.5795 29.4404 97.5795 20.1641" stroke="#0161AB" strokeWidth="6.59854" />
                                <path d="M80.1011 3.36719L21.4796 3.36718" stroke="#FF0009" strokeWidth="6.59854" />
                                <path d="M80.1011 36.9609L21.4796 36.9609" stroke="#4A8B40" strokeWidth="6.59854" />
                                <rect x="7.16504" y="4.46094" width="87.0145" height="30.8506" rx="15.4253" fill="white" />
                                <text x="50%" y="50%" dominantBaseline="middle" textAnchor="middle" fontSize="12" fontWeight="500" fill="#211F54" fontFamily="Poppins, sans-serif">
                                    View All
                                </text>
                            </svg>
                        </Link>
                    </nav>
                </footer> */}
            </div>
        </section>

    )
}
