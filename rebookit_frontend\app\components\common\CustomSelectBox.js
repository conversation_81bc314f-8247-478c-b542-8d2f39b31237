import {useState, useEffect, useRef} from "react";
import Select, {components} from "react-select";
import {CiSearch} from "react-icons/ci";
import SampleBook from "../../../public/images/sampleBookImage.jpg";
import "./common.scss";
import {IoIosSearch} from "react-icons/io";

// Spinner loader (simple CSS spinner)
function Spinner() {
  return (
    <span
      className="inline-block w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"
      style={{verticalAlign: "middle"}}
      aria-label="Loading"
    />
  );
}

// Custom Option with image
const CustomOption = (props) => {
  const {data, innerRef, innerProps} = props;
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);

  const imageSrc = hasError
    ? "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/1c3380ac-27b4-4797-ae3d-c2891334b9cf.jpeg"
    : data.image;

  return (
    <div
      ref={innerRef}
      {...innerProps}
      className="flex items-center justify-between p-2 cursor-pointer hover:bg-[#211F54] hover:text-[#CCCCCC]"
    >
      <div className="flex items-center">
        <CiSearch />
        <span className="text-sm ml-3">{data.label}</span>
      </div>
      <div className="w-8 h-8 relative rounded">
        {!isImageLoaded && (
          <div className="absolute inset-0 bg-gray-200 animate-pulse rounded" />
        )}
        <img
          src={imageSrc}
          alt={data.label}
          className={`w-8 h-8 object-cover rounded transition-opacity duration-300 ${
            isImageLoaded ? "opacity-100" : "opacity-0"
          }`}
          onLoad={() => setIsImageLoaded(true)}
          onError={() => {
            setHasError(true);
            setIsImageLoaded(true);
          }}
        />
      </div>
    </div>
  );
};

// Custom MenuList wrapper
const MenuListHandle = ({children, ...props}) => (
  <components.MenuList {...props}>
    <div className="max-h-[200px] rounded-b-[15px] outline-none">
      {children}
    </div>
  </components.MenuList>
);

// Main component
export default function CustomSelectBox({fetchBook, sellerCss, setValue}) {
  const [options, setOptions] = useState([]);
  const [inputValue, setInputValue] = useState("");
  const wrapperRef = useRef(null);
  const [menuIsOpen, setMenuIsOpen] = useState(false);
  const selectRef = useRef(null);
  const [loading, setLoading] = useState(false);

  const fetchBookFunc = async () => {
    if (!inputValue || inputValue.trim().length < 3) return;
    setLoading(true);
    try {
      const result = await fetchBook(inputValue);
      const formattedOptions = result.map((item, index) => ({
        value: item.key || `book-${index}`,
        label: item.title,
        image: item?.cover_i
          ? `https://covers.openlibrary.org/b/id/${item.cover_i}-M.jpg`
          : SampleBook,
        ...item,
      }));
      setOptions(formattedOptions);

      if (formattedOptions.length > 0) {
        let div = document.getElementById("selectDiv");
        let inputs = div.querySelectorAll("input");
        if (inputs[0]) {
          inputs[0].focus();
        }
        setMenuIsOpen(true);
      }
    } catch (err) {
      console.error("Fetch failed", err);
    } finally {
      setLoading(false);
    }
  };

  // Trigger search on Enter as well (no button)
  useEffect(() => {
    const input = selectRef.current?.inputRef;
    if (!input) return;
    const handler = (e) => {
      if (e.key === "Enter") {
        e.preventDefault();
        if (!loading) fetchBookFunc();
      }
    };
    input.addEventListener("keydown", handler);
    return () => input.removeEventListener("keydown", handler);
  }, [loading, inputValue]);

  const handleInputChange = (value, {action}) => {
    if (action !== "input-blur" && action !== "menu-close") {
      setInputValue(value);
    }
  };

  // Debounce search when input has at least 3 characters
  useEffect(() => {
    const trimmed = (inputValue || "").trim();
    if (trimmed.length >= 3) {
      const id = setTimeout(() => {
        fetchBookFunc();
      }, 600);
      return () => clearTimeout(id);
    } else {
      // Hide menu and clear options if less than 3 chars
      setMenuIsOpen(false);
      setOptions([]);
    }
  }, [inputValue]);
  useEffect(() => {
    const handleClick = () => {
      setMenuIsOpen(false); // Change state on any click
      // console.log("User clicked somewhere on the page");
    };

    // Attach event listener
    window.addEventListener("click", handleClick);

    // Cleanup on component unmount
    return () => {
      window.removeEventListener("click", handleClick);
    };
  }, []);

  console.log("inputValue", inputValue);
  // console.log("options filled", options);
  return (
    <div className="w-full ">
      <p className="text-[18px] sm:text-[20px] md:text-[24px] font-medium leading-7 mb-4">
        Search By Book Name
      </p>

      <div className="flex flex-col md:flex-row gap-3 w-full">
        {/* Select Dropdown */}
        <div
          className="relative w-full border border-gray-400 rounded-[10px] py-1  flex items-center"
          ref={wrapperRef}
        >
          <div className="flex-1" id="selectDiv">
            <Select
              ref={(ref) => (selectRef.current = ref)}
              options={options}
              isClearable
              value={inputValue}
              inputValue={inputValue}
              onInputChange={handleInputChange}
              onChange={(e) => {
                setValue("title", e?.title);
                setValue("Description", e?.subtitle || e?.title);
                setValue("bookAuthor", e?.author_name?.[0]);
                // Clear search state completely after selecting a suggestion
                setOptions([]);
                setInputValue("");
                setMenuIsOpen(false);
              }}
              menuIsOpen={menuIsOpen}
              menuPlacement="auto"
              menuPortalTarget={document.body}
              components={{
                MenuList: MenuListHandle,
                Option: CustomOption,
                DropdownIndicator: () => null,
                IndicatorSeparator: () => null,
                ClearIndicator: () => (
                  <div
                    onClick={() => setInputValue("")}
                    className="pr-3"
                  >
                    X
                  </div>
                ),
              }}
              placeholder="Search for a book..."
              styles={{
                control: (base) => ({
                  ...base,
                  outline: "none",
                  boxShadow: "none",
                  border: "none",
                  backgroundColor: "transparent",
                  minHeight: "40px",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  whiteSpace: "nowrap",
                  cursor: "text",
                }),
                input: (base) => ({
                  ...base,
                  padding: 0,
                  margin: 0,
                  backgroundColor: "transparent",
                  width: "100%",
                  overflow: "hidden",
                  textOverflow: "ellipsis",
                  cursor: "text",
                  whiteSpace: "nowrap", // This ensures single-line, no wrap
                }),
                menu: (base) => ({
                  ...base,
                  marginTop: 0,
                  border: "1px solid #ccc",
                  borderTop: "none",
                  borderRadius: "0 0 15px 15px",
                  overflow: "hidden",
                  zIndex: 50,
                }),
                menuPortal: (base) => ({
                  ...base,
                  zIndex: 9999,
                  width: wrapperRef.current?.offsetWidth || "100%",
                }),
                menuList: (base) => ({
                  ...base,
                  paddingTop: 0,
                  paddingBottom: 0,
                  maxHeight: "200px",
                  overflowY: "auto",
                  scrollbarWidth: "thin",
                }),
              }}
              getOptionValue={(option) => option.value}
              getOptionLabel={(option) => option.label}
              classNames={{
                control: () => "pl-1.5",
                menu: () => "z-[100]",
              }}
            />
          </div>
          {/* Right-side icon / loading text */}
          <div className="absolute right-[15px] top-1/2 -translate-y-1/2 flex items-center">
            {loading ? (
              <span className="text-gray-500 text-sm animate-pulse">Searching...</span>
            ) : (
              <IoIosSearch className="text-gray-500 text-xl" />
            )}
          </div>
          {/* <div className="absolute bottom-[-22px] right-0">{inputValue.trim().length<3 && inputValue.trim().length>0?"Minimum 3 letter required":""}</div> */}
        </div>
      </div>
    </div>
  );
}
