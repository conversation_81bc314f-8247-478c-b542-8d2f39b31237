import { addQuestion, editAnswer } from '@/app/services/community'
import { getCategories } from '@/app/services/profile'
import { userDataFromLocal } from '@/app/utils/utils'
import React, { useEffect, useState } from 'react'
import { toast } from 'react-toastify'

export default function EditAnswer({ userAnswerForEdit,setuserAnswerForEdit,answerObj ,userAnswersFunc}) {

    let userData=userDataFromLocal()
    const submitQuestion = async () => {

        let payload = { answerText: userAnswerForEdit }

        let submitResponse = await editAnswer(answerObj._id, payload)
        if (submitResponse.status == 200) {
            userAnswersFunc()
            toast.success("Answer Updated Successfully")
            let docElement = document.getElementById('myAnswerModal').classList.add("hidden")
            
        }

    }
    
    return (
        <div>
            <div id="myAnswerModal" className=" text-[black] fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden bg-[#EAEAEA]">
                <div className="bg-white  rounded-lg w-full max-w-lg shadow-lg relative">
                    <div className="flex w-[30px] cursor-pointer  h-[30px] justify-center absolute top-[-10px] right-[-10px] rounded-full bg-[#FFFFFF] " onClick={() => {
                        let docElement = document.getElementById('myAnswerModal').classList.add("hidden")
                        console.log("docElement", docElement)
                        setaddQuestionInput("")
                    }}>
                        <button className="text-gray-500 hover:text-red-600 text-xl font-bold">&times;</button>
                    </div>
                    <div className="py-3 bg-white rounded-lg ">
                        <h2 className="text-xl font-semibold mb-4  border-b w-fit mx-auto">Answer</h2>
                    </div>
                    {/* <!-- Three Textareas --> */}
                    <div className="flex mx-4">
                        <div><img className="w-[20px] h-[20px] rounded-full" src={userData?.profileImage} /></div>
                        <div className="ml-3">Reply To Question</div>
                    </div>
                    <div class=" mx-4 mt-3">
                        <textarea rows={8} value={userAnswerForEdit} onChange={(e) => setuserAnswerForEdit(e.target.value)} placeholder="Type here..." className="w-full bg-[#F4F4F4] rounded-lg p-2  outline-none"></textarea>
                    </div>
                    {/* <!-- Action Button --> */}
                    <div class="my-2 flex justify-start mx-4">
                        <div className='flex gap-3.5 mt-3 items-center justify-center md:flex-col md:justify-center md:h-full md:w-fit md:items-start md:gap-2.5'>
                            <button className='py-[7.5px] px-[30px] text-[13px] font-semibold leading-[18px] text-center text-white rounded-full global_linear_gradient md:order-2 md:text-base md:leading-[22px]'
                                onClick={submitQuestion}
                            >Submit</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
