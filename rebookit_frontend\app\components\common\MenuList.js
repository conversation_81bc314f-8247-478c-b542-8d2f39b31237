import React, {useEffect, useRef, useState} from "react";
import {FaArrowRightLong} from "react-icons/fa6";
import {BsThreeDotsVertical} from "react-icons/bs";
import {DecideItemAction, editItemPayload} from "@/app/utils/utils";
import styles from "./common.scss";
import {ItemListStatusEnum} from "@/app/config/constant";
import Link from "next/link";
import {editListingPrefillData} from "@/app/redux/slices/storeSlice";
import {useDispatch} from "react-redux";
import {useRouter} from "next/navigation";
import {toast} from "react-toastify";
import {markAsSold} from "@/app/services/bookDetails";
import {USER_ROUTES} from "@/app/config/api";

export default function MenuList({item}) {
  const menuRef = useRef(null);
  const [isImageLoaded, setisImageLoaded] = useState(false);
  const [isMenuOpen, setisMenuOpen] = useState({});
  const dispatch = useDispatch();
  const router = useRouter();

  function toTitleCase(str) {
    return str
      .toLowerCase()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  }
  const customReturnStatus = (status) => {
    let cssStyle = {
      padding: "10px",
      border: "1px solid",
      borderRadius: "7px",
      backgroundColor: "#FF2F00",
      color: "white",
    };
    if (status == "accepted") {
      cssStyle = {
        padding: "10px",
        border: "1px solid",
        borderRadius: "7px",
        backgroundColor: "green",
        color: "white",
      };
      return (
        <span className="text-[#008000] text-[14px]" style={cssStyle}>
          {toTitleCase(status)}
        </span>
      );
    } else if (status == "rejected") {
      cssStyle = {
        padding: "10px",
        border: "1px solid",
        borderRadius: "7px",
        backgroundColor: "red",
        color: "white",
      };
      return (
        <span className="text-[#FF5733] text-[14px]" style={cssStyle}>
          {toTitleCase(status)}
        </span>
      );
    } else if (status == "marked as sold") {
      // cssStyle = {
      //     padding: "10px",
      //     border: "1px solid",
      //     borderRadius: "7px",
      //     backgroundColor: "#FFC300",
      //     color: "white"
      // }

      return (
        <span className="text-[#FFC300] text-[14px]" style={cssStyle}>
          {"Sold"}
        </span>
      );
    } else if ((status = ItemListStatusEnum.PENDING)) {
      cssStyle = {
        padding: "10px",
        border: "1px solid",
        borderRadius: "7px",
        backgroundColor: "#fcd403",
        color: "white",
      };
      return (
        <span className="bg-[#FFC300] text-[14px]" style={cssStyle}>
          {ItemListStatusEnum.PENDING}
        </span>
      );
    }
  };

  const MenuListComp = ({item}) => {
    let actionPayload = DecideItemAction(item.status);
    console.log("actionPayload", actionPayload);
    return (
      item.status != ItemListStatusEnum.remove && (
        <div className="z-[200] p-2 absolute right-[20px] bg-white rounded-md transition-all duration-200 transform scale-0  scale-95">
          {actionPayload.edit && (
            <div>
              <button
                className="p-2 cursor-pointer hover:bg-gray-100 w-full rounded-md"
                onClick={() => editBook(item)}
              >
                Edit
              </button>
            </div>
          )}
          {actionPayload.mark_as_sold && (
            <div>
              <button
                className="p-2 cursor-pointer hover:bg-gray-100 w-full rounded-md"
                onClick={() => markAsSoldFun(item._id)}
              >
                Mark As Sold
              </button>
            </div>
          )}
          {actionPayload.remove && (
            <div>
              <button
                className="p-2 cursor-pointer hover:bg-gray-100 w-full rounded-md"
                onClick={() => {
                  toast.success("work in progress");
                }}
              >
                Remove
              </button>
            </div>
          )}
        </div>
      )
    );
  };
  const openBookMenu = (id) => {
    document.getElementById(id).classList.remove("hidden");
    let allMenu = document.getElementsByClassName("menuList");
    console.log("allMenu", allMenu);
    // Array.from(allMenu).map((item) => {
    //     if (item.id !== id) {
    //         item.classList.add("hidden")
    //     }
    // })
  };
  const editBook = (item) => {
    let payloadToSet = editItemPayload(item);
    dispatch(editListingPrefillData(payloadToSet));
    router.push("/become-seller");
  };

  const markAsSoldFun = async (id) => {
    let response = await markAsSold(`${USER_ROUTES.MARK_AS_SOLD}/${id}`, {
      status: "marked as sold",
    });
    if (response.status == 200) {
      toast.success(response.data.message || "Marked as sold");
      getAllBooksOfuser();
    }
  };
  useEffect(() => {
    const handleClickOutside = (e) => {
      if (menuRef.current && !menuRef.current.contains(e.target)) {
        setisMenuOpen({});
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  return (
    <div className="w-full  p-2">
      <div
        className={` relative mb-6 m-auto flex flex-col   rounded-lg shadow bg-[#fcd403]`}
      >
        <div ref={menuRef}>
          <div
            className="cursor-pointer absolute right-0 flex justify-end top-3"
            onClick={(e) => {
              e.stopPropagation(); // prevent outside click firing
              setisMenuOpen(item);
              openBookMenu(item._id);
            }}
          >
            <BsThreeDotsVertical size={25} />
          </div>

          {isMenuOpen._id === item._id && (
            <MenuListComp
              item={item}
              closeMenu={() => setisMenuOpen({})} // Pass close function
            />
          )}
        </div>

        <div className="py-2 px-4 w-[180px] h-[200px] flex justify-center mx-auto">
          <img
            src={item.images[0]}
            // className="w-full h-[200px] rounded-t-[10px] object-cover"
            alt="No Preview"
            className={`boxshadow w-full mx-auto aspect-[3/4] mt-[40px] mb-[-40px] rounded-t-[10px] object-cover  transition-opacity duration-300 shadow-right ${
              isImageLoaded ? "opacity-100" : "opacity-0"
            }`}
            onLoad={() => setisImageLoaded(true)}
          />
        </div>
        {/* Content */}
        <div className="z-[100] flex flex-col justify-between h-full  rounded-[10px] bg-white p-4">
          <h3 className="text-lg font-semibold line-clamp-2  h-[53px]">
            {item.title}
          </h3>
          <div className="flex items-center justify-between">
            {customReturnStatus(item.status)}

            <Link
              href={{pathname: "/book-detail", query: {id: item._id}}}
              aria-label="View all book categories"
            >
              <button
                className={` flex items-center  bg-gradient-to-r from-[#211F54] to-[#0161AB] bg-clip-text text-transparent`}
              >
                See Details <FaArrowRightLong />
              </button>
            </Link>
          </div>
          <div className="flex justify-center py-3"></div>
        </div>
      </div>
    </div>
  );
}
