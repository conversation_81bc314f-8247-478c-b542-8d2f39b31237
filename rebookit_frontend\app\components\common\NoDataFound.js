import React from "react";

export default function NoDataFound({actionFunc, title, btntxt}) {
  return (
    <div className="text-center m-auto h-[60vh]  w-full">
      <div className="flex items-center m-auto flex-wrap min-h-[60vh] justify-center gap-4">
        <img className="" src="/icons/noBookImage.png" />
        <div className="flex flex-col">
          {/* <p className="text-[24px] font-bold text-center md:text-start ">No Book Found</p> */}
          <p className="text-[24px] font-bold text-center md:text-start ">
            {title}
          </p>

          <div className="my-4">
            <p className="text-center md:text-start text-[#838282] text-[18px] ">
              Nothing on our list yet.
            </p>
            <p className="text-center md:text-start text-[#838282] text-[18px]">
              It's never too late to change it 😊
            </p>
          </div>
          {btntxt ? (
            <div className="mt-3 text-center md:text-start">
              <button
                className="py-[18.5px] px-[30px] text-[13px] font-semibold leading-[18px] text-center text-white rounded-full global_linear_gradient md:order-2 md:text-base md:leading-[22px]"
                // onClick={() => router.push("/become-seller")}
                onClick={actionFunc}
              >
                {/* List Your Book Now */}
                {btntxt}
              </button>
            </div>
          ) : (
            ""
          )}
        </div>
      </div>
    </div>
  );
}
