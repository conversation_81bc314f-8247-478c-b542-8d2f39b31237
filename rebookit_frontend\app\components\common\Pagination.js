import React from "react";
import { FaChevronLeft } from "react-icons/fa6";
import { FaChevronRight } from "react-icons/fa6";

export default function Pagination({
  setPageSize,
  setCurrentPage,
  getListing,
  currentPage=1,
  totalPages=2,
  totalItems,
  pageSize=10,
}) {
  console.log("totalPages inpagination", totalPages);
  const handlePageChange = (number) => {
    setCurrentPage(number);
  };
  if(totalPages>1)
  return (
    <div className="flex flex-col md:flex-row items-center justify-end mt-4 gap-4">
      {/* <div className="flex items-center">
        <div>
          <span className="mr-2 text-sm">Items per page:</span>
          <select
            value={pageSize}
            onChange={(e) => {
              const newSize = parseInt(e.target.value);
              setPageSize(newSize);
              setCurrentPage(1);
              getListing(undefined, 1);
            }}
            className="border rounded px-2 py-1 text-sm"
          >
            <option value={5}>5</option>
            <option value={10}>10</option>
            <option value={20}>20</option>
            <option value={50}>50</option>
          </select>
        </div>
        <div className="text-sm ml-2">
          Showing {Math.min((currentPage - 1) * pageSize + 1, totalItems)}-
          {Math.min(currentPage * pageSize, totalItems)} of {totalItems} items
        </div>
      </div> */}
        
      <div className="flex items-center gap-2">
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={`px-3 py-1 rounded-[8px] h-[30px] border border-gray-300 text-sm ${currentPage === 1
              ? " cursor-not-allowed"
              : "hover:bg-gray-100"
            }`}
        >
          <FaChevronLeft/>
        </button>

        {Array.from({ length: totalPages }, (_, i) => {
          let pageNum;
          if (totalPages <= 5) {
            pageNum = i + 1;
          } else if (currentPage <= 3) {
            pageNum = i + 1;
          } else if (currentPage >= totalPages - 2) {
            pageNum = totalPages - 4 + i;
          } else {
            pageNum = currentPage - 2 + i;
          }

          return (
            <button
              key={pageNum}
              onClick={() => handlePageChange(pageNum)}
              className={`px-3 py-1 h-[30px] rounded-[8px] border border-gray-300 text-sm ${currentPage === pageNum
                  ? "bg-[linear-gradient(268deg,_#211f54_11.09%,_#0161ab_98.55%)] text-white"
                  : "hover:bg-gray-100"
                }`}
            >
              {pageNum}
            </button>
          );
        })}

        {totalPages > 5 && currentPage < totalPages - 2 && (
          <span className="px-2">...</span>
        )}

        {totalPages > 5 && currentPage < totalPages - 1 && (
          <button
            onClick={() => handlePageChange(totalPages)}
            className={`px-3 py-1 h-[30px] rounded-[8px] border border-gray-300 text-sm ${currentPage === totalPages
                ? "bg-blue-500 text-white"
                : "hover:bg-gray-100"
              }`}
          >
            {totalPages}
          </button>
        )}

        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={`px-3 py-1 h-[30px] rounded-[8px] border border-gray-300 text-sm ${currentPage === totalPages
              ? "bg-gray-100 cursor-not-allowed"
              : "hover:bg-gray-100"
            }`}
        >
          <FaChevronRight/>
        </button>
      </div>


    </div>
  );
  else return ""
}
