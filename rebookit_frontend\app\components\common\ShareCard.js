import React from 'react'
import { IoArrowRedo } from "react-icons/io5";
import { IoCopy } from "react-icons/io5";
import { RxCross1 } from "react-icons/rx";
import { toast } from 'react-toastify';

export default function ShareCard({url}) {
  

  // allresponses?id=684bad555ed120f70572e24c
  let urlLocation=`${window.location.origin}/${url}`
  return (
    <div id="myShareModal" className=" fixed inset-0 flex items-center justify-center  bg-opacity-10 z-50  hidden bg-[#000000bf] ">
      <div className="bg-white flex flex-col  h-[400px] ounded-lg w-full max-w-lg shadow-lg relative rounded-[20px]">

        {/* <!-- Close Button --> */}
        <div className="flex w-[50px] cursor-pointer mx-3 flex items-center h-[50px] justify-center absolute top-[-10px] right-[-20px] rounded-full bg-[#fefefe] " onClick={() => {
          let docElement = document.getElementById('myShareModal').classList.add("hidden")
          console.log("docElement", docElement)
        }}>
          {/* <button className="text-gray-500 hover:text-red-600 text-[35px] font-bold">&times;</button> */}
          <RxCross1 className='text-[#000000]'/>
        </div>
        <div className='h-[220px] flex items-center justify-center'>
          <img className='w-[50px]' src={"https://rebook-it.s3.us-east-1.amazonaws.com/uploads/49e5dbbe-fb83-49e2-8475-9ff3a11971c3.png"} />
        </div>
        {/* <IoArrowRedo className='global_linear_gradient'/> */}
        <div className='h-[220px] w-full  global_linear_gradient rounded-t-[10px] rounded-b-[20px]'>
          <div className='flex justify-center mt-4' onClick={()=>{
                 navigator.clipboard.writeText(urlLocation)
                 toast.success("URL copied")
              }}>
            <div className=' w-[50px] h-[50px] flex items-center p-3 bg-white rounded-full cursor-pointer'><IoCopy size={25} className='text-[#211F54]' /></div>

            <div className='text-white ml-4 flex items-center'>
              <div className='text-[20px]' >Copy Link</div>
            </div>

          </div>
              {/* <div className='text-center text-white mt-2'>{`${window.location.origin}/allresponses?id=${id}`}</div> */}

              <div className='text-center text-white mt-2'>{urlLocation}</div>


          <div className='flex gap-5  justify-center mt-[25px] pb-4'>
              <div className='w-[60px] h-[60px] bg-white p-2 rounded-full cursor-pointer' onClick={()=>window.open("https://www.facebook.com","_blank")}><img src={"https://rebook-it.s3.us-east-1.amazonaws.com/uploads/4cea7427-6894-463a-bff6-34aa09bc1f06.png"}/></div>
              <div className='w-[60px] h-[60px] bg-white p-2 rounded-full cursor-pointer' onClick={()=>window.open("https://www.instagram.com","_blank")}><img src={"https://rebook-it.s3.us-east-1.amazonaws.com/uploads/47f5618b-0452-4f67-b2db-6c5fec6827a1.png"}/></div>
              <div className='w-[60px] h-[60px] bg-white p-2 rounded-full cursor-pointer' onClick={()=>window.open("https://www.whatsapp.com","_blank")}><img src={"https://rebook-it.s3.us-east-1.amazonaws.com/uploads/18681b6c-c4e2-4072-9a82-be38416d1d17.png"}/></div>
              <div className='w-[60px] h-[60px] bg-white p-2 rounded-full cursor-pointer' onClick={()=>window.open("https://www.x.com","_blank")}><img src={"https://rebook-it.s3.us-east-1.amazonaws.com/uploads/8652b904-0d14-4d3e-a913-eb999d96cf06.png"}/></div>
              <div className='w-[60px] h-[60px] bg-white p-2 rounded-full cursor-pointer' onClick={()=>window.open("https://www.pintrest.com","_blank")}><img src={"https://rebook-it.s3.us-east-1.amazonaws.com/uploads/76225cab-d847-4a93-a24d-0ff3b1bdff6d.png"}/></div> 
          </div>
        </div>


      </div>
    </div>

  )
}
