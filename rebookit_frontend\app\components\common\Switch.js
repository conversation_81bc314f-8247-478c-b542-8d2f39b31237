import React from 'react';

const ToggleSwitch = ({
    checked,
    onChange,
    width = 30,
    height = 15,
    thumbSize = 12,
    top = 1.5,
    left = 2,
    classname,
}) => {
    const translateX = width - thumbSize - 2; // 2px padding from start

    return (
        <label class={`inline-flex items-center cursor-pointer ${classname}`}>
            <input type="checkbox" value="" class="sr-only peer" checked={checked} onChange={onChange} />
            <div class={`relative w-[${width}px] h-[${height}px] bg-[#9BA8B0] peer-focus:outline-none rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-[${translateX}] rtl:peer-checked:after:-translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[${top}px] after:start-[${left}px] after:bg-white after:rounded-full after:h-[${thumbSize}px] after:w-[${thumbSize}px] after:transition-all dark:border-gray-600 peer-checked:bg-blue-600 dark:peer-checked:bg-blue-600`}></div>
        </label>
    );
};

export default ToggleSwitch;
