import { useEffect, useRef, useState } from "react";

export function useElementWidth() {
    const ref = useRef(null);
    const [width, setWidth] = useState(0);
    console.log("refd",ref)
    useEffect(() => {
        const updateWidth = () => {
            if (ref.current) {
                setWidth(ref.current.offsetWidth);
            }
        };

        updateWidth(); // Initial measure

        window.addEventListener("resize", updateWidth);
        return () => window.removeEventListener("resize", updateWidth);
    }, []);

    return [ref, width];
}
