input :focus{
    outline: none;
    border: none;
    box-shadow: none;
}
input {
    outline: none;
    border: none;
    box-shadow: none;
}


.css-s9537u-control{
    border: none;
}
.css-s9537u-control:focus{
    border: none;
    outline: 0.5px;
}
.hide-scrollbar {
  /* Hide scrollbar for Chrome, Safari and Opera */
  scrollbar-width: none;           /* Firefox */
  -ms-overflow-style: none;        /* IE and Edge */

  overflow-y: auto;                  /* or scroll if you want to force scroll */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;                   /* Chrome, Safari, Opera */
}

.mybookContainer {

    .seeDetails {
        color: #0161AB;
    }

    .soldoutBtn {
        background-color: #211F54;
    }
}
.soldoutBtn {
        background-color: #211F54;
    }

    .shadow-right {
  box-shadow: 8px 0 12px -4px rgba(0, 0, 0, 0.15);
}

.boxshadow{
    box-shadow: 5px 5px 15px black;
}