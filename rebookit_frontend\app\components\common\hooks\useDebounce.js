import { useState, useEffect } from 'react';

// For API calls Using a value
export function useDebounce(value, delay) {
    const [debouncedValue, setDebouncedValue] = useState(value);

    useEffect(() => {
        const timer = setTimeout(() => {
            setDebouncedValue(value);
        }, delay);

        return () => clearTimeout(timer); // cleanup on value or delay change
    }, [value, delay]);

    return debouncedValue;
}
