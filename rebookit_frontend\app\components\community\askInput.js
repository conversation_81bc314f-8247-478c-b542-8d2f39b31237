import React, { useState } from "react";
import { useDebounce } from "../common/hooks/useDebounce";
import { useRouter } from "next/navigation";
import { getToken } from "@/app/utils/utils";

function askInput({ getQuestionFunc, openMOdelFunction, setsearchText }) {
  const router=useRouter()

  const [searchedText, setsearchedText] = useState("");

  function debounce(fn, delay) {
    let timeoutId;
    return function (...args) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => fn.apply(this, args), delay);
    };
  }
  let deboundHandler = debounce((e) => setsearchText(e.target.value), 500);
  // console.log("searchedText in ask",searchedText)
  return (
    <div>
      {/* Ask - Desktop */}
      <section className="px-2.5 md:px-[50-x] lg:px-[100px] md:flex justify-between hidden items-center">
        <div className='flex justify-between items-center border border-[#909090] shadow-["0px_1px_5px_-4px_rgba(0,0,0,0.40)"] rounded-full py-[5px] pl-3 pr-[5px] md:w-[75%] md:py-2 md:pl-[50px] md:pr-[11px] gap-5'>
          <div className="flex items-center gap-1.5 w-full">
            <input
              type="text"
              onChange={deboundHandler}
              className="text-base leading-[25px] placeholder:text-[#757575] tracking-[0.32px] focus:outline-none w-full appearance-none"
              placeholder="What do you want to ask?"
            />
          </div>

          <div className="global_linear_gradient py-2 px-[44px] text-center rounded-full flex justify-center items-center cursor-pointer text-white">
            <button
              className="text-xl leading-[40px]"
              onClick={() => getQuestionFunc(searchedText)}
            >
              Search
            </button>
          </div>
        </div>

        <div>
          <button
            className="bg-[#025FA8] text-white rounded-full px-[35px] py-[19px]"
            onClick={() => {
              if (!getToken()) {
                router.replace("/login");
                return;
              }
              openMOdelFunction && openMOdelFunction();
            }}
          >
            Ask your question
          </button>
        </div>
      </section>
    </div>
  );
}

export default askInput;
