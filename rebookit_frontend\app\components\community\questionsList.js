"use client";

import React, {useEffect, useState} from "react";
import dynamic from "next/dynamic";
import {AiOutlineSwap} from "react-icons/ai";
import {IoIosArrowDown} from "react-icons/io";
import "./questionsList.scss";
import {PiShareFill} from "react-icons/pi";
import moment from "moment";
import Link from "next/link";
import {addQuestion} from "@/app/services/community";
import {toast} from "react-toastify";
import ShareCard from "../common/ShareCard";
import AskQuestion from "../common/AskQuestion";
import { userDataFromLocal } from "@/app/utils/utils";

const SearchInput = dynamic(() =>
  import("@/app/components/community/searchInput")
);
const AskInput = dynamic(() => import("@/app/components/community/askInput"));

function questionsList({
  question,
  isLoading,
  setisLoading,
  getQuestionFunc,
  categoryState,
  setoldest,
  oldest,
  setsearchText,
}) {
  const [tabsList, setTabsList] = useState([
    "School Books",
    "College & University",
    "Fiction & Non-fiction",
    "Language & Literature",
  ]);
  const [selectedTab, setSelectedTab] = useState("all");
  const [seeMore, setSeeMore] = useState([]);
  const [searchQuestionText, setsearchQuestionText] = useState("");
  const [addQuestionInput, setaddQuestionInput] = useState("");
  const [selectedCategory, setselectedCategory] = useState(null);
  const [selectedShareId, setselectedShareId] = useState(null);

  console.log("selectedShareId", selectedShareId);
  const seeMoreDetailsHandler = (index) => {
    setSeeMore((prev) =>
      !prev.includes(index) ? [...prev, index] : prev.filter((p) => p !== index)
    );
  };
  console.log("selectedCategory", selectedCategory);
  useEffect(() => {
    if (categoryState.length > 0) {
      // setSelectedTab(categoryState[0]._id)
    }
  }, [categoryState]);

  useEffect(() => {
    if (selectedTab) {
      getQuestionFunc("", selectedTab);
    }
  }, [selectedTab]);
  const openMOdelFunction = () => {
    console.log("hii btn click");
    let docElement = document
      .getElementById("myModal")
      .classList.remove("hidden");
    console.log("docElement", docElement);
  };
  const Skeleton = (idx) => {
    return (
      <div
        key={`skeleton-${idx}}`}
        className="bg-[#FDFDFD] rounded-xl animate-pulse p-6 flex flex-col gap-4 shadow-[0_0_26.714px_0_rgba(24,24,24,0.10)]"
      >
        <div className="h-5 bg-gray-300 rounded w-3/4"></div>
        <div className="h-4 bg-gray-200 rounded w-full"></div>
        <div className="h-4 bg-gray-200 rounded w-5/6"></div>
        <div className="h-4 bg-gray-200 rounded w-2/3"></div>
        <div className="flex justify-between mt-4">
          <div className="h-4 bg-gray-300 rounded w-1/3"></div>
          <div className="h-4 bg-gray-300 rounded w-1/4"></div>
        </div>
        <div className="h-8 bg-gray-300 rounded w-1/2 mt-4"></div>
      </div>
    );
  };
  console.log("selectedTab", selectedTab);
  const submitQuestion = async () => {
    let submitResponse = await addQuestion({
      title: addQuestionInput,
      categoryId: selectedCategory,
    });
    if (submitResponse.status == 200) {
      toast.success("Question Added Successfully");
      let docElement = document
        .getElementById("myModal")
        .classList.add("hidden");
    }
  };
  console.log("categoryState", categoryState);
  let userData=userDataFromLocal()
  return (
    <div className="questionsListContainer">
      <div className="">
        <SearchInput
          getQuestionFunc={getQuestionFunc}
          openMOdelFunction={openMOdelFunction}
        />

        <AskInput
          getQuestionFunc={getQuestionFunc}
          categoryState={categoryState}
          openMOdelFunction={openMOdelFunction}
          setsearchText={setsearchText}
        />

        <section className="mt-4 md:mt-7 lg:mt-[46px] ">
          <div className="md:flex md:justify-between md:px-[50px] lg:px-[100px] md:gap-8 lg:gap-12 lg:items-center">
            <div className=" px-2.5 md:order-2 w-fit ">
              <button
                className="rounded-full flex items-center justify-around py-3.5 global_linear_gradient lg:p-6"
                onClick={() => setoldest(!oldest)}
              >
                <AiOutlineSwap className="rotate-90 w-[19px] h-[19px] fill-white" />
                <span className=" text-sm leading-4 font-medium text-white whitespace-nowrap	px-2">
                  {oldest ? "Least Popular" : "Popular Questions"}
                </span>
                {/* <IoIosArrowDown className="w-[18px] h-[18px] fill-white" /> */}
              </button>
            </div>

            <ul
              className="flex overflow-auto scrollbar-thin scrollbar-thumb-gray-500 scrollbar-track-gray-200 items-center gap-7 mt-5 px-2.5 md:order-1 md:gap-7 lg:gap-[45px]  md:mt-0 "
              style={{overflow: "auto", scrollbarColor: "red"}}
            >
              {categoryState?.map((tab, index) => (
                <li
                  key={`tab-${index}`}
                  className={`text-xs font-semibold leading-normal tracking-[0.3px] md:tracking-[0.5px] pb-[5px] whitespace-nowrap transition-all duration-300 ease-in-out md:text-sm lg:text-xl lg:tracking-[0.5px] cursor-pointer ${
                    selectedTab == tab._id
                      ? "global_text_linear_gradient gradient-bottom-border"
                      : "text-black md:text-[#AAA]"
                  }`}
                  onClick={() => setSelectedTab(tab._id)}
                >
                  {tab?.name}
                </li>
              ))}
            </ul>
          </div>

          {!isLoading ? (
            <div className="mt-7 grid grid-cols-1 gap-4 px-4 md:grid-cols-2 md:px-[100px] lg:px-[100px] lg:grid-cols-3 lg:px-12 w-full overflow-x-hidden">
              {question.length > 0 ? (
                question?.map((item, idx) => (
                  <div
                    key={`question-${idx}`}
                    className="bg-[#FDFDFD] gradient-all-round-border px-5 py-4 shadow-md lg:py-6 lg:px-6 flex flex-col justify-between max-w-full overflow-hidden"
                  >
                    <h3 className="text-base truncate leading-7 font-semibold global_text_linear_gradient lg:text-lg lg:leading-[29px] lg:tracking-[0.6px] h-[50px] overflow-hidden text-ellipsis">
                      {item.title}?
                    </h3>

                    <p className="mt-2 text-sm leading-[22px] font-light lg:text-base lg:leading-[26px]">
                      <div
                        className={`flex flex-col gap-2.5 transition-all duration-1000 linear overflow-hidden ${
                          seeMore.includes(idx)
                            ? "h-fit pb-[22px]"
                            : "max-h-[50px] pb-0"
                        }`}
                      >
                        {item?.latestAnswer?.answerText}
                      </div>
                      {item?.latestAnswer?.answerText ? (
                        <span
                          onClick={() => seeMoreDetailsHandler(idx)}
                          className="font-medium global_text_linear_gradient underline underline-offset-auto gradient-border-bottom-see-more cursor-pointer"
                        >
                          {seeMore.includes(idx) ? "See Less" : "See More"}
                        </span>
                      ) : (
                        <span>No Answer Yet</span>
                      )}
                    </p>

                    <div className="mt-2 mb-3 pt-[7px] pb-2 text-[#7E7E7E] text-xs border-t border-b border-[#F4F4F4] lg:mt-2.5 lg:mb-4 lg:py-2 lg:text-sm">
                      <div className="flex justify-between items-center">
                        <p className="leading-[17px] lg:leading-5">
                          Posted By: <span>{item.askedBy?.firstName}</span>
                        </p>
                        <p className="leading-[17px] lg:leading-5">
                          Responses: {item.totalAnswers}
                        </p>
                      </div>
                      <div className="mt-1 leading-[17px] lg:leading-5">
                        Date: {moment(item.createAt).format("DD/MMMM/YYYY")}
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <div
                        className="flex flex-row gap-2 items-center cursor-pointer"
                        onClick={() => {
                          document
                            .getElementById("myShareModal")
                            .classList.remove("hidden");
                          setselectedShareId(item._id);
                        }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="18"
                          height="19"
                          viewBox="0 0 18 19"
                          fill="none"
                          className="lg:w-[19px] lg:h-[19px]"
                        >
                          <path
                            d="M16.037 8.25674L12.6851 11.6086C..."
                            fill="url(#paint0_linear_1943_10155)"
                          />
                          <defs>
                            <linearGradient
                              id="paint0_linear_1943_10155"
                              x1="14.5799"
                              y1="5.01475"
                              x2="1.51041"
                              y2="5.47995"
                              gradientUnits="userSpaceOnUse"
                            >
                              <stop stopColor="#211F54" />
                              <stop offset="1" stopColor="#0161AB" />
                            </linearGradient>
                          </defs>
                        </svg>
                        <span className="text-sm global_text_linear_gradient">
                          Share
                        </span>
                      </div>

                      <ShareCard url={`allresponses?id=${selectedShareId}`} />

                      <Link href={`/allresponses?id=${item._id}`}>
                        <button className="py-2.5 px-6 text-xs leading-4 tracking-[0.2px] font-medium text-white global_linear_gradient rounded-full lg:py-3 lg:px-8 lg:text-[15px] lg:leading-[21px] lg:tracking-[0.3px]">
                          See All Responses
                        </button>
                      </Link>
                    </div>
                  </div>
                ))
              ) : (
                <div className="mx-auto col-span-1 md:col-span-2 md:col-span-3">
                  No question Found
                </div>
              )}
            </div>
          ) : (
            <div className="mt-7 grid grid-cols-1 gap-4 px-4 md:grid-cols-2 md:px-8 lg:grid-cols-3 lg:px-12">
              {Array.from({length: 3}).map((val, idx) => (
                <Skeleton idx={idx} key={idx} />
              ))}
            </div>
          )}
        </section>
        {userData&&<AskQuestion functionCallAfterSubmit={getQuestionFunc} />}
      </div>
    </div>
  );
}

export default questionsList;
