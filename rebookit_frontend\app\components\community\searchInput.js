import React, { useState } from 'react'
import { Lu<PERSON><PERSON>O<PERSON> } from 'react-icons/lu';
import { FiSearch } from "react-icons/fi";

function searchInput({ getQuestionFunc}) {

    const [searchedText, setsearchedText] = useState("")
    function debounce(fn, delay) {
        let timeoutId;
        return function (...args) {
            clearTimeout(timeoutId);
            timeoutId = setTimeout(() => fn.apply(this, args), delay);
        };
    }
    let deboundHandler = debounce((e) => setsearchedText(e.target.value), 500)

    return (
        <div>
            {/* If want to use for desktop, customize it, it wasn't made for desktop */}
            {/* Search - Mobile */}
            <section className='px-2.5 md:px-[50-x] lg:px-[100px] flex justify-center md:hidden'>
                <div className='w-full flex justify-between items-center border border-[#909090] shadow-[0px_1px_5px_-4px_rgba(0,0,0,0.40)] rounded-full py-[5px] pl-3 pr-[5px]'>
                    <div className='flex items-center gap-1.5 w-[70%]'>
                        <div className='bg-[#025FA8] p-[5px] rounded-full flex justify-center items-center'>
                            <LuBookOpen className='stroke-white w-[11px] h-[11px]' />
                        </div>
                        <input autoComplete='off' type='search' id='searchInput'  onChange={deboundHandler} className='text-xs placeholder:text-[#757575] italic leading-normal focus:outline-none w-full' placeholder='Search by Title, Author, ISBN, Publisher.....' />
                    </div>

                    <div className='global_linear_gradient p-[7px] rounded-full flex justify-center items-center cursor-pointer'>
                        <FiSearch className='stroke-white w-[17px] h-[17px]'  onClick={()=>getQuestionFunc(searchedText)}/>
                    </div>
                </div>
            </section>
        </div>
    )
}

export default searchInput
