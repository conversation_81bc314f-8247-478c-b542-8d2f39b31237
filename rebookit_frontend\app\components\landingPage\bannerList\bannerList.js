"use client";

import {updateProfileComponentIndex} from "@/app/redux/slices/storeSlice";
import {getToken} from "@/app/utils/utils";
import {useRouter} from "next/navigation";
import React from "react";
import {useDispatch} from "react-redux";
import unionImage from "@/public/landing/Union.png";
import starImage from "@/public/landing/star.png";
import lightImage from "@/public/landing/light.png";
import homeCss from "@/app/home.module.scss";
import Image from "next/image";
import {bookSubcategories} from "@/app/config/constant";

function bannerList() {
  const auth = getToken();
  const router = useRouter();
  const dispatch = useDispatch();

  const redirectionHandler = (link, idx) => {
    if (!auth) router.push("/login");
    else router.push(link);

    if (idx === 0) {
      dispatch(updateProfileComponentIndex(1));
    }
  };

  const bannerList1 = [
    {
      heading: "Your online marketplace for preowned books",
      text: "Connect with booksellers near you.",
      articleClass: "",
      buttonText: "Find a book",
      sideImage: unionImage,
      svg: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="177"
          height="177"
          viewBox="0 0 177 177"
          fill="none"
          className="absolute -top-[68px] -right-[55px] lg:scale-140 lg:-top-[32px] lg:-right-[45px]"
        >
          <path
            d="M89.2334 0.00683594C137.363 0.616206 176.19 39.8205 176.19 88.0928L176.184 89.2314C175.574 137.359 136.369 176.185 88.0957 176.186L86.957 176.178C39.207 175.573 0.611815 136.98 0.00683594 89.2314L0 88.0928C0 39.4406 39.4421 0 88.0957 0L89.2334 0.00683594ZM88.0957 9.91211C44.9163 9.91211 9.91211 44.9154 9.91211 88.0928C9.91222 131.27 44.9164 166.272 88.0957 166.272C131.275 166.272 166.278 131.27 166.278 88.0928C166.278 44.9155 131.275 9.91237 88.0957 9.91211ZM88.8428 30.208C120.47 30.6088 145.985 56.3712 145.985 88.0928L145.98 88.8408C145.58 120.467 119.817 145.982 88.0947 145.982L87.3457 145.978C55.9676 145.58 30.6055 120.218 30.208 88.8408L30.2031 88.0928C30.2031 56.1215 56.1225 30.2034 88.0947 30.2031L88.8428 30.208ZM88.0947 40.1152C61.5968 40.1155 40.1152 61.5963 40.1152 88.0928C40.1154 114.589 61.5969 136.069 88.0947 136.069C114.593 136.069 136.073 114.589 136.073 88.0928C136.073 61.5963 114.593 40.1155 88.0947 40.1152ZM88.6787 65.4492C100.92 65.7591 110.747 75.7785 110.747 88.0938L110.74 88.6787C110.43 100.919 100.41 110.746 88.0947 110.746L87.5098 110.738C75.4637 110.433 65.7546 100.724 65.4492 88.6787L65.4414 88.0938C65.4414 75.5834 75.584 65.4416 88.0947 65.4414L88.6787 65.4492ZM88.0947 75.3535C81.0582 75.3538 75.3535 81.0582 75.3535 88.0938C75.3535 95.1294 81.0582 100.834 88.0947 100.834C95.1313 100.834 100.835 95.1294 100.835 88.0938C100.835 81.0581 95.1313 75.3537 88.0947 75.3535Z"
            fill="white"
          />
        </svg>
      ),
      // imageClass: "translate-x-[270px] -translate-y-[90px]",
      imageClass:
        "-top-[68px] -right-[55px] lg:scale-130 lg:-top-[32px] lg:-right-[45px]",
      backgroundColor: "#211F54",
      clickFn: () =>
        router.push(
          `/search?category=${bookSubcategories.carrabian[0].categoryId}`
        ),
    },
    {
      heading: "List a book, service, event or scholarship offer",
      text: "Find your target audience.",
      articleClass: "",
      buttonText: " Create a new listing",
      sideImage: lightImage,
      svg: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="144"
          height="163"
          viewBox="0 0 144 163"
          fill="none"
          className="absolute -top-[47px] -right-[40px] lg:scale-150 lg:-top-[32px] lg:-right-0"
        >
          <path
            d="M66.8384 150.749L134.758 85.0531L51.9809 118.96L96.5534 57.5034L32.8784 74.4571L66.8384 32.0729H24.3884L32.8784 13"
            stroke="#FFC72C"
            strokeWidth="24.0409"
            strokeLinecap="round"
          />
        </svg>
      ),
      // imageClass: "translate-x-[280px] -translate-y-[86px]",
      imageClass:
        "-top-[47px] -right-[40px] lg:scale-130 lg:-top-[32px] lg:-right-[45px]",
      backgroundColor: "#4d7906",
      clickFn: () => router.push("/become-seller"),
    },
    {
      heading: "Have a question? Need support?",
      text: "Connect with members in the known",
      articleClass: "lg:col-span-2 xl:col-span-1",
      buttonText: "Ask the community",
      sideImage: starImage,
      svg: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="149"
          height="147"
          viewBox="0 0 149 147"
          fill="none"
          className="absolute -top-[41px] -right-[31px] lg:scale-130 lg:-top-[6px] lg:right-[2px]"
        >
          <path
            d="M101.753 7.87683C104.914 4.97496 109.912 7.72382 109.243 11.8856L109.207 12.0849L102.79 43.7747C101.286 51.2051 104.736 58.7597 111.338 62.487L139.493 78.3828C143.23 80.4924 142.161 86.095 137.996 86.7443L137.794 86.7718L105.674 90.461C98.377 91.2991 92.4072 96.5965 90.6732 103.682L90.5186 104.373L84.1015 136.062C83.2499 140.268 77.5908 140.983 75.6862 137.223L75.5979 137.039L62.1628 107.631C59.1109 100.951 52.2284 96.9094 44.9535 97.4499L44.2483 97.5172L12.1276 101.206C7.86457 101.696 5.43557 96.5341 8.42351 93.5606L8.57093 93.4207L32.3888 71.5553C37.799 66.5884 39.5149 58.7937 36.7528 52.0421L36.4716 51.3921L23.0365 21.9839C21.2251 18.0188 25.5454 14.052 29.3415 16.1951L57.4965 32.0903C63.8922 35.7011 71.8359 34.9239 77.4036 30.2105L77.9349 29.7422L101.753 7.87683Z"
            stroke="white"
            strokeWidth="13"
          />
        </svg>
      ),
      // imageClass: "translate-x-[267px] -translate-y-[94px]",
      imageClass: "-top-[41px] -right-[31px]",
      backgroundColor: "#e4000c",
      clickFn: () => router.push("/community"),
    },
  ];

  return (
    <>
    
      {bannerList1?.map((list, idx) => {
        return (
          <article
            key={idx}
            className={`
        relative
        flex-1                    
        min-w-[280px]                
        h-[236px] lg:h-[387px]
        rounded-[52px] xl:rounded-[69px]
        overflow-hidden
        px-4 py-[36px] xl:py-[55px] xl:px-[25px]
        flex flex-col justify-end
        ${list.articleClass}
      `}
            style={{backgroundColor: list.backgroundColor}}
          >
            {/* <Image
                            src={list.sideImage}
                            className={`absolute ${list.imageClass}`}
                            alt={list.altText || "Promotional banner"} // Make sure to include descriptive alt text
                        /> */}

            {list.svg}

            <div className="">
              <h2 className="text-white font-semibold text-lg my-2 md:md:text-[25px] md:w-9/12">
                {list.heading}
              </h2>
              <p className="text-white text-[14px] md:text-[16px] line-clamp-4 xl:w-[316px] ">
                {list.text}
              </p>
              <button
                className="mt-3  bg-white py-[13px] w-[221px] rounded-full text-[#211F54] font-medium gradient-all-round shadow-[0_3.172px_3.172px_0_rgba(255,255,255,0.32)_inset]"
                onClick={list.clickFn}
              >
                {list.buttonText}
              </button>
            </div>
          </article>
        );
      })}
    </>
  );
}

export default bannerList;
