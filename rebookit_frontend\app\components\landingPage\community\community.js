"use client";

import moment from "moment";
import {useEffect, useState} from "react";
import communityCss from "./community.module.scss";

import {HiChatBubbleLeftRight} from "react-icons/hi2";
import {PiShareFill} from "react-icons/pi";
import Link from "next/link";
import {addQuestion, allQuestion} from "@/app/services/community";
import {toast} from "react-toastify";
import {getCategories} from "@/app/services/profile";
import ShareCard from "../../common/ShareCard";
import AskQuestion from "../../common/AskQuestion";
import {useRouter} from "next/navigation";
import {getToken, userDataFromLocal} from "@/app/utils/utils";

export default function Community() {
  const [question, setQuestion] = useState([]);
  const [searchQuestiontext, setsearchQuestiontext] = useState("");
  const [addQuestionInput, setaddQuestionInput] = useState("");
  const [categoryState, setcategoryState] = useState([]);
  const [selectedCategory, setselectedCategory] = useState("");
  const [selectedId, setselectedId] = useState(null);
  const dummyQuestionsData = [
    {
      question: "What is Rebookit?",
      answer:
        "The ultimate online community for connecting local sellers with buyers for the sustainable disposal of used textbooks at all academic levels.  From primary to post-graduate school....",
      postedBy: "Kashish Akanksha",
      createdAt: "2025-04-14T17:47:26.064+00:00",
      responses: 0,
    },
    {
      question: "What is Rebookit?",
      answer:
        "The ultimate online community for connecting local sellers with buyers for the sustainable disposal of used textbooks at all academic levels.  From primary to post-graduate school....",
      postedBy: "Kashish Akanksha",
      createdAt: "2025-04-14T17:47:26.064+00:00",
      responses: 0,
    },
    {
      question: "What is Rebookit?",
      answer:
        "The ultimate online community for connecting local sellers with buyers for the sustainable disposal of used textbooks at all academic levels.  From primary to post-graduate school....",
      postedBy: "Kashish Akanksha",
      createdAt: "2025-04-14T17:47:26.064+00:00",
      responses: 0,
    },
  ];

  const bookCategories = ["School Books", "PEP", "CSEC", "CAPE", "Other"];
  // Set initial activeCategory to "all"
  const [activeCategory, setActiveCategory] = useState("all");

  const [communityQuestions, setCommunityQuestions] = useState([]);
  const router = useRouter();

  useEffect(() => {
    setCommunityQuestions(dummyQuestionsData);
    getQuestionFunc();
    fetchMasterCategory();
  }, []);

  useEffect(() => {
    // If "all" is selected, don't pass categoryId
    if (activeCategory === "all") {
      getQuestionFunc("");
    } else {
      getQuestionFunc("", activeCategory);
    }
  }, [activeCategory]);

  const getQuestionFunc = async (searchText, categoryId) => {
    let searchQuery = "";
    if (searchText) {
      searchQuery += `searchTerm=${searchText}`;
    }
    // Only add categoryId if not "all" and not empty
    if (categoryId && categoryId !== "all") {
      searchQuery += `categoryId=${categoryId}`;
    }
    let questions = await allQuestion(searchQuery);
    if (questions.status == 200) {
      if (questions.data.data.length > 4) {
        setQuestion(questions.data.data.slice(0, 4));
      } else {
        setQuestion(questions.data.data);
      }
    }
  };

  let categories = [
    {label: "School Books", values: "School Books"},
    {label: "PEP", values: "PEP"},
    {label: "CSEC", values: "CSEC"},
    {label: "CAPE", values: "CAPE"},
    {label: "Others", values: "Others"},
  ];

  const submitQuestion = async () => {
    let submitResponse = await addQuestion({
      title: addQuestionInput,
      categoryId: selectedCategory,
    });
    if (submitResponse.status == 200) {
      toast.success("Question Added Successfully");
      let docElement = document
        .getElementById("myModal")
        .classList.add("hidden");
    }
  };

  const fetchMasterCategory = async () => {
    try {
      let getCateogriesData = await getCategories();
      // Add "All" item at the start of the categories
      if (getCateogriesData?.status == 200) {
        if (getCateogriesData?.data?.categories) {
          let state = [
            {name: "Other", _id: "all"},

            ...getCateogriesData?.data?.categories,
          ];
          setcategoryState(state);
        }
      }
      // setisLoading(false)
    } catch (err) {
      // setisLoading(false)
      console.log("Err fetchMasterCategory", err);
    }
  };

  // Debug logs
  // console.log("categoryState", categoryState);
  // console.log("selectedCategory", selectedCategory);
  // console.log("selectedId", selectedId);
  // console.log("activeCategory", activeCategory);
  // console.log("question landing", question);
  let userData = userDataFromLocal();
  return (
    <section className={`${communityCss.communityContainer} `}>
      <div className="container-wrapper px-[10px] px-4 py-10 sm:px-6 md:px-12 lg:px-20 xl:px-18">
        <header>
          <section className="md:flex md:justify-between md:items-start  ">
            <section className="md:w-8/12">
              <h2 className="text-[22px] font-semibold leading-normal uppercase md:text-[48px]">
                Ask The Community
              </h2>
              <p className="mt-2.5 font-light text-[12px] md:text-[18px] leading-[18px] md:leading-[27px]">
                About textbook printing, use, and disposal, along with reasons
                why reselling and buying used textbooks helps promote the green
                agenda:
              </p>
            </section>
          </section>

          <section className="md:flex md:justify-between md:items-center my-5 md:my-[30px] ">
            <div className={`${communityCss.searchBox} w-full`}>
              <input
                type="search"
                className="w-7/12"
                onChange={(e) => setsearchQuestiontext(e.target.value)}
                placeholder="Type your query here"
              />
              <button
                onClick={(e) =>
                  getQuestionFunc(
                    searchQuestiontext,
                    activeCategory === "all" ? undefined : activeCategory
                  )
                }
              >
                {" "}
                Search Now
              </button>
            </div>

            <button
              className={`hidden ${communityCss.askQuestion}  `}
              onClick={() => {
                if (!getToken()) {
                  // RedirectToLoginIfNot("/",router)
                  router.replace("/login");
                  RedirectToLoginIfNot("/community", router);
                  return;
                } else {
                  let docElement = document
                    .getElementById("myModal")
                    .classList.remove("hidden");
                  console.log("docElement", docElement);
                }
              }}
            >
              Ask your question
            </button>
          </section>

          <div className="md:mt-16 flex flex-wrap no-scrollbar overflow-auto   items-center gap-5 mt-6 md:mt-[30px] md:justify-between ">
            <div
              style={{scrollbarWidth: "1px"}}
              className=" hide-scrollbar flex justify-start     items-center md:gap-0 border-b border-[#b4b4b42e] lg:justify-center lg:gap-8 lg:overflow-none"
            >
              {categoryState.map((category, idx) => {
                const isActive = activeCategory === category._id;
                const categoryClass = `text-[11.5px] md:text-[20px] whitespace-nowrap cursor-pointer pt-[17px] pb-[16px] px-[25px] text-center ${
                  isActive ? communityCss.active : ""
                }`;

                return (
                  <p
                    key={idx}
                    className={categoryClass.trim()}
                    onClick={() => setActiveCategory(category._id)}
                  >
                    {category.name}
                  </p>
                );
              })}
            </div>
          </div>
        </header>

        <section className="flex flex-col items-center gap-5 mt-6 md:mt-8 ">
          {question.length > 0 ? (
            question?.map((question, idx) => (
              <article
                key={idx}
                className="w-full bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden flex flex-col md:flex-row"
              >
                <div className="max-w-[85%] flex-1 p-4 md:p-6">
                  <div className="">
                    <div className="mb-4 ">
                      <h3 className="text-lg font-semibold line-clamp-2 text-gray-900 md:text-xl break-words  max-w-[90%]">
                        {`Q. ${question?.title}?`}
                      </h3>
                      <p className="mt-2 text-gray-700 text-sm md:text-base line-clamp-2 break-words   max-w-[90%]">
                        {`A. ${question?.latestAnswer?.answerText || "N/A"}`}
                      </p>
                    </div>

                    <footer className=" text-xs text-gray-500 flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-0 ">
                      <div className="flex flex-col sm:flex-row sm:gap-4">
                        <p>
                          Posted By:{" "}
                          <span className="font-medium">
                            {question?.askedBy?.firstName || "Unknown"}
                          </span>
                        </p>
                        <p>
                          Date:{" "}
                          <span className="font-medium">
                            {moment(question?.createdAt).format("DD/MMMM/YYYY")}
                          </span>
                        </p>
                      </div>
                      <p className="sm:self-end">
                        Responses:{" "}
                        <span className="font-medium">
                          {question?.totalAnswers}
                        </span>
                      </p>
                    </footer>
                  </div>

                  <div className="flex gap-3 mt-4 md:hidden ">
                    <button
                      className="flex items-center justify-center px-3 py-2 global_linear_gradient text-white rounded-full text-xs sm:text-sm flex-1"
                      onClick={() =>
                        router.push(`/allresponses?id=${question._id}`)
                      }
                    >
                      <HiChatBubbleLeftRight className="mr-1.5" size={16} />
                      All Responses
                    </button>
                    <button
                      className="flex items-center justify-center px-3 py-2 border border-black-600 text-black rounded-full text-xs sm:text-sm flex-1"
                      onClick={() => {
                        document
                          .getElementById("myShareModal")
                          .classList.remove("hidden");
                        setselectedId(question._id);
                      }}
                    >
                      <PiShareFill size={16} className="mr-1.5" />
                      Share
                    </button>
                  </div>
                </div>

                <div className="hidden md:flex flex-col justify-center items-center p-4 md:p-6 gap-3 border-l ">
                  <button
                    className="flex items-center px-4 py-2 global_linear_gradient text-white rounded-full text-sm min-w-[160px] justify-center"
                    onClick={() =>
                      router.push(`/allresponses?id=${question._id}`)
                    }
                  >
                    <HiChatBubbleLeftRight className="mr-2" size={18} />
                    All Responses
                  </button>
                  <button
                    className="flex items-center px-4 py-2 border border-black text-black rounded-full text-sm min-w-[160px] justify-center"
                    onClick={() => {
                      document
                        .getElementById("myShareModal")
                        .classList.remove("hidden");
                      setselectedId(question._id);
                    }}
                  >
                    <PiShareFill size={18} className="mr-2" />
                    Share
                  </button>
                </div>
              </article>
            ))
          ) : (
            <div className="w-full py-10 text-center">
              <p className="text-gray-500 text-lg">No Questions Found</p>
            </div>
          )}

          <div className="hidden md:flex justify-center my-8">
            <Link
              href="/community"
              className="inline-block transition-transform hover:scale-105"
              aria-label="View all questions"
            >
              <svg
                width="178"
                height="72"
                viewBox="0 0 178 72"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="w-44 md:w-48 lg:w-52"
              >
                {" "}
                <path
                  d="M171.629 35.7285C171.629 19.31 157.778 6.00018 140.692 6.00018L36.9361 6.00017C19.8503 6.00017 5.99954 19.31 5.99954 35.7285"
                  stroke="#211F54"
                  strokeWidth="11.679"
                />
                <path
                  d="M171.629 35.7285C171.629 19.31 157.827 6.00018 140.802 6.00018L37.412 6.00017"
                  stroke="#0161AB"
                  strokeWidth="11.679"
                />
                <path
                  d="M6 35.7285C6 52.147 19.8507 65.4569 36.9365 65.4569H140.693C157.779 65.4569 171.629 52.147 171.629 35.7285"
                  stroke="#EFDC2A"
                  strokeWidth="11.679"
                />
                <path
                  d="M37.4121 65.4569H140.802C157.827 65.4569 171.629 52.147 171.629 35.7285"
                  stroke="#0161AB"
                  strokeWidth="11.679"
                />
                <path
                  d="M140.693 6L36.937 5.99999"
                  stroke="#FF0009"
                  strokeWidth="11.679"
                />
                <path
                  d="M140.693 65.457L36.937 65.457"
                  stroke="#4A8B40"
                  strokeWidth="11.679"
                />
                <rect
                  x="11.6016"
                  y="7.93848"
                  width="154.01"
                  height="54.6036"
                  rx="27.3018"
                  fill="white"
                />
                <text
                  x="50%"
                  y="50%"
                  dominantBaseline="middle"
                  textAnchor="middle"
                  fontSize="20"
                  fill="#211F54"
                  fontFamily="Poppins, sans-serif"
                >
                  View All
                </text>
              </svg>
            </Link>
          </div>

          <button
            className="w-full max-w-md py-3 bg-gradient-to-r bg-[#025fa8] text-white rounded-full font-medium md:hidden mt-4"
            onClick={() => {
              if (!getToken()) {
                router.replace("/login");
                RedirectToLoginIfNot("/community", router);
                return;
              } else {
                document.getElementById("myModal").classList.remove("hidden");
              }
            }}
          >
            Ask your Question
          </button>
        </section>
      </div>
      <ShareCard url={`allresponses?id=${selectedId}`} />

      {/* <ShareCard  id={selectedId} /> */}
      {userData && <AskQuestion />}
    </section>
  );
}
