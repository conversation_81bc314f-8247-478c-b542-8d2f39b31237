.communityContainer {
  padding: 28px 0 48px;
  // min-height: 30vh;
  background: #211f54;
  color: #fff;

  .searchBox {
    width: 100%;
    height: 35.296px;
    flex-shrink: 0;
    border-radius: 27.503px;
    background: #fff;
    box-shadow: 0px 8.709px 38.504px 0px rgba(0, 0, 0, 0.04);
    padding: 3px;
    // margin: 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    input[type="search"] {
      border: none;
      outline: none;
      color: #000;
      font-size: 8.3px;
      padding-left: 10px;
      margin-left: 15px;
    }

    button {
      width: 94.886px;
      height: 27.503px;
      flex-shrink: 0;
      border-radius: 16.96px;
      background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
      color: #fff;
      font-size: 8.25px;
      cursor: pointer;
    }
  }

  .hr {
    width: 100%;
    height: 0.575px;
    background: #b4b4b42e;
    position: relative;
    z-index: 1;
  }

  .active {
    position: relative;

    &::after {
      content: "";
      display: block;
      position: absolute;
      height: 1px;
      bottom: 0;
      width: 100%;
      background: white;
      // top: 10px;
      left: 0;
      z-index: 2;
    }
  }

  .card {
    width: 100%;
    flex-shrink: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 8px;
    background: #fdfdfd;
    padding: 22px 18px 21px 17px;
    color: #000;

    .response {
      // width: 165px;
      // width: 50%;
      padding: 9px;
      // height: 37px;
      flex-shrink: 0;
      border-radius: 44.025px;
      background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
      text-transform: capitalize;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      color: #fff;
      cursor: pointer;
      gap: 6px;
    }

    .share {
      // width: 165px;
      width: 50%;
      padding: 9px;
      // height: 37px;
      flex-shrink: 0;
      // background: #FDFDFD;
      text-transform: capitalize;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      border-radius: 44.025px;
      // border: 1.268px solid #025FA8;
      color: #000;
      cursor: pointer;
    }
  }

  .askQuestion {
    // width: 195.136px;
    // height: 47.916px;
    // flex-shrink: 0;
    border-radius: 69.443px;
    background: #025fa8;
    font-size: 12px;
    cursor: pointer;
    padding: 15px 45px 16px;
  }

  .hide-scrollbar {
    overflow-y: auto;
    /* or scroll */
    -ms-overflow-style: none;
    /* IE/Edge */
    scrollbar-width: none;
    /* Firefox */
  }

  .hide-scrollbar::-webkit-scrollbar {
    display: none;
    /* Chrome, Safari */
  }

  @media (min-width: 769px) {
    padding: 70px 0 90px;

    .searchBox {
      width: 55%;
      height: 70px;
      border-radius: 46.617px;
      background: #fff;
      box-shadow: 0px 14.762px 65.263px 0px rgba(0, 0, 0, 0.04);
      // margin: 30px 0;

      input[type="search"] {
        font-size: 16px;
      }

      button {
        width: 160.827px;
        height: 46.617px;
        flex-shrink: 0;
        border-radius: 28.747px;
        background: var(
          --Linear,
          linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%)
        );
        margin-right: 5px;
        font-size: 14px;
      }
    }

    .hr {
      height: 1.575px;
    }

    // .active {
    //     &::after {

    //         top: 19px;

    //     }
    // }

    .askQuestion {
      // height: 60.916px;
      display: block;
    }

    .card {
      width: 100%;
      // height: 197px;
      border-radius: 21.763px;
      background: #fdfdfd;
      padding: 15px 57px 15px 57px;
      color: #000;
      display: flex;
      justify-content: space-between;

      .response {
        // width: 195.081px;
        // height: 58.524px;
        // flex-shrink: 0;
        border-radius: 69px;
        background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
        text-transform: capitalize;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        color: #fff;
        cursor: pointer;
        padding: 10px 20px;
        line-height: normal;
        width: 100%;
        white-space: nowrap;
        gap: 9px;
      }

      .share {
        // width: 195.081px;
        padding: 10px 20px;
        width: 100%;
        // height: 58.524px;
        // flex-shrink: 0;
        border-radius: 69.025px;
        // background: #FDFDFD;
        text-transform: capitalize;
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 12px;
        // border: 1.268px solid #025FA8;
        color: #000;
        cursor: pointer;
      }
    }
  }
}

.hide-scrollbar {
  overflow-y: auto;
  /* or scroll */
  -ms-overflow-style: none;
  /* IE/Edge */
  scrollbar-width: none;
  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari */
}
