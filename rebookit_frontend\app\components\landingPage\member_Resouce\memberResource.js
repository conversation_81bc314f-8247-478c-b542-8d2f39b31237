"use client";

import memberCss from "./memberResource.module.scss";
import bookCategory from "@/public/landing/bookCategory.png";
import Image from "next/image";
import Link from "next/link";
import img1 from "../../../assets/Group 1321316166.png";

// import bookCategories from "@/app/static_data/bookCategories.json"
import {USER_ROUTES} from "@/app/config/api";
import {useEffect, useState} from "react";
import {getCategories} from "@/app/services/profile";

export default function MemberResource() {
  const [bookCategories, setBookCategories] = useState([]);

  const fetchCategories = async () => {
    try {
      let getCateogries = await getCategories();
      if (getCateogries?.status == 200) {
        console.log(getCateogries, "getCateogries");
        setBookCategories(getCateogries?.data?.categories);
      }
      // fetch(USER_ROUTES.MASTER_DATA_CATEGORY, {
      //     method: "GET",
      //     // headers: {
      //     //     "Authorization": `Bearer ${auth}`
      //     // }
      // }).then(async res => {
      //     const response = await res.json();
      //     console.log("response categories is", response?.data?.categories);

      //     if (!response?.error) {
      //         // setBookCategories(response?.data?.categories?.slice(0, 4))
      //         setBookCategories(response?.data?.categories)
      //     }
      // })
    } catch (error) {
      console.log("error", error);
    }
  };

  console.log("bookCategories", bookCategories);

  useEffect(() => {
    console.log("member resource");
    fetchCategories();
  }, []);

  return (
  
    <section
      aria-labelledby="member-resources-heading"
      className="px-4 py-10 sm:px-6 md:px-12 lg:px-20 xl:px-24"
    >
      <div className="max-w-7xl mx-auto">
        <header className="text-center md:text-left">
          <h2
            id="member-resources-heading"
            className="text-2xl uppercase font-semibold leading-tight
                   md:text-4xl lg:text-5xl"
          >
            Member Resources
          </h2>
          <p
            className="mt-3 text-sm font-light leading-relaxed
                    md:text-base md:leading-7 md:w-4/5"
          >
            Find good reads and academic essentials—textbooks, tutors, schools,
            events, scholarships and so much more—all in one place at{" "}
            <strong className="font-medium">ReBookIt.Club</strong>.
          </p>
        </header>

        <ul
          role="list"
          className="mt-8 grid grid-cols-2 gap-6
                 sm:grid-cols-2
                 md:grid-cols-3
                 lg:grid-cols-4
                 xl:grid-cols-5"
        >
          {bookCategories?.map((category) => (
            <li key={category._id} className="flex justify-center">
              <Link
                href={`/search?category=${category._id}`}
                aria-label={`Browse books for ${category.name}`}
              >
                <article className="flex flex-col items-center justify-between text-center">
                  <div className="relative w-32 h-32 sm:w-40 sm:h-40">
                    <div className="absolute inset-x-0 top-0 h-1/2 global_linear_gradient rounded-t-full" />
                    <div className="absolute inset-x-0 bottom-0 h-1/2 global_linear_gradient rounded-b-full" />
                    <div className="absolute inset-0 flex items-center justify-center">
                      <Image
                        src={category.image || "/images/trial_book.jpg"}
                        alt={`Cover for ${category.name}`}
                        width={78}
                        height={78}
                        className="object-contain"
                      />
                    </div>
                  </div>
                  <p className="mt-4 text-base font-semibold text-center leading-tight">
                    {category.name}
                  </p>
                </article>
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </section>
  );
}
