
'use client'

import moment from "moment"
import { useEffect, useState } from "react"
import rebookerCss from "./rebookerMonth.module.scss"
import Link from "next/link"
import Image from "next/image"


import dummyImage from "@/public/test.jpeg"
import preferredBatch from "@/public//landing/preferredSeller.png"

import { FaStar } from "react-icons/fa6";

export default function RebookerMonth() {


    const dummyData = [
        {
            name: "<PERSON><PERSON>",
            bookName: "The Lord Of Rings",
            userCreatedAt: "2025-04-14T17:47:26.064+00:00",
            totalListing: "10",
            avgRating: "4.3",
            image: dummyImage,
            bookImage: dummyImage,
            totalOrders: 10
        },
        {
            name: "<PERSON><PERSON><PERSON>",
            bookName: "<PERSON> Potter",
            userCreatedAt: "2025-04-14T17:47:26.064+00:00",
            totalListing: "5",
            avgRating: "4.8",
            image: dummyImage,
            bookImage: dummyImage,
            totalOrders: 10
        },
        {
            name: "<PERSON><PERSON><PERSON>",
            bookName: "<PERSON>",
            userCreatedAt: "2025-04-14T17:47:26.064+00:00",
            totalListing: "5",
            avgRating: "4.8",
            image: dummyImage,
            bookImage: dummyImage,
            totalOrders: 10
        },
        {
            name: "Himanshu",
            bookName: "Harry Potter",
            userCreatedAt: "2025-04-14T17:47:26.064+00:00",
            totalListing: "5",
            avgRating: "4.8",
            image: dummyImage,
            bookImage: dummyImage,
            totalOrders: 10
        },
    ]

    const [awardData, setAwardData] = useState([])

    useEffect(() => {

        // fetch api call 
        setAwardData(dummyData)
    }, [])



    return (
        <section className={`${rebookerCss.rebookerContainer} md:my-[70px]`}>

            <div className="container-wrapper">

                <section className="md:flex md:justify-between md:items-start md:px-[50px] lg:px-[100px]">
                    <header>
                        <h2 className="text-[22px] leading-normal font-semibold uppercase md:text-[48px]">Rebooker of the month</h2>
                        <p className="mt-2.5  leading-normal text-xs md:text-[18px] md:w-8/12">
                            Explore stories, knowledge, and imagination with <strong> ReBookIt.</strong> Find academic essentials, timeless novels, and rare gems—all in one place.
                        </p>
                    </header>

                    <footer className="hidden md:flex justify-center my-5 ">
                        <Link href="/" aria-label="View all book categories">
                            <svg width="178" height="72" viewBox="0 0 178 72" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M171.629 35.7285C171.629 19.31 157.778 6.00018 140.692 6.00018L36.9361 6.00017C19.8503 6.00017 5.99954 19.31 5.99954 35.7285" stroke="#211F54" strokeWidth="11.679" />
                                <path d="M171.629 35.7285C171.629 19.31 157.827 6.00018 140.802 6.00018L37.412 6.00017" stroke="#0161AB" strokeWidth="11.679" />
                                <path d="M6 35.7285C6 52.147 19.8507 65.4569 36.9365 65.4569H140.693C157.779 65.4569 171.629 52.147 171.629 35.7285" stroke="#EFDC2A" strokeWidth="11.679" />
                                <path d="M37.4121 65.4569H140.802C157.827 65.4569 171.629 52.147 171.629 35.7285" stroke="#0161AB" strokeWidth="11.679" />
                                <path d="M140.693 6L36.937 5.99999" stroke="#FF0009" strokeWidth="11.679" />
                                <path d="M140.693 65.457L36.937 65.457" stroke="#4A8B40" strokeWidth="11.679" />
                                <rect x="11.6016" y="7.93848" width="154.01" height="54.6036" rx="27.3018" fill="white" />
                                <text x="50%" y="50%" dominantBaseline="middle" textAnchor="middle" fontSize="20" fill="#211F54" fontFamily="Poppins, sans-serif">
                                    View All
                                </text>
                            </svg>
                        </Link>
                    </footer>

                </section>

                <div className="md:pl-[50px] lg:pl-[100px]">

                    <main className="flex flex-col justify-center m-auto w-full gap-5 md:gap-10 mt-5 mb-[40px] md:my-16 md:justify-between md:flex-row md:overflow-x-auto no_scrollbar">
                        {awardData.map((user, idx) => (
                            <article key={idx} className={`${rebookerCss.card} w-full md:min-w-[378px]`} aria-label={`Profile of ${user.name}`}>
                                <header className={`${rebookerCss.cardTop}`}>
                                    <div className="flex">
                                        <figure className={`${rebookerCss.imageContainer}`}>
                                            <Image src={user.image} alt={`${user.name} profile image`} />
                                        </figure>
                                        <div className="ml-2.5 flex flex-col gap-0.5 justify-center">
                                            <p className="text-white text-sm leading-[13px] font-light md:text-[15px]">Rebookit Preferred</p>
                                            <p className="text-white text-base leading-[22px] font-semibold md:text-[18px]">{user.name}</p>
                                        </div>
                                    </div>
                                    <figure className="relative overflow-hidden w-[46px] h-[36px]">
                                        <Image src={preferredBatch} alt="Preferred Batch badge" fill objectFit="cover" sizes="33w" />
                                    </figure>
                                </header>

                                <section className={`${rebookerCss.cardMid}`}>
                                    <div className="flex justify-start items-center">
                                        <figure className={`${rebookerCss.bookContainer}`}>
                                            <div className="relative overflow-hidden w-full h-full">
                                                <Image src={user.bookImage} alt={`Book by ${user.name}`} fill sizes="33w" objectFit="cover" />
                                            </div>
                                        </figure>
                                    </div>

                                    <div className="flex flex-col justify-center w-full">
                                        <h2 className="text-[13px] leading-[17px] font-semibold line-clamp-1">Latest Book Name</h2>

                                        <div className="flex mt-[5px] justify-between items-end w-[80%]">
                                            <div className="text-xs leading-4">
                                                <p>Profile Since</p>
                                                <p>July 2024</p>
                                            </div>

                                            <div className="border-l border-[#D1D1D1] h-[25px] mx-2 self-center"></div>

                                            <div className="text-[11px] leading-[15px]">
                                                <p>Buyers Served</p>
                                                <p>6000+</p>
                                            </div>
                                        </div>
                                    </div>

                                </section>

                                <footer className={`${rebookerCss.cardEnd}`}>
                                    <div>
                                        <p className="font-semibold text-lg leading-[21px]">{user.totalListing}</p>
                                        <p className="mt-[3px] text-xs leading-[17px]">Books for sale</p>
                                    </div>

                                    <div>
                                        <p className="font-semibold text-lg leading-[21px]">4</p>
                                        <p className="mt-[3px] text-xs leading-[17px]">Top Ranking</p>
                                    </div>

                                    <div>
                                        <p className="font-semibold flex items-center text-lg leading-[21px] md:text-[19px]">
                                            {Number(user.avgRating)?.toFixed(1)}{" "}
                                            <span>
                                                <FaStar size={12} color="#FEC107" className="ml-2" />
                                            </span>
                                        </p>
                                        <p className="mt-[3px] text-xs leading-[17px] md:text-[15px]">Buyer Rating</p>
                                    </div>
                                </footer>
                            </article>
                        ))}

                        <footer className="flex md:hidden justify-center w-full">
                            <Link href="/" aria-label="View all book categories">
                                <svg className="cursor-pointer" width="101" height="41" viewBox="0 0 101 41" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M97.5791 20.1641C97.5791 10.8877 89.7535 3.36779 80.1002 3.36779L21.4787 3.36779C11.8254 3.36779 3.99986 10.8877 3.99986 20.1641" stroke="#211F54" strokeWidth="6.59854" />
                                    <path d="M97.5791 20.1641C97.5791 10.8877 89.7812 3.36779 80.162 3.36779L21.7476 3.36779" stroke="#0161AB" strokeWidth="6.59854" />
                                    <path d="M4.00049 20.1641C4.00049 29.4404 11.826 36.9603 21.4794 36.9603H80.1009C89.7542 36.9603 97.5797 29.4404 97.5797 20.1641" stroke="#EFDC2A" strokeWidth="6.59854" />
                                    <path d="M21.748 36.9603H80.1624C89.7816 36.9603 97.5795 29.4404 97.5795 20.1641" stroke="#0161AB" strokeWidth="6.59854" />
                                    <path d="M80.1011 3.36719L21.4796 3.36718" stroke="#FF0009" strokeWidth="6.59854" />
                                    <path d="M80.1011 36.9609L21.4796 36.9609" stroke="#4A8B40" strokeWidth="6.59854" />
                                    <rect x="7.16504" y="4.46094" width="87.0145" height="30.8506" rx="15.4253" fill="white" />
                                    <text x="50%" y="50%" dominantBaseline="middle" textAnchor="middle" fontSize="12" fontWeight="500" fill="#211F54" fontFamily="Poppins, sans-serif">
                                        View All
                                    </text>
                                </svg>
                            </Link>
                        </footer>
                    </main>
                </div>
            </div>
        </section >

    )
}
