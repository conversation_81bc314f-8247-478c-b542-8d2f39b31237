.rebookerContainer {

    padding: 0 10px;



    .card {

        border-radius: 8.797px;
        border: 1.1px solid var(--Linear, #211F54);
        display: flex;
        flex-direction: column;
        // width: 369px;
        justify-content: center;
        align-items: center;

        .cardTop {
            background: linear-gradient(268deg, #0161ab 11.09%, #211f54 98.55%);
            border-radius: 7px 7px 0 0;
            flex-shrink: 0;
            justify-content: space-between;
            align-items: center;
            // width: 369px;
            width: 100%;
            display: flex;
            padding: 7px 10px 9px 11px;


            .imageContainer {

                width: 48.384px;
                height: 48.384px;
                flex-shrink: 0;
                border-radius: 4.399px;
                background: linear-gradient(286deg, #FFC72C 13.43%, #D8232A 88.8%);
                display: flex;
                justify-content: center;
                align-items: center;

                img {
                    width: 70%;
                    margin: auto;
                    height: 77%;
                }
            }
        }

        .cardMid {
            border-bottom: 1.1px solid #E8E8E8;
            display: inline-flex;
            height: 78px;
            padding: 12.195px 10px 12.996px 10px;
            align-items: center;
            flex-shrink: 0;
            width: 100%;
            align-items: center;
            gap: 8px;

            .bookContainer {
                display: flex;
                width: 52.004px;
                height: 52.004px;
                padding: 8.855px 12.208px 8.218px 13.196px;
                justify-content: center;
                align-items: center;
                flex-shrink: 0;
                border-radius: 4.953px;
                border: 1.238px solid #E8E8E8;

                // img {
                //     width: 70%;
                //     margin: auto;
                //     height: 77%;
                // }
            }
        }

        .cardEnd {
            // width: 369px;
            width: 100%;
            height: 60px;
            flex-shrink: 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px
        }
    }

    @media (min-width: 769px) {

        // margin: 20px 0;
        gap: 20px;
        padding: 0;

        .card {

            // width: 390px;
            // width: 100%;
            border-radius: 10.31px;

            .cardTop {
                // width: 390px;
                height: 76.036px;
                flex-shrink: 0;
                border-radius: 9px 9px 0px 0px;
                background: #211F54;
            }

            .cardMid {

                // width: 390px;
                display: inline-flex;
                padding: 15.465px 45.403px 16.754px 12.887px;
                align-items: center;
                gap: 10.31px;
                border-bottom: 1.289px solid #E8E8E8;
                height: 100px;
            }

            .cardEnd {
                // width: 390px;
                flex-shrink: 0;
                height: 75px;
                display: flex;
                align-items: center;
            }
        }
    }

}