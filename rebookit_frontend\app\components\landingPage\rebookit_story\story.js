"use client";
import Image from "next/image";
import storycss from "./story.module.scss";
// import storyImage from "@/public/landing/storybanner.png";
import storyImage from "@/public/images/landing2.png";
import logo from "@/public/transparentIcon.png";
import Link from "next/link";
// import { useRouter } from "next/navigation";

export default function RebookitStory() {
  return (
    <section className={storycss.mainContainer}>
      <div className="container-wrapper lg:flex lg:justify-between lg:gap-10">
        <header className="lg:w-[42%]">
          <h2 className="text-white text-[22px] font-semibold mb-4 leading-normal uppercase lg:text-[48px] lg:leading-[46px]">
            WHO CAN REBOOKIT?
          </h2>
          <p className="hidden lg:block text-white mb-9 w-6/12 lg:w-10/12 text-lg leading-7  font-light mt-5">
            Welcome to ReBookIt.Club, the ultimate online community for
            connecting members who need educational resources and support, with
            those who provide them.
          </p>

          {/* <div className="relative w-full min-w-[500px] h-full overflow-hidden ">
            <Image
              src={storyImage}
              alt="A promotional banner showing textbooks and ReBookIt branding"
              className="rounded-2xl hidden lg:block"
              // fill
              objectFit="cover"
              sizes="33w"
            />
          </div> */}

          <div className="hidden lg:block relative min-w-[350px] max-w-[500px] h-[562px] overflow-hidden">
            <img
              src={storyImage.src}
              alt="A promotional banner showing textbooks and ReBookIt branding"
              fill
              className="rounded-2xl object-cover h-full"
              sizes="(max-width: 500px) 100vw, 500px"
            />
          </div>
        </header>

        <article className="flex flex-wrap justify-between lg:w-[58%] lg:text-[20px] lg:relative tracking-[1px] lg:flex">
          <div className="text-sm leading-5  text-white lg:text-xl lg:leading-8">
            <p>
              Welcome to ReBookIt.Club, the ultimate online community for
              connecting local sellers with buyers for the sustainable disposal
              of used textbooks at all academic levels. From primary to
              post-graduate school, we've got you covered.
            </p>
            <p className="my-8">Our platform enables our members to:</p>
            <ul>
              <li>
                - Find preowned books (i.e. text and non-textbooks) for sale
              </li>
              <li>
                - List preowned books (i.e. text and non-textbooks) they have
                for sale
              </li>
              <li>
                - List educational services (e.g. formal education, tutoring and
                extracurricular activities) they offer professionally
              </li>
              <li>
                - List educational events (e.g. seminars, workshops, courses and
                exhibitions){" "}
              </li>
              <li>- List scholarships, grants & awards on offer.* </li>
            </ul>

            <p className="my-8 italic">
              *Members must be a registered company, foundation, embassy,
              development fund or government agency to do so.
            </p>

            <figure className="mt-5 rounded-[14px] block lg:hidden">
              <Image
                src={storyImage}
                alt="A promotional banner showing textbooks and ReBookIt branding"
                className=" m-auto rounded-[14px]"
              />
              <figcaption className="sr-only">
                ReBookIt community and sustainability banner
              </figcaption>
            </figure>
          </div>

          <aside className="bg-[#E4000C] text-lg  my-5 py-[25px] px-[18px] leading-[30px] rounded-lg text-white lg:text-[22px] lg:my-[44px] lg:py-5 lg:px-[32px]">
            <p className="italic">
              <strong className="text-[22px] font-bold lg:text-[31px] ">
                Fun Fact:
              </strong>{" "}
              <span className="text-[#FFC72C] font-semibold italic">
                4 to 8 BILLION trees are cut down every year to make paper,
              </span>{" "}
              yet the average textbook is{" "}
              <span className="text-[#FFC72C] font-semibold italic">
                used for only 4–8 months.
              </span>{" "}
              Some aren't used at all.
            </p>
          </aside>

          {/* <footer className="flex justify-center w-full lg:w-auto">
            <Link href="/about" passHref>
              <button className="cursor-pointer gradient-all-round-border border-2 py-[13px] w-[164px] text-[#211F54] text-sm font-medium leading-3.5 rounded-full lg:w-[232px] lg:h-[65px] lg:py-[22px] lg:text-[21px] lg:leading-[22px]" onClick={()=>router.push("/about-us")}>
                Read More
              </button>
            </Link>
          </footer> */}
        </article>

        {/* Decorative Logo */}
        <div aria-hidden="true" className="lg:hidden">
          <Image
            src={logo}
            alt="Rebookit logo"
            className="absolute right-0 bottom-0 scale-100 lg:scale-150 lg:right-[180px] lg:bottom-[37px]"
          />
        </div>
      </div>
    </section>
  );
}
