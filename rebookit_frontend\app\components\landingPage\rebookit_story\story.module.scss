.mainContainer {
    background: #211F54;
    margin: 10px 0;
    padding: 36px 10px 34px;
    position: relative;
    overflow: hidden;


    // button {
    //     width: 164.938px;
    //     height: 41.234px;
    //     flex-shrink: 0;
    //     border-radius: 25.375px;
    //     border: 1.586px solid var(--Linear, #211F54);
    //     background: #FFF;
    //     box-shadow: 0px 3.172px 3.172px 0px rgba(255, 255, 255, 0.32) inset;
    //     margin: auto;
    // }


    @media (min-width: 769px) {

        padding: 90px 100px;


        // button {
        //     width: 232.856px;
        //     height: 65.886px;
        //     flex-shrink: 0;
        //     border-radius: 40.537px;
        //     border: 2.534px solid var(--Linear, #211F54);
        //     background: #FFF;
        //     box-shadow: 0px 5.067px 5.067px 0px rgba(255, 255, 255, 0.32) inset;
        // }
    }

}