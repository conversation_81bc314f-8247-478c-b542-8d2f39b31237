"use client";

import React, {useEffect, useMemo, useRef, useState} from "react";
import searchComponentCss from "./search.module.scss";
import Image from "next/image";

import {LuBookOpen} from "react-icons/lu";
import {IoSearchSharp} from "react-icons/io5";
import {MdSearch} from "react-icons/md";

import testImage from "@/public/test.jpeg";
import landingPage1 from "@/public/images/landing_page_1.png";
import landingPage2 from "@/public/images/landing_page_2.png";
import landingPage3 from "@/public/images/landing_page_3.png";
import landingPage4 from "@/public/images/landing_page_4.png";
import landingPage5 from "@/public/images/landing_page_5.png";
import landing1 from "@/public/images/landing1.png";
import Link from "next/link";
import {useRouter} from "next/navigation";
import {ELASTIC_DB_ROUTES} from "@/app/config/api";
import {toast} from "react-toastify";
import {useDebounce} from "../../common/hooks/useDebounce";
import {bookSuggestion} from "@/app/services/bookDetails";

export default function BookSearchComponent() {
  const router = useRouter();
  const suggestionsRef = useRef(null);
  const [visible, setVisible] = useState(false);
  let avatarImages = [
    // landing1,
    landingPage1,
    landingPage2,
    landingPage3,
    landingPage4,
    landingPage5,
  ];

  const [searchInput, setSearchInput] = useState("");
  const [searching, setSearching] = useState(false);
  const [hasFetched, setHasFetched] = useState(false);
  const [searchSuggestions, setSearchSuggestions] = useState([]);

  console.log(searchSuggestions, "searchSuggestions");

  console.log("searchInput", searchInput);

  const fetchSearchData = async () => {
    try {
      let urlString = `${
        ELASTIC_DB_ROUTES.SUGGESTION
      }?searchTerm=${searchInput.trim()}&page=1`;
      let searchData = await bookSuggestion(urlString);
      console.log("searchData", searchData);
      if (searchData.status == 200) {
        setSearchSuggestions(searchData.data.data);
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setSearching(false);
      setHasFetched(true);
    }
  };

  // Debouncing in the search text [Using Value]
  const debouncedSearch = useDebounce(searchInput, 500);

  useEffect(() => {
    // You can choose how to handle empty input here
    console.log("debouncedSearch", debouncedSearch);
    if (!debouncedSearch) {
      // Handle empty search case, like clearing results
      setSearchSuggestions([]);
      setHasFetched(false);
    } else {
      fetchSearchData(debouncedSearch);
    }
  }, [debouncedSearch]);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        suggestionsRef.current &&
        !suggestionsRef.current.contains(event.target)
      ) {
        // setVisible(false);
        setSearchSuggestions([]);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    // <section
    //   className={`${searchComponentCss.searchContainer} relative lg:!px-[100px]`}
    // >
    //   <div className="container-wrapper">
    //     <div className="mt-[27px] mb-7 md:mt-[11vh]">
    //       <div className="flex gap-1 md:items-center justify-start">
    //         <div className="flex relative">
    //           {avatarImages.map((image, idx) => {
    //             const overlapOffset = idx * -4; // 4px to the left
    //             const zIndex = 0 + idx;

    //             return (
    //               <div
    //                 key={idx}
    //                 className="relative h-[14px] w-[14px] md:h-[27px] md:w-[27px] rounded-full ring-[0.7px] ring-black overflow-hidden"
    //                 style={{
    //                   // position: 'relative',
    //                   left: `${overlapOffset}px`,
    //                   zIndex: zIndex,
    //                 }}
    //               >
    //                 <Image
    //                   key={idx}
    //                   src={image}
    //                   alt=""
    //                   className="inline-block rounded-full"
    //                   fill
    //                   objectFit="cover"
    //                   sizes="33w"
    //                 />
    //               </div>
    //             );
    //           })}
    //         </div>
    //         {/* <p className='text-[10px] text-white leading-normal md:text-md md:ml-[42px] lg:ml-0 absolute left-[18%] w-[55%] md:relative md:left-0'> Trusted by thousands of book lovers and sellers worldwide</p> */}
    //         <p className="text-[10px] text-white leading-normal md:text-sm lg:ml-0 relative -left-[10px]">
    //           {" "}
    //           Jamaica's First Digital Education Ecosystem
    //         </p>
    //       </div>

    //       <h1
    //         className={`${searchComponentCss.mainHeading} mt-5  md:mt-[23px]`}
    //       >
    //         “Don’t go broke buying it new,
    //         <br />
    //         or throw it out when you are through.
    //         <br />
    //         <span className="inline-block">ReBookIt and $ave!”</span>
    //       </h1>
    //     </div>

    //     <form
    //       onSubmit={(e) => {
    //         e.preventDefault();
    //         if (searchInput?.length > 0) {
    //           router.push(`/search?book=${searchInput}`);
    //         }
    //       }}
    //       className={`w-[98%] rounded-3xl m-auto md:absolute md:left-0 md:scale-[0.8] lg:scale-100 lg:relative lg:w-9/12 md:m-0 md:max-w-[900px] md:flex md:items-center relative lg:ml-[16px] ring-[8px] md:ring-[16px] ring-[#ffffff26] z-2 ${
    //         searchSuggestions?.length > 0 || hasFetched
    //           ? "rounded-[15px]"
    //           : "md:rounded-full"
    //       }`}
    //       role="search"
    //     >
    //       <div
    //         className={`${searchComponentCss.searchBox} ${
    //           searchSuggestions?.length > 0 || hasFetched
    //             ? "!rounded-t-[15px] !rounded-b-none !border-none"
    //             : ""
    //         }`}
    //       >
    //         <div className="rounded-full bg-[#025FA8] p-1 md:p-2">
    //           <LuBookOpen
    //             className="w-[10px] h-[10px] md:w-[23px] md:h-[23px]"
    //             color="#fff"
    //           />
    //         </div>

    //         <input
    //           type="text"
    //           className="w-full px-2 outline-none border-none font-poppins font-[275] italic md:text-[16.77px] text-[12px] leading-[100%] tracking-[0%]  text-[#757575]"
    //           placeholder="Search by Books, ED-irectory, Events, Scholarship & Awards."
    //           aria-label="Search books"
    //           value={searchInput}
    //           onChange={(e) => setSearchInput(e.target.value)}
    //           // onFocus={() => setSearching(true)}
    //           // onBlur={() => setSearching(false)}
    //         />

    //         <button className="flex justify-center items-center rounded-full">
    //           <IoSearchSharp
    //             className="w-[10px] h-[10px] md:w-[20px] md:h-[20px]"
    //             color="#fff"
    //           />
    //           <p className={`${searchComponentCss.buttonText}`}>Search</p>
    //         </button>
    //       </div>

    //       {/* {
    //         // searchInput.length ?

    //         // Search List
    //         <div
    //           id="suggestions"
    //           ref={suggestionsRef}
    //           className={`flex flex-col justify-start absolute left-0 top-full bg-white w-full h-fit rounded-b-[15px] overflow-y-auto overflow-x-hidden max-h-[180px] lg:max-h-[300px] no_scrollbar shadow-xl`}
    //         >
    //           {searchSuggestions?.map((words) => {
    //             return (
    //               <Link
    //                 href={`book-detail?id=${words._id}`}
    //                 key={words?.title}
    //                 className="py-[16px] px-5 flex justify-between gap-10 group border-b border-b-gray-300 hover:bg-[#211F54]"
    //               >
    //                 <p className=" text-sm md:text-base flex items-center  lg:text-lg capitalize text-[#666] leading-normal lg:leading-[29px] line-clamp-1 group-hover:text-white">
    //                   {words?.title}
    //                 </p>
    //                 <div className="relative overflow-hidden w-[50px] h-[60px]">
    //                   <img
    //                     src={
    //                       words.images?.length
    //                         ? words.images[0]
    //                         : "/images/book_1.jpg"
    //                     }
    //                     alt={`search-book-${words?.title}`}
    //                     fill
    //                     //   objectFit="cover"
    //                     className="w-full h-full"
    //                   />
    //                 </div>
    //               </Link>
    //             );
    //           })}
    //         </div>
    //         // ) : // :
    //         // // <Link href={`search?book=${searchInput}`} className={`${searchComponentCss.searchSuggestion}`} >
    //         // //     <div className='flex justify-start items-center text-left w-full border-b-1 border-gray-200/10 hover:bg-gray-200 rounded-lg'>
    //         // //         <MdSearch color='#000' size={20} className='mx-2' />
    //         // //         {searchInput}
    //         // //     </div>
    //         // // </Link>
    //         // null
    //       } */}
    //       {searchInput && (
    //         <div
    //           id="suggestions"
    //           ref={suggestionsRef}
    //           className="absolute left-0 top-full bg-white w-full rounded-b-[15px] overflow-y-auto max-h-[300px] shadow-xl"
    //         >
    //           {searching ? (
    //             <div className="py-4 px-5 text-center text-gray-500 italic">
    //               Loading…
    //             </div>
    //           ) : hasFetched && searchSuggestions.length === 0 ? (
    //             <p className="py-4 text-center  px-5  text-gray-500 italic">
    //               No results found
    //             </p>
    //           ) : (
    //             searchSuggestions.map((book) => (
    //               <Link
    //                 href={`book-detail?id=${book._id}`}
    //                 key={book._id}
    //                 className="py-3 px-5 flex justify-between border-b border-gray-200 hover:bg-[#211F54]"
    //               >
    //                 <p className="truncate text-[#666] flex items-center group-hover:text-white">
    //                   {book.title}
    //                 </p>
    //                 <div className="w-[50px] h-[60px] overflow-hidden">
    //                   <img
    //                     src={book.images?.[0] || "/images/book_1.jpg"}
    //                     alt={book.title}
    //                     className="w-full h-full object-cover"
    //                   />
    //                 </div>
    //               </Link>
    //             ))
    //           )}
    //         </div>
    //       )}
    //     </form>
    //   </div>
    // </section>

    <section
      className={`${searchComponentCss.searchContainer} relative p-[10px] md:p-[40px_50px] min-h-[360px] md:min-h-[590px] lg:!px-[100px]`}
      // style={{
      //   backgroundImage:
      //     "linear-gradient(to right, rgba(0, 0, 0, 1), transparent), url('../../../public/images/landing1.png')",
      //   backgroundSize: "cover",
      //   backgroundPosition: "center",
      //   backgroundRepeat: "no-repeat",
      // }}
    >
      <div className="container-wrapper">
        <div className="mt-[27px] mb-7 md:mt-[11vh]">
          <div className="flex gap-1 md:items-center justify-start">
            {/* Avatar images */}
            <div className="flex relative">
              {avatarImages.map((image, idx) => (
                <div
                  key={idx}
                  className="relative h-[14px] w-[14px] md:h-[27px] md:w-[27px] rounded-full ring-[0.7px] ring-black overflow-hidden"
                  style={{left: `${idx * -4}px`, zIndex: idx}}
                >
                  <Image
                    src={image}
                    alt=""
                    className="inline-block rounded-full"
                    fill
                    objectFit="cover"
                    sizes="33w"
                  />
                </div>
              ))}
            </div>
            <p className="text-[10px] text-white leading-normal md:text-sm relative -left-[10px]">
              Jamaica's First Digital Education Ecosystem
            </p>
          </div>

          {/* Main Heading */}
          <h1
            className="
    mt-5
    text-[22px] sm:text-3xl md:mt-6 md:text-[40px] md:leading-[55px] md:tracking-tight
    lg:text-[58px] lg:leading-[72px] lg:tracking-[-2.88px]
    xl:leading-[75px]
    font-[playfair] font-bold text-[#ffc72c] lg:text-[#ffb800]
    
  "
          >
            "Don't go broke buying it new,
            <br />
            or throw it out when you are through.
            <br />
            <span
              className="
      text-white font-playfair
      text-lg sm:text-xl md:text-[36px] md:leading-[50px] md:tracking-tight
      lg:text-[55px] lg:leading-[65px]
      xl:text-[65px] xl:leading-[72px]
    "
            >
              ReBookIt and $ave!
            </span>
          </h1>
        </div>

        {/* Search Form */}
        <form
          onSubmit={(e) => {
            e.preventDefault();
            if (searchInput?.length > 0) {
              router.push(`/search?book=${searchInput}`);
            }
          }}
          className={`w-[98%] rounded-3xl m-auto md:absolute md:left-0 md:scale-[0.8] lg:scale-100 lg:relative lg:w-9/12 md:m-0 md:max-w-[900px] md:flex md:items-center relative lg:ml-[16px] ring-[8px] md:ring-[16px] ring-[#ffffff26] z-2 ${
            searchSuggestions?.length > 0 || hasFetched
              ? "rounded-[15px]"
              : "md:rounded-full"
          }`}
          role="search"
        >
          <div
            className={`flex justify-between items-center w-full bg-white rounded-[47.159px] p-2
                   md:rounded-[103px] md:p-[17px_21px_17px_29px] 
                   ${
                     searchSuggestions?.length > 0 || hasFetched
                       ? "!rounded-t-[15px] !rounded-b-none border-none"
                       : "border border-[#909090] shadow-[0px_1.43238px_5.72951px_-4.29713px_rgba(0,0,0,0.4)]"
                   }`}
            style={{borderWidth: "0.477459px"}}
          >
            <div className="rounded-full bg-[#025FA8] p-1 md:p-2">
              <LuBookOpen className="w-[10px] h-[10px] md:w-[23px] md:h-[23px] text-white" />
            </div>

            <input
              type="text"
              className="w-full px-2 outline-none border-none font-poppins font-[275] italic text-[12px] md:text-[16.77px] leading-[100%] text-[#757575] "
              placeholder="Search by Books, E-Directory, Events, Scholarship & Awards."
              aria-label="Search books"
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
            />

            <button
              className="flex justify-center items-center rounded-full bg-gradient-to-r from-[#211F54] to-[#0161ab] 
                           md:w-[159.355px] md:h-[50.323px] md:rounded-[27.5px] p-2 md:p-3"
            >
              <IoSearchSharp className="w-[10px] h-[10px] md:w-[20px] md:h-[20px] text-white" />
              <span className="text-white text-[10px] md:text-[16px]">
                Search
              </span>
            </button>
          </div>

          {/* Search Suggestions */}
          {searchInput && (
            <div
              id="suggestions"
              ref={suggestionsRef}
              className="absolute left-0 top-full bg-white w-full rounded-b-[15px] overflow-y-auto max-h-[300px] shadow-xl"
            >
              {searching ? (
                <div className="py-4 px-5 text-center text-gray-500 italic">
                  Loading…
                </div>
              ) : hasFetched && searchSuggestions.length === 0 ? (
                <p className="py-4 text-center px-5 text-gray-500 italic">
                  No results found
                </p>
              ) : (
                searchSuggestions.map((book) => (
                  <Link
                    href={`book-detail?id=${book._id}`}
                    key={book._id}
                    className="py-3 px-5 flex justify-between border-b border-gray-200 hover:bg-[#211F54]"
                  >
                    <p className="truncate text-[#666] hover:text-white">
                      {book.title}
                    </p>
                    <div className="w-[50px] h-[60px] overflow-hidden">
                      <img
                        src={book.images?.[0] || "/images/book_1.jpg"}
                        alt={book.title}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </Link>
                ))
              )}
            </div>
          )}
        </form>
      </div>
    </section>
  );
}
