.searchContainer {
  background: linear-gradient(to right, rgba(0, 0, 0, 1), transparent),
    url("../../../../public/images/landing1.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  // min-height: 37vh;
  // min-height: 360px;

  // @media (min-width: 768px) {
  //   height: 80px;
  //   padding: 40px 50px;
  //   // min-height: 78vh;
  //   // min-height: 78vh;
  //   min-height: 590px;
  // }

  // .mainHeading {
  //   // left: 10px;
  //   // top: 162px;
  //   font-family: "Playfair";
  //   font-style: normal;
  //   font-weight: 700;
  //   font-size: 31.504px;
  //   line-height: 32px;
  //   letter-spacing: -1.26px;
  //   color: #ffc72c;

  //   span {
  //     color: #fff;
  //   }

  //   @media (min-width: 769px) and (max-width: 999px) {
  //     color: #ffb800;
  //     letter-spacing: -2.88px;
  //     font-family: Playfair;
  //     // font-size: 62px;
  //     font-size: 72px;
  //     font-style: normal;
  //     // font-weight: 600;
  //     line-height: 72px;

  //     span {
  //       color: #fff;
  //       font-family: Playfair;
  //       // font-size: 72px;
  //       font-size: 40px;
  //       font-style: normal;
  //       font-weight: 600;
  //       line-height: 55px;
  //       letter-spacing: -2.88px;
  //     }
  //   }
  //   @media (min-width: 1000x) and (max-width: 1299px) {
  //     color: #ffb800;
  //     letter-spacing: -2.88px;
  //     font-family: Playfair;
  //     // font-size: 62px;
  //     font-size: 54px;
  //     font-style: normal;
  //     // font-weight: 600;
  //     line-height: 40px;

  //     span {
  //       color: #fff;
  //       font-family: Playfair;
  //       // font-size: 72px;
  //       font-size: 40px;
  //       font-style: normal;
  //       font-weight: 600;
  //       line-height: 55px;
  //       letter-spacing: -2.88px;
  //     }
  //   }

  //   @media (min-width: 1300px) and (max-width: 1600px) {
  //     color: #ffb800;
  //     letter-spacing: -2.88px;
  //     font-family: Playfair;
  //     // font-size: 62px;
  //     font-size: 72px;
  //     font-style: normal;
  //     font-weight: 600;
  //     line-height: 75px;

  //     span {
  //       color: #fff;
  //       font-family: Playfair;
  //       // font-size: 72px;
  //       font-size: 65px;
  //       font-style: normal;
  //       font-weight: 600;
  //       line-height: 72px;
  //       letter-spacing: -2.88px;
  //     }
  //   }
  // }

  // .searchBox {
  //   box-sizing: border-box;
  //   width: 100%;
  //   // height: 38.77px;
  //   background: #ffffff;
  //   border: 0.477459px solid #909090;
  //   box-shadow: 0px 1.43238px 5.72951px -4.29713px rgba(0, 0, 0, 0.4);
  //   border-radius: 47.159px;
  //   cursor: pointer;
  //   display: flex;
  //   justify-content: space-between;
  //   align-items: center;
  //   padding: 8px 12px;

  //   input[type="search"] {
  //     font-size: 12px;
  //     padding: 2px;
  //   }

  //   button {
  //     // width: 72.57px;
  //     // height: 22.92px;
  //     padding: 6px 16px;
  //     background: linear-gradient(268.27deg, #211f54 11.09%, #0161ab 98.55%);
  //     // border-radius: 12.4139px;
  //     // margin-right: 4px;
  //     cursor: pointer;
  //   }

  //   .buttonText {
  //     font-size: 10px;
  //     color: #fff;
  //     // margin-left: 2px;
  //   }

  //   @media (min-width: 769px) {
  //     // height: 75px;
  //     // height: 65px;
  //     border-radius: 103px;
  //     // width: 97%;
  //     // margin: auto;
  //     padding: 17px 21px 17px 29px;

  //     input[type="search"] {
  //       // font-size: 17px;
  //       font-size: 16px;
  //       margin-left: 10px;
  //     }

  //     button {
  //       display: flex;
  //       width: 159.355px;
  //       height: 50.323px;
  //       padding: 12.581px 35.209px 14.677px 35.521px;
  //       justify-content: center;
  //       align-items: center;
  //       gap: 5.241px;
  //       flex-shrink: 0;
  //       border-radius: 27.5px;
  //     }

  //     .buttonText {
  //       // font-size: 18px;
  //       font-size: 16px;
  //     }
  //   }
  // }

  // .searchSuggestion {
  //   cursor: pointer;
  //   background: #d9ceceab;
  //   border-radius: 10px;
  //   width: 94%;
  //   max-height: 200px;
  //   padding: 10px 42px;
  //   display: flex;
  //   position: absolute;
  //   top: 100px;
  //   left: 28px;
  // }
}
