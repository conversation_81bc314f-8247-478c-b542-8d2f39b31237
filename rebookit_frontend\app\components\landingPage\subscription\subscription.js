"use client";

import {useEffect, useState} from "react";
import subscriptionCss from "./subscription.module.scss";
import Slider from "react-slick";

import {
  IoArrowDownCircleOutline,
  IoArrowUpCircleOutline,
} from "react-icons/io5";
import {USER_ROUTES} from "@/app/config/api";
import {toast} from "react-toastify";
import {getsubscriptionPlans} from "@/app/services/profile";
import {useDispatch} from "react-redux";
import {planToPurchase} from "@/app/redux/slices/storeSlice";
import {subscriptionPlans} from "@/app/services/membership";

export default function Subscription({
  profileSection = false,
  cutomSize = null,
  setisPurchase,
  subscriptionList,
  currentPlan,
}) {
  const dummyData = [
    {
      planName: "yearly",
      description: "sample",
      postRangeFrom: "10",
      postRangeTo: "100",
      ed_Directory: "Yes",
      pastPapers: "Yes",
      educational_Resource: "Yes",
      ed_directory_individualListing: "1",
      ed_directory_busisnessListing: "1",
      featuredListing: "1",
      freeAds: "1",
      priceUnit: "JMD",
      price: "20",
      planMonths: "Monthly",
      chargeFeeCheckbox: true,
      usageCheckbox: true,
      trialPeriodCheckbox: true,
      trialPeriodDays: "10",
      trialPeriodUnit: "Days",
      benefits: [
        "Access to the Ed-irectory (e.g. Tutor listing)",
        "Access to the Ed-irectory (e.g. Tutor listing)",
        "Access to the Ed-irectory (e.g. Tutor listing)",
      ],
    },
    {
      planName: "monthly",
      description: "sample",
      postRangeFrom: "10",
      postRangeTo: "100",
      ed_Directory: "Yes",
      pastPapers: "Yes",
      educational_Resource: "Yes",
      ed_directory_individualListing: "1",
      ed_directory_busisnessListing: "1",
      featuredListing: "1",
      freeAds: "1",
      priceUnit: "JMD",
      price: "40",
      planMonths: "Yearly",
      chargeFeeCheckbox: true,
      usageCheckbox: true,
      trialPeriodCheckbox: true,
      trialPeriodDays: "10",
      trialPeriodUnit: "Days",
      benefits: ["Access to the Ed-irectory (e.g. Tutor listing)"],
    },
    {
      planName: "half Yearly",
      description: "sample",
      postRangeFrom: "10",
      postRangeTo: "100",
      ed_Directory: "Yes",
      pastPapers: "Yes",
      educational_Resource: "Yes",
      ed_directory_individualListing: "1",
      ed_directory_busisnessListing: "1",
      featuredListing: "1",
      freeAds: "1",
      priceUnit: "JMD",
      price: "40",
      planMonths: "Quarterly",
      chargeFeeCheckbox: true,
      usageCheckbox: true,
      trialPeriodCheckbox: true,
      trialPeriodDays: "10",
      trialPeriodUnit: "Days",
      benefits: ["Access to the Ed-irectory (e.g. Tutor listing)"],
    },
  ];
  const [formatedCategory, setformatedCategory] = useState({});

  const settings = {
    dots: false,
    infinite: true,
    speed: 500,
    slidesToShow: 3,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    initialSlide: 0,
    responsive: [
      {
        breakpoint: 1400,
        settings: {
          slidesToShow: 2,
          slidesToScroll: 1,
          infinite: true,
          dots: false,
        },
      },
      {
        breakpoint: 1070,
        settings: {
          slidesToShow: 1,
          slidesToScroll: 1,
          infinite: true,
          dots: false,
        },
      },
      // {
      //     breakpoint: 769,
      //     settings: {
      //         slidesToShow: 1,
      //         slidesToScroll: 1,
      //         infinite: true,
      //         dots: false
      //     }
      // },
    ],
  };

  const [activePlan, setActivePlan] = useState(1);
  const [openDropdownIdx, setOpenDropdownIdx] = useState(null);
  const [seeMore, setSeeMore] = useState([]);
  const dispatch = useDispatch();

  const toggleDropdown = (idx) => {
    setOpenDropdownIdx((prev) => (prev === idx ? null : idx));
  };

  useEffect(() => {}, []);

  useEffect(() => {
    if (subscriptionList?.length > 0) {
      const allIndices = subscriptionList.map((_, idx) => idx);
      setSeeMore(allIndices);
    }
  }, [subscriptionList]);
  console.log("subscriptionList in listin", subscriptionList);
  const seeMoreDetailsHandler = (index) => {
    setSeeMore((prev) =>
      !prev.includes(index) ? [...prev, index] : prev.filter((p) => p !== index)
    );
  };
  //   console.log(seeMore, "seeMore");
  // feature for a subscription
  const FeatureLine = ({text}) => (
    <div className="flex gap-2.5">
      <div className="bg-[#1DC9A0] p-0.5 w-fit self-baseline rounded-[2px] mt-1">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="9"
          height="9"
          viewBox="0 0 9 9"
          fill="none"
        >
          <path
            d="M6.83984 2.95337L3.34326 6.44995L1.75391 4.8606"
            stroke="white"
            strokeWidth="0.817383"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </div>
      <p className="text-sm leading-5 text-[#1a1a1ab3]">{text}</p>
    </div>
  );

  const isyourPlan = (currentPlant, plan) => {
    if (plan._id == currentPlant?.planId?._id) {
      return true;
    } else {
      return false;
    }
  };
  let planState = ["Active", "DownGrade", "Upgrade"];
  const upgradeDowngrade = (currentPlant, plan) => {
    if (currentPlant?.planId?.price == plan?.price) {
      return planState[0];
    } else if (currentPlant?.planId.price > plan.price) {
      return planState[1];
    } else {
      return planState[2];
    }
  };
  return (
    <section
      className={`${subscriptionCss.subscriptionContainer} ${
        !profileSection && "md:px-[50px] lg:px-[100px] md:py-[40px]"
      }`}
    >
      <section className="container-wrapper">
        {!profileSection && (
          <header>
            <h2 className="text-[22px] font-semibold leading-normal mb-2.5 uppercase md:text-[48px]">
              A “Small change” can do a world of good
            </h2>
            <p className="font-light leading-[18px] text-xs md:text-[18px] md:leading-[27px] md:w-8/12">
              Explore stories, knowledge, and imagination with
              <span className="font-semibold"> ReBookIt.Club </span>
              Find academic essentials, timeless novels, and rare gems—all in
              one place.
            </p>
          </header>
        )}

        {/* <div className="slider-container hidden md:block lg:grid lg:grid-cols-3">
                    <Slider {...settings}>
                        {subscriptionList?.map((item, idx) => (
                            <section
                                key={idx}
                                className="py-20 pr-10"
                            >
                                <section
                                    className={`${subscriptionCss.card} ${activePlan == idx && subscriptionCss.active} ${cutomSize ? cutomSize : "scale-110"}`}
                                    onClick={() => setActivePlan(idx)}
                                    aria-label={`Subscription plan: ${item.planName}`}
                                >
                                    <div>
                                        <h3 className={`${subscriptionCss.planName} font-semibold `}>
                                            {item.planName}
                                        </h3>
                                        <p className='md:text-[33px]'>
                                            <span className='font-bold'>{item.priceUnit}$ {item.price} </span>
                                            <span className='text-sm leading-[23px] text-[#1a1a1ab3] font-normal'>/per </span><span className='text-sm leading-[23px] text-[#1a1a1ab3] font-normal capitalize'>{item?.planMonths}</span>
                                        </p>
                                        <p className='text-gray-600 text-[14px] md:text-[16px]'>{item.description}</p>
                                    </div>

                                    <button className={`${subscriptionCss.button}`}>Get Started</button>

                                    <div className={`${subscriptionCss.dashedBorder}`}></div>

                                    <footer className='flex justify-between items-center w-full'
                                    // onClick={() => toggleDropdown(idx)}
                                    >
                                        <p className='text-[12px] text-gray-600 md:text-[16px]'>See More Details</p>
                                        <p>
                                            {openDropdownIdx === idx ?
                                                <IoArrowUpCircleOutline className='cursor-pointer h-[24px] w-[24px] md:h-[31px] md:w-[31px]' size={24} />
                                                :
                                                <IoArrowDownCircleOutline className='cursor-pointer h-[24px] w-[24px] md:h-[31px] md:w-[31px]' size={24} />
                                            }
                                        </p>
                                    </footer>

                                    {openDropdownIdx === idx && (
                                        <section className={`${subscriptionCss.dropDown}`}>
                                            <ul className='mt-[20px]'>
                                                {item?.benefits?.map(list => (
                                                    <li key={list} className='text-[13px] list-disc mt-[9px]'>
                                                        {list}
                                                    </li>
                                                ))}
                                            </ul>
                                        </section>
                                    )}

                                </section>
                            </section>
                        ))}
                    </Slider>
                </div> */}

        <div
          className={`my-5 grid grid-cols-1 justify-center gap-2.5 md:gap-[18px] md:grid-cols-2 lg:grid-cols-3`}
        >
          {subscriptionList?.map((item, idx) => (
            <section
              key={idx}
              className={`w-full h-fit ${subscriptionCss.card} ${
                isyourPlan(currentPlan, item) && subscriptionCss.active
              } `}
              onClick={() => setActivePlan(idx)}
              aria-label={`Subscription plan: ${item?.planName}`}
            >
              <div>
                <h3 className={`${subscriptionCss.planName} font-semibold`}>
                  {item?.planName}
                </h3>
                <p className="mt-2 md:mt-3">
                  <span className="text-[22px] leading-[30px] font-bold md:text-[25px] md:leading-[36px]">
                    {item?.priceUnit}$ {item?.price}{" "}
                  </span>
                  <span className="text-[#1A1A1AB3] text-xs leading-[17px] md:leading-[19px]">
                    /per&nbsp;
                  </span>
                  <span className="text-[#1A1A1AB3] text-xs leading-[17px] md:leading-[19px] lowercase">
                    {item?.planMonths}
                  </span>
                </p>
                {/* <p className='mt-2 md:mt-3 text-[#1A1A1AB3] leading-5 text-[14px] md:text-xs md:leading-[19px]'>{item.description}</p> */}
              </div>

              <button
                className={`mt-3 md:mt-5 lg:mt-[22px] ${subscriptionCss.button}`}
                onClick={() => {
                  if (upgradeDowngrade(currentPlan, item) == planState[0]) {
                    return;
                  }
                  dispatch(planToPurchase(item));
                  setisPurchase(true);
                }}
              >
                {upgradeDowngrade(currentPlan, item)}
              </button>

              <div
                className={`my-3 md:my-5 lg:my-[22px] ${subscriptionCss.dashedBorder}`}
              ></div>

              <div
                className={`flex flex-col gap-2.5 transition-all duration-1000 linear overflow-hidden ${
                  seeMore.includes(idx)
                    ? "h-fit max-h-[250px] pb-[22px]"
                    : "max-h-0 pb-0"
                }`}
              >
                {item?.listings?.map((data) => {
                  console.log("data in lising", data);
                  return (
                    <FeatureLine
                      text={`${data?.noOfListing} ${data.category?.name} (${data?.listingValidityDays} Days listing validity) `}
                    />
                  );
                })}
                <FeatureLine text={`${item.boosts} Boost-up`} />
                <FeatureLine text={`Unlimited Chats`} />
                <FeatureLine
                  text={`Access to view E-Directory, Ed-Events, SG&A`}
                />

                {/* {
                  item?.listings?.map((data)=>{
                  return <FeatureLine text={`${data?.noOfListing} ${data.name||"NA"}`} />
                  })
                } */}
              </div>

              <footer
                className="flex justify-between items-center w-full cursor-pointer "
                onClick={() => seeMoreDetailsHandler(idx)}
              >
                <p
                  className={`text-xs text-[#1A1A1AB3] leading-[26px] md:leading-[19px]`}
                >
                  See {seeMore.includes(idx) ? "Less" : "More"} Details
                </p>
                <p>
                  <IoArrowDownCircleOutline
                    className={`cursor-pointer h-[24px] w-[24px] transition-transform duration-200 ease-in-out ${
                      seeMore.includes(idx) ? "-rotate-180" : ""
                    }`}
                    size={24}
                  />
                </p>
              </footer>
            </section>
          ))}
        </div>
      </section>
    </section>
  );
}
