.subscriptionContainer {




    .dropDown {

        height: 141px;
        padding: 15px;


        .benefits {
            margin-top: 16px;
            font-size: 13px;
            padding: 14px;

            ul {
                li {
                    list-style: disc;
                }
            }
        }

        transition: all 0.3s ease-in;

    }

    .card {
        display: flex;
        // width: 370px;
        // min-width: 320.305px;
        padding: 9px 18.841px;
        flex-direction: column;
        align-items: flex-start;
        border-radius: 11.305px;
        border: 0.942px solid #E1E1E2;
        background: #fff;
        box-shadow: 0.471px 0.471px 3 0px #1A1A1A · 8%;
        cursor: pointer;
        transition: all 0.3s ease-in;


        .planName {
            color: #1A1A1AB3;
            font-size: 16px;
            line-height: 30px;

        }

        .button {
            display: flex;
            height: 56px;
            min-height: 41.451px;
            padding: 0px 15.073px;
            justify-content: center;
            align-items: center;
            gap: 11.305px;
            align-self: stretch;
            border-radius: 71.3px;
            background: #1D1D20;
            color: #fff;
            cursor: pointer;
        }

        .dashedBorder {
            height: 0.942px;
            align-self: stretch;
            border: none;
            border-top: 0.942px dashed #1A1A1A2E;

        }
    }



    .active {

        background: linear-gradient(180deg, rgba(3, 93, 165, 0.15) 0%, rgba(33, 31, 84, 0.10) 100%);
        border-radius: 11.305px;
        border: 0.942px solid #0161AB;
        box-shadow: 0.471px 0.471px 3 0px #1A1A1A14;

        .planName {
            color: #4D7906;
        }

        .button {

            border-radius: 71.3px;
            border: 0.942px solid #1A1A1A14;
            background: #025FA8;
        }
    }


    @media (min-width: 769px) {

        // padding: 40px 100px;

        .card {
            display: flex;
            // width: 369.927px;
            // width: 360.927px;
            // min-width: 320.305px;
            padding: 18px 15px;
            flex-direction: column;
            align-items: flex-start;
            border-radius: 11.305px;
            border: 0.942px solid #E1E1E2;
            background: #fff;
            box-shadow: 0.471px 0.471px 3 0px #1A1A1A · 8%;
            // transform: scale(1.1);

            .planName {
                line-height: 22px;
            }

            .button {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 11.305px;
                align-self: stretch;
                border-radius: 71.3px;
                background: #1D1D20;
            }
        }


        .active {

            background: linear-gradient(180deg, rgba(3, 93, 165, 0.15) 0%, rgba(33, 31, 84, 0.10) 100%);
            border-radius: 11.305px;
            border: 0.942px solid #0161AB;
            box-shadow: 0.471px 0.471px 3 0px #1A1A1A14;

            .planName {
                color: #4D7906;
            }

            .button {

                border-radius: 71.3px;
                border: 0.942px solid #1A1A1A14;
                background: #025FA8;
            }
        }

    }
}