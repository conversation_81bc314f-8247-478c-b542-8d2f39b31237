.testimonialContainer {
    // padding: 10px;

    overflow: hidden;

    .testimonial-card {
        width: 369px;
        height: 320px;
        flex-shrink: 0;
        border-radius: 5.608px;
        border: 0.561px solid #025FA8;
        background: #FAFAFA;
        position: relative;
        padding: 10px;

        img {
            width: 135.907px;
            height: 135.907px;
            flex-shrink: 0;
            border-radius: 50%;
            margin: auto;
        }
    }


    @media (min-width: 769px) {

        padding: 40px 100px;

        .slick-slide {
            transition: transform 300ms ease, opacity 300ms ease;
        }

        .testimonial-card {
            // width: 365.391px;
            height: 338px;
            flex-shrink: 0;
            border-radius: 6.207px;
            border: 0.621px solid var(--Linear, #c4c4c4c4);
            background: #FAFAFA;

            img {
                width: 150.415px;
                height: 150.415px;
                flex-shrink: 0;

            }

            transform: scale(1);
            transition: all 0.2s linear;
        }

        .testimonials-slick-slide {
            transition: transform 300ms ease, opacity 300ms ease;
        }

        // .testimonials-slick-center {
        //     transform: scale(1.3);
        //     z-index: 1;
        //     opacity: 1;
        //     margin: 0 6px;
        // }


        // .slick-slide:not(.testimonials-slick-center) {
        //     transform: scale(1) !important;
        //     opacity: 0.8;
        // }

    }

    // .active {
    //     transform: scale(1.3);
    //     z-index: 2;

    // }
}