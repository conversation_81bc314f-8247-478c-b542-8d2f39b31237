"use client";

import React, {useEffect, useRef, useState} from "react";
import "./testimonial.scss";
// import Slider from 'react-slick';

import {PiQuotes} from "react-icons/pi";
import javon from "@/public/images/testimonials/javon.svg";
import michelle from "@/public/images/testimonials/michelle.svg";
import andre from "@/public/images/testimonials/andre.svg";
import david from "@/public/images/testimonials/david.svg";
import tiana from "@/public/images/testimonials/tiana.svg";
import Image from "next/image";
import {useElementWidth} from "../../common/WidthChecker";
import dynamic from "next/dynamic";
import {getTestimonials} from "@/app/services/profile";

const Slider = dynamic(() => import("@/app/components/common/Slider"), {
  ssr: false,
  loading: () => <div>Loading slider...</div>,
});

export default function Testimonials() {
  const [ref, width] = useElementWidth();
  const sliderRef = useRef(null);

  const [isDesktop, setIsDesktop] = useState(false);

  console.log("width", width);

  useEffect(() => {
    setIsDesktop(width >= 768);
  }, [width]);

  const [testimonialData, setTestimonialData] = useState([]);

  const getTestimonialsFunc = async () => {
    let response = await getTestimonials();
    console.log("response testimonials", response);
    if (response.status == 200) {
      setTestimonialData(response.data);
    }
  };
  useEffect(() => {
    // setTestimonialData(dummyTesData);
    getTestimonialsFunc();
  }, []);

  const settings = {
    dots: width < 720 ? true : false,
    infinite: true,
    speed: 500,
    slidesToShow: width < 720 ? 1 : 3,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
    // className: "center",
    // centerMode: true,
    // centerPadding: "0px",

    // afterChange: (current) => {
    //     // This ensures the next slide gets the slick-center class
    //     document.querySelector('.testimonials-slider .slick-current + .slick-active')?.classList.add("testimonials-slick-center");
    //     document.querySelector('.testimonials-slider .slick-current')?.classList.remove("testimonials-slick-center");
    // },

    responsive: [
      {
        breakpoint: 720,
        settings: {
          slidesToShow: 1,
          centerMode: false,
          initialSlide: 0,
        },
      },
    ],

    appendDots: (dots) => {
      // Get active dot index
      const activeIndex = dots.findIndex((dot) =>
        dot.props.className.includes("slick-active")
      );
      let visibleDots = [];

      // Show max 3, center active dot if possible
      if (dots.length <= 3) {
        visibleDots = dots;
      } else {
        const start = Math.max(0, Math.min(activeIndex - 1, dots.length - 3));
        visibleDots = dots.slice(start, start + 3);
      }

      return (
        <div className="mt-4">
          <ul className="flex justify-center gap-5">{visibleDots}</ul>
        </div>
      );
    },
  };

  // // Initialize center slide on mount
  useEffect(() => {
    if (sliderRef.current && width >= 768) {
      setTimeout(() => {
        const sliderParent = document.querySelector(".testimonials-slider");
        const slides = sliderParent.querySelectorAll(".slick-slide");

        slides[3]?.classList.remove(
          "slick-current",
          "testimonials-slick-center"
        );
        slides[4]?.classList.add("slick-current", "testimonials-slick-center");
      }, 100);
    }
  }, [width]);

  // Initialize center slide on mount
  useEffect(() => {
    if (!isDesktop || !sliderRef.current) return;
    const sliderParent = document.querySelector(".testimonials-slider");
    const slides = sliderParent.querySelectorAll(".slick-slide");

    if (slides.length > 4) {
      slides[3]?.classList.remove("slick-current", "testimonials-slick-center");
      slides[4]?.classList.add("slick-current", "testimonials-slick-center");
    }
  }, [isDesktop]);

  return (
    <section ref={ref} className={`testimonialContainer`}>
      <div className="container-wrapper">
        <header className="md:flex md:justify-between md:items-center p-2.5 md:py-[40px]">
          <h2 className="text-[22px] font-semibold mt-5 leading-normal uppercase md:text-[48px]">
            What our users Say
          </h2>
          <p className="mt-2.5 font-light leading-[18px] text-xs md:text-[18px] md:leading-[27px] md:w-5/12">
            Explore stories, knowledge, and imagination with{" "}
            <strong>ReBookIt.Club</strong> Find academic essentials, timeless
            novels, and rare gems—all in one place.
          </p>
        </header>

        <section className="my-5 md:my-10">
          <div className="slider-container ">
            <Slider
              ref={sliderRef}
              sliderSettings={settings}
              className="custom_slider_dots testimonials-slider w-full"
            >
              {testimonialData?.map((data, idx) => (
                <div
                  key={`testimonial-${idx} `}
                  className="h-[320px] md:h-[445px] overflow-hidden md:px-2.5 "
                >
                  <div className="slide-content h-full w-full flex flex-col items-center justify-center">
                    <div className={`testimonial-card border border-gray-300`}>
                      <Image
                        src={data.image}
                        width={200}
                        height={200}
                        alt="user Image"
                      />
                      <PiQuotes
                        size={20}
                        color="#07559B"
                        className="absolute right-5 top-46"
                      />
                      <div className="my-3 text-center text-[15px]">
                        <p className="capitalize font-medium">{data.name}</p>
                        <p className="my-2 font-light text-gray-900">
                          {data?.title.slice(0, 60)}
                        </p>
                        <p className="text-[13px] font-light text-gray-900 break-words">
                          {" "}
                          {data?.content?.slice(0, 200)}...
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </Slider>
          </div>
        </section>
      </div>
    </section>
  );
}
