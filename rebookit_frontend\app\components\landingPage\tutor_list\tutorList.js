"use client";

import { useEffect, useState } from "react";
import Image from "next/image";
import sampleImage from "@/public/test.jpeg";

import { HiChatBubbleLeftRight } from "react-icons/hi2";
import Link from "next/link";
import { isWithin10Km } from "@/app/utils/utils";
import { bookSearch } from "@/app/services/bookDetails";
import { ELASTIC_DB_ROUTES } from "@/app/config/api";
import { ItemKindEnum, itemToKind } from "@/app/config/constant";
import style from "./tutorList.module.scss";
import { useRouter } from "next/navigation";
import { getCategories } from "@/app/services/profile";
export default function TutorList() {
  const [tutorList, setTutorList] = useState([]);
  const [location, setLocation] = useState({});
  const router = useRouter();

  console.log(tutorList, "tutorList")

  useEffect(() => {
    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          setLocation({ lat: latitude, lng: longitude });
        },
        (error) => {
          console.error("Error getting location:", error);
        }
      );
    } else {
      console.log("Geolocation is not supported");
    }
  }, []);


  const getAllTutor = async () => {

    let getCateogriesData = await getCategories();

    // Get the id of the item that has name "E-directory"
    const eDirectoryCategory = getCateogriesData?.data?.categories.find((item) => item.name === "E-directory");
    const eDirectoryId = eDirectoryCategory ? eDirectoryCategory._id : undefined;
    console.log(eDirectoryId, "searchData===");

    let payload = {
      filters: {
        "category": [eDirectoryId],
      },
      sort: {},
    };
    let urlstring =
      ELASTIC_DB_ROUTES.SEARCH?.replace("{{page}}", 1) + "&pageSize=10";

    let searchData = await bookSearch(urlstring, payload);
    console.log("searchData", searchData);
    if (searchData?.data) {
      // let tempArr = [];
      // searchData.data?.data?.map((item) => {
      //   console.log("item type", item);

      //   //"SchoolItem"
      //   if (item["__t"] == "TutorItem") {
      //     // console.log()
      //     let userLoc = item.address.geometry.location.coordinates;
      //     const isWithinRange = isWithin10Km(
      //       location.lat,
      //       location.lng,
      //       userLoc[1],
      //       userLoc[0]
      //     );
      //     console.log("isWithinRange", isWithinRange);
      //     if (isWithinRange) {
      //       tempArr.push(item);
      //     }
      //   }
      // });
      // console.log("tempArr", tempArr);
      setTutorList(searchData.data?.data);
    }
  };
  console.log(tutorList, "tutorList");
  let sampleList = [
    {
      image: sampleImage,
      name: "Kerri Berrick",
      location: "Kingston, Jamican",
    },
    {
      image: sampleImage,
      name: "Kerri Berrick",
      location: "Kingston, Jamican",
    },
    {
      image: sampleImage,
      name: "Kerri Berrick",
      location: "Kingston, Jamican",
    },
    {
      image: sampleImage,
      name: "Kerri Berrick",
      location: "Kingston, Jamican",
    },
  ];
  useEffect(() => {
    getAllTutor();
    if (location?.lat) {
    }
  }, []);

  // isWithin10Km

  const cutText = (text, limit) => {
    if (text.length > limit) {
      return text.slice(0, limit) + "...";
    } else {
      return text;
    }
  };
  if (tutorList.length) {
    return (
      <section
        className={`bg-[#211F54] text-white pt-6 pb-[38px] px-2.5 md:px-[50px] lg:px-[100px] md:pt-[80px] md:pb-[90px]`}
        aria-labelledby="tutor-section-heading"
      >
        <div className="container-wrapper">
          <section className="md:flex md:justify-between md:items-start">
            <header>
              <h2
                id="tutor-section-heading"
                className="text-[22px] font-semibold uppercase leading-normal md:text-[48px]"
              >
                Edu-Service Listings By Parish
              </h2>
              <p className="text-xs leading-[18px] font-light mt-2.5 md:text-[18px] md:leading-[27px] md:w-10/12">
                Find the right school, tutor, or extra-curricular activity for you
                or your child, near you.
              </p>
            </header>

            <footer className="hidden md:flex justify-center my-5 ">
              {/* <Link href="/search" aria-label="View all book categories">
              <svg
                width="178"
                height="72"
                viewBox="0 0 178 72"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M171.629 35.7285C171.629 19.31 157.778 6.00018 140.692 6.00018L36.9361 6.00017C19.8503 6.00017 5.99954 19.31 5.99954 35.7285"
                  stroke="#211F54"
                  strokeWidth="11.679"
                />
                <path
                  d="M171.629 35.7285C171.629 19.31 157.827 6.00018 140.802 6.00018L37.412 6.00017"
                  stroke="#0161AB"
                  strokeWidth="11.679"
                />
                <path
                  d="M6 35.7285C6 52.147 19.8507 65.4569 36.9365 65.4569H140.693C157.779 65.4569 171.629 52.147 171.629 35.7285"
                  stroke="#EFDC2A"
                  strokeWidth="11.679"
                />
                <path
                  d="M37.4121 65.4569H140.802C157.827 65.4569 171.629 52.147 171.629 35.7285"
                  stroke="#0161AB"
                  strokeWidth="11.679"
                />
                <path
                  d="M140.693 6L36.937 5.99999"
                  stroke="#FF0009"
                  strokeWidth="11.679"
                />
                <path
                  d="M140.693 65.457L36.937 65.457"
                  stroke="#4A8B40"
                  strokeWidth="11.679"
                />
                <rect
                  x="11.6016"
                  y="7.93848"
                  width="154.01"
                  height="54.6036"
                  rx="27.3018"
                  fill="white"
                />
                <text
                  x="50%"
                  y="50%"
                  dominantBaseline="middle"
                  textAnchor="middle"
                  fontSize="20"
                  fill="#211F54"
                  fontFamily="Poppins, sans-serif"
                  fontWeight="500"
                >
                  View All
                </text>
              </svg>
            </Link> */}
            </footer>
          </section>
          <ul
            className="
            flex 
            flex-row 
            gap-4 
            mt-8 
            mb-5 
            overflow-x-auto 
            no-scrollbar
            snap-x
            snap-mandatory
            px-1
            h-[360px]
          "
            role="list"
            style={{ WebkitOverflowScrolling: "touch" }}
          >
            {tutorList.map((tutor) => (
              <li
                key={tutor._id}
                onClick={() => router.push(`book-detail?id=${tutor._id}`)}
                className="
              mb-4
                flex 
                flex-col 
                bg-white 
                rounded-lg 
                p-4 
                gap-4 
                cursor-pointer 
                hover:shadow-lg 
                transition-shadow
                min-w-[260px]
                max-w-xs
                snap-start
                flex-shrink-0
              "
                style={{ width: "260px" }}
              >
                {/* Image */}
                <div className="w-full aspect-[4/3] overflow-hidden rounded-lg">
                  <img
                    src={tutor.images[0]}
                    alt={`${tutor.name} profile`}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Info */}
                <article className="flex flex-col flex-1 justify-between">
                  <div>
                    <h3 className="text-lg font-semibold uppercase text-black truncate">
                      {tutor.title}
                    </h3>
                    <p className="mt-1 text-sm text-gray-600 truncate">
                      Category: {tutor.subcategoryDoc?.name}
                    </p>
                    {/* <p className="mt-1 text-sm text-gray-600">
                  Description: {tutor.description}
                  </p> */}
                    <p className="mt-1 text-sm text-gray-600 line-clamp-2 truncate">
                      Location: {cutText(tutor.address.formatted_address, 55)}
                    </p>
                  </div>
                  <button
                    className="
                    mt-4 
                    w-full 
                    py-2 
                    rounded-full 
                    text-white 
                    global_linear_gradient 
                    font-medium 
                    text-sm 
                    text-center
                  "
                  >
                    J$ {tutor.price}
                  </button>
                </article>
              </li>
            ))}
          </ul>

          <footer className="flex md:hidden justify-center">
            {/* <Link href="/search" aria-label="View all book categories">
            <svg
              className="cursor-pointer"
              width="101"
              height="41"
              viewBox="0 0 101 41"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M97.5791 20.1641C97.5791 10.8877 89.7535 3.36779 80.1002 3.36779L21.4787 3.36779C11.8254 3.36779 3.99986 10.8877 3.99986 20.1641"
                stroke="#211F54"
                strokeWidth="6.59854"
              />
              <path
                d="M97.5791 20.1641C97.5791 10.8877 89.7812 3.36779 80.162 3.36779L21.7476 3.36779"
                stroke="#0161AB"
                strokeWidth="6.59854"
              />
              <path
                d="M4.00049 20.1641C4.00049 29.4404 11.826 36.9603 21.4794 36.9603H80.1009C89.7542 36.9603 97.5797 29.4404 97.5797 20.1641"
                stroke="#EFDC2A"
                strokeWidth="6.59854"
              />
              <path
                d="M21.748 36.9603H80.1624C89.7816 36.9603 97.5795 29.4404 97.5795 20.1641"
                stroke="#0161AB"
                strokeWidth="6.59854"
              />
              <path
                d="M80.1011 3.36719L21.4796 3.36718"
                stroke="#FF0009"
                strokeWidth="6.59854"
              />
              <path
                d="M80.1011 36.9609L21.4796 36.9609"
                stroke="#4A8B40"
                strokeWidth="6.59854"
              />
              <rect
                x="7.16504"
                y="4.46094"
                width="87.0145"
                height="30.8506"
                rx="15.4253"
                fill="white"
              />
              <text
                x="50%"
                y="50%"
                dominantBaseline="middle"
                textAnchor="middle"
                fontSize="12"
                fontWeight="500"
                fill="#211F54"
                fontFamily="Poppins, sans-serif"
              >
                View All
              </text>
            </svg>
          </Link> */}
          </footer>
        </div>
      </section>
    );
  } else {
    return ""
  }
}
