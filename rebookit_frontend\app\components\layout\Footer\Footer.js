import React from "react";
import Link from "next/link";

import {FaFacebookF} from "react-icons/fa";
import {BsTwitterX} from "react-icons/bs";
import {FaInstagram} from "react-icons/fa";
import {AiOutlineTikTok} from "react-icons/ai";
import {PiSnapchatLogoFill} from "react-icons/pi";

import footerCss from "./footer.module.scss";

export default function Footer() {
  const footerLinks = {
    "Company Info": [
      {name: "About Rebookit.Club", link: "/about"},
      {name: "Contact us", link: "/contact-us"},
      {name: "Community guidelines", link: "/community"},
      {name: "Privacy policy", link: "/privacy-policy"},
      // {name: "Disclaimer", link: "/disclaimer"},
      // {name: "Terms & Conditions", link: "/terms-and-conditions"},
    ],
    "Help & Support": [
      {name: "Customer care", link: "/contact-us"},
      {name: "How to sell books", link: "/seller"},
      // {name: "Shipping & Delivery Info", link: "/shipping-delivery-info"},
      // {name: "Return & Refunds", link: "/return-refund"},
      // {name: "Payment Methods", link: "/payment-methods"},
    ],
    Resources: [
      // {name: "How it works", link: "/how-it-works"},
      {name: "Report an issue", link: "/contact-us"},
      {name: "FAQs", link: "/faq"},
    ],
    Socials: [
      {
        icon: <FaFacebookF size={15} color="#211F54" />,
        label: "Facebook",
        link: "https://www.facebook.com",
      },
      {
        icon: <BsTwitterX size={15} color="#211F54" />,
        label: "Twitter",
        link: "https://www.x.com",
      },
      {
        icon: <FaInstagram size={15} color="#211F54" />,
        label: "Instagram",
        link: "https://www.instagram.com",
      },
      // {
      //   icon: <AiOutlineTikTok size={15} color="#211F54" />,
      //   label: "TikTok",
      //   link: "#",
      // },
      // {
      //   icon: <PiSnapchatLogoFill size={15} color="#211F54" />,
      //   label: "Snapchat",
      //   link: "#",
      // },
    ],
  };

  return (
    <footer
      className={`${footerCss.footerContainer} text-white flex flex-col justify-center items-center lg:relative`}
    >
      <section className="container-wrapper flex flex-col justify-center items-center w-full">
        <div className="flex justify-between items-start w-full flex-wrap lg:flex-nowrap ">
          <section className="flex flex-wrap justify-between p-3 w-full ">
            {Object.keys(footerLinks).map((key, index) => (
              <nav
                key={index}
                className={`w-40 lg:w-60 ${key == "Socials" && "lg:hidden"}`}
                aria-label={key}
              >
                <h2
                  className={`font-semibold text-lg my-4 lg:text-[20px] ${
                    key == "Socials" && "lg:hidden"
                  }`}
                >
                  {key}
                </h2>

                {key !== "Socials" ? (
                  <ul className="flex flex-col  flex-wrap gap-2">
                    {footerLinks[key].map((item, idx) => (
                      <li key={idx}>
                        <Link href={item.link}>
                          <p className="text-xs hover:underline lg:text-[16px]">
                            {item.name}
                          </p>
                        </Link>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <ul className="flex flex-wrap gap-2 lg:hidden">
                    {footerLinks[key].map((item, idx) => (
                      <li key={idx}>
                        <Link href={item.link} aria-label={item.label}>
                          <div className="h-6 w-6 bg-white rounded-full flex justify-center items-center">
                            {item.icon}
                          </div>
                        </Link>
                      </li>
                    ))}
                  </ul>
                )}
              </nav>
            ))}
          </section>

          <section className="p-3 lg:min-w-[500px]">
            <section>
              <h2 className="hidden lg:block font-semibold text-lg my-4 lg:text-[20px]">
                Socials
              </h2>
              <ul className="hidden lg:flex flex-wrap gap-2 ">
                {footerLinks["Socials"].map((item, idx) => (
                  <li key={idx}>
                    <a
                      href={item.link}
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      <div className="h-6 w-6 bg-white rounded-full flex justify-center items-center">
                        {item.icon}
                      </div>
                    </a>
                  </li>
                ))}
              </ul>
            </section>
            {/* <div className='mt-8 w-full '>
              <h2 className='text-sm font-medium lg:text-lg lg:text-[18px]'>SIGN UP for Rebookit NEWS</h2>
              <form className='flex items-center gap-2 mt-2' onSubmit={(e) => e.preventDefault()}>
                <label htmlFor="email-newsletter" className="sr-only">Email</label>
                <input
                  type='email'
                  id="email-newsletter"
                  placeholder="Enter your email"
                  className='bg-white rounded-2xl border-none h-8 px-3 text-black text-[14px] w-full lg:text-[14px] lg:p-5 outline-none'
                  required
                />
                <input
                  type='button'
                  value="SUBSCRIBE"
                  className='text-[12px] bg-[#211F54] text-white px-4 py-1 rounded-full cursor-pointer'
                />
              </form>
            </div> */}
          </section>
        </div>

        <hr className="w-11/12 mx-auto border-t border-[#DBDBDB] lg:my-5 lg:w-full" />

        <p className="text-sm text-center my-5 lg:my-10 lg:text-[16px]">
          &#169;2025 <span className="font-semibold">| Rebookit.Club</span> All
          Rights Reserved
        </p>
      </section>
    </footer>
  );
}
