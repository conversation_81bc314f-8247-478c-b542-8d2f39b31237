.footerContainer {

    background: #211F54;
    overflow-x: hidden;
    min-height: 20vh;

    padding: 10px;

    input[type='text'] {
        width: 225px;
        padding: 20px;
    }

    input[type='button'] {

        box-sizing: border-box;
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
        gap: 10px;
        width: 130px;
        height: 40px;
        background: linear-gradient(268.27deg, #211F54 11.09%, #0161AB 98.55%);
        border-radius: 110px;
        text-transform: uppercase;
        border: 1px solid #025fa9;
        cursor: pointer;
        font-size: 14px;

    }

    @media (min-width: 769px) {
        padding: 25px 100px;
    }

}