import React, {useEffect, useState} from "react";
import {usePathname, useRouter} from "next/navigation";

import Image from "next/image";
import Link from "next/link";
import {RxHamburgerMenu} from "react-icons/rx";

import flag from "@/public/flag.png";
import logo from "@/public/images/rebookitLogo.gif";
import headerCss from "./header.module.scss";

import DropdownItem from "./dropDown";

import {IoIosArrowDown} from "react-icons/io";
import {LuBookmark} from "react-icons/lu";
import {RiUserLine} from "react-icons/ri";
import dynamic from "next/dynamic";
import {BiLogIn} from "react-icons/bi";
import {getToken, userDataFromLocal} from "@/app/utils/utils";
import {USER_ROUTES} from "@/app/config/api";
import {getCategories, getSubCategories} from "@/app/services/profile";
import {useDispatch} from "react-redux";
import {updateProfileComponentIndex} from "@/app/redux/slices/storeSlice";
import {userInfo_api} from "@/app/services/auth";
import {toast} from "react-toastify";
import {categoriesLocalData} from "@/app/config/constant";

const Sidebar = dynamic(() => import("./SideBar"));

export default function Header() {
  const pathname = usePathname();
  const whitePaths = [
    "/login",
    "/signup",
    "/forgot-password",
    "/verify-code",
    "/create-password",
    "/become-seller",
    "/profile",
    "/search",
    "/profile/billing",
    "/profile/campaign",
    "/profile/community",
    "/profile/bookmark",
    "/profile/messages",
    "/profile/membership",
    "/profile/mybooks",
    "/book-detail",
    "/verify-email",
    "/privacy-policy",
  ];
  const [isLightPage, setIsLightPage] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const auth = getToken();
  const [user, setUser] = useState(null);
  const dispatch = useDispatch();
  const [categoryList, setCategoryList] = useState({});
  const router = useRouter();

  const [subNavbar, setSubNavbar] = useState([
    {name: "Sell", link: "/seller"},
    {name: "Books", link: "", subList: []},
    {
      name: "E-Directory",
      subList: [
        {name: "Schools", link: ""},
        {name: "Tutors", link: ""},
        {name: "Coaches", link: ""},
      ],
    },
    {
      name: "Edu-Opportunity",
      subList: [
        {name: "Events", link: ""},
        {name: "Scholarships & Awards", link: ""},
        // { name: "Coaches", link: "" },
      ],
    },
    {name: "Our Story", link: "/about"},
    // { name: "How To Rebook", link: "/" },
    // { name: "Contact Us", link: "/contact-us" },
    {name: "FAQ", link: "/faq"},
    // { name: "Resources", link: "/" },
  ]);

  console.log("auth", auth);
  console.log("pathname", pathname);

  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

  useEffect(() => {
    setIsLightPage(whitePaths.indexOf(pathname) > -1);
  }, [pathname]);

  // fetch user profile
  const fetchProfile = async () => {
    try {
      let response = await userInfo_api();
      if (response?.status == 200) {
        //  toast.error(response.message || "No Info Found")
        return;
      }

      let userData = response?.data;
      setUser(userData);
    } catch (error) {
      console.log(error);
    }
  };

  console.log("user", user);

  const fetchMasterCategory = async () => {
    try {
      // const res = await fetch(USER_ROUTES.MASTER_DATA_CATEGORY, {
      //   method: "GET",
      //   headers: {
      //     "Authorization": `Bearer ${auth}`
      //   }
      // });
      // const response = await res.json();
      // const categories = response?.data || [];
      let CateogriesData = await getCategories();
      console.log("CateogriesData", CateogriesData);
      let categories = CateogriesData?.data.categories || [];

      let filteredItem = categories.filter(
        (item) => item.name.toLowerCase() == "book"
      );
      let filteredItemForEd = categories.filter(
        (item) => item.name == "E-directory"
      );
      // For each category, fetch its subcategories
      console.log("filteredItemForEd", filteredItemForEd);
      let subCat = await fetchMasterSubCategory(filteredItem[0]?._id);
      let subCatForEd = await fetchMasterSubCategory(filteredItemForEd[0]?._id);
      subCatForEd = subCatForEd?.subCategories;
      subCat = subCat?.subCategories;
      console.log("subCat", subCat);
      let enrichedCategoriesForEd = subCatForEd?.map((category) => {
        console.log("category", category.name.toLowerCase());

        // const subcategories = await fetchMasterSubCategory(category?._id);
        // console.log("subcategories ", subcategories)

        return {
          name: category?.name,
          // link: `/search?category=${category?.name}`,
          link: `/search?category=${filteredItemForEd[0]?._id}&subCategory=${category._id}`,
          // subList: subcategories?.subCategories?.map((sub) => ({
          //   name: sub?.name,
          //   link: `/search?category=${category?._id}&subCategory=${sub?._id}`,
          // })),
        };
      });
      let enrichedCategories = subCat?.map((category) => {
        console.log("category", category.name.toLowerCase());

        // const subcategories = await fetchMasterSubCategory(category?._id);
        // console.log("subcategories ", subcategories)

        return {
          name: category?.name,
          // link: `/search?category=${category?.name}`,
          link: `/search?category=${filteredItem[0]?._id}&subCategory=${category._id}`,
          // subList: subcategories?.subCategories?.map((sub) => ({
          //   name: sub?.name,
          //   link: `/search?category=${category?._id}&subCategory=${sub?._id}`,
          // })),
        };
      });
      // const enrichedCategories = await Promise.all(
      //   subCat?.map(async (category) => {
      //     console.log("category",category.name.toLowerCase())

      //     const subcategories = await fetchMasterSubCategory(category?._id);
      //     // console.log("subcategories ", subcategories)

      //     return {
      //       name: category?.name,
      //       // link: `/search?category=${category?.name}`,
      //       link: `/search?category=${category?._id}`,
      //       // subList: subcategories?.subCategories?.map((sub) => ({
      //       //   name: sub?.name,
      //       //   link: `/search?category=${category?._id}&subCategory=${sub?._id}`,
      //       // })),
      //     };
      //   })
      // );

      // console.log("enrichedCategories", enrichedCategories)
      console.log("enrichedCategories", enrichedCategories);
      let restItems = categories.slice(2, categories.length).map((item) => {
        return {
          name: item?.name,
          // link: `/search?category=${category?.name}`,
          link: `/search?category=${item?._id}`,
          // subList: subcategories?.subCategories?.map((sub) => ({
          //   name: sub?.name,
          //   link: `/search?category=${category?._id}&subCategory=${sub?._id}`,
          // })),
        };
      });
      setSubNavbar([
        {name: "Sell", link: "/seller"},
        {name: "Books", link: "", subList: enrichedCategories},
        {
          name: "E-Directory",
          subList: enrichedCategoriesForEd,
        },
        {
          name: "Edu-Opportunity",
          subList: restItems,
        },
        {name: "Our Story", link: "/about"},
        // {name: "How To Rebook", link: "/"},
        // {name: "Contact Us", link: "/contact-us"},
        // {name: "FAQ", link: "/faq"},
        {name: "Community", link: "/community"},
        // {name: "Resources", link: "/"},
      ]);
    } catch (error) {
      console.error("Category fetch error:", error);
    }
  };

  // console.log("navbar", subNavbar)

  const fetchMasterSubCategory = async (categoryId) => {
    try {
      let response = await getSubCategories(categoryId);
      console.log("response", response);
      return response?.data || [];
    } catch (error) {
      console.error("Subcategory fetch error:", error);
      return [];
    }
  };
  const fetchMasterSubSubCategory = async (categoryId) => {
    try {
      let response = await getSubCategories(categoryId);
      console.log("response", response);
      return response?.data || [];
    } catch (error) {
      console.error("Subcategory fetch error:", error);
      return [];
    }
  };

  useEffect(() => {
    fetchMasterCategory();
    fetchProfile();
  }, []);

  // console.log("categoryList", categoryList)

  const navbarLinks = [
    // {name: "Privacy Policy", link: "/"},
    // {name: "Disclaimer", link: "/"},
    // {name: "Terms & Condition", link: "/"},
    {name: "Contact Us", link: "/contact-us"},
    {name: "FAQs", link: "/faq"},
  ];

  const loggedInNavbar = [
    {
      loggedIn: true,
      name: "Wishlist",
      icon: (
        <LuBookmark
          className={`${isLightPage ? "text-white" : "text-black"}`}
          size={20}
        />
      ),
      link: "/profile/bookmark",
    },
    // { loggedIn: true, name: "Profile", icon: <RiUserLine className={`${isLightPage ? "text-white" : "text-black"}`} size={20} />, link: "/profile" }
  ];

  return (
    <div className={``}>
      <header className={`${headerCss.headerContainer} border-b py-3 md-px-4`}>
        <div className="container-wrapper   flex items-center md:items-start justify-between w-[100%] px-4 ">
          {/* Left Side: Logo + Branding */}
          {/* <div className="flex items-center justify-start w-6/12 ">
            <Link href="/" aria-label="Home">
              <Image
                src={logo}
                height={30}
                width={30}
                alt="Rebookit Logo"
                className="mr-2 md:scale-150"
              />
            </Link>
            <Link href={"/"}>
              <div className="flex items-center">
                <p className="text-white text-lg font-semibold ml-2 md:text-3xl">
                  ReBookIt.Club
                </p>
                <Image
                  src={flag}
                  height={50}
                  width={35}
                  alt="Jamaican Flag"
                  className="ml-1 h-[17px] md:h-[25px]"
                />
              </div>
            </Link>
          </div> */}

          <Link href="/" aria-label="Home" className="lg-mt-2">
            <div className="relative h-12 w-46 md:h-12 md:w-48 lg:h-16 lg:w-64">
              <Image
                src={logo}
                alt="Rebookit Logo"
                fill
                className="mr-2 object-contain"
                priority
                sizes="(max-width: 768px) 128px, (max-width: 1024px) 192px, 256px"
              />
            </div>
          </Link>
          {/* Right Side: Hamburger Menu */}
          <nav aria-label="Main Navigation" className="lg:hidden">
            <button className=" cursor-pointer" aria-label="Open menu">
              <RxHamburgerMenu size={30} color="#fff" onClick={toggleSidebar} />
            </button>
          </nav>

          {/* desktop links  */}
          <div className="hidden lg:flex justify-start items-center gap-5 ml-5 md:justify-end md:w-full">
            {navbarLinks?.map((nl, idx) => (
              <div key={nl.name} className="flex justify-center items-center ">
                <Link href={nl.link}>
                  <p className="text-[15px] text-white">{nl.name}</p>
                </Link>
                {idx != navbarLinks.length - 1 && (
                  <div className="w-[1px] h-[14px] ml-[25px] bg-white"></div>
                )}
              </div>
            ))}

            <footer className="none md:flex justify-center items-center ">
              <Link href="/become-seller" aria-label="View all book categories">
                <svg
                  width="164"
                  height="52"
                  viewBox="0 0 164 52"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M101.203 5.00005L27.0963 5.00004C14.8929 5.00004 5.00021 14.5064 5.00021 26.2331"
                    stroke="#211F54"
                    strokeWidth="8.34158"
                  />
                  <path
                    d="M159.299 26.2332C159.299 14.5064 149.441 5.00005 137.281 5.00005L63.436 5.00004"
                    stroke="#0161AB"
                    strokeWidth="8.34158"
                  />
                  <path
                    d="M5 26.2332C5 37.9599 14.8927 47.4663 27.096 47.4663H101.203"
                    stroke="#EFDC2A"
                    strokeWidth="8.34158"
                  />
                  <path
                    d="M63.4355 47.4663H137.28C149.441 47.4663 159.298 37.9599 159.298 26.2332"
                    stroke="#0161AB"
                    strokeWidth="8.34158"
                  />
                  <path
                    d="M121 47.4663L46.8933 47.4663"
                    stroke="#4A8B40"
                    strokeWidth="8.34158"
                  />
                  <path
                    d="M121 5.00001L46.8933 5"
                    stroke="#FF0009"
                    strokeWidth="8.34158"
                  />
                  <rect
                    x="9"
                    y="7"
                    width="146"
                    height="39"
                    rx="19.5"
                    fill="white"
                  />

                  <text
                    x="50%"
                    y="50%"
                    dominantBaseline="middle"
                    textAnchor="middle"
                    fontSize="13"
                    fontWeight="500"
                    fill="#211F54"
                    fontFamily="Poppins, sans-serif"
                  >
                    {auth ? "+ Sell" : "Become a Seller"}
                  </text>
                </svg>
              </Link>
            </footer>
          </div>
        </div>
      </header>

      {/* sub navbar list for more info  */}
      <div
        className={`${headerCss.extraNavbar}  ${
          isLightPage
            ? "border-b border-[#cccccc]"
            : "border-b border-[#211F54]"
        }  hidden lg:block   ${isLightPage && headerCss.LightPage}`}
      >
        <div className="container-wrapper flex items-center justify-between px-4 sm:px-6 md:px-8">
          <div
            className={`${headerCss.subNavbar} ${
              isLightPage ? "text-black" : "text-white"
            } z-[1000] flex md:justify-between cursor-pointer md:text-[10px] lg:text-[14px] lg:w-[85%] xl:text-[16px] items-center md:w-[70%]`}
          >
            {subNavbar.map((item, idx) =>
              item.subList ? (
                <DropdownItem key={idx} item={item} />
              ) : (
                // <Link key={idx} href={item.link || "/"} className='ml-4'>
                <p onClick={() => router.push(item.link || "/")}>{item.name}</p>
                // </Link>
              )
            )}
          </div>

          <div
            className={`${
              headerCss.rightSide
            }   flex justify-end  items-center ${
              isLightPage ? "text-black" : "text-white"
            } w-7/12`}
          >
            {auth &&
              loggedInNavbar?.map((list, idx) => {
                return (
                  <Link
                    key={idx}
                    href={auth ? list.link : "/profile"}
                    className="mr-10 flex justify-center items-center"
                    onClick={() => {
                      dispatch(updateProfileComponentIndex(4));
                    }}
                  >
                    <p
                      className={`h-9 w-9 flex justify-center items-center rounded-full ${
                        isLightPage ? "bg-[#211F54]" : "bg-white"
                      } mr-4`}
                    >
                      {" "}
                      {list.icon}
                    </p>
                    <p>{list.name}</p>
                  </Link>
                );
              })}

            {auth ? (
              <Link
                href={"/profile/mybooks"}
                className="mr-10 flex justify-center items-center"
              >
                <div
                  className={`h-9 w-9 flex justify-center items-center rounded-full ${
                    isLightPage ? "bg-[#211F54]" : "bg-white"
                  } mr-4`}
                >
                  {user?.profileImage ? (
                    <img
                      src={user?.profileImage}
                      alt="Profile"
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <RiUserLine
                      className={`${isLightPage ? "text-white" : "text-black"}`}
                      size={20}
                    />
                  )}
                </div>
                <p>My Account</p>
              </Link>
            ) : (
              <Link href={"/login"} className="flex gap-4 items-center ">
                <span
                  className={`py-2 pl-[7px] pr-2 rounded-full flex items-center justify-center ${
                    isLightPage ? "bg-[#211F54]" : "bg-white"
                  }`}
                >
                  <BiLogIn
                    className={` w-5 h-5 ${
                      isLightPage ? "fill-white" : "fill-black"
                    }`}
                  />
                </span>
                <p>Login/Signup</p>
              </Link>
            )}
          </div>
        </div>
      </div>
      {/* Sub Navbar */}
      {/* <div
        className={`${headerCss.extraNavbar} ${
          isLightPage ? "border-[#cccccc]" : "border-[#211F54]"
        } border-b hidden lg:block`}
      >
        <div className="container-wrapper flex items-center justify-between px-4 sm:px-6 md:px-8">
          <div
            className={`${
              headerCss.subNavbar
            } flex flex-wrap gap-6 md:gap-10 text-sm lg:text-base ${
              isLightPage ? "text-black" : "text-white"
            }`}
          >
            {subNavbar.map((item, idx) =>
              item.subList ? (
                <DropdownItem key={idx} item={item} />
              ) : (
                <p
                  key={idx}
                  onClick={() => router.push(item.link || "/")}
                  className="cursor-pointer whitespace-nowrap"
                >
                  {item.name}
                </p>
              )
            )}
          </div>

          <div className="flex items-center gap-6">
            {auth &&
              loggedInNavbar?.map((list, idx) => (
                <Link
                  key={idx}
                  href={auth ? list.link : "/profile"}
                  onClick={() => dispatch(updateProfileComponentIndex(4))}
                  className="flex items-center gap-2 whitespace-nowrap"
                >
                  <span
                    className={`flex items-center justify-center h-9 w-9 rounded-full ${
                      isLightPage ? "bg-[#211F54]" : "bg-white"
                    }`}
                  >
                    {list.icon}
                  </span>
                  <p>{list.name}</p>
                </Link>
              ))}

            {auth ? (
              <Link
                href="/profile/mybooks"
                className="flex items-center gap-2 whitespace-nowrap"
              >
                <span
                  className={`flex items-center justify-center h-9 w-9 rounded-full ${
                    isLightPage ? "bg-[#211F54]" : "bg-white"
                  }`}
                >
                  {user?.profileImage ? (
                    <img
                      src={user.profileImage}
                      alt="Profile"
                      className="h-full w-full rounded-full object-cover"
                    />
                  ) : (
                    <RiUserLine
                      size={20}
                      className={`${isLightPage ? "text-white" : "text-black"}`}
                    />
                  )}
                </span>
                <p>My Account</p>
              </Link>
            ) : (
              <Link
                href="/login"
                className="flex items-center gap-2 whitespace-nowrap"
              >
                <span
                  className={`p-2 rounded-full flex items-center ${
                    isLightPage ? "bg-[#211F54]" : "bg-white"
                  }`}
                >
                  <BiLogIn
                    size={20}
                    className={`${isLightPage ? "text-white" : "text-black"}`}
                  />
                </span>
                <p>Login/Signup</p>
              </Link>
            )}
          </div>
        </div>
      </div> */}

      <Sidebar isOpen={isSidebarOpen} onClose={toggleSidebar} />
    </div>
  );
}
