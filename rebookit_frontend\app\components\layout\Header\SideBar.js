"use client";

import React, {useState, useEffect, useRef} from "react";
import Image from "next/image";
import {RiArrowDownSLine} from "react-icons/ri";
import {useRouter} from "next/navigation";
import {toast} from "react-toastify";
import {userInfo_api} from "@/app/services/auth";
import Link from "next/link";
import {getCategories} from "@/app/services/profile";
import {clearLocalStorge, getToken} from "@/app/utils/utils";
import {MdClose} from "react-icons/md";
import {createInitialsAvatar} from "../../InitialAvatar/CreateInitialAvatar";
import Popup from "../../popup/popup";

function Sidebar({isOpen, onClose}) {
  const router = useRouter();
  const auth = getToken();
  const [logoutConfirm, setLogoutConfirm] = useState(false);
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [bookCategories, setBookCategories] = useState([]);
  const [navbarList, setNavbarList] = useState([
    {
      name: "Sell",
      icon: false,
      link: "/seller",
    },
    {
      name: "Marketplace",
      icon: true,
      dropdownOpen: false,
      children: [],
    },
    {
      name: "Our Story",
      icon: false,
      link: "",
    },
    {
      name: "Wishlist",
      icon: false,
      link: "/profile/bookmark",
    },
    {
      name: "Contact Us",
      icon: false,
      link: "/contact-us",
    },
    {
      name: "FAQ",
      icon: false,
      link: "/faq",
    },
  ]);

  const sidebarRef = useRef(null);

  // Handle logout
  const handleLogout = () => {
    // clearLocalStorge();
    // router.push("/");
    // onClose();
    setLogoutConfirm(false);
    router.push("/login");
    setTimeout(() => {
      toast.success("Logged out");
      // dispatch(setToDefault());
      clearLocalStorge();
      // dispatch(setToDefault());
      // dispatch(updateProfileComponentIndex(idx))
      removeToken();

      // window.open("/login", "_self");
      // onclick = {onClose};
    }, 100);
  };

  // Fetch categories
  const fetchCategories = async () => {
    try {
      const getCateogries = await getCategories();
      if (getCateogries?.status == 200) {
        setBookCategories(getCateogries?.data?.categories);
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  // Get user info
  const getUserInfoFunc = async () => {
    try {
      if (auth) {
        setLoading(true); // Start loading
        const responseUserInfo = await userInfo_api();
        if (responseUserInfo?.data) {
          setUser(responseUserInfo.data);
        }
      }
    } catch (err) {
      console.log("getUserInfoFunc err", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getUserInfoFunc();
    fetchCategories();
  }, [auth]);

  // Update navbar list with categories and My Account
  useEffect(() => {
    let updatedList = [
      {
        name: "Sell",
        icon: false,
        link: "/seller",
      },
      {
        name: "Marketplace",
        icon: true,
        dropdownOpen: false,
        children: bookCategories.map((category) => ({
          name: category.name,
          link: `/search?category=${category._id}`,
        })),
      },
      {
        name: "Our Story",
        icon: false,
        link: "",
      },
      {
        name: "Community",
        icon: false,
        link: "/community",
      },
      {
        name: "Wishlist",
        icon: false,
        link: "/profile/bookmark",
      },
      {
        name: "Contact Us",
        icon: false,
        link: "/contact-us",
      },
      {
        name: "FAQ",
        icon: false,
        link: "/faq",
      },
    ];

    // Add My Account only if user is authenticated
    if (auth) {
      updatedList.splice(2, 0, {
        name: "My Account",
        icon: true,
        dropdownOpen: false,
        children: [
          {name: "My Listing", link: "/profile/mybooks"},
          // {name: "Buy Membership", link: "/profile/membership"},
          {name: "My Messages", link: "/profile/messages"},
          {name: "Ask Community", link: "/profile/community"},
          // {name: "Billing", link: "/profile/billing"},
        ],
      });
    }

    setNavbarList(updatedList);
  }, [bookCategories, auth]);

  const avatar = createInitialsAvatar(`${user?.firstName} ${user?.lastName}`);

  const toggleDropdown = (targetName) => {
    setNavbarList((prevList) =>
      prevList.map((item) => {
        if (item.name === targetName && item.hasOwnProperty("dropdownOpen")) {
          return {...item, dropdownOpen: !item.dropdownOpen};
        }
        return item;
      })
    );
  };

  const handleClickOutside = (event) => {
    if (sidebarRef.current && !sidebarRef.current.contains(event.target)) {
      onClose();
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    } else {
      document.removeEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  return (
    <>
      <div
        ref={sidebarRef}
        className={`fixed top-0 right-0 bg-white w-[85%] h-full z-50 pt-[33px] pb-[10px] flex flex-col transform transition-transform duration-300 ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <section className="px-[30px] flex-shrink-0">
          <MdClose size={25} onClick={onClose} className="absolute right-5" />
          {auth ? (
            <div>
              <Link href={"/profile"}>
                <div className="w-[68px] h-[68px] rounded-full">
                  {loading ? ( // Show placeholder while loading
                    <div className="bg-gray-200 border-2 border-dashed rounded-full w-full h-full" />
                  ) : user?.profilePicture ? (
                    <Image
                      src={user.profilePicture}
                      alt="Profile Image"
                      width={68}
                      height={68}
                      className="rounded-full object-cover"
                    />
                  ) : user ? ( // Only show avatar if user data exists
                    <Image
                      src={createInitialsAvatar(
                        `${user.firstName} ${user.lastName}`
                      )}
                      alt="Profile Image"
                      width={68}
                      height={68}
                      className="rounded-full object-cover"
                    />
                  ) : (
                    <div className="bg-gray-200 border-2 border-dashed rounded-full w-full h-full" />
                  )}
                </div>

                <div className="mt-5">
                  {loading ? ( // Show placeholder text while loading
                    <>
                      <div className="h-5 w-40 bg-gray-200 rounded mb-2"></div>
                      <div className="h-4 w-56 bg-gray-200 rounded"></div>
                    </>
                  ) : user ? ( // Only show name if user data exists
                    <>
                      <p className="text-[16px] leading-4 font-bold">
                        {`${user.firstName} ${user.lastName}`}
                      </p>
                      <p className="mt-1.5 text-[#6C7072] leading-[26px]">
                        {user.email}
                      </p>
                    </>
                  ) : (
                    <>
                      <p className="text-[16px] leading-4 font-bold">
                        User Name
                      </p>
                      <p className="mt-1.5 text-[#6C7072] leading-[26px]">
                        <EMAIL>
                      </p>
                    </>
                  )}
                </div>
              </Link>
              {/* Logout button below user info */}
              <button
                onClick={() => {
                  setLogoutConfirm(true);
                }}
                className="mt-4 global_linear_gradient text-[16px] px-6 text-white p-2 rounded-full"
              >
                Log out
              </button>
            </div>
          ) : (
            <div className="flex flex-col gap-4 mt-auto pt-10 w-full">
              <Link href={"/login"} onClick={onClose}>
                <button className="w-full global_linear_gradient text-white font-medium py-2 rounded-full transition duration-200">
                  Login
                </button>
              </Link>
              <p className="text-sm text-gray-700">
                New here?{" "}
                <Link href={"/verify-email"} onClick={onClose}>
                  <button className="text-blue-800 underline">
                    Create new account
                  </button>
                </Link>
              </p>
            </div>
          )}
        </section>

        <hr className="mt-[23px] mb-[16px] text-[#E2E4E5] flex-shrink-0" />

        <section className="px-[30px] flex-1 overflow-auto">
          <ul className="flex flex-col gap-5 cursor-pointer">
            {navbarList.map((item, index) => (
              <li
                key={index}
                className="text-[16px] leading-5 flex flex-col cursor-pointer"
              >
                {item.children ? (
                  <>
                    <span
                      onClick={() => toggleDropdown(item.name)}
                      className="flex gap-1 cursor-pointer select-none items-center"
                    >
                      {item.name}
                      <RiArrowDownSLine
                        className={`text-[20px] transform transition-transform ${
                          item.dropdownOpen ? "rotate-180" : "rotate-0"
                        }`}
                      />
                    </span>
                    <div
                      className={`overflow-hidden transition-all duration-300 ${
                        item.dropdownOpen ? "max-h-96 mt-3" : "max-h-0"
                      }`}
                    >
                      <ul className="flex flex-col gap-5 pl-4">
                        {item.children.map((child, childIndex) => (
                          <li
                            key={childIndex}
                            className="border-b border-[#00000014] pb-1"
                          >
                            {child.link ? (
                              <Link
                                href={child.link}
                                onClick={onClose}
                                className="block w-full"
                              >
                                {child.name}
                              </Link>
                            ) : (
                              <span>{child.name}</span>
                            )}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </>
                ) : item.link ? (
                  <Link href={item.link} onClick={onClose}>
                    <span className="flex gap-1 cursor-pointer select-none items-center">
                      {item.name}
                    </span>
                  </Link>
                ) : (
                  <span className="flex gap-1 cursor-pointer select-none items-center">
                    {item.name}
                  </span>
                )}
              </li>
            ))}
          </ul>

          <div className="mt-8 " onClick={onClose}>
            <Link href="/become-seller" aria-label="View all book categories">
              <svg
                width="164"
                height="52"
                viewBox="0 0 164 52"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M101.203 5.00005L27.0963 5.00004C14.8929 5.00004 5.00021 14.5064 5.00021 26.2331"
                  stroke="#211F54"
                  strokeWidth="8.34158"
                />
                <path
                  d="M159.299 26.2332C159.299 14.5064 149.441 5.00005 137.281 5.00005L63.436 5.00004"
                  stroke="#0161AB"
                  strokeWidth="8.34158"
                />
                <path
                  d="M5 26.2332C5 37.9599 14.8927 47.4663 27.096 47.4663H101.203"
                  stroke="#EFDC2A"
                  strokeWidth="8.34158"
                />
                <path
                  d="M63.4355 47.4663H137.28C149.441 47.4663 159.298 37.9599 159.298 26.2332"
                  stroke="#0161AB"
                  strokeWidth="8.34158"
                />
                <path
                  d="M121 47.4663L46.8933 47.4663"
                  stroke="#4A8B40"
                  strokeWidth="8.34158"
                />
                <path
                  d="M121 5.00001L46.8933 5"
                  stroke="#FF0009"
                  strokeWidth="8.34158"
                />
                <rect
                  x="9"
                  y="7"
                  width="146"
                  height="39"
                  rx="19.5"
                  fill="white"
                />

                <text
                  x="50%"
                  y="50%"
                  dominantBaseline="middle"
                  textAnchor="middle"
                  fontSize="13"
                  fontWeight="500"
                  fill="#211F54"
                  fontFamily="Poppins, sans-serif"
                >
                  {auth ? "+ Sell" : "Become a Seller"}
                </text>
              </svg>
            </Link>
          </div>
        </section>
      </div>
      <Popup
        isOpen={logoutConfirm}
        title="Confirm Logout"
        message="Are you sure you want to log out?"
        confirmText="Log Out"
        cancelText="Cancel"
        onConfirm={handleLogout}
        onCancel={() => setLogoutConfirm(false)}
      />
    </>
  );
}

export default Sidebar;
