"use client"

import { IoIosArrowDown } from 'react-icons/io'
import Link from 'next/link'
import { useState } from 'react'
import headerCss from "./header.module.scss";
import { useRouter } from 'next/navigation';



export default function DropdownItem({ item }) {
    const [isOpen, setIsOpen] = useState(false)
    const router = useRouter()

    return (
        <div
            className="relative group cursor-pointer" style={{cursor:"pointer"}}
            onClick={() => setIsOpen(true)}
            onMouseLeave={() => setIsOpen(false)}
        >
            <div className="flex items-center ml-4 cursor-pointer">
                <p className="">{item.name}</p>
                <IoIosArrowDown size={15} className="ml-2" />
            </div>

            {item.subList && isOpen &&
                (
                    <div className={`absolute z-[1000] left-0 top-full bg-white text-gray-600 shadow-md cursor-pointer z-50 min-w-max rounded md:text-[12px] xl:text-[14px] [line-height:30px] `}
                        onMouseLeave={() => setIsOpen(false)}
                    >
                        {item.subList.map((sub, i) => (
                            <div key={`sublist-${i}`} className="relative bg-white group/subitem " style={{cursor:"pointer"}} >
                                {sub.subList ? (
                                    <Link href={sub.link}>
                                        <div className="flex items-center justify-between px-4 py-2 hover:text-gray-900 hover:bg-gray-100 whitespace-nowrap cursor-pointer z-20 ">
                                            <span className='xl:text-[14px] md:text-[12px]'>{sub.name}</span>
                                            <IoIosArrowDown size={12} className="ml-2" />
                                        </div>
                                        <div className="absolute w-[270px] left-full top-0 bg-white text-gray-600 hover:text-gray-900 shadow-md hidden group-hover/subitem:block min-w-max rounded ">
                                            {sub.subList.map((nested, j) => (
                                                // <Link href={nested.link || "/"} key={j}>
                                                <div
                                                    key={`sub-sublist-${j}`}
                                                    className="px-4 py-2 hover:bg-gray-100 whitespace-nowrap line-clamp-1"
                                                    onClick={(e) => {
                                                        e.preventDefault();
                                                        e.stopPropagation();
                                                        setIsOpen(false)
                                                        router.push(nested.link)
                                                    }}
                                                >
                                                    {nested.name}
                                                </div>
                                                // </Link>
                                            ))}
                                        </div>
                                    </Link>
                                ) : (
                                    <Link href={sub?.link || "/"} onClick={() => { setIsOpen(false) }}>
                                        <div className="px-4 py-2 hover:bg-gray-100 text-gray-900 whitespace-nowrap">{sub.name}</div>
                                    </Link>
                                )}
                            </div>
                        ))}
                    </div>
                )}
        </div>
    )
}