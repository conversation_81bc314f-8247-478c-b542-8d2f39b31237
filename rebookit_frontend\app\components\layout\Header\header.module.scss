.headerContainer {
  height: 60px;
  background: linear-gradient(268.27deg, #211f54 11.09%, #0161ab 98.55%);
  // height: 92px;
  // padding: 25px;
  @media (min-width: 768px) {
    // height: 170px;
    height: 92px;
    padding: 25px;

  }

  @media (min-width: 1024px) {
    // height: 170px;
    height: 92px;
    padding: 25px;
  }

  @media (min-width: 1280px) {
    // height: 170px;
    height: 92px;
    padding: 25px 100px;
  }
}

.extraNavbar {
  // background: linear-gradient(90deg, #000 0%, #000 34.16%, rgba(0, 0, 0, 0.1) 100%);
  height: 80px;
  position: absolute;
  top: 90px;
  left: -1px;
  width: 100%;
  border: 1px solid transparent;
  border-bottom: 1px solid #cccccc;
  padding-left: 6rem;
  padding-top: 25px;
  padding-right: 6rem;
  padding-bottom: 26px;
  justify-content: space-around;
  align-items: center;
  z-index: 5;

  @media (max-width: 1280px) {
    padding: 20px;
    .subNavbar {
      font-size: 12px;
    }
  }

  .LightPage {
    background: transparent;
    border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  }
  .rightSide {
    @media (max-width: 1280px) {
      font-size: 12px;
    }
  }
}
