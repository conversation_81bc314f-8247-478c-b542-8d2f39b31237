"use client";

import {useEffect, useState} from "react";
import Subscription from "../landingPage/subscription/subscription";
import membershipCss from "./membership.module.scss";
import SubscriptionTable from "./subscriptionTable";
import CheckoutForm from "./checkout";
import {Elements} from "@stripe/react-stripe-js";
import {loadStripe} from "@stripe/stripe-js";
import {subscriptionPlans} from "@/app/services/membership";
import {toast} from "react-toastify";

export default function Membership() {
  const [currentScreen, setcurrentScreen] = useState("Membership");
  const listScreen = ["Membership", "YourPlan"];
  const [isPurchase, setisPurchase] = useState(false);
  const [subscriptionList, setSubscriptionList] = useState([]);
  const [currentPlan, setcurrentPlan] = useState({});

  console.log("currentPlan", currentPlan);

  const stripePromise = loadStripe(
    process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
  );

  async function getSubscriptionData() {
    try {
      let subscriptionData = await subscriptionPlans();
      // let subscriptionData = await getsubscriptionPlans()
      console.log(
        "subscriptionData?.data?.currentSubscription",
        subscriptionData?.data?.currentSubscription
      );
      if (subscriptionData.status == 200) {
        setSubscriptionList(subscriptionData?.data.plans);
        setcurrentPlan(subscriptionData?.data?.currentSubscription);
      } else {
        toast.error("Something went wrong");
      }
    } catch (err) {
      toast.error("Something went wrong");
    }
  }
  useEffect(() => {
    getSubscriptionData();
  }, []);
  if (isPurchase) {
    return (
      <section
        className={`${membershipCss.tableContainer} w-[95%] mx-auto my-5 overflow-hidden `}
      >
        <Elements stripe={stripePromise}>
          <CheckoutForm setisPurchase={setisPurchase} />
        </Elements>
      </section>
    );
  } else
    return (
      <section className={membershipCss.subscriptionContainer}>
        <section className="">
          <header>
            <p className="text-[20px] md:text-[24px] font-semibold">
              Membership Settings
            </p>
          </header>

          <header className="relative flex gap-10 text-[14px] my-5 after:content-[''] after:absolute after:left-0 after:w-full after:h-[1px] after:bg-gray-300 after:bottom-[-7px]">
            {listScreen?.map((list) => (
              <p
                key={list}
                className={`cursor-pointer ${
                  list == currentScreen && membershipCss.activeList
                }`}
                onClick={() => setcurrentScreen(list)}
              >
                {list}
              </p>
            ))}
          </header>
          <div className="md:border md:border-[#ededed] md:px-5 md:pb-3 rounded-lg">
            <header className="flex justify-between items-center my-4 md:my-7">
              <p className="text-[16px] md:text-[18px] font-medium">
                Plan: {currentPlan?.planId?.planName}
              </p>
              {/* <button className={membershipCss.upgradePlan} >Upgrade Plan</button> */}
            </header>

            <p className="text-[#8A8A8A] text-[14px]">
              Take your ads to the next level with more features.
            </p>
            <p className="text-[#0161AB] font-semibold">
              ${currentPlan?.planId?.price} / Annum sd
            </p>
          </div>
        </section>

        {currentScreen == "Membership" && (
          <section className="md:mt-5">
            <Subscription
              profileSection={true}
              setisPurchase={setisPurchase}
              subscriptionList={subscriptionList}
              currentPlan={currentPlan}
            />
          </section>
        )}

        {currentScreen == "YourPlan" && (
          <section
            className={`${membershipCss.tableContainer} w-[95%] mx-auto my-5 overflow-hidden `}
          >
            <SubscriptionTable />
          </section>
        )}

        {currentScreen == "paymentPage" && (
          <section
            className={`${membershipCss.tableContainer} w-[95%] mx-auto my-5 overflow-hidden `}
          >
            <Elements stripe={stripePromise}>
              <CheckoutForm />
            </Elements>
          </section>
        )}
      </section>
    );
}
