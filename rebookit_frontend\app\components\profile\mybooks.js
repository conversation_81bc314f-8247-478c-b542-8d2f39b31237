`use strict`;
import {USER_ROUTES} from "@/app/config/api";
import {getToken, userDataFromLocal} from "@/app/utils/utils";
import {useEffect, useState} from "react";
import {FaArrowRightLong} from "react-icons/fa6";
import styles from "./mybooks.module.scss";
import Link from "next/link";
import {getMyBooks, searchItemByName} from "@/app/services/profile";
import {toast} from "react-toastify";

export default function MyBooks() {
  const [bookData, setBookData] = useState([]);
  const [searchedText, setsearchedText] = useState("");
  const [isLoading, setisLoading] = useState(false);
  const [isImageLoaded, setisImageLoaded] = useState(false);
  const getAllBooksOfuser = async () => {
    try {
      setisLoading(true);
      let userToken = getToken();
      let response = await getMyBooks();
      console.log("response getAllBooksOfuser", response);
      if (response.status == 200) {
        setBookData(response.data);
      }
      setisLoading(false);
    } catch (err) {
      console.log("err", err);
      setisLoading(false);
      toast.error("Something Went Wrong");
    }
    // setBookData()
  };

  function debounce(func, delay) {
    let timer;
    return function (...args) {
      clearTimeout(timer);
      timer = setTimeout(() => {
        func.apply(this, args);
      }, delay);
    };
  }

  const getItemsByName = async (text) => {
    try {
      let userToken = getToken();
      let response = await searchItemByName(text);
      console.log("response getItemsByName", response);
      if (response.status == 200) {
        setBookData(response.data);
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  console.log(bookData, "bookData");
  useEffect(() => {
    getAllBooksOfuser();
    getItemsByName("");
  }, []);

  if (bookData.length) {
    console.log("bookData author");
    bookData?.map((item) => {
      console.log(`AUthor Name`, item);
    });
  }
  const handledebondSearchItem = debounce(getItemsByName, 1000);

  let str = "test";
  console.log(str.toLocaleUpperCase(), "check case");
  function toTitleCase(str) {
    return str
      .toLowerCase()
      .split(" ")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  }
  const customReturnStatus = (status) => {
    if (status == "accepted") {
      return <span className="text-[#008000]">{toTitleCase(status)}</span>;
    } else if (status == "rejected") {
      return <span className="text-[#FF5733]">{toTitleCase(status)}</span>;
    } else {
      return <span className="text-[#FFC300]">{toTitleCase(status)}</span>;
    }
  };
  let userData = userDataFromLocal();
  userData = JSON.parse(userData);
  console.log("isLoading", isLoading);
  return (
    <div className="mybookContainer">
      <div className="flex items-center justify-between flex-wrap gap-4 py-4">
        <h1 className="text-xl font-semibold text-[#211F54]">
          {userData?.firstName} Books
        </h1>

        <div className="flex items-center gap-4 w-[40%]">
          {/* Search Box */}
          <div className="flex items-center w-full bg-white rounded-full border border-[#211F54] px-2 py-1 h-[45px]">
            <input
              type="text"
              placeholder="Search..."
              autoComplete="off"
              onChange={(e) => handledebondSearchItem(e.target.value)}
              className="flex-grow px-4 py-1 outline-none bg-transparent text-gray-700 w-full"
            />
            <button
              className="px-4  py-1 text-white text-sm  font-medium rounded-full"
              style={{
                backgroundImage: "linear-gradient(to right, #211F54, #0161AB)",
              }}
            >
              Search
            </button>
          </div>

          {/* Add Book Button SVG */}
          {/* <div className="w-[130px] h-[45px] flex items-center justify-center">
                        <Link href="/become-seller" aria-label="View all book categories">
                            <svg
                                width="130"
                                height="45"
                                viewBox="0 0 164 52"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path d="M101.203 5.00005L27.0963 5.00004C14.8929 5.00004 5.00021 14.5064 5.00021 26.2331" stroke="#211F54" strokeWidth="8.34158" />
                                <path d="M159.299 26.2332C159.299 14.5064 149.441 5.00005 137.281 5.00005L63.436 5.00004" stroke="#0161AB" strokeWidth="8.34158" />
                                <path d="M5 26.2332C5 37.9599 14.8927 47.4663 27.096 47.4663H101.203" stroke="#EFDC2A" strokeWidth="8.34158" />
                                <path d="M63.4355 47.4663H137.28C149.441 47.4663 159.298 37.9599 159.298 26.2332" stroke="#0161AB" strokeWidth="8.34158" />
                                <path d="M121 47.4663L46.8933 47.4663" stroke="#4A8B40" strokeWidth="8.34158" />
                                <path d="M121 5.00001L46.8933 5" stroke="#FF0009" strokeWidth="8.34158" />
                                <rect x="9" y="7" width="110" height="39" rx="19.5" fill="white" />
                                <text x="50%" y="50%" dominantBaseline="middle" textAnchor="middle" fontSize="13" fill="#211F54" fontFamily="Poppins, sans-serif">
                                    Add book
                                </text>
                            </svg>
                        </Link>
                    </div> */}
        </div>
      </div>

      <div className="w-full  shadow">
        <div className="w-full rounded-lg bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white px-4 py-2 text-lg font-semibold">
          All Books
        </div>

        {!isLoading ? (
          <div
            className={`${
              bookData.length > 0
                ? "grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4"
                : ""
            }  py-4`}
          >
            {bookData.length > 0 ? (
              bookData?.map((item) => (
                <div className="w-full px-2">
                  <div
                    className={`${styles.mybookContainer} mb-6 m-auto flex flex-col bg-white rounded-lg shadow`}
                  >
                    {/* Image */}
                    <img
                      src={item.frontImage}
                      // className="w-full h-[200px] rounded-t-[10px] object-cover"
                      alt="No Preview"
                      className={`w-full h-[200px] rounded-t-[10px] object-cover  transition-opacity duration-300 ${
                        isImageLoaded ? "opacity-100" : "opacity-0"
                      }`}
                      onLoad={() => setisImageLoaded(true)}
                    />

                    {/* Content */}
                    <div className="flex flex-col justify-between h-full rounded-b-[10px] bg-white p-2">
                      <h3 className="text-sm font-semibold line-clamp-2">
                        {item.itemName}
                      </h3>
                      <div className="flex items-center justify-between">
                        <Link
                          href={{
                            pathname: "/book-detail",
                            query: {id: item._id},
                          }}
                          aria-label="View all book categories"
                        >
                          <button
                            className={`${styles.seeDetails} flex items-center mt-2 bg-gradient-to-r from-[#211F54] to-[#0161AB] bg-clip-text text-transparent`}
                          >
                            See Details <FaArrowRightLong />
                          </button>
                        </Link>
                        {customReturnStatus(item.status)}
                      </div>
                      <div className="flex justify-center py-3">
                        <button
                          className={`${styles.soldoutBtn} text-sm sm:text-base mx-auto rounded-full px-4 py-2 bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white`}
                        >
                          Mark as Sold Out
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center mx-auto w-full">
                No Data Available
              </div>
            )}
          </div>
        ) : (
          <div className="mx-auto text-center">...Loading</div>
        )}
      </div>
    </div>
  );
}
