'use client';

import React, { useEffect, useState } from 'react';
import { Elements } from '@stripe/react-stripe-js';
import { loadStripe } from '@stripe/stripe-js';
import CheckoutForm from './checkout';
import { USER_ROUTES } from '@/app/config/api';
import { getPaymentIntent } from '@/app/services/profile';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);

export default function StripeWrapper() {
    const [clientSecret, setClientSecret] = useState('');
    const fetchPaymentIntent =async()=>{
        let responsePaymentIntent=await getPaymentIntent()
        console.log("responsePaymentIntent",responsePaymentIntent)
        if(responsePaymentIntent.status==200){
            setClientSecret(responsePaymentIntent.data.clientSecret)
        }
    }
    useEffect(() => {
        fetchPaymentIntent()
        // fetch(USER_ROUTES.PAYMENT_INTENT, {
        //     method: 'POST',
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify({ amount: 1000, currency:"usd" }) // e.g., $10.00
        // })
        // .then(res => res.json())
        // .then(data => setClientSecret(data.data.clientSecret));
    }, []);

    const options = {
        clientSecret,
        appearance: {
            theme: 'flat'
        }
    };

    return (
        <>
            {clientSecret && (
                <Elements stripe={stripePromise} options={options}>
                    <CheckoutForm />
                </Elements>
            )}
        </>
    );
}
