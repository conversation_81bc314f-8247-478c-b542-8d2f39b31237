import React, { useEffect, useState } from "react";
import DatePicker from "react-datepicker";
import "react-datepicker/dist/react-datepicker.css";
import sellerCss from "./sellerComponent.module.scss";
import { toast } from "react-toastify";
import { SlCalender } from "react-icons/sl";

export default function DatePickerComp({
  register,
  setValue,
  name,
  watch,
  checkWith,
  errors,
}) {
  const watchedValue = watch(name);
  const checkWithValue = watch(checkWith);
  const [selectedDate, setSelectedDate] = useState(watchedValue || null);

  // Register the input with react-hook-form
  useEffect(() => {
    register(name);
  }, [register, name]);

  // Keep selectedDate in sync with watched value
  useEffect(() => {
    setSelectedDate(watchedValue || null);
  }, [watchedValue]);

  const handleChange = (date) => {
    if (date === null) {
      // Allow clearing
      setValue(name, "");
      setSelectedDate(null);
      return;
    }

    if (checkWith && checkWithValue) {
      const startDate = new Date(checkWithValue);
      const endDate = new Date(date);

      if (startDate > endDate) {
        toast.error("End date cannot be before Start date");
        return;
      }
    }

    setValue(name, date);
    setSelectedDate(date);

    // If this is the start date, clear the end date
    if (name === "eventStartDate") {
      setValue("eventEndDate", "");
    }
  };

  // Disable end date if no start date
  const isDisabled = () => {
    return name === "eventEndDate" && !checkWithValue;
  };

  // Min date logic
  const getMinDate = () => {
    if (name === "eventEndDate") {
      return checkWithValue || new Date();
    }
    return new Date(); // Default: today
  };

  return (
    <div
      className={`border rounded-lg border-gray-300 justify-center mt-3 relative ${sellerCss.inputContainer}`}
      style={{ maxHeight: "50px" }}
    >
      <div className="flex items-center justify-between w-full pl-2 pr-3 rounded-md">
        <DatePicker
        autoComplete="off"
          key={name + (selectedDate ? selectedDate.toString() : "empty")}
          selected={selectedDate}
          {...register(name, {
            required: `${name} is required`,
          })}
          placeholderText="dd/mm/yyyy"
          minDate={getMinDate()}
          onChange={handleChange}
          disabled={isDisabled()}
          timeInputLabel="Time:"
          dateFormat="MM/dd/yyyy h:mm aa"
          showTimeInput
          // isClearable
          className="py-2 w-full px-1.5 border border-gray-300 rounded-lg shadow-sm"
        />
        <SlCalender />
      </div>
      {errors && errors[name] && !selectedDate && (
        <p className="text-red-500 text-[12px] mt-1 absolute bottom-[-22px] right-0">
          {errors[name].message || "Required"}
        </p>
      )}
    </div>
  );
}
