"use client";
import React, { useRef, useState, useCallback, useEffect } from "react";
import sellerCss from "./sellerComponent.module.scss";
import {
  GoogleMap,
  LoadScript,
  Autocomplete,
  Marker,
} from "@react-google-maps/api";
import { useSelector } from "react-redux";
import { toast } from "react-toastify";
import { MdClose } from "react-icons/md";

const containerStyle = {
  width: "100%",
  height: "500px",
  borderRadius: "12px",
};

const center = {
  lat: 49.84534910000001,
  lng: -97.2143011,
};

const MapWithSearchBox = ({
  register,
  errors,
  setgeoLocation,
  setValue,
  watch,
  customError,
}) => {
  // Helper to extract formatted_address from address object or string
  const getDisplayAddress = (address) => {
    if (!address) return "";
    if (typeof address === "string") return address;
    if (typeof address === "object" && address.formatted_address)
      return address.formatted_address;
    return "";
  };

  const [mapCenter, setMapCenter] = useState(center);
  const autocompleteRef = useRef(null);
  const userList = useSelector((x) => x.storeData.userListing);
  const [marker, setMarker] = useState(center);
  const mapRef = useRef(null);
  const [changeState, setchangeState] = useState(true);
  const [inputValue, setInputValue] = useState("");
  const [selectedAddress, setSelectedAddress] = useState(
    watch()?.address || ""
  );

  const onPlaceChanged = useCallback(() => {
    const place = autocompleteRef.current?.getPlace();
    const addressData = addressMapping();
    if (place?.geometry) {
      const location = {
        lat: place.geometry.location.lat(),
        lng: place.geometry.location.lng(),
      };
      setMapCenter(location);
      setMarker(location);
      setValue("address", addressData.address);
      setSelectedAddress(addressData.address); // Store the full address object
      setInputValue(""); // Clear input after selection
    }
  }, [setValue]);

  useEffect(() => {
    if (userList?.locationCoordinates?.lat) {
      setMarker(userList.locationCoordinates);
      if (mapRef.current) {
        mapRef.current.panTo(userList.locationCoordinates);
      }
    } else {
      setMarker(center);
    }
  }, [userList.locationCoordinates]);

  useEffect(() => {
    // Set initial selected address from watch
    const watchedAddress = watch()?.address;
    if (
      watchedAddress &&
      JSON.stringify(watchedAddress) !== JSON.stringify(selectedAddress)
    ) {
      setSelectedAddress(watchedAddress);
    }
  }, [watch()?.address]);

  const addressMapping = () => {
    if (autocompleteRef.current) {
      const place = autocompleteRef.current?.getPlace();
      let presentAdminLevel1 = false;
      (place.address_components || []).forEach((item) => {
        if (item.types.includes("administrative_area_level_1")) {
          presentAdminLevel1 = true;
        }
      });
      if (!presentAdminLevel1) {
        toast.error("Please select precise address");
        return {};
      }
      const components = place.address_components || [];
      const geometry = place.geometry?.location || {};
      const mapped = components.reduce(
        (acc, { long_name, short_name, types }) => {
          types.forEach((type) => {
            acc[type] = long_name;
            acc[`${type}_short`] = short_name;
          });
          return acc;
        },
        {}
      );
      // FINDING THE ADDRESS FROM PLACE
      const obj = {
        address: {
          formatted_address: place.formatted_address || "",
          geometry: {
            location: {
              type: "Point",
              coordinates: [
                typeof geometry.lng === "function"
                  ? geometry.lng()
                  : geometry.lng ?? 0,
                typeof geometry.lat === "function"
                  ? geometry.lat()
                  : geometry.lat ?? 0,
              ],
            },
          },
          ...mapped, // spread out all extracted types
        },
      };
      return obj;
    }
    return {};
  };

  const removeAddress = () => {
    setSelectedAddress("");
    setValue("address", "");
    setInputValue("");
    setMapCenter(center);
    setMarker(center);
  };

  return (
    <>
      <div key={changeState} className="">
        <LoadScript
          googleMapsApiKey={process.env.NEXT_PUBLIC_GOOGLE_MAPS_NEW_API_KEY}
          libraries={["places"]}
        >
          <div className="mb-4 w-full">
            <label className="block text-lg font-medium text-gray-800 mb-3">
              Select Location
            </label>

            <div className="relative">
              <div className="flex min-h-[50px] w-full items-center flex-wrap gap-2 px-3 py-2 border border-gray-300 rounded-lg bg-white focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-transparent">
                {/* Selected Address Tag */}
                {getDisplayAddress(selectedAddress) && (
                  <div className="flex items-center bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm font-medium max-w-full overflow-hidden">
                    <span
                      className="truncate max-w-full block"
                      style={{ maxWidth: "calc(100vw - 120px)" }}
                      title={getDisplayAddress(selectedAddress)}
                    >
                      {getDisplayAddress(selectedAddress)}
                    </span>
                    <button
                      type="button"
                      onClick={removeAddress}
                      className="ml-2 text-gray-500 hover:text-red-500 transition-colors flex-shrink-0"
                    >
                      <MdClose size={16} />
                    </button>
                  </div>
                )}

                {/* Input Field */}
                {!getDisplayAddress(selectedAddress) && (
                  <div className="flex-1 min-w-[200px]">
                    <Autocomplete
                      onLoad={(ref) => (autocompleteRef.current = ref)}
                      onPlaceChanged={() => {
                        onPlaceChanged();
                        const place = autocompleteRef.current?.getPlace();
                        if (place?.formatted_address) {
                          setInputValue("");
                        }
                      }}
                      className="w-full"
                    >
                      <input
                        type="text"
                        placeholder="Search and select location..."
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        className="w-full border-none outline-none bg-transparent text-base placeholder-gray-400"
                      />
                    </Autocomplete>
                  </div>
                )}
              </div>
            </div>

            {/* Error Message */}
            <label className="flex justify-end my-2">
              {!selectedAddress && errors.location && (
                <p
                  id="location-error"
                  className="text-red-500 text-[12px] right-3"
                >
                  {errors?.location?.message}
                </p>
              )}
            </label>
          </div>

          <GoogleMap
            onLoad={(map) => (mapRef.current = map)}
            key={`${marker.lat}-${marker.lng}`}
            mapContainerStyle={containerStyle}
            center={mapCenter}
            zoom={14}
          >
            {marker && <Marker position={marker} />}
          </GoogleMap>
        </LoadScript>
      </div>

      <button
        type="button"
        onClick={() => setchangeState(!changeState)}
        className={"bg-[#d8232a] rounded-full px-4 py-2 text-white mt-2"}
      >
        Reload Map
      </button>
    </>
  );
};

export default MapWithSearchBox;
