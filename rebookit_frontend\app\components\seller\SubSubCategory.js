import React, { useEffect, useState } from "react";

import bookCategories from "@/app/static_data/bookSubCategories.json";
import Image from "next/image";

import sellerCss from "./sellerComponent.module.scss";

import { MdArrowRightAlt } from "react-icons/md";
import { useDispatch, useSelector } from "react-redux";
import {
  changeCompletedStep,
  updateUserListing,
} from "@/app/redux/slices/storeSlice";
import { getToken } from "@/app/utils/utils";
import { USER_ROUTES } from "@/app/config/api";
import {
  getSubCategories,
  getSubSubCategories,
  getSubSubSubCategories,
} from "@/app/services/profile";
import SubSubSubCategories from "./subSubSub.Category";

export default function SubSubCategories({ subCategoryId }) {
  const dispatch = useDispatch();
  const userListing = useSelector((store) => store.storeData.userListing);
  const [subsubCategoryState, setsubsubCategoryState] = useState([]);
  const [isLoading, setisLoading] = useState(false);
  const [isImageLoaded, setisImageLoaded] = useState(false);
  const [selectedSubSubCategory, setselectedSubSubCategory] = useState("");
  const [subSubSubCategories, setsubSubSubCategories] = useState([]);

  const auth = getToken();

  // Helper: get selected sub-sub-category id (from redux or local state)
  const selectedSubSubCategoryId =
    userListing?.subSubCategory?._id || selectedSubSubCategory;

  // On mount or when redux subSubCategory changes, sync local state for instant UI
  useEffect(() => {
    if (userListing?.subSubCategory?._id) {
      setselectedSubSubCategory(userListing.subSubCategory._id);
    }
  }, [userListing?.subSubCategory?._id]);

  const handleCategoryChange = async (subcategory, idx) => {
    // Reset subSubSubCategory in Redux and local state when subSubCategory changes
    dispatch(
      updateUserListing({
        subSubCategory: subcategory,
        subSubSubCategory: null,
        visitedStep: 2,
      })
    );
    setselectedSubSubCategory(subcategory._id);
    setsubSubSubCategories([]);
    let response = await getSubSubSubCategories(subcategory._id);
    response = response?.data;
    setsubSubSubCategories(response?.subSubSubCategories || []);
    if (response?.subSubSubCategories.length < 1) {
      dispatch(
        updateUserListing({
          currentStep: 2,
          subSubCategory: subcategory,
          visitedStep: 2,
        })
      );
      if (userListing.completedStep > 1) {
        return;
      } else {
        dispatch(changeCompletedStep(1));
      }
    }
    setTimeout(() => {
      // document.getElementById("SubSubSubCat")?.scrollIntoView({ behavior: "smooth", block: "start" })
    }, 1000);
  };

  const fetchMasterSubSubCategory = async (categoryId) => {
    setisLoading(true);

    try {
      let response = await getSubSubCategories(categoryId);
      response = response?.data;
      setsubsubCategoryState(response?.subSubCategories || []);
      setisLoading(false);
      if (response?.subSubCategories.length) {
        document
          .getElementById("subSubCat")
          ?.scrollIntoView({ behavior: "smooth", block: "start" });
      }
      return response?.data || [];
    } catch (error) {
      setisLoading(false);
      console.error("Subcategory fetch error:", error);
      return [];
    }
  };

  useEffect(() => {
    // Only reset if subCategoryId is different from Redux state
    if (subCategoryId && subCategoryId !== userListing?.subCategory?._id) {
      dispatch(
        updateUserListing({ subSubCategory: null, subSubSubCategory: null })
      );
      setselectedSubSubCategory("");
      setsubSubSubCategories([]);
      fetchMasterSubSubCategory(subCategoryId);
    } else if (subCategoryId && userListing?.subCategory?._id) {
      // If subCategoryId matches Redux, just fetch subSubCategories for display
      fetchMasterSubSubCategory(subCategoryId);
    }
  }, [subCategoryId, userListing?.subCategory?._id]);

  // Fetch sub-sub-sub-categories if a sub-sub-category is already selected (on mount or when selectedSubSubCategoryId changes)
  useEffect(() => {
    if (selectedSubSubCategoryId) {
      (async () => {
        let response = await getSubSubSubCategories(selectedSubSubCategoryId);
        response = response?.data;
        setsubSubSubCategories(response?.subSubSubCategories || []);
      })();
    }
  }, [selectedSubSubCategoryId]);

  let mappingIcon = {
    Book: "/icons/openBookIcon.png",
    "E-directory": "/icons/openFolderIcon.png",
    "Scholarship & Awards": "/icons/scholerShipIcon.png",
    Events: "/icons/eventIcon.png",
  };

  // Helper: is this sub-sub-category selected? (either by redux, local state, or pending)
  const isSubSubCategorySelected = (category) => {
    return selectedSubSubCategoryId === category._id;
  };

  return (
    <section className="my-8 md:my-16">
      <header className="mb-6 md:mb-10">
        <h2
          id="subsubcategory-heading"
          className="text-2xl font-semibold my-4 md:text-3xl lg:text-[40px] "
        >
          Choose a sub-sub category
        </h2>
      </header>

      <ul
        className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 md:gap-6"
        role="list"
      >
        {!isLoading ? (
          subsubCategoryState?.map((category, idx) => {
            const selected = isSubSubCategorySelected(category);
            return (
              <li
                key={category._id || idx}
                // Remove alert and always allow click
                onClick={() => {
                  handleCategoryChange(category, idx);
                }}
                className="flex flex-col h-full"
                style={{ cursor: "pointer" }}
              >
                <article className="flex flex-col items-center h-full p-4 bg-white rounded-lg ">
                  {/* Image container - responsive circle */}
                  <div className="bg-[#211F54] rounded-full p-3 w-16 h-16 sm:w-20 sm:h-20 md:w-30 md:h-30 flex items-center justify-center">
                    <img
                      src={
                        category.image ||
                        mappingIcon[userListing.category?.name]
                      }
                      alt={`Cover for ${category.text || category.name}`}
                      className={`
                        w-full h-full max-w-[40px] max-h-[40px] 
                        sm:max-w-[50px] sm:max-h-[50px] 
                        md:max-w-[60px] 
                        object-contain transition-opacity duration-300 
                        ${isImageLoaded ? "opacity-100" : "opacity-0"}
                      `}
                      onLoad={() => setisImageLoaded(true)}
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = "/path/to/fallback-image.png";
                      }}
                    />
                  </div>

                  {/* Category name - responsive text with consistent height */}
                  <p className="text-center font-medium mt-3 mb-4 text-xs sm:text-sm md:text-base min-h-[3.5em] flex items-center justify-center px-1">
                    {String(category.text || category.name).includes("/")
                      ? String(category.text || category.name)
                          .split("/")
                          .map((item, index, arr) => (
                            <React.Fragment key={index}>
                              {item}
                              {index < arr.length - 1 && "/"}
                              <br />
                            </React.Fragment>
                          ))
                      : category.text || category.name}
                  </p>

                  {/* Button - fully responsive */}
                  <div className="mt-auto w-full pt-2 sm:pt-3 md:pt-4">
                    <button
                      type="button"
                      // Button is always enabled, even if selected
                      disabled={false}
                      className={`
      w-full flex items-center justify-center
      gap-1             /* mobile gap */
      sm:gap-2          /* ≥640px */
      md:gap-3          /* ≥768px */

      py-2 px-3         /* mobile padding */
      sm:py-2.5 sm:px-4 /* ≥640px */
      md:py-3 md:px-5   /* ≥768px */

      text-sm           /* mobile font */
      sm:text-base      /* ≥640px */
      md:text-lg        /* ≥768px */

      rounded-full
      transition-all duration-200
      whitespace-nowrap
      border            

      ${
        selected
          ? "global_linear_gradient text-white"
          : "text-gray-800 hover:bg-gray-200"
      }
    `}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleCategoryChange(category, idx);
                      }}
                    >
                      <span className="truncate max-w-[80px] sm:max-w-[120px] md:max-w-none">
                        {selected ? "Selected" : "Select"}
                      </span>
                      {!selected && (
                        <MdArrowRightAlt
                          className="
          flex-shrink-0
          w-4 h-4         
          sm:w-5 sm:h-5               md:w-6 md:h-6   
        "
                        />
                      )}
                    </button>
                  </div>
                </article>
              </li>
            );
          })
        ) : (
          // Improved loading indicator
          <div className="col-span-full flex justify-center py-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        )}
      </ul>

      <div id="SubSubSubCat" className="mt-8 md:mt-12">
        {/* Only show if a subSubCategory is selected and there are subSubSubCategories */}
        {selectedSubSubCategoryId && subSubSubCategories.length > 0 && (
          <SubSubSubCategories subsubCategoryId={selectedSubSubCategoryId} />
        )}
      </div>
    </section>
  );
}
