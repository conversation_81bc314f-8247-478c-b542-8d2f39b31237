import React, {useEffect, useState, useRef} from "react";

import bookCategories from "@/app/static_data/bookCategories.json";
import Image from "next/image";

import sellerCss from "./sellerComponent.module.scss";

import {MdArrowRightAlt} from "react-icons/md";
import {useDispatch, useSelector} from "react-redux";
import {
  changeCompletedStep,
  resetListingData,
  updateUserListing,
} from "@/app/redux/slices/storeSlice";
import {USER_ROUTES} from "@/app/config/api";
import {getToken} from "@/app/utils/utils";
import {getCategories, getSubCategories} from "@/app/services/profile";
// import SimpleMap from "./TestMap";

export default function Categories({setUserChoice}) {
  const [categoryState, setcategoryState] = useState([]);
  const dispatch = useDispatch();
  const userList = useSelector((x) => x.storeData.userListing);
  const [isImageLoaded, setisImageLoaded] = useState(false);

  const [isLoading, setisLoading] = useState(false);
  const [pendingSelection, setPendingSelection] = useState(null); // for instant UI feedback
  const auth = getToken();

  // To prevent double click/fast click issues
  const isHandlingRef = useRef(false);

  // Always allow handleCategoryChange, even if already selected, to fix the bug
  const handleCategoryChange = async (category, idx) => {
    dispatch(resetListingData());
    if (isHandlingRef.current) return;
    isHandlingRef.current = true;

    // Set pending selection for instant UI feedback
    setPendingSelection(category._id);

    // Immediately update Redux for instant UI feedback
    // We'll optimistically update the UI before the API call
    let subCategoryLength = false;
    try {
      setisLoading(true);
      let response = await getSubCategories(category._id);
      response = response?.data;
      subCategoryLength = response?.subCategories?.length > 0;
    } catch (e) {
      // fallback: treat as no subcategories
      subCategoryLength = false;
    } finally {
      setisLoading(false);
    }

    // Reset all child selections when category changes
    dispatch(
      updateUserListing({
        currentStep: subCategoryLength ? 1 : 2,
        category: category,
        kind: category.kind,
        subCategory: null,
        subSubCategory: null,
        subSubSubCategory: null,
        visitedStep: 1, // Reset visitedStep on category change
        completedStep: 1, // Reset completedStep on category change
      })
    );

    // Remove pending selection after Redux update
    setTimeout(() => {
      setPendingSelection(null);
      isHandlingRef.current = false;
    }, 0);
  };

  const fetchMasterCategory = async () => {
    try {
      setisLoading(true);
      let getCateogriesData = await getCategories();
      if (getCateogriesData?.status == 200) {
        setcategoryState(getCateogriesData?.data?.categories);
      }
      setisLoading(false);
    } catch (err) {
      setisLoading(false);
      console.log("Err fetchMasterCategory", err);
    }
  };

  useEffect(() => {
    fetchMasterCategory();
  }, []);

  let mappingIcon = {
    Book: "/icons/openBookIcon.png",
    "E-directory": "/icons/openFolderIcon.png",
    "Scholarship & Awards": "/icons/scholerShipIcon.png",
    Events: "/icons/eventIcon.png",
  };

  // For instant UI feedback, use pendingSelection if set, else Redux
  const getIsSelected = (category) => {
    if (pendingSelection !== null) {
      return pendingSelection === category._id;
    }
    return userList.category._id === category._id;
  };

  return (
    <section className="my-md-20">
      <header>
        <h2
          id="member-resources-heading"
          className="text-[22px] font-semibold md:font-medium my-4 md:text-[48px]"
        >
          Choose a category
        </h2>
        {/* <p className="mt-3 font-extralight text-[12px] mb-3 md:text-[18px]">
          RebookIt collects this information to better understand and serve your
          business.
        </p> */}
      </header>

      <ul
        className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 md:gap-6 auto-rows-fr"
        role="list"
      >
        {!isLoading ? (
          categoryState?.map((category, idx) => {
            const isSelected = getIsSelected(category);
            return (
              <li
                className="flex w-full h-full"
                key={category.text || category._id}
                onClick={() => {
                  // Always allow handleCategoryChange, even if already selected
                  if (!pendingSelection) handleCategoryChange(category, idx);
                }}
                style={{cursor: pendingSelection ? "not-allowed" : "pointer"}}
              >
                <article className="flex flex-col items-center w-full h-full p-4 rounded-lg transition-all">
                  {/* Image container with responsive sizing */}
                  <div className="bg-[#211F54] rounded-full p-3 sm:p-4 md:p-5 w-16 h-16 sm:w-20 sm:h-20 md:w-24 md:h-24 flex items-center justify-center">
                    <img
                      src={mappingIcon[category.name]}
                      alt={`Cover for ${category.text}`}
                      className={`
                    w-full h-full max-w-[40px] max-h-[40px] 
                    sm:max-w-[50px] sm:max-h-[50px] 
                    md:max-w-[60px] md:max-h-[60px]
                    object-contain transition-opacity duration-300 
                    ${isImageLoaded ? "opacity-100" : "opacity-0"}
                  `}
                      onLoad={() => setisImageLoaded(true)}
                    />
                  </div>

                  {/* Category name with responsive text and consistent height */}
                  <p className="text-center font-medium text-sm md:text-base my-3 leading-snug flex items-center justify-center">
                    {category.text || category.name}
                  </p>

                  {/* Button container */}
                  <div className="mt-auto pt-3 flex">
                    <button
                      className={`w-full flex items-center justify-center gap-1 py-2 px-3 sm:py-2.5 sm:px-4 md:py-3 md:px-5 text-sm sm:text-base rounded-lg transition-all duration-75 whitespace-nowrap ${
                        isSelected
                          ? sellerCss.SelectActivebutton
                          : sellerCss.Selectbutton
                      } ${
                        pendingSelection && !isSelected
                          ? "opacity-60 pointer-events-none"
                          : ""
                      }`}
                      disabled={!!pendingSelection}
                      type="button"
                    >
                      <span className="truncate max-w-[70px] sm:text-[12px] sm:max-w-none">
                        {isSelected ? "Selected" : "Select"}
                      </span>
                      {!isSelected && (
                        <MdArrowRightAlt className="flex-shrink-0 w-4 h-4 sm:w-5 sm:h-5" />
                      )}
                    </button>
                  </div>
                </article>
              </li>
            );
          })
        ) : (
          <div className="text-center w-full col-span-5 flex justify-center items-center py-10">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          </div>
        )}
      </ul>
    </section>
  );
}
