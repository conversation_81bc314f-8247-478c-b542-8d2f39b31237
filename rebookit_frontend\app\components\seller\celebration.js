"use client";

import React, {Fragment, useEffect} from "react";
import Confetti from "react-confetti";
import sellerCss from "./sellerComponent.module.scss";

import {RiCheckboxCircleFill} from "react-icons/ri";
import Link from "next/link";
import {useDispatch} from "react-redux";
import {updateUserListing} from "@/app/redux/slices/storeSlice";
import {useRouter} from "next/navigation";

export default function Celebration() {
  const dispatch = useDispatch();
  const router = useRouter();

  // useEffect(() => {
  //   const timer = setTimeout(() => {
  //     router.push("/profile/mybooks");
  //   }, 2000);
  //   return () => clearTimeout(timer);
  // }, [router]);

  return (
    <Fragment>
      <Confetti
        className="w-[100%] h-[100%]  md:m-auto"
        opacity={0.9}
        wind={0.04}
      />

      <section className={`h-[50vh] ${sellerCss.celebration}`}>
        <div className="bg-[#27AE60] h-[150px] w-[150px] rounded-full mx-auto my-10">
          <RiCheckboxCircleFill className="h-[150px] w-[150px] text-white " />
        </div>

        <section>
          <p className="md:text-[38px] font-medium text-[20px] text-center">
            Your request has been submitted.
          </p>
          <p className="text-[#475569] md:text-[18px] text-center text-[12px] my-5">
            Your book is now listed! <br />A small action that’s part of a
            greener, smarter world.
          </p>
        </section>

        <section className="flex flex-wrap justify-center items-center gap-5">
          <Link href={"/profile/mybooks"}>
            <button className={`${""}`}>See Listings</button>
          </Link>
          <Link
            href={"/become-seller"}
            onClick={() => dispatch(updateUserListing({currentStep: 0}))}
          >
            <button className={`${sellerCss.active}`}>Start Selling</button>
          </Link>
        </section>
      </section>
    </Fragment>
  );
}
