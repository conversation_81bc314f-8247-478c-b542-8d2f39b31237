"use client";

import CreatableSelect from "react-select/creatable";
import Slider from "react-slick";

import { ELASTIC_DB_ROUTES, USER_ROUTES } from "@/app/config/api";
import {
  changeCompletedStep,
  resetListingData,
  updateUserListing,
} from "@/app/redux/slices/storeSlice";
import Image from "next/image";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Controller, useForm } from "react-hook-form";
import { LuBookOpen } from "react-icons/lu";
import { Circles } from "react-loader-spinner";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import MapSearchable from "./mapSearchable";
import sellerCss from "./sellerComponent.module.scss";
import { Debounce, getToken } from "@/app/utils/utils";
import { IoAddOutline } from "react-icons/io5";
import { MdDelete } from "react-icons/md";

import { usePlacesWidget } from "react-google-autocomplete";
import Select, { StylesConfig } from "react-select";
import CustomSelectBox from "../common/CustomSelectBox";
import {
  listItem,
  searchByName,
  searchISBN,
  uploadPhotoSingle,
} from "@/app/services/profile";
import dynamic from "next/dynamic";
import {
  activityTypeArr,
  classesOfferedArr,
  eventModeArr,
  frequencyArr,
  ItemKindEnum,
  itemToKind,
  ParishesListEnum,
  QualificationTypeEnum,
  schoolTypes,
  TargetClassTypeEnum,
} from "@/app/config/constant";
import DatePickerComp from "./DatePicker";
import PreviewForm from "./previewForm";
import { IoIosLink } from "react-icons/io";
import { FiLink } from "react-icons/fi";
// import MapWithSearchBox from  './GoogleMapComponent';

const MapWithSearchBox = dynamic(() => import("./GoogleMapComponent"), {
  ssr: false,
});
// import Select from 'react-select/dist/declarations/src/Select';

import { useRouter } from "next/navigation";
import { createPortal } from "react-dom";

export default function ListingForm({}) {
  const dispatch = useDispatch();
  const userList = useSelector((x) => x.storeData.userListing);
  const dateRef = useRef(null);
  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
  };

  const [preview, setPreview] = useState({
    0: null,
    1: null,
    2: null,
    3: null,
    4: null,
  });

  console.log("preview", preview);
  const [images, setImages] = useState({
    0: null,
    1: null,
    2: null,
    3: null,
    4: null,
  });

  const [suggestions, setSuggestions] = useState([]);
  const [bookSearchSuggestion, setbookSearchSuggestion] = useState([]);
  const [loading, setloading] = useState(false);
  const [imagesObj, setimagesObj] = useState({});

  const {
    register,
    watch,
    formState: { errors },
    getValues,
    handleSubmit,
    setValue,
    reset,
    setError,
    clearErrors,
    control,
  } = useForm({
    defaultValues: {
      title: userList.listData.title,
      Enter_ISBN: userList.listData.ISBN,
      Description: userList.listData.desciption,
      bookAuthor: userList.listData.bookAuthor,
      price: userList.listData.price,
      priceUnit: userList.listData.priceUnit || "per item",
      bookCondition: userList.listData.bookCondition,
      Quantity: userList.listData.quantity,
      location: userList.listData.address,
      website: userList.listData.website,
      targetClasses: userList.listData.targetClasses,
      highestQualification: userList.listData.highestQualification,
      experience: userList.listData?.experience,
      activityType: userList.listData?.activityType,
      targetClasses: userList.listData?.targetClasses,
      frequency: userList.listData.frequency,
      targetStudents: userList.listData?.targetStudents,
      address: userList.listData.address,
      eventEndDate: userList.listData.eventEndDate,
      eventStartDate: userList.listData.eventStartDate,
      eligibilityCriteria: userList.listData.eligibilityCriteria,
      scholarshipType: userList.listData.scholarshipType,
      parish: userList.listData?.address?.parish,
      tags: userList.listData.tags?.map((item) => {
        return { label: item, value: item };
      }),
      ...(userList.listData.classesOffered && {
        classesOffered: {
          value: userList.listData.classesOffered,
          label: userList.listData.classesOffered,
        },
      }),
      ...(userList.listData.targetClasses && {
        targetClasses: {
          value: userList.listData.targetClasses,
          label: userList.listData.targetClasses,
        },
      }),
      ...(userList.listData.highestQualification && {
        highestQualification: {
          value: userList.listData.highestQualification,
          label: userList.listData.highestQualification,
        },
      }),
      ...(userList.listData.experience && {
        experience: {
          value: userList.listData.experience,
          label: userList.listData.experience,
        },
      }),
      ...(userList.listData.ExtracurricularActivityItem && {
        ExtracurricularActivityItem: {
          value: userList.listData.ExtracurricularActivityItem,
          label: userList.listData.ExtracurricularActivityItem,
        },
      }),
      ...(userList.listData.eventMode && {
        eventMode: {
          value: userList.listData.eventMode,
          label: userList.listData.eventMode,
        },
      }),
      eventStartDate: userList.listData.eventStartDate || "",
      eventEndDate: userList.listData.eventEndDate || "",
    },
    mode: "onSubmit",
  });

  const [latLngLocation, setlatLngLocation] = useState({});
  const { ref: googleRef } = usePlacesWidget({
    apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_NEW_API_KEY,
    onPlaceSelected: (place) => {
      if (place.address_components) {
        setlatLngLocation(place);
        setValue("address", place.formatted_address);
        setValue("location", place.formatted_address);
      }
    },
    options: {
      types: ["geocode"],
    },
  });

  const fetchSearchData = (ISB_number) => {
    try {
      let userToken = getToken();
      fetch(ELASTIC_DB_ROUTES.SEARCH?.replace("{{page}}", 1), {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userToken}`,
        },
        body: JSON.stringify({
          filters: {
            regex: ISB_number || "",
          },
        }),
      }).then(async (res) => {
        const response = await res.json();
        let searchData = response;
        console.log("api response", searchData);
        if (!searchData?.count) setSuggestions([]);
        else if (!searchData?.error) {
          setSuggestions(searchData);
        }
      });
    } catch (error) {
      console.log("error", error);
    }
  };

  console.log("userList", userList);
  const debounceSearch = useMemo(() => Debounce(fetchSearchData, 1000), []);

  const fetchBook = async (value) => {
    if (value && value.length > 2) {
    } else {
      return;
    }

    try {
      const isbnRegex =
        /^(?:ISBN(?:-13)?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){4})[- 0-9X]{13}$)97[89][- ]?[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$|^(?:ISBN(?:-10)?:? )?(?=[0-9X]{10}$|(?=(?:[0-9]+[- ]){3})[- 0-9X]{13}$)[0-9]{1,5}[- ]?[0-9]+[- ]?[0-9]+[- ]?[0-9X]$/i;
      let search = value;
      let ISBN;
      let response;
      if (isbnRegex.test(search)) {
        ISBN = search.toLocaleLowerCase();

        response = await searchISBN(ISBN);
      } else {
        response = await searchByName(search);
      }

      setloading(true);
      // const response = await fetch(USER_ROUTES.ISBN_SEARCH.replace("{{ISBN}}", ISBN));
      let data = response.data;
      if (data?.docs.length) {
        setbookSearchSuggestion(
          data.docs.map((item) => {
            return {
              ...item,
              value: item.title,
              label: item.title,
              image: item?.cover_i,
            };
          })
        );
        toast.success("Data fetched..!");
      } else {
        toast.error("No Data Found");
      }

      const BookData = data[`${"ISBN:" + ISBN}`];
      setValue("BookName", BookData?.title);
      setValue("Description", BookData?.subtitle);
      setValue("bookAuthor", BookData?.authors?.[0]?.name);
      // setbookSearchSuggestion()
      return data?.docs;
      // const response = await fetch(USER_ROUTES.BOOK_SEARCH_BY_NAME.replace("{search}", search));
    } catch (err) {
      console.log("error is", err);
      toast.error(err);
    } finally {
      setloading(false);
    }
  };

  useEffect(() => {
    // fetchBook({event:{target:{value:"978-3-16-148410-0"}}})
    setSelectedTages(
      userList.listData?.tags?.map((item) => {
        return { label: item, value: item };
      })
    );
  }, [userList.listData.tags]);

  useEffect(() => {
    if (userList.listData.bookImages) {
      setPreview(userList.listData.bookImages);
      setImages(userList.listData.bookImages);
    }
  }, [userList.listData.bookImages]);

  const [isClient, setIsClient] = useState(false);
  const [selectedTages, setSelectedTages] = useState([]);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const handleTagChange = (newValue) => {
    // Only allow up to 3 selections
    if (newValue.length <= 3) {
      setSelectedTages(newValue);
    } else toast.info("Maximum upto 3 tags");
  };

  let testString = "C:\fakepathChatGPT Image May 28, 2025, 05_45_13 PM.png";

  const handleImageChange = (e, type) => {
    let typeArr = [".jpg", ".jpeg", ".png", ".webp"];
    setloading(true);
    setIsUploadingImage(true);
    let checkIfValid = false;

    typeArr.map((item) => {
      if (e.target.value.includes(item)) {
        checkIfValid = true;
      }
    });
    if (!checkIfValid) {
      toast.error("File Type Not Allowed");
      setloading(false);
      setIsUploadingImage(false);
      return;
    }
    const file = e.target.files[0];
    if (file && file.size <= 5 * 1024 * 1024) {
      // 5MB limit

      setImages((prev) => ({ ...prev, [type]: file }));
      if (type === "0") {
        setValue("coverImage", true, { shouldValidate: true });
        clearErrors("coverImage");
      }
      if (["1", "2", "3", "4"].includes(type))
        setValue("additionalImages", true, { shouldValidate: true });
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreview((prev) => ({ ...prev, [type]: reader.result }));
        setIsUploadingImage(false);
        setloading(false);
      };
      reader.readAsDataURL(file);
    } else {
      alert("File is too large or not supported.");
      setIsUploadingImage(false);
    }
  };

  const onSubmit = async (data) => {
    try {
      // debugger;
      // forward the step
      let uploadedImages = {
        0: null,
        1: null,
        2: null,
        3: null,
        4: null,
      };

      setloading(true);

      console.log("images", images);

      let isImageAvailable = true;
      // Check that at least 2 valid images exist in the images object
      const validImagesCount = Object.values(images).filter(
        (file) =>
          (typeof file === "string" && file.startsWith("http")) ||
          (file && typeof file === "object" && file instanceof File)
      ).length;

      if (validImagesCount < 2) {
        toast.error("At least 2 images are required");
        return;
      }

      for (const [key, file] of Object.entries(images)) {
        if (!file) continue;

        if (typeof file === "string" && file.includes("http")) {
          uploadedImages[key] = file;
          continue;
        }
        const formData = new FormData();
        formData.append("file", file);

        const userToken = getToken();

        try {
          let response = await uploadPhotoSingle(formData);
          if (response.status != 200) {
            // toast.error(`Upload failed for ${key}`);
            toast.error(`Some thing went wrong`);
            throw new Error(`Upload failed for ${key}`);
          }
          // const response = await fetch(USER_ROUTES.SINGLE_UPLOAD_FILE, {
          //     method: "POST",
          //     headers: {
          //         "Authorization": `Bearer ${userToken}`
          //     },
          //     body: formData,
          // });

          // if (!response.ok) {
          //     throw new Error(`Upload failed for ${key}`);
          // }

          const data = response.data;

          uploadedImages[key] = data?.url;
        } catch (error) {
          console.error(`Error uploading ${key}:`, error);
          toast.error(`Failed to upload ${key}`);
          return;
        }
      }

      let payload = {
        ...userList.listData,
        tags: selectedTages?.map((x) => x.value),
        bookImages: uploadedImages, // update this with upload api
        title: data.title,
        desciption: data.Description,
        price: data.price,
        priceUnit: data.priceUnit,
        address: data.address,
        parish: data.parish,
      };

      if (kind == ItemKindEnum.BookItem) {
        payload = {
          ...payload,
          bookAuthor: data.bookAuthor,
          bookCondition: data.bookCondition,
          ISBN: data.ISBN || data.Enter_ISBN,
          tags: data.tags?.map((item) => item.value),
        };
      }

      if (kind == ItemKindEnum.TutorItem) {
        payload = {
          ...payload,
          targetClasses: data.targetClasses?.value,
          highestQualification: data.highestQualification?.value,
          experience: data.experience?.value,
          website: data.website,
        };
      }
      //         Highest Qualification
      // Target Student
      // Experience
      // Website
      if (kind == ItemKindEnum.EventItem) {
        payload = {
          ...payload,
          eventMode: data.eventMode?.value,
          website: data.website,
          eventStartDate: data.eventStartDate,
          eventEndDate: data.eventEndDate,
        };
      }
      if (kind == ItemKindEnum.SchoolItem) {
        payload = {
          ...payload,
          [itemToKind.SchoolItem.classesOffered]:
            data[itemToKind.SchoolItem.classesOffered].value,
          [itemToKind.SchoolItem.schoolType]:
            data[itemToKind.SchoolItem.schoolType],
          [itemToKind.SchoolItem.website]: data[itemToKind.SchoolItem.website],
        };
      }
      if (kind == ItemKindEnum.ExtracurricularActivityItem) {
        payload = {
          ...payload,
          [itemToKind.ExtracurricularActivityItem.activityType]:
            data[itemToKind.ExtracurricularActivityItem.activityType],
          [itemToKind.ExtracurricularActivityItem.frequency]:
            data[itemToKind.ExtracurricularActivityItem.frequency],
          [itemToKind.ExtracurricularActivityItem.targetStudents]:
            data[itemToKind.ExtracurricularActivityItem.targetStudents],
        };
      }
      if (kind == ItemKindEnum.ScholarshipAwardItem) {
        payload = {
          ...payload,
          [itemToKind.ScholarshipAwardItem.eligibilityCriteria]:
            data[itemToKind.ScholarshipAwardItem.eligibilityCriteria],
          [itemToKind.ScholarshipAwardItem.scholarshipType]:
            data[itemToKind.ScholarshipAwardItem.scholarshipType],
          [itemToKind.ScholarshipAwardItem.website]:
            data[itemToKind.ScholarshipAwardItem.website],
        };
      }
      dispatch(updateUserListing({ currentStep: 3, listData: payload }));

      if (userList.completedStep > 2) {
        return;
      } else {
        dispatch(changeCompletedStep(2));
      }
    } catch (error) {
      console.log("error submit", error);
      toast.error("Internal Server Error");
    } finally {
      setloading(false);
    }
  };

  function updateBookForm(data) {
    setValue("ISBN", data.isbn_number);
    setValue("BookName", data.itemName);
    setValue("bookAuthor", data.author);

    setSuggestions([]);
  }
  console.log("selectedTages", selectedTages);
  const customStyles = {
    control: (provided, state) => ({
      ...provided,
      border: "1px solid #D1D5DB", // Tailwind's gray-500 ≈ #a3a3a3
      borderRadius: "7px",
      height: "50px",
      boxShadow: "none",
      paddingLeft: "6px",
      "&:hover": {
        borderColor: "#737373", // Tailwind gray-600
      },
    }),
  };

  const MAX_PRICE = 100000;
  let kind = userList.kind;

  let options = [
    { value: "brand new", label: "Brand New" },
    { value: "opened", label: "Opened" },
    { value: "like new", label: "Like New" },
    { value: "very good", label: "Very Good" },
    { value: "good", label: "Good" },
  ];

  // Dummy unit options for price unit selection
  const priceUnitOptions = [
    { value: "per hour", label: "/ Hour" },
    { value: "per item", label: "/ Item" },
    { value: "per day", label: "/ Day" },
    { value: "per week", label: "/ Week" },
    { value: "per month", label: "/ Month" },
  ];

  const ExperienceTypeOptions = [
    { value: "<1 year", label: "Less than 1 year" },
    { value: "1-2 years", label: "1 to 2 years" },
    { value: "2-5 years", label: "2 to 5 years" },
    { value: "5-10 years", label: "5 to 10 years" },
    { value: "10-15 years", label: "10 to 15 years" },
    { value: ">15 years", label: "More than 15 years" },
  ];
  
  const QualificationTypeOptions = [
    {value: "bachelor", label: "High School Diploma"},
    {value: "bachelor", label: "Skills Certification"},
    {value: "bachelor", label: "Professional Certification",},
    {value: "bachelor", label:  "Associates Degree",}, 
   
    {value: "bachelor", label: "Bachelor's Degree (Level 5)"},
    {
      value: "postgrad_diploma",
      label: "Postgraduate Diploma/Certificate (Level 6)",
    },
    { value: "masters", label: "Master's Degree (Level 7)" },
    { value: "doctorate", label: "Doctoral Degree (Level 8)" },
  ];

  const TargetClassTypeOptions = [
    {
      value: "primary 1 to 6",
      label: "Primary 1 to 6 (Compulsory Primary School)",
    },
    { value: "lower secondary 7 to 9", label: "Lower Secondary 7 to 9" },
    {
      value: "upper secondary 10 to 11",
      label: "Upper Secondary 10 to 11 (CSEC Exams)",
    },
    {
      value: "sixth form 12 to 13",
      label: "Sixth Form 12 to 13 (CAPE / A-Levels)",
    },
    {
      value: "diploma",
      label:
        "Diploma (Post-Secondary Non-Degree e.g. HEART-NTA, Community Colleges)",
    },
    {
      value: "associate degree",
      label: "Associate Degree (Regional, via CXC/HE Institutions)",
    },
    { value: "bachelor", label: "Bachelor's Degree (Undergraduate)" },
    { value: "master", label: "Master's Degree (Postgraduate)" },
    { value: "doctorate", label: "Doctorate (Terminal Academic Degree)" },
  ];

  const BookNameCompo = () => {
    if (kind == "book" && !userList.isEdit) {
      return (
        <CustomSelectBox
          options={bookSearchSuggestion}
          fetchBook={fetchBook}
          setValue={setValue}
          sellerCss={sellerCss}
          watch={watch}
        />
      );
    } else {
      return <div></div>;
    }
  };
  const SubComponent = useCallback(() => {
    // Helper for limiting number input to 10 digits
    const handleNumberInput = (e) => {
      if (e.target.value.length > 10) {
        e.target.value = e.target.value.slice(0, 10);
      }
    };

    if (kind == ItemKindEnum.BookItem) {
      return (
        <div className=" w-full ">
          <div className={` w-full relative mb-4`}>
            <p className="text-lg font-medium :leading-5 md:text-lg mb-2 md:leading-7">
              Enter ISBN Number
            </p>
            <div
              className="relative w-full px-4 py-3 text-base
        border border-gray-300 rounded-lg
        focus:ring-2 focus:ring-blue-500 focus:border-transparent
        transition-all duration-200"
            >
              <input
                type="text"
                className="placeholder:text-lg border-none w-full outline-none capitalize"
                id="Enter_ISBN"
                maxLength={17} // ISBN-13 is 17 chars with hyphens, 13 without
                {...register("Enter_ISBN", {
                  required: "ISBN is required",
                  maxLength: { value: 17, message: "Max length is 17" },
                  // pattern: {
                  //   value: /^(?:\d{9}[\dXx]|\d{13}|(?:\d{1,5}-){3,4}[\dXx])$/,
                  //   message:
                  //     "Enter a valid ISBN-10 or ISBN-13 (digits, may include hyphens)",
                  // },
                })}
                placeholder="Enter ISBN (e.g. 978-3-16-148410-0)"
                autoComplete="off"
              />
            </div>

            {errors.Enter_ISBN && (
              <p
                id="Description-error"
                className="text-red-500 text-[12px] mt-1 absolute right-0 bottom-[-22px] md:bottom-[-22px] "
              >
                {errors.Enter_ISBN?.message}
              </p>
            )}
          </div>

          <div className={`mb-4 relative `}>
            <p className="text-lg font-medium mb-2 leading-7">Enter Author</p>
            <div
              className="relative w-full px-4 py-3 text-base
        border border-gray-300 rounded-lg
        "
            >
              <input
                type="text"
                className="border-none w-full outline-none capitalize"
                id="Enter_Author"
                maxLength={50}
                {...register("bookAuthor", {
                  required: " Author is required",
                  maxLength: { value: 50, message: "Max length is 50" },
                })}
                placeholder="Enter Book Author"
                autoComplete="off"
              />
            </div>

            {errors.bookAuthor && (
              <p
                id="Description-error"
                className="text-red-500 text-[12px] mt-1 absolute right-0 bottom-0 md:bottom-[-22px]"
              >
                {errors.bookAuthor?.message}
              </p>
            )}
          </div>

          <div className={` relative`}>
            <p className="text-lg mb-2 font-medium leading-7">
              Select Condition
            </p>
            <div className="w-full">
              <Controller
                name="bookCondition"
                control={control}
                className="pl-4"
                rules={{ required: "Book Condition is required" }}
                render={({ field }) => (
                  <Select
                    {...field}
                    options={options}
                    styles={customStyles}
                    className="selectBox-Condition"
                    value={options.find((opt) => opt.value === field.value)}
                    onChange={(val) => field.onChange(val.value)}
                  />
                )}
              />
            </div>

            {errors.bookCondition && (
              <p
                id="bookCondition-error"
                className="text-red-500 text-[12px] mt-1 absolute right-0 bottom-[-22px]"
              >
                {errors.bookCondition?.message}
              </p>
            )}
          </div>
        </div>
      );
    } else if (kind == ItemKindEnum.TutorItem) {
      return (
        <div className="">
          <div className={`mb-4 relative`}>
            <p className="text-lg md:text-lg leading-7 mb-2 font-medium text-gray-700">
              Highest Qualification
            </p>
            <div className="w-full relative border-gray-300 ">
              <Controller
                name={`${itemToKind.TutorItem.highestQualification}`}
                control={control}
                rules={{ required: "Qualification is required" }}
                defaultValue={
                  userList?.listData?.eventMode
                    ? {
                        value: userList.listData.eventMode,
                        label: userList.listData.eventMode,
                      }
                    : null
                }
                render={({ field }) => (
                  <Select
                    styles={{
                      ...customStyles,
                      control: (base) => ({
                        ...base,
                        borderRadius: "8px",
                        border: "1px solid #D1D5DB",
                        padding: "6px 4px",
                      }),
                      placeholder: (base) => ({
                        ...base,
                        color: "#a0aec0",
                      }),
                    }}
                    className="selectBox-Condition w-full"
                    classNamePrefix="select"
                    options={Object.keys(QualificationTypeEnum).map((item)=>{return {label:QualificationTypeEnum[item],value:item}})}
                    value={field.value}
                    onChange={(selected) => {
                      field.onChange(selected);
                    }}
                  />
                )}
              />
            </div>

            {errors.highestQualification && (
              <p className="text-red-500 text-[12px] mt-1 absolute right-0 bottom-[-22px]">
                {errors.highestQualification?.message}
              </p>
            )}
          </div>

          <div className={`mb-4 relative `}>
            <label className="block text-lg  md:text-lg font-medium text-gray-700 mb-2">
              Target Classes
            </label>

            <div className="relative w-full border-gray-300 ">
              <Controller
                name={`${itemToKind.TutorItem.targetClasses}`}
                control={control}
                rules={{ required: "Target Class is required" }}
                defaultValue={
                  userList?.listData?.eventMode
                    ? {
                        value: userList.listData.eventMode,
                        label: userList.listData.eventMode,
                      }
                    : null
                }
                render={({ field }) => (
                  <Select
                    styles={{
                      ...customStyles,
                      control: (base) => ({
                        ...base,
                        borderRadius: "8px",
                        border: "1px solid #D1D5DB",
                        padding: "6px 4px",

                        minHeight: "44px",
                      }),
                      placeholder: (base) => ({
                        ...base,
                        color: "#a0aec0",
                        fontSize: "16px",
                      }),
                      option: (base) => ({
                        ...base,
                        padding: "10px 12px",
                      }),
                    }}
                    className="selectBox-Condition w-full"
                    classNamePrefix="select"
                    options={TargetClassTypeOptions}
                    value={field.value}
                    onChange={(selected) => {
                      field.onChange(selected);
                    }}
                  />
                )}
              />
            </div>

            {errors.targetClasses && (
              <p className="text-red-500 text-[12px] mt-1 absolute right-0 bottom-[-22px]">
                {errors.targetClasses?.message}
              </p>
            )}
          </div>

          <div className={`mb-4 relative `}>
            <label className="block text-[16px] md:text-lg font-medium text-gray-700 mb-2">
              Select Experience
            </label>

            <div className="relative w-full border-gray-300 ">
              <Controller
                name={`${itemToKind.TutorItem.experience}`}
                control={control}
                rules={{ required: "Experience is required" }}
                defaultValue={
                  userList?.listData?.eventMode
                    ? {
                        value: userList.listData.eventMode,
                        label: userList.listData.eventMode,
                      }
                    : null
                }
                render={({ field }) => (
                  <Select
                    styles={{
                      control: (base) => ({
                        ...base,
                        borderRadius: "8px",
                        border: "1px solid #D1D5DB",
                        padding: "6px 4px",

                        minHeight: "46px",
                        fontSize: "16px",
                      }),
                      placeholder: (base) => ({
                        ...base,
                        color: "#a0aec0",
                      }),
                      option: (base, { isFocused, isSelected }) => ({
                        ...base,
                        color: isSelected ? "white" : "#2d3748",
                        padding: "10px 15px",
                      }),
                    }}
                    className="selectBox-Condition w-full"
                    classNamePrefix="select"
                    options={ExperienceTypeOptions}
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Select your experience level..."
                  />
                )}
              />
            </div>

            {errors.experience && (
              <p className="text-red-500 text-xs mt-1 absolute bottom-[-22px] right-0">
                {errors.experience?.message}
              </p>
            )}
          </div>

          <div className={` mb-4 relative`}>
            <label className="block text-[16px] md:text-lg font-medium text-gray-700 mb-2">
              Website
            </label>

            <div
              className={` relative rounded-xl border-2 border-gray-200 hover:border-gray-300 `}
            >
              <input
                type="url"
                autoComplete="off"
                placeholder="https://yourwebsite.com"
                className="w-full pl-4 pr-4 py-3 text-[16px] outline-none placeholder:text-gray-400 placeholder:font-light"
                maxLength={200}
                {...register("website", {
                  required: false,
                  validate: {
                    noSpaces: (value) =>
                      !/\s/.test(value) ||
                      "Spaces are not allowed in the website URL",
                    pattern: (value) =>
                      value && !/\s/.test(value)
                        ? /^(https?:\/\/)?(www\.)?[a-z0-9-]+(\.[a-z]{2,}){1,3}(:\d{1,5})?(\/\S*)?$/i.test(
                            value
                          )
                          ? true
                          : "Please enter a valid website URL"
                        : true,
                    validTld: (value) =>
                      /\.(com|org|net|edu|gov|co|io|ai|us|uk|[a-z]{2,})$/.test(
                        value
                      ) || "Invalid domain extension",
                    maxLength: (value) =>
                      value?.length <= 200 ||
                      "URL cannot exceed 200 characters",
                    secure: (value) =>
                      !/^http:\/\//.test(value) ||
                      "Consider using HTTPS for security",
                  },
                  maxLength: {
                    value: 200,
                    message: "URL cannot exceed 200 characters",
                  },
                })}
              />
            </div>

            {errors.website && (
              <p className="text-red-500 text-xs mt-1 absolute bottom-[-22px] right-0">
                <span>{errors.website?.message}</span>
              </p>
            )}
          </div>
        </div>
      );
    } else if (kind == ItemKindEnum.EventItem) {
      return (
        <div className="w-full">
          <div className="w-full flex flex-col sm:flex-row justify-between gap-6 mb-4">
            <div className="w-full sm:w-[48%]">
              <div className="mb-3">
                <label className="block text-lg font-medium text-gray-700">
                  Start Date
                </label>
              </div>

              <div className="relative">
                <DatePickerComp
                  register={register}
                  setValue={setValue}
                  watch={watch}
                  name={"eventStartDate"}
                  className="w-full px-2  border-2 border-gray-300 rounded-lg "
                  errors={errors}
                />
              </div>
            </div>

            <div className="w-full sm:w-[48%]">
              <div className="mb-3">
                <label className="block text-lg font-medium text-gray-700">
                  End Date
                </label>
              </div>

              <div className="relative">
                <DatePickerComp
                  register={register}
                  setValue={setValue}
                  watch={watch}
                  name={"eventEndDate"}
                  checkWith={"eventStartDate"}
                  className="w-full px-2 py-2 border-2 border-gray-200 rounded-lg "
                  errors={errors}
                />
              </div>
            </div>
          </div>

          <div>
            <div className="mb-4 relative">
              <label className="block text-lg font-medium text-gray-800 mb-2">
                Website
              </label>

              <div
                className={`${sellerCss.inputBox} relative flex items-center pr-3 rounded-lg border-2 border-gray-300  bg-white`}
              >
                <div className="absolute left-3 text-gray-400"></div>
                <input
                  type="url"
                  autoComplete="off"
                  placeholder="https://yourwebsite.com"
                  className="w-full pl-4 pr-4 py-3.5 text-[16px] outline-none placeholder:text-gray-400 placeholder:font-light bg-transparent"
                  maxLength={200}
                  {...register("website", {
                    required: "Website URL is required",
                    validate: {
                      noSpaces: (value) =>
                        !/\s/.test(value) ||
                        "Spaces are not allowed in the website URL",
                      pattern: (value) =>
                        value && !/\s/.test(value)
                          ? /^(https?:\/\/)?(www\.)?[a-z0-9-]+(\.[a-z]{2,}){1,3}(:\d{1,5})?(\/\S*)?$/i.test(
                              value
                            )
                            ? true
                            : "Please enter a valid website URL"
                          : true,
                      validTld: (value) =>
                        /\.(com|org|net|edu|gov|co|io|ai|us|uk|[a-z]{2,})$/.test(
                          value
                        ) || "Invalid domain extension",
                      maxLength: (value) =>
                        value?.length <= 200 ||
                        "URL cannot exceed 200 characters",
                      secure: (value) =>
                        !/^http:\/\//.test(value) ||
                        "Consider using HTTPS for security",
                    },
                    maxLength: {
                      value: 200,
                      message: "URL cannot exceed 200 characters",
                    },
                  })}
                />
                <FiLink />
              </div>

              {errors.website && (
                <p
                  id="ISBN-error"
                  className="text-red-500 text-xs mt-1 absolute bottom-[-22px] right-0"
                >
                  {errors.website?.message}
                </p>
              )}
            </div>

            <div className="mb-4  relative">
              <label className="block text-lg font-medium text-gray-800 mb-3">
                Event Mode
              </label>

              <div className="relative">
                <Controller
                  name={`${itemToKind.EventItem.eventMode}`}
                  control={control}
                  rules={{ required: "Please select an event mode" }}
                  defaultValue={
                    userList?.listData?.eventMode
                      ? {
                          value: userList.listData.eventMode,
                          label: userList.listData.eventMode,
                        }
                      : null
                  }
                  render={({ field }) => (
                    <Select
                      styles={customStyles}
                      className="selectBox-Condition"
                      options={eventModeArr}
                      value={field.value}
                      onChange={(selected) => {
                        field.onChange(selected);
                      }}
                    />
                  )}
                />
              </div>

              {errors[itemToKind.EventItem.eventMode] && (
                <p
                  id="ISBN-error"
                  className="text-red-500 text-xs mt-1 absolute bottom-[-22px] right-0"
                >
                  {errors[itemToKind.EventItem.eventMode].message}
                </p>
              )}
            </div>
          </div>
        </div>
      );
    } else if (kind == ItemKindEnum.SchoolItem) {
      return (
        <div className="w-full ">
          <div className="mb-4 relative">
            <label className="block text-lg font-medium text-gray-800 mb-3">
              Class Offered
            </label>

            <div className="relative">
              <Controller
                name={itemToKind.SchoolItem.classesOffered}
                control={control}
                rules={{ required: "Please select classes offered" }}
                defaultValue={
                  userList.listData[itemToKind.SchoolItem.classesOffered] || ""
                }
                render={({ field }) => (
                  <Select
                    {...field}
                    styles={customStyles}
                    className="selectBox-Condition"
                    options={TargetClassTypeEnum}
                    value={field.value}
                    onChange={(val) => {
                      field.onChange(val);
                    }}
                  />
                )}
              />
            </div>

            {errors[itemToKind.SchoolItem.classesOffered] && (
              <div className="mt-2 flex justify-end items-start text-red-500 text-[12px] pl-1">
                <span>
                  {" "}
                  {errors[itemToKind.SchoolItem.classesOffered]?.message}
                </span>
              </div>
            )}
          </div>

          <div className={` mb-4 w-full`}>
            <label
              htmlFor="schoolType"
              className="block text-lg font-medium mb-2"
            >
              School Type
            </label>
            <div className="relative w-full">
              <Controller
                name={itemToKind.SchoolItem.schoolType}
                control={control}
                rules={{ required: "School Type is required" }}
                defaultValue={
                  userList.listData[itemToKind.SchoolItem.schoolType] || null
                }
                render={({ field }) => (
                  <Select
                    {...field}
                    styles={customStyles}
                    className="selectBox-Condition"
                    options={schoolTypes}
                    value={schoolTypes.find((opt) => opt.value === field.value)}
                    onChange={(val) => field.onChange(val.value)}
                  />
                )}
              />
            </div>

            {errors[itemToKind.SchoolItem.schoolType] && (
              <div className="mt-2 flex justify-end items-start text-red-500 text-[12px] pl-1">
                <span> {errors[itemToKind.SchoolItem.schoolType].message}</span>
              </div>
            )}
          </div>

          <div className="mt-2 mb-4">
            <label
              htmlFor="website-input"
              className="block text-lg font-medium text-gray-800 mb-2"
            >
              Website
              <span className="text-red-500 ml-1">*</span>
            </label>

            <div className="border rounded-lg border-gray-300 relative">
              <input
                id="website-input"
                type="url"
                autoComplete="off"
                placeholder="https://yourschool.edu"
                className={`block w-full pl-4 pr-3 py-3 text-base rounded-lg border transition-colors  border-gray-300`}
                aria-invalid={errors.website ? "true" : "false"}
                aria-describedby="website-error"
                maxLength={200}
                {...register("website", {
                  required: {
                    value: true,
                    message: "Website URL is required",
                  },
                  validate: {
                    noSpaces: (value) =>
                      !/\s/.test(value) ||
                      "Spaces are not allowed in the website URL",
                    pattern: (value) =>
                      value && !/\s/.test(value)
                        ? /^(https?:\/\/)?(www\.)?[a-z0-9-]+(\.[a-z]{2,}){1,3}(:\d{1,5})?(\/\S*)?$/i.test(
                            value
                          )
                          ? true
                          : "Please enter a valid website URL"
                        : true,
                    validTld: (value) =>
                      /\.(com|org|net|edu|gov|co|io|ai|us|uk|[a-z]{2,})$/.test(
                        value
                      ) || "Invalid domain extension",
                    maxLength: (value) =>
                      value?.length <= 200 ||
                      "URL cannot exceed 200 characters",
                    secure: (value) =>
                      !/^http:\/\//.test(value) ||
                      "Consider using HTTPS for security",
                  },
                  maxLength: {
                    value: 200,
                    message: "URL cannot exceed 200 characters",
                  },
                })}
              />
            </div>

            {errors.website && (
              <div className="mt-2 flex justify-end items-start text-[12px] text-red-500  pl-1">
                <span> {errors.website.message}</span>
              </div>
            )}
          </div>
        </div>
      );
    } else if (kind == ItemKindEnum.ExtracurricularActivityItem) {
      return (
        <div className="w-full ">
          <div className={`mb-4 relative `}>
            <p className="text-lg mb-2 font-medium leading-7">Activity Type</p>
            <div className="w-full">
              <Controller
                name={itemToKind.ExtracurricularActivityItem.activityType}
                control={control}
                rules={{ required: "Activity Type is required" }}
                defaultValue={
                  userList.listData[
                    itemToKind.ExtracurricularActivityItem.activityType
                  ] || ""
                }
                render={({ field }) => (
                  <Select
                    {...field}
                    styles={customStyles}
                    className="selectBox-Condition"
                    options={activityTypeArr}
                    value={field.value}
                    onChange={(val) => {
                      field.onChange(val);
                    }}
                  />
                )}
              />
            </div>

            {errors[
              `${itemToKind.ExtracurricularActivityItem.activityType}`
            ] && (
              <p
                id="eventMode-error"
                className="text-red-500 text-xs mt-1 absolute bottom-[-22px] right-0"
              >
                {
                  errors[
                    `${itemToKind.ExtracurricularActivityItem.activityType}`
                  ]?.message
                }
              </p>
            )}
          </div>

          <div className={`mb-4 relative`}>
            <p className="text-lg font-medium leading-7 mb-2">Frequency</p>
            <div className="w-full">
              <Controller
                name={itemToKind.ExtracurricularActivityItem.frequency}
                control={control}
                rules={{ required: "Frequency is required" }}
                defaultValue={
                  userList.listData[
                    itemToKind.ExtracurricularActivityItem.frequency
                  ] || ""
                }
                render={({ field }) => (
                  <Select
                    {...field}
                    styles={customStyles}
                    className="selectBox-Condition"
                    options={frequencyArr}
                    value={field.value}
                    onChange={(val) => {
                      field.onChange(val);
                    }}
                  />
                )}
              />
            </div>

            {errors[`${itemToKind.ExtracurricularActivityItem.frequency}`] && (
              <p
                id="eventMode-error"
                className="text-red-500 text-xs mt-1 absolute bottom-[-22px] right-0"
              >
                {
                  errors[`${itemToKind.ExtracurricularActivityItem.frequency}`]
                    ?.message
                }
              </p>
            )}
          </div>
          <div className={`mb-4 relative mt-4`}>
            <p className="text-lg mb-2 font-medium leading-7">
              Target Students
            </p>
            <div className="border border-gray-400 w-full px-3 py-3 rounded-lg ">
              <input
                type="text"
                autoComplete="off"
                placeholder="Enter Target Student"
                className="w-full text-base outline-none appearance-none bg-white px-1"
                maxLength={150}
                {...register(
                  `${itemToKind.ExtracurricularActivityItem.targetStudents}`,
                  {
                    required: "Target is required",
                    maxLength: {
                      value: 150,
                      message: "Max length is 150",
                    },
                  }
                )}
              />
            </div>

            {errors[
              `${itemToKind.ExtracurricularActivityItem.targetStudents}`
            ] && (
              <p
                id="ISBN-error"
                className="text-red-500 text-xs mt-1 absolute bottom-[-22px] right-0"
              >
                {
                  errors[
                    `${itemToKind.ExtracurricularActivityItem.targetStudents}`
                  ]?.message
                }
              </p>
            )}
          </div>
        </div>
      );
    } else if (kind == ItemKindEnum.ScholarshipAwardItem) {
      return (
        <div className="w-full ">
          <div className="mb-4 relative">
            <label
              htmlFor="Eligibility"
              className="block text-lg md:text-lg font-medium text-gray-800 mb-3"
            >
              Eligibility Criteria
            </label>

            <div
              className={`${sellerCss.inputBox} relative rounded-lg border-2 border-gray-200  bg-white`}
            >
              <input
                type="text"
                id="Eligibility"
                placeholder="Enter eligibility requirements..."
                autoComplete="off"
                className="w-full px-4 py-3.5 text-[16px] outline-none placeholder:text-gray-400 placeholder:font-light bg-transparent"
                maxLength={"80"}
                {...register(
                  `${itemToKind.ScholarshipAwardItem.eligibilityCriteria}`,
                  {
                    required: "Eligibility criteria is required",
                    maxLength: {
                      value: "80",
                      message: "Max length is 80 characters",
                    },
                  }
                )}
              />
            </div>

            {errors[
              `${itemToKind.ScholarshipAwardItem.eligibilityCriteria}`
            ] && (
              <div className="mt-2 flex items-start text-red-500 text-sm pl-1">
                <p
                  id="ISBN-error"
                  className="text-red-500 text-xs mt-1 absolute bottom-[-22px] right-0"
                >
                  {
                    errors[
                      `${itemToKind.ScholarshipAwardItem.eligibilityCriteria}`
                    ]?.message
                  }
                </p>
              </div>
            )}
          </div>

          <div className="mb-4 relative">
            <label
              htmlFor="scholarship-type"
              className="block text-lg font-medium text-gray-800 mb-3"
            >
              Scholarship Type
            </label>

            <div
              className={`${sellerCss.inputBox} relative rounded-xl border-2 border-gray-200  bg-white`}
            >
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></div>
              <input
                type="text"
                id="scholarship-type"
                placeholder="Enter scholarship type"
                autoComplete="off"
                className="w-full pl-4 pr-4 py-3.5 text-[16px] outline-none placeholder:text-gray-400 placeholder:font-light bg-transparent"
                maxLength={80}
                {...register(
                  `${itemToKind.ScholarshipAwardItem.scholarshipType}`,
                  {
                    required: "Scholarship type is required",
                    maxLength: {
                      value: 80,
                      message: "Max length is 80",
                    },
                  }
                )}
              />
            </div>

            {errors[`${itemToKind.ScholarshipAwardItem.scholarshipType}`] && (
              <div className="mt-2 flex items-start text-red-500 text-sm pl-1">
                <p
                  id="ISBN-error"
                  className="text-red-500 text-xs mt-1 absolute bottom-[-22px] right-0"
                >
                  {
                    errors[`${itemToKind.ScholarshipAwardItem.scholarshipType}`]
                      ?.message
                  }
                </p>
              </div>
            )}
          </div>
          <div className="mb-4 relative">
            <label className="block text-lg font-medium text-gray-800 mb-3">
              Website
            </label>

            <div className="relative rounded-lg border-2 border-gray-200  bg-white">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"></div>
              <input
                type="url"
                autoComplete="off"
                placeholder="https://yourwebsite.com"
                className="w-full pl-4 pr-4 py-3.5 text-[16px] outline-none placeholder:text-gray-400 placeholder:font-light bg-transparent"
                maxLength={200}
                {...register("website", {
                  required: "Website URL is required",
                  validate: {
                    noSpaces: (value) =>
                      !/\s/.test(value) ||
                      "Spaces are not allowed in the website URL",
                    pattern: (value) =>
                      value && !/\s/.test(value)
                        ? /^(https?:\/\/)?(www\.)?[a-z0-9-]+(\.[a-z]{2,}){1,3}(:\d{1,5})?(\/\S*)?$/i.test(
                            value
                          )
                          ? true
                          : "Please enter a valid website URL"
                        : true,
                    validTld: (value) =>
                      /\.(com|org|net|edu|gov|co|io|ai|us|uk|[a-z]{2,})$/.test(
                        value
                      ) || "Invalid domain extension",
                    maxLength: (value) =>
                      value?.length <= 200 ||
                      "URL cannot exceed 200 characters",
                    secure: (value) =>
                      !/^http:\/\//.test(value) ||
                      "Consider using HTTPS for security",
                  },
                  maxLength: {
                    value: 200,
                    message: "URL cannot exceed 200 characters",
                  },
                })}
              />
            </div>

            {errors.website && (
              <div className="mt-2 flex items-start text-red-500 text-sm pl-1">
                <p
                  id="ISBN-error"
                  className="text-red-500 text-xs mt-1 absolute bottom-[-22px] right-0"
                >
                  {errors.website?.message}
                </p>
              </div>
            )}
          </div>
        </div>
      );
    }
  }, [errors]);

  const deleteImageFunction = (index) => {
    // Remove the image from preview
    setPreview((prev) => {
      const updated = { ...prev };
      delete updated[index];
      return updated;
    });

    // Remove the image from images as well
    setImages((prev) => {
      const updated = { ...prev };
      delete updated[index];
      return updated;
    });

    setValue("additionalImages", false, { shouldValidate: true });
    // just add only additional image required here
    register("additionalImages", {
      validate: () => "At least one additional image is required",
    });
  };

  // Register custom validations for images and location
  useEffect(() => {
    register("coverImage", {
      validate: () => (isUploadingImage || images[0] ? true : "Cover Image required"),
    });
    register("additionalImages", {
      validate: () =>
        isUploadingImage || [1, 2, 3, 4].some((idx) => images[idx])
          ? true
          : "At least one additional image is required",
    });
    register("location", {
      validate: () => !!watch("address") || "Address required",
    });
  }, [register, images, watch, isUploadingImage]);

  const router = useRouter();
  const [showDiscardModal, setShowDiscardModal] = useState(false);

  console.log("watch--------------------", watch(), images);

  return (
    <section className="my-4 md:my-10 relative w-full">
      {loading && (
        <div className="z-50 h-full w-full bg-white opacity-80 absolute">
          <Circles
            height="80"
            width="80"
            color="#4fa94d"
            ariaLabel="circles-loading"
            wrapperStyle={{}}
            wrapperClass="sticky top-[10%] left-[45%] justify-center"
            visible={true}
          />
        </div>
      )}

      <header className="my-4">
        <h2
          id="member-resources-heading"
          className="text-[22px] font-semibold md:font-medium my-4 md:text-[48px]"
        >
          List Your Item
        </h2>
        {/* <p className="mt-3 font-extralight text-[12px] md:text-[18px] md:w-7/12">
          DigiRoad collects this information to better understand and serve your
          business.
        </p> */}
      </header>

      <div className=" mx-auto  ">
        <section className="flex flex-col lg:flex-row gap-8 my-8">
          <section className="w-full lg:w-1/2 flex flex-col justify-center items-center lg:items-start gap-6">
            {kind == ItemKindEnum.BookItem && (
              <CustomSelectBox
                options={bookSearchSuggestion}
                fetchBook={fetchBook}
                setValue={setValue}
                sellerCss={sellerCss}
                watch={watch}
              />
            )}

            {/* Title Input with maxLength 50 */}
            <div className="relative  w-full ">
              <label className="block text-lg font-medium text-gray-800 mb-2">
                Title
              </label>
              <div
                className="relative px-4 py-3 text-base
        border border-gray-300 rounded-lg
        focus:ring-2 focus:ring-blue-500 focus:border-transparent
        transition-all duration-200"
              >
                <input
                  type="text"
                  autoComplete="off"
                  disabled={userList.isEdit}
                  placeholder="Enter title"
                  maxLength={100}
                  className={` w-full
        placeholder-gray-400
        ${userList.isEdit ? "bg-gray-100 cursor-not-allowed" : "bg-white"}
        ${errors.title ? "border-red-500" : ""}
      `}
                  {...register("title", {
                    required: "Title is required",
                    maxLength: {
                      value: 150,
                      message: "Maximum 150 characters allowed",
                    },
                    validate: (value) => {
                      const wordCount = value.trim()?.split(/\s+/).length;
                      return wordCount <= 10 || "Maximum 10 words allowed";
                    },
                  })}
                  onInput={(e) => {
                    const words = e.target.value.trim()?.split(/\s+/);
                    if (words.length > 10) {
                      e.target.value = words.slice(0, 10).join(" ");
                    }
                    // Enforce max 50 chars (should be handled by maxLength, but for extra safety)
                    if (e.target.value.length > 150) {
                      e.target.value = e.target.value.slice(0, 150);
                    }
                  }}
                />
                {errors.title && (
                  <p className="text-red-500 text-xs mt-1 absolute bottom-[-22px] right-0">
                    {errors.title?.message}
                  </p>
                )}
              </div>
            </div>

            {/* Description Input with maxLength 100 */}
            <div className="relative w-full ">
              <label className="block text-lg font-medium text-gray-800 mb-2">
                Description
              </label>
              <div
                className="relative px-4 py-3 text-base
        border border-gray-300 rounded-lg
        focus:ring-2 focus:ring-blue-500 focus:border-transparent
        transition-all duration-200"
              >
                <input
                  type="text"
                  className={`
       w-full
        placeholder-gray-400
        ${errors.Description ? "border-red-500" : ""}
      `}
                  id="Description"
                  maxLength={300}
                  {...register("Description", {
                    required: "Description is required",
                    maxLength: {
                      value: 300,
                      message: "Maximum 300 characters allowed",
                    },
                  })}
                  placeholder="Enter description"
                  autoComplete="off"
                  onInput={(e) => {
                    // Enforce max 100 chars (should be handled by maxLength, but for extra safety)
                    if (e.target.value.length > 300) {
                      e.target.value = e.target.value.slice(0, 300);
                    }
                  }}
                />
                {errors.Description && (
                  <p className="text-red-500 text-xs mt-1 absolute bottom-[-22px] right-0">
                    {errors.Description?.message}
                  </p>
                )}
              </div>
            </div>

            {/* ++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++ */}

            <div className="w-full">
              <SubComponent />
            </div>

            {/* Set Price Input with unit selector */}
            <div className="relative w-full ">
              <label className="block text-lg font-medium text-gray-800 mb-3">
                Set Price
              </label>

              <div className="relative flex items-stretch border border-gray-300 rounded-lg transition-colors overflow-visible p-[2px]">
                <div className="flex items-center px-4 bg-white">
                  <span className="text-gray-600 font-medium mr-2">J$</span>
                </div>
                <input
                  type="number"
                  className="w-full text-base outline-none appearance-none bg-white px-1 py-3"
                  id="price"
                  min="1"
                  onWheel={(e) => e.target.blur()}
                  max="1000000000"
                  inputMode="decimal"
                  placeholder="Enter price"
                  autoComplete="off"
                  maxLength={10}
                  {...register("price", {
                    required: "Price is required",
                    pattern: {
                      value: /^(?!0\d)\d*(\.\d{0,2})?$/,
                      message: "Enter a valid price",
                    },
                    validate: (value) => {
                      const [intPart] = String(value || "").split(".");
                      if (intPart && intPart.length > 10) {
                        return "Maximum 10 digits allowed";
                      }

                      const numeric = parseFloat(value);
                      if (isNaN(numeric)) return "Enter a valid number";

                      if (numeric > 1000000000)
                        return "Price must be ≤ 1,000,000,000";
                      if (numeric < 1) return "Price must be ≥ 1";

                      return true;
                    },
                  })}
                  onInput={(e) => {
                    let val = e.target.value;
                    val = val.replace(/^0+/, "");
                    const [intPart, decPart] = val.split(".");
                    if (intPart && intPart.length > 10) {
                      e.target.value =
                        intPart.slice(0, 10) +
                        (decPart !== undefined ? "." + decPart : "");
                    }
                    if (!val.includes(".") && val.length > 10) {
                      e.target.value = val.slice(0, 10);
                    }
                  }}
                  onKeyDown={(e) => {
                    const allowedKeys = [
                      "Backspace",
                      "ArrowLeft",
                      "ArrowRight",
                      "Tab",
                      "Delete",
                      ".",
                    ];
                    if (allowedKeys.includes(e.key)) return;

                    const currentValue = e.currentTarget.value;
                    const selectionStart = e.currentTarget.selectionStart;
                    const selectionEnd = e.currentTarget.selectionEnd;
                    if (selectionStart !== selectionEnd) return;

                    let nextValue =
                      currentValue.slice(0, selectionStart) +
                      e.key +
                      currentValue.slice(selectionEnd);
                    const [intPart] = nextValue.split(".");
                    if (intPart.length > 10) {
                      e.preventDefault();
                      return;
                    }
                    if (!/^\d*\.?\d{0,2}$/.test(nextValue)) {
                      e.preventDefault();
                      return;
                    }
                    const numeric = parseFloat(nextValue);
                    if (numeric > 1000000000) {
                      e.preventDefault();
                    }
                  }}
                />
                <div className="w-[30%] min-w-[140px] border-l border-gray-300 ">
                  <Controller
                    name="priceUnit"
                    control={control}
                    rules={{ required: "Unit is required" }}
                    // defaultValue={watch("priceUnit") || "per item"}
                    render={({ field }) => (
                      <Select
                        {...field}
                        styles={{
                          control: (base) => ({
                            ...base,
                            height: "100%",
                            border: "0px",
                            borderRadius: 0,
                            boxShadow: "none",
                            color: "red",
                          }),
                          valueContainer: (base) => ({
                            ...base,
                            height: "100%",
                            padding: "0 10px",
                          }),
                          menuPortal: (base) => ({
                            ...base,
                            zIndex: 9999,
                          }),
                        }}
                        className="h-full"
                        // classNamePrefix="select"
                        options={priceUnitOptions}
                        value={priceUnitOptions.find(
                          (opt) => opt.value === field.value
                        )}
                        onChange={(opt) => field.onChange(opt.value)}
                        // menuPortalTarget={typeof window !== "undefined" ? document.body : null}
                        // menuPosition="fixed"
                      />
                    )}
                  />
                </div>
              </div>

              {errors.price && (
                <p className="text-red-500 text-[12px] mt-1 absolute right-0 bottom-[-22px]">
                  {errors.price?.message}
                </p>
              )}
              {errors.priceUnit && (
                <p className="text-red-500 text-[12px] mt-1 absolute right-0 bottom-[-40px]">
                  {errors.priceUnit?.message}
                </p>
              )}
            </div>

            {/* Enter Tags */}
            <div className="relative w-full ">
              <label className="block text-lg font-medium text-gray-800 mb-3">
                Enter Tags
              </label>

              {isClient && (
                <Controller
                  name="tags"
                  control={control}
                  render={({ field }) => {
                    // We'll use a ref to access the input element inside CreatableSelect
                    let inputValue = "";
                    let selectRef = null;

                    // Custom onInputChange to keep track of the current input value
                    const handleInputChange = (val, { action }) => {
                      if (action === "input-change") {
                        // Limit tag input to 50 chars
                        if (val.length > 50) {
                          inputValue = val.slice(0, 150);
                          return inputValue;
                        }
                        inputValue = val;
                      }

                      if (val.length < 50) {
                        return val;
                      } else {
                      }
                    };

                    // Custom onBlur handler
                    const handleBlur = (e) => {
                      // If there is a value in the input, add it as a tag
                      if (inputValue && inputValue.trim() !== "") {
                        // Add as a new tag, limit to 50 chars
                        const trimmed = inputValue.trim().slice(0, 50);
                        const newTag = {
                          label: trimmed,
                          value: trimmed,
                        };
                        let newValue = Array.isArray(field.value)
                          ? [...field.value]
                          : [];
                        // Prevent duplicate tags
                        if (
                          !newValue.some((tag) => tag.value === newTag.value)
                        ) {
                          newValue = [...newValue, newTag];
                          field.onChange(newValue);
                          handleTagChange(newValue);
                        }
                        inputValue = "";
                        // Clear the input in CreatableSelect
                        if (selectRef && selectRef.select) {
                          selectRef.select.setState({ inputValue: "" });
                        }
                      }
                    };

                    return (
                      <div className="relative">
                        <CreatableSelect
                          ref={(ref) => {
                            selectRef = ref;
                          }}
                          isMulti
                          className="react-select-container"
                          classNamePrefix="react-select"
                          {...field}
                          value={selectedTages}
                          onChange={(selected) => {
                            field.onChange(selected);
                            handleTagChange(selected);
                          }}
                          onInputChange={handleInputChange}
                          onBlur={handleBlur}
                          placeholder="Enter tag and hit enter"
                          components={{
                            DropdownIndicator: () => null,
                            IndicatorSeparator: () => null,
                            Menu: () => null, // Remove dropdown menu entirely
                            NoOptionsMessage: () => null, // Remove "No options" message
                          }}
                          styles={{
                            control: (base) => ({
                              ...base,
                              minHeight: "50px",
                              border: "1px solid #d1d5db",
                              borderRadius: "0.5rem",
                              boxShadow: "none",
                              "&:hover": {
                                borderColor: "#d1d5db",
                              },
                            }),
                            valueContainer: (base) => ({
                              ...base,
                              padding: "4px 8px",
                            }),
                            input: (base) => ({
                              ...base,
                              margin: 0,
                              padding: 0,
                              maxLength: 50, // visually limit input
                            }),
                            multiValue: (base) => ({
                              ...base,
                              backgroundColor: "#e5e7eb",
                              borderRadius: "4px",
                            }),
                            multiValueLabel: (base) => ({
                              ...base,
                              color: "#374151",
                              fontWeight: "500",
                            }),
                            multiValueRemove: (base) => ({
                              ...base,
                              color: "#6b7280",
                              ":hover": {
                                backgroundColor: "#f3f4f6",
                                color: "#ef4444",
                              },
                            }),
                            menu: (base) => ({
                              ...base,
                              display: "none", // Hide dropdown menu
                            }),
                          }}
                          theme={(theme) => ({
                            ...theme,
                            borderRadius: 8,
                            colors: {
                              ...theme.colors,
                              primary: "#3b82f6",
                              primary25: "#eff6ff",
                              neutral20: "#d1d5db",
                              neutral30: "#9ca3af",
                            },
                          })}
                          // Limit input to 50 chars at the HTML level (for accessibility)
                          inputProps={{ maxLength: 50 }}
                        />
                      </div>
                    );
                  }}
                />
              )}

              {errors.tags && (
                <p className="text-red-500 text-[12px] mt-1 absolute right-0 bottom-[-22px]">
                  {errors.tags?.message}
                </p>
              )}
            </div>

            {/* Cover Page and Additional Images Section */}
            <div className="w-full mb-8">
              {/* === Cover Page Section === */}
              <div className="mb-8">
                <h3 className="text-lg font-medium text-gray-800 mb-3">
                  Cover Page
                </h3>

                <div className="flex flex-col lg:flex-row gap-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
                  {/* Image Preview */}
                  <div className="w-full lg:w-1/2 aspect-[3/4] max-h-[300px] bg-gray-100 rounded-lg flex items-center justify-center overflow-hidden">
                    {preview[0] ? (
                      <img
                        src={preview[0]}
                        alt="Cover preview"
                        className="w-full h-full object-contain"
                      />
                    ) : (
                      <div className="text-gray-400 flex flex-col items-center text-center">
                        <IoAddOutline size={40} className="mb-2" />
                        <span>No cover image</span>
                      </div>
                    )}
                  </div>

                  {/* Upload Info + Button */}
                  <div className="w-full lg:w-1/2 flex flex-col justify-between">
                    <div className="space-y-2 text-sm text-gray-600 mb-4">
                      <p className="flex gap-1">
                        <span className="font-medium">File size:</span>
                        <span>Up to 5MB</span>
                      </p>
                      <p className="flex gap-1">
                        <span className="font-medium">Optimal dimensions:</span>
                        <span>600x280px</span>
                      </p>
                      <p className="flex gap-1">
                        <span className="font-medium">File types:</span>
                        <span>JPG, JPEG, PNG, GIF, WEBP</span>
                      </p>
                    </div>

                    <label className="inline-block py-2.5 px-4 w-full text-center bg-white border border-blue-500 text-blue-500 rounded-lg cursor-pointer hover:bg-blue-50 transition-colors">
                      Add Cover Image
                      <input
                        type="file"
                        accept="image/*"
                        onChange={(e) => handleImageChange(e, "0")}
                        className="hidden"
                        autoComplete="off"
                      />
                    </label>

                    {errors.coverImage && !preview[0] && (
                      <p className="text-red-500 text-sm mt-2">
                        {errors.coverImage.message}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* === Additional Images Section === */}
              <div>
                <h3 className="text-lg font-medium text-gray-800 mb-3">
                  Additional Images
                </h3>

                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
                  {[1, 2, 3, 4].map((index) => (
                    <div
                      key={index}
                      className="relative aspect-square bg-gray-100 rounded-lg border border-dashed border-gray-300 group"
                    >
                      {preview[index] ? (
                        <>
                          <img
                            src={preview[index]}
                            alt={`Preview ${index}`}
                            className="w-full h-full object-cover rounded-lg"
                          />
                          <button
                            type="button"
                            onClick={() =>
                              deleteImageFunction(index.toString())
                            }
                            className="absolute -top-2 -right-2 bg-white p-1.5 rounded-full shadow-md hover:bg-red-50 text-red-500 transition-colors"
                          >
                            <MdDelete size={18} />
                          </button>
                        </>
                      ) : (
                        <label className="w-full h-full flex flex-col items-center justify-center cursor-pointer">
                          <IoAddOutline
                            size={32}
                            className="text-gray-400 mb-1"
                          />
                          <span className="text-xs text-gray-500">
                            Add image
                          </span>
                          <input
                            type="file"
                            accept="image/*"
                            onChange={(e) =>
                              handleImageChange(e, index.toString())
                            }
                            className="hidden"
                            autoComplete="off"
                          />
                        </label>
                      )}
                    </div>
                  ))}
                </div>

                {errors.additionalImages &&
                  !isUploadingImage &&
                  ![1, 2, 3, 4].some((idx) => preview[idx]) && (
                    <p className="text-red-500 text-sm mt-2 text-right">
                      {errors.additionalImages.message}
                    </p>
                  )}
              </div>
            </div>

            <div className="w-[100%]  ">
              <MapWithSearchBox
                register={register}
                errors={errors}
                setValue={setValue}
                watch={watch}
                customError={errors}
              />
            </div>
            <div className={`${sellerCss.inputContainer} relative mt-5`}>
              <p className="text-lg font-medium leading-7">Parish Name</p>
              <div className="w-full">
                <Controller
                  name="parish"
                  control={control}
                  rules={{ required: "Parish is required" }}
                  defaultValue={userList.listData.parish || ""}
                  render={({ field }) => (
                    <Select
                      styles={customStyles}
                      className="selectBox-Condition capitalize"
                      options={Object.entries(ParishesListEnum).map(
                        ([label, value]) => ({
                          label: value,
                          value: value,
                        })
                      )}
                      value={
                        field.value
                          ? { label: field.value, value: field.value }
                          : null
                      }
                      onChange={(e) => field.onChange(e.value)}
                    />
                  )}
                />
              </div>

              {errors.parish && (
                <p
                  id="bookCondition-error"
                  className="text-red-500 text-[12px] mt-1 absolute right-0 bottom-2"
                >
                  {errors.parish?.message}
                </p>
              )}
            </div>

            <div className="w-full flex flex-col sm:flex-row justify-between items-center gap-4 mt-4">
              {/* <div className="w-[50%]"> */}
              <button
                className="w-full sm:w-1/2 global_linear_gradient text-white rounded-full px-4 py-3"
                onClick={handleSubmit(onSubmit)}
              >
                Save & Next
              </button>
              {/* </div> */}
              {/* <div className="w-[50%]"> */}
              <button
                className="w-full sm:w-1/2 border rounded-full px-4 py-3"
                onClick={(e) => {
                  e.preventDefault();
                  setShowDiscardModal(true);
                }}
              >
                Discard
              </button>
            </div>
            {/* </div> */}
          </section>

          <div className="w-full lg:w-1/2">
            <PreviewForm
              userList={userList}
              preview={Object.fromEntries(
                Object.entries(preview).filter(([_, value]) => !!value)
              )}
              watch={watch}
            />
          </div>
        </section>
      </div>

      {showDiscardModal &&
        typeof window !== "undefined" &&
        createPortal(
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-[#000000bf] bg-opacity-20 w-screen h-screen overflow-hidden">
            <div className="bg-white rounded-lg shadow-lg p-6 w-full max-w-sm">
              <h3 className="text-lg font-semibold mb-4">Confirm Discard</h3>
              <p className="mb-6">
                Are you sure you want to discard your changes? This action
                cannot be undone.
              </p>
              <div className="flex justify-end gap-3">
                <button
                  className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300"
                  onClick={() => setShowDiscardModal(false)}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 rounded bg-red-500 text-white hover:bg-red-600"
                  onClick={() => {
                    router.replace("/");
                    dispatch(resetListingData());
                    setShowDiscardModal(false);
                  }}
                >
                  Discard
                </button>
              </div>
            </div>
          </div>,
          document.body
        )}
    </section>
  );
}
