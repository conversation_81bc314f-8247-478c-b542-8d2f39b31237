import React from "react";

import moment from "moment";
import {ItemKindEnum, itemToKind, labelItemToKind} from "@/app/config/constant";

console.log("ItemKindEnum respective", ItemKindEnum);
export default function ItemRespectiveDetails({userList}) {
  if (userList.kind == ItemKindEnum.BookItem) {
    return (
      <div className="">
        <p
          className={`text-[16px] md:text-[16px] capitalize font-semibold truncate w-full mt-2`}
        >
          Condition:{" "}
          <span
            title={userList.listData?.bookCondition}
            className=" font-normal break-words "
          >
            {userList.listData?.bookCondition || ""}
          </span>
        </p>
        <p
          className={`text-[16px] md:text-[16px] font-semibold capitalize truncate w-full mt-2`}
        >
          ISBN:{" "}
          <span
            title={userList.listData?.ISBN}
            className="  font-normal break-words "
          >
            {userList.listData?.ISBN || ""}
          </span>
        </p>
        <p className="text-[16px] md:text-[16px] font-semibold capitalize break-words  w-full mt-2">
          Author:{" "}
          <span
            title={userList.listData?.bookAuthor}
            className=" font-normal break-words "
          >
            {userList.listData?.bookAuthor}
          </span>
        </p>
      </div>
    );
  } else if (userList.kind == ItemKindEnum.TutorItem) {
    return (
      <div>
        <p className={`text-[13px] md:text-[20px] capitalize truncate w-full mt-2`}>
          {[labelItemToKind.TutorItem.experience]}:{" "}
          <span className="font-medium">
            {userList.listData?.experience || ""}
          </span>
        </p>
        <p className={`text-[13px] md:text-[20px] capitalize truncate w-full mt-2`}>
          {[labelItemToKind.TutorItem.highestQualification]}:{" "}
          <span className="font-medium">
            {userList.listData?.highestQualification || ""}
          </span>
        </p>
      </div>
    );
  } else if (userList.kind == ItemKindEnum.EventItem) {
    return (
      <div>
        <p className={`font-semibold capitalize truncate w-full mt-2`}>
          {[labelItemToKind?.EventItem?.eventStartDate]}:{" "}
          <span className="font-normal truncate w-full">
            {moment(userList?.listData?.eventStartDate)?.format(
              "DD-MMM-YYYY hh:mm"
            ) || ""}
          </span>
        </p>
        <p className={`font-semibold capitalize mt-2`}>
          {[labelItemToKind?.EventItem?.eventEndDate]}:{" "}
          <span className="font-normal truncate w-full">
            {moment(userList?.listData?.eventEndDate)?.format(
              "DD-MMM-YYYY hh:mm"
            ) || ""}
          </span>
        </p>
        <p className={`font-semibold capitalize mt-2`}>
          {[labelItemToKind.EventItem.eventMode]}:{" "}
          <span className="font-normal">
            {userList?.listData?.eventMode || ""}
          </span>
        </p>
        <p className={`font-semibold  mt-2`}>
          {[labelItemToKind.EventItem.website]}:{" "}
          <span className="font-normal underline text-blue-700">
           <a href={userList?.listData?.website}  target="_blank"> {userList?.listData?.website || ""}</a>
          </span>
        </p>
      </div>
    );
  } else if (userList.kind == ItemKindEnum.SchoolItem) {
    return (
      <div>
        <p className={`font-semibold capitalize truncate w-full mt-2`}>
          {[labelItemToKind.SchoolItem.classesOffered]}:{" "}
          <span className="font-normal truncate w-full">
            {userList.listData?.classesOffered || ""}
          </span>
        </p>
        <p className={`font-semibold capitalize mt-2`}>
          {[labelItemToKind.SchoolItem.schoolType]}:{" "}
          <span className="font-normal truncate w-full">
            {userList.listData?.schoolType}
          </span>
        </p>
        <p className={`font-semibold  truncate w-full mt-2 `}>
          {[labelItemToKind.SchoolItem.website]}:{" "}
          <span className="font-normal underline text-blue-700"><a href={userList?.listData?.website}  target="_blank">{userList.listData?.website}</a></span>
        </p>
      </div>
    );
  } else if (userList.kind == ItemKindEnum.ExtracurricularActivityItem) {
    return (
      <div>
        <p className={`font-semibold capitalize truncate w-full mt-2`}>
          {[labelItemToKind.ExtracurricularActivityItem.targetStudents]}:{" "}
          <span className="font-normal">
            {userList.listData?.targetStudents || ""}
          </span>
        </p>
        <p className={`font-semibold capitalize truncate w-full mt-2`}>
          {[labelItemToKind.ExtracurricularActivityItem.activityType]}:{" "}
          <span className="font-normal">
            {userList.listData?.activityType.value}
          </span>
        </p>
        {/* <p
                className={`text-[13px] md:text-[20px] capitalize`}
            >
                {[labelItemToKind.ExtracurricularActivityItem.activityType]}: <span className='font-medium'>{userList.listData?.activityType || ""}</span>
            </p> */}
      </div>
    );
  } else if (userList.kind == ItemKindEnum.ScholarshipAwardItem) {
    return (
      <div>
        <p className={`font-semibold capitalize truncate w-full mt-2`}>
          {[labelItemToKind.ScholarshipAwardItem.eligibilityCriteria]}:{" "}
          <span className="font-normal">
            {userList.listData?.eligibilityCriteria || ""}
          </span>
        </p>
        <p className={`font-semibold capitalize truncate w-full mt-2`}>
          {[labelItemToKind.ScholarshipAwardItem.scholarshipType]}:{" "}
          <span className="font-normal">
            {userList.listData?.scholarshipType}
          </span>
        </p>
        <p className={`font-semibold  truncate w-full mt-2`}>
          {[labelItemToKind.ScholarshipAwardItem.website]}:{" "}
          <span className="font-normal underline text-blue-700">
            <a href={userList?.listData?.website}  target="_blank">{userList.listData?.website || ""}</a>
          </span>
        </p>
      </div>
    );
  }
}
