"use client"

import React, { useEffect, useState } from "react";
import { AdvancedMarker, APIProvider, Map } from "@vis.gl/react-google-maps";
import "./mapSearchable.scss"
import Skeleton from 'react-loading-skeleton'
import 'react-loading-skeleton/dist/skeleton.css'


const MapSearchable = ({
    dataLocation,
    setValue,
    width,
    height,
}) => {
    const [location, setLocation] = useState(null);
    const [loading, setLoading] = useState(true);
    const googleMapsApiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_NEW_API_KEY;
    const [title, settitle] = useState("")

    useEffect(() => {
        if (navigator?.geolocation) {
            navigator.geolocation?.getCurrentPosition(
                (position) => {
                    const { latitude, longitude } = position?.coords;
                    setLocation({ lat: latitude, lng: longitude });
                    setLoading(false);
                },
                (error) => {
                    console.log("Error getting user location:", error);
                    setLoading(false);
                    setLocation({
                        lat: 28.5728,
                        lng: 77.3562
                    });
                }
            );
        }
    }, []);

    useEffect(() => {

        const coordinates = dataLocation?.geometry?.location;
        settitle(dataLocation?.formatted_address)

        setLocation({ lat: coordinates?.lat(), lng: coordinates?.lng() });
        setValue("locationCoordinates", { lat: coordinates?.lat(), lng: coordinates?.lng() });

    }, [dataLocation]);


    if (loading) {
        return (
            <div className="loader-container">
                <Skeleton height={height} />
            </div>
        );
    }

    const handleMapClick = (event) => {
        // console.log('event click', event?.detail?.latLng?.lat,event?.detail?.latLng?.lng)
        const lat = event?.detail?.latLng?.lat;
        const lng = event?.detail?.latLng?.lng;
        reverseGeocode(lat, lng);
    };

    const reverseGeocode = (lat, lng) => {

        setValue("locationCoordinates", { lat: lat, lng: lng });
        setLocation({ lat: lat, lng: lng });

        const geocoder = new window.google.maps.Geocoder();

        geocoder.geocode({ location: { lat, lng } }, (results, status) => {
            if (status === "OK") {
                if (results[0]) {
                    console.log("Address:", results[0].formatted_address);
                    // You can now set this in state if needed
                    setValue("address", results[0].formatted_address);
                } else {
                    console.log("No results found");
                }
            } else {
                console.error("Geocoder failed due to:", status);
            }
        });
    };
    if (!location) return null;

    return (
        <APIProvider apiKey={`${googleMapsApiKey}`} libraries={['places']}>
            <Map
                // key={location?.lat + location?.lng}
                defaultCenter={location}
                draggableCursor={"grab"}
                onClick={handleMapClick}
                defaultZoom={14}
                gestureHandling="greedy"
                style={{
                    width: width,
                    height: height,
                    borderRadius: "15px",
                }}
                className="mapWrapper"
                mapId="DEMO_MAP_ID"
                zoomControl
            >


                {/* {data?.map((list, index) => ( */}

                <AdvancedMarker
                    // key={index}
                    position={location}
                    title={title}
                />

                {/* ) */}
                {/* ) */}
                {/* } */}



            </Map>
            {/* )} */}
        </APIProvider >
    );
}

export default MapSearchable;
