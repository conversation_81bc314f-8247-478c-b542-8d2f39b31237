.mapWrapper {
    height: 400px !important;


    .gm-style {

        .gmnoprint {
            // display: none !important;
            // top: 0;
        }

        .gm-style-mtc-bbw {
            display: none !important;
        }

        .gm-fullscreen-control {
            right: auto;
            left: 0;

            img {
                // display: none !important;
                // background: url("/public/logo.png") !important;
            }
        }


        .gm-svpc {
            display: none;
        }
    }
}

@media screen and (max-width: 426px) {
    .mapWrapper {
        height: 308px !important;
    }
}