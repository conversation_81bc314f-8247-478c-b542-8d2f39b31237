import { useEffect, useRef } from 'react';

export default function OtpInput({ length = 4, setotpNumber,watch,register,setValue }) {
  const inputsRef = useRef([]);
  const handleChange = (e, index) => {
    const value = e.target.value;
    if (!/^\d?$/.test(value)) return;

    if (value && index < length - 1) {
      inputsRef.current[index + 1]?.focus();
    }

    e.target.value = value;
    const otp = inputsRef.current.map((input) => input?.value || "").join("");
    setotpNumber(otp);
    setValue("listData.otp",otp)

  };

  const handleKeyDown = (e, index) => {
    if (e.key === 'Backspace' && !e.target.value && index > 0) {
      inputsRef.current[index - 1]?.focus();
    }
  };
  useEffect(() => {
    register("otp", {required: "OTP is required"})
  }, [])
  

  return (
    <div className="flex space-x-2">
      {Array.from({ length }).map((_, i) => (
        <input
          key={i}
          type="text"
            
          maxLength={1}
          className="w-12 h-12 md:h-20 md:w-20 text-[18px] text-center border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-[#0161ab]"
          style={{border:"1px solid "}}
          onChange={(e) => handleChange(e, i)}
          onKeyDown={(e) => handleKeyDown(e, i)}
          ref={(el) => (inputsRef.current[i] = el)}
          autoComplete="off"
        />
      ))}
    </div>
  );
}
