import React, { useCallback, useMemo } from "react";
import sellerCss from "./sellerComponent.module.scss";
import Slider from "react-slick";
import Image from "next/image";
import { ItemKindEnum } from "@/app/config/constant";
import moment from "moment";



export default function PreviewForm({ preview, watch, userList }) {
  const images = useMemo(
    () =>
      Object.values(preview).filter(
        (img) => typeof img === "string" && img.length > 0
      ),
    [preview]
  );

  const settings = {
    dots: true,
    infinite: images.length > 1,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
  };

  let kind = userList.kind;

  const FormatedFont = ({ text }) => {
    return <span className="font-medium">{text}</span>;
  };

  const DynamicDetails = useCallback(() => {
    if (kind == ItemKindEnum.BookItem) {
      return (
        <div>
          <p className="font-medium text-[18px] md:text-[24px] break-words">
            {watch("BookName")}
          </p>
          <p className="my-1 md:text-[18px]">
            Category: {userList?.category?.name || ""}
          </p>
          <p className="my-1 md:text-[18px]">
            Type: {userList?.listData.condition || ""}
          </p>
          <p>
            Location:{" "}
            <span className="font-medium">
              {watch("address.formatted_address")}
            </span>
          </p>
        </div>
      );
    } else if (kind == ItemKindEnum.TutorItem) {
      return (
        <div>
          <p className="font-medium text-[18px] md:text-[24px] break-words">
            {watch("title")}
          </p>
          <p className="my-1 md:text-[18px]">
            Category:
            <span className="font-medium">
              {" "}
              {userList?.category?.name || ""}
            </span>
          </p>
          <div className="flex gap-2">
            <p className="my-1 md:text-[18px]">
              Class:{" "}
              <FormatedFont
                text={
                  userList?.listData.targetClasses?.value ||
                  watch().targetClasses?.value
                }
              />
            </p>
            <p className="my-1 md:text-[18px]">
              Exp:{" "}
              <span className="font-medium">
                {userList?.listData.experience?.value ||
                  watch().experience?.value}
              </span>
            </p>
          </div>
          <p>
            Location:{" "}
            <span className="font-medium">
              {watch("address.formatted_address")}
            </span>
          </p>
        </div>
      );
    } else if (kind == ItemKindEnum.EventItem) {
      return (
        <div>
          <p className="font-medium text-[18px] md:text-[24px] break-words">
            {watch("title")}
          </p>
          <p className="my-1 md:text-[18px]">
            {userList?.listData.website || watch().website}
          </p>
          <p className="my-1 md:text-[18px]">
            Event Date :{" "}
            {moment(userList?.listData.eventStartDate).format("DD-MMM-YYYY") ||
              moment(watch().eventStartDate).format("DD-MMM-YYYY")}
          </p>
          <p className="my-1 md:text-[18px]">
            Event Time :{" "}
            {moment(userList?.listData.eventStartDate).format("hh:mm A") ||
              moment(watch().eventStartDate).format("hh:mm A")}
          </p>
          <p>
            Location:{" "}
            <span className="font-medium">
              {watch("address.formatted_address")}
            </span>
          </p>
        </div>
      );
    } else if (kind == ItemKindEnum.SchoolItem) {
      return (
        <div>
          <p className="font-medium text-[18px] md:text-[24px] break-words">
            {watch("title")}
          </p>
          <p className="my-1 md:text-[18px]">
            Category: {userList?.category?.name || ""}
          </p>
          <p className="my-1 md:text-[18px]">
            Class:{" "}
            {userList?.listData.classesOffered?.value ||
              watch().classesOffered?.value}
          </p>
          <p>
            Location:{" "}
            <span className="font-medium">
              {watch("address.formatted_address")}
            </span>
          </p>
        </div>
      );
    } else if (kind == ItemKindEnum.ExtracurricularActivityItem) {
      return (
        <div>
          <p className="font-medium text-[18px] md:text-[24px] break-words">
            {watch("title")}
          </p>
          <p className="my-1 md:text-[18px]">
            Category: {userList?.category?.name || ""}
            {userList?.subCategory?.name
              ? `/${userList?.subCategory?.name}`
              : ""}
          </p>
          <p className="my-1 md:text-[18px]">
            Class: {userList?.listData.targetStudents || watch().targetStudents}
          </p>
          <p>
            Location:{" "}
            <span className="font-medium">
              {watch("address.formatted_address")}
            </span>
          </p>
        </div>
      );
    } else if (kind == ItemKindEnum.ScholarshipAwardItem) {
      return (
        <div>
          <p className="font-medium text-[18px] md:text-[24px] break-words">
            {watch("title")}
          </p>
          <p className="my-1 md:text-[18px]">
            Category:
            <FormatedFont text={userList?.category?.name || ""} />
          </p>
          <p className="my-1 md:text-[18px]">
            scholarshipType:{" "}
            <FormatedFont
              text={
                userList?.listData.scholarshipType || watch().scholarshipType
              }
            />
          </p>
          <p>
            Location:{" "}
            <span className="font-medium">
              {watch("address.formatted_address")}
            </span>
          </p>
        </div>
      );
    }
    return null;
  }, [kind, userList, watch]);

  return (
    <section
      className={`
            ${sellerCss.itemContainer}
           flex flex-col
            space-y-6
            px-4
            sm:px-6
            md:px-8
          `}
    >
      <h2
        id="member-resources-heading"
        className="text-lg font-semibold my-6 sm:my-8 md:hidden text-center"
      >
        Preview
      </h2>

      {/* image slider  */}
      <div
        className="
               bg-[#FFFBF6]
                w-full
                h-48
                sm:h-60
                md:h-80
                rounded-md
                overflow-hidden
                relative
             "
        style={{ minHeight: "12rem", display: "flex", alignItems: "center", justifyContent: "center" }}
      >
        {images.length > 0 ? (
          <div className="w-full h-full">
            <Slider {...settings} className="h-full flex">
              {images.map((img, idx) => (
                <div
                  key={idx}
                  className="w-full h-full flex items-center justify-center"
                >
                  {/* Use next/image for better optimization */}
                  <Image
                    src={img}
                    alt={`preview image ${idx + 1}`}
                    width={400}
                    height={320}
                    style={{
                      objectFit: "contain",
                      width: "100%",
                      height: "100%",
                      maxHeight: "320px",
                      background: "#FFFBF6",
                    }}
                    className="p-3 sm:p-4"
                    unoptimized
                  />
                </div>
              ))}
            </Slider>
          </div>
        ) : (
          <div className="absolute inset-0 flex items-center justify-center">
          No image uploaded
          </div>
        )}
      </div>

      {/* item description  */}
      <div
        className="
                flex flex-col
                space-y-4
                my-6
                text-sm
                sm:text-base
              "
      >
        <DynamicDetails />
        <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-6 mt-4">
          <p className="text-lg sm:text-xl font-medium">Price</p>
          <button
            className={`
                      ${sellerCss.buttonPrice}
                      mt-2 sm:mt-0
                      w-full sm:w-auto
                      px-4 py-2
                      text-base sm:text-lg
                      rounded-md
                    `}
          >
            J$ {watch("price")}
          </button>
        </div>
      </div>
    </section>
  );
}
