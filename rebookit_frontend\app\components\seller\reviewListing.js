import {useForm} from "react-hook-form";
import Slider from "react-slick";
import sellerCss from "./sellerComponent.module.scss";

import {HiChatBubbleLeftRight} from "react-icons/hi2";

import {IoStarSharp} from "react-icons/io5";
import {toast} from "react-toastify";
import {useCallback, useEffect, useState} from "react";
import {Circles} from "react-loader-spinner";

import moment from "moment";
import {USER_ROUTES} from "@/app/config/api";
import {getToken, userDataFromLocal} from "@/app/utils/utils";
import {useDispatch, useSelector} from "react-redux";
import {useRouter} from "next/navigation";
import {createPortal} from "react-dom";
import {
  changeCompletedStep,
  isVerifiedChange,
  resetListingData,
  setToDefault,
  updateUserListing,
  updateUserListingData,
} from "@/app/redux/slices/storeSlice";
import {editItem, listItem, verifyUserData} from "@/app/services/profile";
import Celebration from "./celebration";
import {ItemKindEnum, itemToKind} from "@/app/config/constant";
import ItemRespectiveDetails from "./itemRespectiveDetails";

export default function ReviewListing({}) {
  const userList = useSelector((x) => x.storeData.userListing);
  const userData = userDataFromLocal();
  const {
    register,
    watch,
    formState: {errors},
    getValues,
    handleSubmit,
    setValue,
    reset,
    setError,
  } = useForm({
    defaultValues: {...userList, email: userData?.email},
  });

  const dispatch = useDispatch();
  const router = useRouter();
  const [showDiscardModal, setShowDiscardModal] = useState(false);

  const [loading, setLoading] = useState(false);
  const [submitSuccess, setsubmitSuccess] = useState(true);

  // OTP flow removed: publish button is shown directly on this screen

  // Helper to render tags safely (fixes "object as React child" error)
  const renderTags = (tags) => {
    if (!tags) return null;
    if (Array.isArray(tags)) {
      return tags.map((tag, idx) => {
        if (typeof tag === "string" || typeof tag === "number") {
          return (
            <span
              key={idx}
              className="flex items-center bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-1 mb-1 break-words truncate max-w-[30%] w-fit "
            >
              {tag}
            </span>
          );
        }
        // If tag is an object with label/value, render label
        if (tag && typeof tag === "object" && (tag.label || tag.value)) {
          return (
            <span
              key={idx}
              className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-1 mb-1"
            >
              {tag.label || tag.value}
            </span>
          );
        }
        return null;
      });
    }
    // If tags is a string
    if (typeof tags === "string" || typeof tags === "number") {
      return (
        <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-1 mb-1">
          {tags}
        </span>
      );
    }
    return null;
  };

  const onSubmit = async (data) => {
      try {
        setLoading(true);
        let imagesAr = [];
        Object.entries(getValues("listData.bookImages")).map(([key, value]) => {
          if (value) {
            imagesAr.push(value);
          }
        });
        let userPayload = {
          title: getValues("listData.title"),
          description: getValues("listData.desciption"),
          price: getValues("listData.price"),
          // priceUnit: getValues("listData.priceUnit"),
          tags: getValues("listData.tags"),
          images: imagesAr,
          categoryId: getValues("category._id"),
          ...(getValues("subCategory._id")
            ? {subCategoryId: getValues("subCategory._id")}
            : null),
          ...(getValues("subSubCategory._id")
            ? {subSubCategoryId: getValues("subSubCategory._id")}
            : null),
          ...(getValues("subSubSubCategory._id")
            ? {subSubSubCategoryId: getValues("subSubSubCategory._id")}
            : null),
          address: {
            ...getValues("listData.address"),
            parish: getValues("listData.parish"),
          },
          kind: getValues("kind"),
        };
        if (getValues("kind") == ItemKindEnum.BookItem) {
          userPayload = {
            ...userPayload,
            isbn_number: getValues("listData.ISBN"),
            authors: [getValues("listData.bookAuthor")],
            condition: getValues("listData.bookCondition"),
          };
        }

        if (getValues("kind") == ItemKindEnum.TutorItem) {
          userPayload = {
            ...userPayload,
            highestQualification: getValues("listData.highestQualification"),
            targetClasses: getValues("listData.targetClasses"),
            experience: getValues("listData.experience"),
            website: getValues("listData.website"),
          };
        }
        if (getValues("kind") == ItemKindEnum.EventItem) {
          userPayload = {
            ...userPayload,
            [itemToKind.EventItem.eventStartDate]: getValues(
              "listData.eventStartDate"
            ),
            [itemToKind.EventItem.eventEndDate]: getValues(
              "listData.eventEndDate"
            ),
            [itemToKind.EventItem.eventMode]: getValues("listData.eventMode"),
            [itemToKind.EventItem.website]: getValues("listData.website"),
          };
        }
        if (getValues("kind") == ItemKindEnum.SchoolItem) {
          userPayload = {
            ...userPayload,
            [itemToKind.SchoolItem.classesOffered]: getValues(
              "listData.classesOffered"
            ),
            [itemToKind.SchoolItem.schoolType]: getValues(
              "listData.schoolType"
            ),
            [itemToKind.SchoolItem.website]: getValues("listData.website"),
          };
        }
        if (getValues("kind") == ItemKindEnum.ExtracurricularActivityItem) {
          userPayload = {
            ...userPayload,
            [itemToKind.ExtracurricularActivityItem.activityType]:
              getValues("listData.activityType")?.value ??
              getValues("listData.activityType")?.label ??
              getValues("listData.activityType"),
            [itemToKind.ExtracurricularActivityItem.frequency]:
              getValues("listData.frequency")?.value ??
              getValues("listData.frequency")?.label ??
              getValues("listData.frequency"),
            [itemToKind.ExtracurricularActivityItem.targetStudents]: getValues(
              "listData.targetStudents"
            ),
          };
        }
        if (getValues("kind") == ItemKindEnum.ScholarshipAwardItem) {
          userPayload = {
            ...userPayload,
            [itemToKind.ScholarshipAwardItem.eligibilityCriteria]: getValues(
              "listData.eligibilityCriteria"
            ),
            [itemToKind.ScholarshipAwardItem.scholarshipType]: getValues(
              "listData.scholarshipType"
            ),
            [itemToKind.ScholarshipAwardItem.website]:
              getValues("listData.website"),
          };
        }

        if (userList.isEdit) {
          delete userPayload.title;
          delete userPayload.categoryId;
          delete userPayload.subCategoryId;
          // delete userPayload.price
          let reponse = await editItem(userPayload, userList.editItemId);
          if (reponse.status == 200) {
            setTimeout(() => {
              // dispatch(resetListingData());
              dispatch(setToDefault());
            }, 4000);
            toast.success(reponse.data?.message || "Successfully Updated");
            dispatch(updateUserListing({currentStep: 4}));
            dispatch(changeCompletedStep(3));
            // dispatch(setToDefault());
          } else {
            toast.error(reponse.data?.message || "Invalid");
          }
          return;
        }
        let response = await listItem(userPayload);
        if (response.status == 200) {
          setTimeout(() => {
            dispatch(resetListingData());
          }, 10000);
          toast.success(
            response.data?.message || "Successfully Sent for Review"
          );
          // router.push("/profile/mybooks");
          dispatch(updateUserListing({currentStep: 4}));
          dispatch(changeCompletedStep(3));
          // dispatch(setToDefault());
        } else {
          toast.error(response.data.message || "Invalid");
        }
      } catch (error) {
        toast.error(error || "Internal Server Error");
      } finally {
        setLoading(false);
        }
  };

  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
  };

  const returnIfPresent = () => {
    let str = "";
    if (userList?.category?.name) {
      str = userList?.category?.name;
      if (userList?.subCategory?.name) {
        str = str + `/${userList.subCategory.name}`;
      }
      if (userList?.subSubCategory?.name) {
        str = str + `/${userList.subSubCategory.name}`;
      }
      if (userList?.subSubSubCategory?.name) {
        str = str + `/${userList?.subSubSubCategory?.name}`;
      }
    }
    return str;
  };

  // if(submitSuccess) return <Celebration/>
  return (
    <section className={`mt-10 ${sellerCss.reviewContainer} relative`}>
      {/* Loading spinner remains same */}

      <header className="px-4 md:px-0">
        <h2 className="text-2xl font-semibold md:text-[32px] my-4 md:my-5">
          Review And Submit
        </h2>
        {/* <p className="mt-3 text-sm md:text-base text-[#313131] md:w-10/12">
        {/* <p className="mt-3 text-sm md:text-base text-[#313131] md:w-10/12">
          DigiRoad collects this information to better understand and serve your
          business.
        </p> 
        </p> */}
      </header>

      {/* OTP flow removed */}

      {/* Main Content - Responsive grid */}
      <div className="flex flex-col md:flex-row gap-6 px-4 md:px-0">
        {/* Image Gallery */}
        <div className="bg-[#FFFBF6] w-full md:w-2/5 h-[280px] md:h-[350px] rounded-md overflow-hidden">
          {Object.keys(getValues("listData.bookImages"))?.some(
            (key) => getValues("listData.bookImages")?.[key]?.length
          ) ? (
            <Slider {...settings} className="h-full">
              {Object.keys(getValues("listData.bookImages")).map(
                (objkey, idx) => {
                  if (getValues("listData.bookImages")[objkey]) {
                    return (
                      <div key={objkey} className="h-full flex items-center">
                        <img
                          src={getValues("listData.bookImages")[objkey]}
                          alt={`${objkey} image`}
                          className="object-contain w-full h-[250px] md:h-[320px]"
                        />
                      </div>
                    );
                  }
                }
              )}
            </Slider>
          ) : (
            <div className="flex items-center justify-center h-full">
              <p>No image selected</p>
            </div>
          )}
        </div>

        {/* Details Section */}
        <div className="w-full md:w-3/5">
          <div className="border-b border-gray-200 pb-4 mb-4 mt-1">
            <h1 className="text-xl md:text-2xl font-bold capitalize break-words">
              {getValues("listData.title")}
            </h1>
            <p className="text-gray-600 mt-1 md:mt-2">{returnIfPresent()}</p>
          </div>

          <div className="space-y ">
            <p className="font-semibold mt-2">
              Seller:{" "}
              <span
                // title={userList.listData?.bookAuthor}
                className=" font-normal break-words "
              >
                {userDataFromLocal().firstName}
              </span>
            </p>

            <ItemRespectiveDetails userList={userList} />

            <p className=" font-semibold line-clamp-2 mt-1">
              Location:{" "}
              <span className=" font-normal break-words">
                {userList?.listData?.address.formatted_address || ""}
              </span>
            </p>

            {getValues("listData.tags") && (
              <div className="mt-0 flex w-full mt-2 items-center">
                <span className="font-semibold mr-2">Tags:</span>
                <div className="w-full flex gap-1 mt-1">
                  {renderTags(getValues("listData.tags"))}
                </div>
              </div>
            )}
          </div>

          <p className="font-semibold w-fit px-6 py-3 text-amber-50 text-lg rounded-full global_linear_gradient md:text-3xl mt-4 md:mt-6">
            J$ {getValues("listData.price")}
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="px-4 md:px-0">
        <div className="flex flex-col-reverse sm:flex-row justify-end items-center gap-4 mt-10 md:mt-16">
          <button
            className="bg-white text-[#0a2e5c] border-2 border-[#0a2e5c] rounded-full px-6 py-2 w-full sm:w-auto font-semibold hover:bg-gray-50"
            onClick={() => setShowDiscardModal(true)}
          >
            Discard
          </button>

          <button
            className="global_linear_gradient text-white rounded-full px-8 py-3 w-full sm:w-auto font-semibold shadow-md hover:opacity-95"
            onClick={onSubmit}
          >
            Publish
          </button>
        </div>
      </div>

      {/* Mobile Action Buttons (Fixed) */}
      <div className="fixed bottom-0 left-0 right-0 bg-white p-4 shadow-lg flex justify-center gap-4 md:hidden z-50">
        <button
          className="bg-white text-[#0a2e5c] border-2 border-[#0a2e5c] rounded-full px-6 py-2 font-semibold flex-1 max-w-[150px]"
          onClick={() => setShowDiscardModal(true)}
        >
          Discard
        </button>
        <button
          className="global_linear_gradient text-white rounded-full px-6 py-2 font-semibold flex-1 max-w-[150px]"
          onClick={onSubmit}
        >
          Publish
        </button>
      </div>

      {/* Discard Modal */}
      {showDiscardModal &&
        createPortal(
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-sm mx-4">
              <h3 className="text-lg font-semibold mb-4">Confirm Discard</h3>
              <p className="mb-6">
                Are you sure you want to discard your changes?
              </p>
              <div className="flex justify-end gap-3">
                <button
                  className="px-4 py-2 rounded bg-gray-200 hover:bg-gray-300"
                  onClick={() => setShowDiscardModal(false)}
                >
                  Cancel
                </button>
                <button
                  className="px-4 py-2 rounded bg-red-500 text-white hover:bg-red-600"
                  onClick={() => {
                    router.replace("/");
                    dispatch(resetListingData());
                    setShowDiscardModal(false);
                  }}
                >
                  Confirm
                </button>
              </div>
            </div>
          </div>,
          document.body
        )}
    </section>
  );
}
