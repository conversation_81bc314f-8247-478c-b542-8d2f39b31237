"use client"

import { getToken } from '@/app/utils/utils'
import { useRouter } from 'next/navigation';
import React from 'react'

function sellerButton() {
    const auth = getToken();
    console.log("auth", auth)
    const router = useRouter()

    const redirectionHandler = () => {
        if (auth) router.push("become-seller")
        else router.push("/login")
    }

    return (
        <button
            className='hidden md:block md:mt-[46px] text-white rounded-full py-6 px-[70px] global_linear_gradient md:text-xl lg:text-[25px] font-semibold lg:leading-8'
            onClick={redirectionHandler}
        >
            Start Selling
        </button>
    )
}

export default sellerButton
