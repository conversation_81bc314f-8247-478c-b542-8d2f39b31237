.Selectbutton {
  display: flex;
  width: 124.324px;
  height: 36.352px;
  padding: 6.01px 22.8363px;
  justify-content: center;
  align-items: center;
  gap: 6.01px;
  flex-shrink: 0;
  border-radius: 72.646px;
  border: 0.727px solid #000;
  color: #000;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 13px;
  margin: auto;
  cursor: pointer;

  &:focus {
    border-radius: 72.646px;
    background: var(
      --Linear,
      linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%)
    );
    color: #fff;
    border: none;
  }

  @media (min-width: 769px) {
    border-radius: 108.602px;
    border: 1.087px solid #000;
    display: inline-flex;
    height: 49.345px;
    justify-content: center;
    align-items: center;
    gap: 8.984px;
    flex-shrink: 0;
    width: 170px;
    font-size: 17px;
  }
}

.SelectActivebutton {
  display: flex;
  width: 124.324px;
  height: 36.352px;
  padding: 6.01px 22.8363px;
  justify-content: center;
  align-items: center;
  gap: 6.01px;
  flex-shrink: 0;
  border-radius: 72.646px;
  border: 0.727px solid #000;
  // color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 13px;
  margin: auto;
  cursor: pointer;

  border-radius: 72.646px;
  background: var(
    --Linear,
    linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%)
  );
  color: #fff;
  border: none;

  @media (min-width: 769px) {
    border-radius: 108.602px;
    border: 1.087px solid #000;
    display: inline-flex;
    height: 49.345px;
    justify-content: center;
    align-items: center;
    gap: 8.984px;
    flex-shrink: 0;
    width: 170px;
    font-size: 17px;
  }
}

.justify_start {
  justify-content: start !important;
}

.selectContainer {
  display: flex;
  width: 100%;
  height: 45.275px;
  padding: 6px 9px 7.275px 11.953px;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  border-radius: 5.617px;
  border: 0.702px solid #ccc;
  background: #fff;
  box-shadow: 0px 1.342px 5.369px -4.027px rgba(0, 0, 0, 0.4);
}

.inputContainer {
  display: flex;
  height: 100px;
  flex-direction: column;
  align-items: flex-start;
  gap: 16.303px;
  align-self: stretch;

  .inputBox {
    display: flex;
    width: 100%;
    height: 45.2 75px;
    padding: 6px 9px 7.275px 11.953px;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    border-radius: 5.617px;
    border: 0.702px solid #ccc;
    background: #fff;
    font-size: 20px;
    box-shadow: 0px 1.342px 5.369px -4.027px rgba(0, 0, 0, 0.4);

    input {
      width: 100%;
    }

    button {
      display: flex;
      width: 79px;
      height: 25px;
      padding: 5.363px 23.606px 7.794px 24.73px;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      border-radius: 11.633px;
      background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
      color: #fff;
      font-size: 12px;
      cursor: pointer;
    }

    option {
      width: 100%;
    }
  }

  .inputBox input::placeholder {
    font-size: 1.4rem;
    line-height: 1.9rem;
    //   color: #999;
  }

  .suggestionList {
    top: 108px;
    z-index: 3;
    max-height: 194px;
    // padding: 20px;
    border-radius: 15px;
    background: #e8e7e5;
    position: absolute;
    width: 100%;
    text-transform: capitalize;
    overflow: scroll;

    .suggestionList_data {
      padding: 8px 20px;
      margin: 5px 0;
      border-bottom: 1px solid #0000004a;
      display: flex;
      justify-content: space-between;
      align-items: center;
      cursor: pointer;

      &:hover {
        background-color: #ebf0f4;
        border-radius: 9px;
      }
    }
  }
}

.itemContainer {
  .buttonPrice {
    display: flex;
    width: fit-content;
    height: 49px;
    padding: 13.129px 35.112px 13.437px 35px;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 26.513px;
    background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
    color: #fff;
    font-weight: 500;
    cursor: pointer;
  }
}

.submit {
  background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
  border-radius: 93.728px;
  flex-shrink: 0;
  width: 100;
  height: 53.013px;
  color: #fff;
  cursor: pointer;
  margin-top: 10px;
}

.uploadImageContainer {
  margin: 30px auto;
  width: 100%;

  .uploadButton {
    display: inline-flex;
    height: 40.688px;
    padding: 0px 21.573px 0px 20.273px;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 22.04px;
    background: #d8232a;
    color: #fff;
    font-weight: 500;
    cursor: pointer;
    font-size: 16px;
  }
}

.itemContainer {
  .itemDescription {
    .buttonPrice {
      display: flex;
      width: 140px;
      height: 49px;
      padding: 13.129px 35.112px 13.437px 35px;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      border-radius: 26.513px;
      background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
    }
  }
}

.reviewContainer {
  .inputBox {
    display: flex;
    height: 36.275px;
    align-items: center;
    flex-shrink: 0;
    border-radius: 5.737px;
    border: 0.447px solid #909090;
    background: #fff;
    box-shadow: 0px 1.342px 5.369px -4.027px rgba(0, 0, 0, 0.4);
    margin: 10px 0;
  }

  .sendOtp {
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 5px;
    background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
    color: #fff;
    height: 36.275px;
    padding: 1px 25px;
    font-size: 12px;
    cursor: pointer;
    // width: 119px;
  }

  .chatNowButton {
    display: flex;
    justify-content: center;
    align-items: center;
    align-self: stretch;
    border-radius: 75.963px;
    background: #b2b2b2;
    color: #fff;
    width: 112px;
    height: 33px;
    font-size: 12px;
    margin-top: 6px;
  }

  .publish {
    background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
    border-radius: 67px;
    flex-shrink: 0;
    justify-content: center;
    align-items: center;
    gap: 2px;
    height: 48px;
    padding: 4px 8px;
    display: inline-flex;
    width: 500px;
    color: #fff;
  }
  .disgard {
    border: 1px solid;
    background: white;
    border-radius: 67px;
    flex-shrink: 0;
    justify-content: center;
    align-items: center;
    gap: 2px;
    height: 48px;
    padding: 4px 8px;
    display: inline-flex;
    width: 500px;
    color: black;
  }
}

.celebration {
  button {
    margin-top: 10px;
    width: 174.257px;
    height: 49.965px;
    flex-shrink: 0;
    border-radius: 93.728px;
    border: 3px solid #000;
    color: #000;
    cursor: pointer;
    font-size: 15px;
  }

  .active {
    border: none;
    border-radius: 93.728px;
    background: var(
      --Linear,
      linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%)
    );
    color: #fff;
  }
}

@media (min-width: 769px) {
  .inputContainer {
    height: 130px;

    .selectBox-Condition {
      height: 70px !important;
    }

    .inputBox {
      display: flex;
      // width: 704.273px;
      width: 100% !important;
      height: 68.727px;
      // padding: 14.555px 20.763px 13.484px 22.639px;
      justify-content: space-between;
      align-items: center;
      flex-shrink: 0;
      border-radius: 10.868px 10.868px 10.87px 10.87px;
      border: 0.848px solid #909090;
      background: #fff;
      box-shadow: 0px 2.543px 10.172px -7.629px rgba(0, 0, 0, 0.4);

      button {
        display: flex;
        width: 128.846px;
        height: 40.688px;
        padding: 10.172px 35.261px 11.868px 35.49px;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        border-radius: 22.04px;
        background: var(
          --Linear,
          linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%)
        );
      }
    }
  }

  .selectBox-Condition {
    height: 70px !important;
  }

  .submit {
    width: 285.05px;
    height: 69.013px;
    flex-shrink: 0;
  }

  .uploadImageContainer {
    // margin-left: 3px;
    // width: 93%;
    // width: 704.273px;
    width: 100%;

    margin: 0;

    button {
      font-size: 15px;
    }
  }

  .selectContainer {
    display: flex;
    // width: 704.273px;
    width: 100%;
    height: 68.727px;
    // padding: 14.555px 20.763px 13.484px 22.639px;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    border-radius: 10.868px 10.868px 10.87px 10.87px;
    border: 0.848px solid #909090;
    background: #fff;
    box-shadow: 0px 2.543px 10.172px -7.629px rgba(0, 0, 0, 0.4);
  }

  .chatNowButton {
    display: inline-flex;
    height: 55.726px;
    padding: 12.383px 51.438px 12.343px 52.629px;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 160.985px;
    background: #b2b2b2;
  }

  .reviewContainer {
    .inputBox {
      height: 60px;
    }

    .sendOtp {
      height: 60px;
      padding: 1px 16px;
      font-size: 16px;
    }

    .chatNowButton {
      width: 190px;
      height: 48px;
      margin-top: 6px;
      font-size: 16px;
    }

    .publish {
      height: 62px;
      // width: 40%;
      width: 140px;
      font-size: 20px;
    }
  }

  .celebration {
    button {
      width: 285.05px;
      height: 79.013px;
      flex-shrink: 0;
      border-radius: 93.728px;
      border: 3px solid #000;
      color: #000;
      cursor: pointer;
      font-size: 23px;
    }

    .active {
      border: none;
      border-radius: 93.728px;
      background: var(
        --Linear,
        linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%)
      );
      color: #fff;
    }
  }
}

@media (max-width: 997px) {
  .inputContainer {
    height: 130px;

    .inputBox {
      display: flex;
      // width: 704.273px;
      width: 100% !important;
      height: 68.727px;
      // padding: 14.555px 20.763px 13.484px 22.639px;
      justify-content: space-between;
      align-items: center;
      flex-shrink: 0;
      border-radius: 10.868px 10.868px 10.87px 10.87px;
      border: 0.848px solid #909090;
      background: #fff;
      box-shadow: 0px 2.543px 10.172px -7.629px rgba(0, 0, 0, 0.4);

      button {
        display: flex;
        width: 90.846px;
        height: 30.688px;
        padding: 10.172px 35.261px 11.868px 35.49px;
        justify-content: center;
        align-items: center;
        flex-shrink: 0;
        border-radius: 22.04px;
        background: var(
          --Linear,
          linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%)
        );
      }
    }
  }

  .submit {
    width: 285.05px;
    height: 69.013px;
    flex-shrink: 0;
  }

  .uploadImageContainer {
    // margin-left: 3px;
    // width: 93%;
    // width: 704.273px;
    width: 100%;

    margin: 0;

    button {
      font-size: 15px;
    }
  }

  .selectContainer {
    display: flex;
    // width: 704.273px;
    width: 100%;
    height: 68.727px;
    // padding: 14.555px 20.763px 13.484px 22.639px;
    justify-content: space-between;
    align-items: center;
    flex-shrink: 0;
    border-radius: 10.868px 10.868px 10.87px 10.87px;
    border: 0.848px solid #909090;
    background: #fff;
    box-shadow: 0px 2.543px 10.172px -7.629px rgba(0, 0, 0, 0.4);
  }

  .chatNowButton {
    display: inline-flex;
    height: 55.726px;
    padding: 12.383px 51.438px 12.343px 52.629px;
    justify-content: center;
    align-items: center;
    flex-shrink: 0;
    border-radius: 160.985px;
    background: #b2b2b2;
    width: 150px;
  }

  .reviewContainer {
    .inputBox {
      height: 60px;
    }

    .sendOtp {
      width: 372px;
      height: 60px;
      padding: 1px 16px;
      font-size: 16px;
    }

    .chatNowButton {
      width: 190px;
      height: 48px;
      margin-top: 6px;
      font-size: 16px;
    }

    .publish {
      height: 50px;
      // width: 40%;
      width: 130px;
      font-size: 20px;
    }
  }

  .celebration {
    button {
      width: 285.05px;
      height: 79.013px;
      flex-shrink: 0;
      border-radius: 93.728px;
      border: 3px solid #000;
      color: #000;
      cursor: pointer;
      font-size: 23px;
    }

    .active {
      border: none;
      border-radius: 93.728px;
      background: var(
        --Linear,
        linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%)
      );
      color: #fff;
    }
  }
}

.boxshadow {
  box-shadow: 5px 5px 15px black;
}

.react-datepicker-wrapper {
  padding-left: 0px;
  text-align: start;
}
