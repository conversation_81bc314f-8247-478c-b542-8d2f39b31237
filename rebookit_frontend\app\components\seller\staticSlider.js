"use client";

import React, { useEffect, useRef, useState } from 'react';
import { useElementWidth } from "../common/WidthChecker";
import dynamic from 'next/dynamic';
import "./staticSlider.scss";

const Slider = dynamic(() => import("../../components/common/Slider"), {
    ssr: false,
    loading: () => <div>Loading slider...</div>
});


function ImageSlider(props) {
    const { imagesList = [] } = props;
    const [ref, width] = useElementWidth();

    const sliderRef = useRef(null);

    const [isDesktop, setIsDesktop] = useState(false);

    useEffect(() => {
        setIsDesktop(width >= 768);
    }, [width]);

    const settings = {
        dots: width < 720 ? true : false,
        infinite: true,
        speed: 500,
        slidesToShow: width < 720 ? 1 : 3,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 3000,
        className: "center",
        centerMode: true,
        // centerPadding: width < 720 ? "0px" : "60px",
        centerPadding: "0px",
        // focusOnSelect: true,
        // initialSlide: 1,

        afterChange: (current) => {
            // This ensures the next slide gets the slider-slick-center class
            document.querySelector('.slick-current + .slick-active')?.classList.add('slider-slick-center');
            document.querySelector('.slick-current')?.classList.remove('slider-slick-center');
        },

        responsive: [
            {
                breakpoint: 720,
                settings: {
                    slidesToShow: 1,
                    centerMode: false,
                    initialSlide: 0,
                }
            }
        ],

        appendDots: dots => {
            const activeIndex = dots.findIndex(dot => dot.props.className.includes('slick-active'));
            let visibleDots = [];

            if (dots.length <= 3) {
                visibleDots = dots;
            } else {
                const start = Math.max(0, Math.min(activeIndex - 1, dots.length - 3));
                visibleDots = dots.slice(start, start + 3);
            }

            return (
                <div className="mt-4 w-full">
                    <ul className="flex justify-center gap-5">{visibleDots}</ul>
                </div>
            );
        },
        customPaging: i => (
            <div className="w-[40px] h-2 bg-gray-500 rounded-full opacity-60 hover:opacity-100 transition" />
        ),
    };


    // Initialize center slide on mount
    // useEffect(() => {
    //     if (sliderRef.current && width >= 768) {
    //         setTimeout(() => {
    //             const sliderParent = document.querySelector('.static-slider');
    //             const slides = sliderParent.querySelectorAll('.slick-slide');
    //             slides[3].classList.remove("slick-current", "slider-slick-center")
    //             slides[4]?.classList.add('slick-current', 'slider-slick-center');
    //         }, 100);
    //     }
    // }, [width]);


    // Initialize center slide on mount
    useEffect(() => {
        if (!isDesktop || !sliderRef.current) return;

        const sliderParent = document.querySelector('.static-slider');
        console.log("sliderParent", sliderParent)
        const slides = sliderParent.querySelectorAll('.slick-slide');

        if (slides.length > 4) {
            slides[3]?.classList.remove("slick-current", "slider-slick-center");
            slides[4]?.classList.add('slick-current', 'slider-slick-center');
        }
    }, [isDesktop]);

    const whoCanSellComponents = [
        (
            <div className=" border border-black py-[58px] px-[56px] rounded-[20px] flex flex-col justify-center items-center h-full md:h-[410px] slick-custom-item bg-[#211F54] text-white md:bg-white md:text-black slider-svg-color-change">
                <svg xmlns="http://www.w3.org/2000/svg" className='md:w-[70px] md:h-[70px]' width="99" height="99" viewBox="0 0 71 71" fill="none" >
                    <path d="M20.711 41.3861C17.99 38.477 13.311 39.5949 12.0182 43.1818C11.5 42.6123 10.1704 41.147 10.133 41.1095C9.83756 40.8141 9.67474 40.4211 9.67474 40.0031V23.4475C9.67474 20.7802 7.50469 18.6102 4.83737 18.6102C2.17005 18.6102 0 20.7802 0 23.4475V46.9014C0 48.3785 0.575318 49.7671 1.6196 50.8116C1.65986 50.8519 1.08372 50.322 10.2867 58.7501C10.8 59.2734 11.0823 59.964 11.0823 60.6978V68.9759C11.0823 69.7346 11.6976 70.3499 12.4564 70.3499H28.9673C29.7261 70.3499 30.3414 69.7346 30.3414 68.9759V52.2239C30.3414 51.8753 30.2089 51.5397 29.9708 51.2852L20.711 41.3861ZM27.5933 67.6018H13.8304V60.698C13.8304 59.221 13.2554 57.8324 12.2108 56.7879C12.1649 56.742 12.4877 57.0403 3.54369 48.8492C3.03048 48.326 2.74811 47.6355 2.74811 46.9015V23.4475C2.74811 22.2955 3.68536 21.3582 4.83737 21.3582C5.98938 21.3582 6.92648 22.2955 6.92648 23.4475V40.0031C6.92648 41.1334 7.35918 42.1967 8.14472 43.0057C9.41229 44.4005 15.8107 51.4413 17.0856 52.8442C17.5969 53.4066 18.4658 53.4466 19.0266 52.937C19.5881 52.4267 19.6298 51.5577 19.1193 50.996L14.9416 46.3987C14.1592 45.348 14.338 43.8443 15.3934 43.0124C16.4027 42.2167 17.8259 42.3244 18.7041 43.2632L27.5933 52.7662V67.6018Z" fill="url(#paint0_linear_665_31788)" />
                    <path d="M65.5118 18.6101C62.8445 18.6101 60.6746 20.7801 60.6746 23.4474V40.0031C60.6746 40.4211 60.5119 40.8139 60.2164 41.1095C60.1809 41.1449 58.683 42.7949 58.3311 43.1818C57.0384 39.5951 52.3595 38.4771 49.6383 41.3861L40.3784 51.2852C40.1403 51.5397 40.0078 51.8752 40.0078 52.2238V68.9758C40.0078 69.7346 40.6231 70.3499 41.3819 70.3499H57.8928C58.6516 70.3499 59.2669 69.7346 59.2669 68.9758V60.6979C59.2669 59.964 59.5492 59.2734 60.0625 58.7502C69.1639 50.4151 68.6891 50.8522 68.7296 50.8117C69.774 49.7671 70.3492 48.3785 70.3492 46.9014V23.4474C70.3492 20.7801 68.1791 18.6101 65.5118 18.6101ZM67.6011 46.9014C67.6011 47.6355 67.3187 48.3259 66.8055 48.8492C57.8223 57.0759 58.1822 56.744 58.1384 56.7878C57.0938 57.8323 56.5188 59.221 56.5188 60.6979V67.6018H42.7559V52.7663L51.6451 43.2633C52.5231 42.3249 53.9461 42.2169 54.9558 43.0125C56.0098 43.8431 56.1912 45.3465 55.4076 46.3988L51.2297 50.9961C50.7193 51.5578 50.7609 52.4267 51.3225 52.9371C51.8839 53.4472 52.753 53.4062 53.2635 52.8443C54.5565 51.4215 60.9446 44.3919 62.2043 43.0058C62.9899 42.1967 63.4226 41.1334 63.4226 40.0032V23.4474C63.4226 22.2954 64.3598 21.3582 65.5118 21.3582C66.6638 21.3582 67.6011 22.2956 67.6011 23.4474V46.9014Z" fill="url(#paint1_linear_665_31788)" />
                    <path d="M46.6262 12.6407H43.4529C42.6942 12.6407 42.0789 13.256 42.0789 14.0148V15.3945C42.0789 18.7309 39.6997 21.5216 36.549 22.1605V15.3888H38.0262C38.785 15.3888 39.4003 14.7735 39.4003 14.0148C39.4003 13.256 38.785 12.6407 38.0262 12.6407H32.4616C31.7028 12.6407 31.0875 13.256 31.0875 14.0148C31.0875 14.7735 31.7028 15.3888 32.4616 15.3888H33.8009V22.1605C30.6502 21.5217 28.271 18.7309 28.271 15.3945V14.0148C28.271 13.256 27.6557 12.6407 26.8969 12.6407H23.7237C22.9649 12.6407 22.3496 13.256 22.3496 14.0148C22.3496 14.7735 22.9649 15.3888 23.7237 15.3888H25.5229V15.3945C25.5229 20.2499 29.1274 24.2776 33.8009 24.9468V27.8169H32.4616C31.7028 27.8169 31.0875 28.432 31.0875 29.1909C31.0875 29.9497 31.7028 30.565 32.4616 30.565H38.0262C38.785 30.565 39.4003 29.9497 39.4003 29.1909C39.4003 28.432 38.785 27.8169 38.0262 27.8169H36.549V24.9468C41.2224 24.2776 44.827 20.2498 44.827 15.3945L46.6262 15.3888C47.3849 15.3888 48.0002 14.7735 48.0002 14.0148C48.0002 13.2559 47.3849 12.6407 46.6262 12.6407Z" fill="url(#paint2_linear_665_31788)" />
                    <path d="M54.8389 19.6616C54.8389 8.81964 46.0183 -0.000732422 35.1763 -0.000732422C24.3343 -0.000732422 15.5137 8.81964 15.5137 19.6616C15.5137 30.5035 24.3342 39.324 35.1763 39.324C46.0184 39.324 54.8389 30.5035 54.8389 19.6616ZM35.1763 36.5758C25.8496 36.5758 18.2618 28.9881 18.2618 19.6615C18.2618 10.3351 25.8496 2.74735 35.1763 2.74735C44.503 2.74735 52.0908 10.3351 52.0908 19.6616C52.0908 28.9882 44.503 36.5758 35.1763 36.5758Z" fill="url(#paint3_linear_665_31788)" />
                    <defs>
                        <linearGradient id="paint0_linear_665_31788" x1="62.4993" y1="6.0873" x2="-0.823707" y2="7.99446" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#211F54" />
                            <stop offset="1" stopColor="#0161AB" />
                        </linearGradient>
                        <linearGradient id="paint1_linear_665_31788" x1="62.4993" y1="6.0873" x2="-0.823707" y2="7.99446" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#211F54" />
                            <stop offset="1" stopColor="#0161AB" />
                        </linearGradient>
                        <linearGradient id="paint2_linear_665_31788" x1="62.4993" y1="6.0873" x2="-0.823707" y2="7.99446" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#211F54" />
                            <stop offset="1" stopColor="#0161AB" />
                        </linearGradient>
                        <linearGradient id="paint3_linear_665_31788" x1="62.4993" y1="6.0873" x2="-0.823707" y2="7.99446" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#211F54" />
                            <stop offset="1" stopColor="#0161AB" />
                        </linearGradient>
                    </defs>
                </svg>

                <h3 className='mt-6 text-lg leading-[22px] font-medium text-center '>Parents & Students with unused textbooks</h3>

                <p className='mt-3 text-center text-sm font-light leading-5'>Share your love for literature by passing on duplicate or less-loved books to fellow collectors who truly value rare and meaningful finds.</p>
            </div>
        ),
        (
            <div className=" border border-black py-[58px] px-[56px] rounded-[20px] flex flex-col justify-center items-center h-full md:h-[410px] slick-custom-item bg-[#211F54] text-white md:bg-white md:text-black slider-svg-color-change">
                <svg xmlns="http://www.w3.org/2000/svg" className='md:w-[70px] md:h-[70px]' width="99" height="99" viewBox="0 0 125 125" fill="none">
                    <path d="M111.295 80.4214H105.684C104.342 80.4214 103.194 81.5935 103.194 82.9354V85.375C103.194 91.2748 99.0636 96.2097 93.4753 97.3395V85.2808H96.0885C97.4301 85.2808 98.5182 84.1927 98.5182 82.8511C98.5182 81.5094 97.4301 80.4214 96.0885 80.4214H86.2487C84.907 80.4214 83.819 81.5094 83.819 82.8511C83.819 84.1927 84.907 85.2808 86.2487 85.2808H88.616V97.3393C83.0277 96.2095 78.8972 91.2746 78.8972 85.3748V82.9351C78.8972 81.5932 77.7506 80.4211 76.409 80.4211H70.7979C69.4562 80.4211 68.3682 81.5092 68.3682 82.8508C68.3682 84.1925 69.4562 85.2805 70.7979 85.2805H74.0378V85.3748C74.0378 93.9608 80.355 101.083 88.616 102.266V107.391H86.2487C84.907 107.391 83.819 108.479 83.819 109.821C83.819 111.162 84.907 112.25 86.2487 112.25H96.0885C97.4301 112.25 98.5182 111.162 98.5182 109.821C98.5182 108.479 97.4301 107.391 96.0885 107.391H93.4753V102.266C101.736 101.083 108.113 93.9608 108.113 85.3748L111.295 85.3648C112.637 85.3648 113.725 84.2348 113.725 82.8931C113.725 81.5514 112.637 80.4214 111.295 80.4214Z" fill="url(#paint0_linear_1727_10876)" />
                    <path d="M48.1448 49.5036C46.7135 50.9345 44.3856 50.9345 42.9555 49.5036C42.0065 48.5551 40.4685 48.5551 39.5192 49.5036C38.5704 50.4524 38.5704 51.9909 39.5192 52.94C42.844 56.265 48.2549 56.2657 51.5807 52.94C52.5297 51.9912 52.5297 50.4529 51.5809 49.5039C50.6324 48.5553 49.0936 48.5551 48.1448 49.5036Z" fill="url(#paint1_linear_1727_10876)" />
                    <path d="M37.0107 41.4524C38.3526 41.4524 39.4404 40.3646 39.4404 39.0227C39.4404 37.6808 38.3526 36.593 37.0107 36.593C35.6689 36.593 34.5811 37.6808 34.5811 39.0227C34.5811 40.3646 35.6689 41.4524 37.0107 41.4524Z" fill="url(#paint2_linear_1727_10876)" />
                    <path d="M54.0889 41.4524C55.4307 41.4524 56.5186 40.3646 56.5186 39.0227C56.5186 37.6808 55.4307 36.593 54.0889 36.593C52.747 36.593 51.6592 37.6808 51.6592 39.0227C51.6592 40.3646 52.747 41.4524 54.0889 41.4524Z" fill="url(#paint3_linear_1727_10876)" />
                    <path d="M91.0183 56.8421C86.5764 56.8421 82.3014 57.7056 78.4139 59.2708V32.924C78.4139 14.6085 63.6343 -0.000732422 45.505 -0.000732422C27.3503 -0.000732422 12.5693 14.7691 12.5693 32.924V80.7091C5.0373 86.0301 0.420898 94.6504 0.420898 104.038V121.97C0.420898 123.311 1.51572 124.399 2.85739 124.399H91.0492C109.675 124.399 124.812 109.246 124.812 90.6208C124.812 71.9953 109.644 56.8421 91.0183 56.8421ZM45.5186 4.85864C60.9386 4.85864 73.5542 17.3169 73.5542 32.924V61.6985C68.2089 65.0192 63.6567 69.9743 60.7865 75.5625H55.3316V66.1344C63.1066 62.4469 68.6949 54.4155 68.6949 45.122V29.2646C68.6949 27.9229 67.6178 26.8349 66.2761 26.8349C60.6526 26.8349 55.2351 24.7901 51.0121 21.0773L47.1518 17.6813C46.2305 16.8715 44.8514 16.8751 43.9349 17.6903L40.2199 20.9386C35.9818 24.7058 30.5259 26.7258 24.8558 26.7258H24.8137C23.4721 26.7258 22.2881 27.9229 22.2881 29.2646V45.122C22.2881 54.4155 27.8764 62.4469 35.8943 66.1344V75.5625H28.839C24.9046 75.5625 21.073 76.4387 17.4285 77.9917V32.924C17.4287 17.4486 30.0434 4.85864 45.5186 4.85864ZM45.4914 63.4289C35.397 63.4289 27.1475 55.2163 27.1475 45.1217V31.5925C33.2217 31.081 38.8702 28.6518 43.4009 24.6246L45.5353 22.7487L47.7862 24.7264C52.2852 28.682 58.0042 31.073 63.8355 31.5886V45.1215C63.8357 55.2166 55.586 63.4289 45.4914 63.4289ZM40.7537 67.7676C42.2115 68.1078 43.9361 68.2886 45.6128 68.2886C47.2898 68.2886 48.7717 68.1078 50.4725 67.7676V77.178C48.0428 79.988 43.1834 79.9914 40.7537 77.178V67.7676ZM19.8584 119.54V109.772C19.8584 108.43 18.7704 107.342 17.4287 107.342C16.087 107.342 14.999 108.43 14.999 109.772V119.54H5.28027V104.038C5.28027 90.6878 16.1803 80.4219 28.8393 80.4219H37.012C37.0419 80.4219 37.0708 80.4508 37.1004 80.4499C41.6663 85.3515 49.4107 85.3688 54.0008 80.4418C54.0305 80.4428 54.0594 80.4222 54.0893 80.4222H58.8245C57.8157 83.5808 57.2705 87.0593 57.2705 90.5887C57.2705 102.838 63.825 113.617 73.6109 119.54H19.8584V119.54ZM91.049 119.54C75.1029 119.54 62.1299 106.567 62.1299 90.6208C62.1299 74.675 75.1032 61.7017 91.049 61.7017C106.995 61.7017 119.968 74.675 119.968 90.6208C119.968 106.567 106.995 119.54 91.049 119.54Z" fill="url(#paint4_linear_1727_10876)" />
                    <defs>
                        <linearGradient id="paint0_linear_1727_10876" x1="108.664" y1="83.1756" x2="67.8752" y2="84.9262" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#211F54" />
                            <stop offset="1" stopColor="#0161AB" />
                        </linearGradient>
                        <linearGradient id="paint1_linear_1727_10876" x1="50.7878" y1="49.367" x2="38.684" y2="50.1072" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#211F54" />
                            <stop offset="1" stopColor="#0161AB" />
                        </linearGradient>
                        <linearGradient id="paint2_linear_1727_10876" x1="38.8982" y1="37.0135" x2="34.5242" y2="37.1453" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#211F54" />
                            <stop offset="1" stopColor="#0161AB" />
                        </linearGradient>
                        <linearGradient id="paint3_linear_1727_10876" x1="55.9763" y1="37.0135" x2="51.6023" y2="37.1453" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#211F54" />
                            <stop offset="1" stopColor="#0161AB" />
                        </linearGradient>
                        <linearGradient id="paint4_linear_1727_10876" x1="110.932" y1="10.7646" x2="-1.03559" y2="14.1367" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#211F54" />
                            <stop offset="1" stopColor="#0161AB" />
                        </linearGradient>
                    </defs>
                </svg>

                <h3 className='mt-6 text-lg leading-[22px] font-medium text-center '>Independent sellers & small bookstores</h3>

                <p className='mt-3 text-center text-sm font-light leading-5'>Expand your reach by listing unsold inventory or second-hand books on ReBookIt—connect with more readers and grow your bookstore digitally.</p>
            </div>
        ),
        (
            <div className=" border border-black py-[58px] px-[56px] rounded-[20px] flex flex-col justify-center items-center h-full  md:h-[410px] slick-custom-item bg-[#211F54] text-white md:bg-white md:text-black slider-svg-color-change">
                <svg xmlns="http://www.w3.org/2000/svg" className='md:w-[70px] md:h-[70px]' width="99" height="99" viewBox="0 0 71 71" fill="none">
                    <path d="M20.711 41.3861C17.99 38.477 13.311 39.5949 12.0182 43.1818C11.5 42.6123 10.1704 41.147 10.133 41.1095C9.83756 40.8141 9.67474 40.4211 9.67474 40.0031V23.4475C9.67474 20.7802 7.50469 18.6102 4.83737 18.6102C2.17005 18.6102 0 20.7802 0 23.4475V46.9014C0 48.3785 0.575318 49.7671 1.6196 50.8116C1.65986 50.8519 1.08372 50.322 10.2867 58.7501C10.8 59.2734 11.0823 59.964 11.0823 60.6978V68.9759C11.0823 69.7346 11.6976 70.3499 12.4564 70.3499H28.9673C29.7261 70.3499 30.3414 69.7346 30.3414 68.9759V52.2239C30.3414 51.8753 30.2089 51.5397 29.9708 51.2852L20.711 41.3861ZM27.5933 67.6018H13.8304V60.698C13.8304 59.221 13.2554 57.8324 12.2108 56.7879C12.1649 56.742 12.4877 57.0403 3.54369 48.8492C3.03048 48.326 2.74811 47.6355 2.74811 46.9015V23.4475C2.74811 22.2955 3.68536 21.3582 4.83737 21.3582C5.98938 21.3582 6.92648 22.2955 6.92648 23.4475V40.0031C6.92648 41.1334 7.35918 42.1967 8.14472 43.0057C9.41229 44.4005 15.8107 51.4413 17.0856 52.8442C17.5969 53.4066 18.4658 53.4466 19.0266 52.937C19.5881 52.4267 19.6298 51.5577 19.1193 50.996L14.9416 46.3987C14.1592 45.348 14.338 43.8443 15.3934 43.0124C16.4027 42.2167 17.8259 42.3244 18.7041 43.2632L27.5933 52.7662V67.6018Z" fill="url(#paint0_linear_665_31788)" />
                    <path d="M65.5118 18.6101C62.8445 18.6101 60.6746 20.7801 60.6746 23.4474V40.0031C60.6746 40.4211 60.5119 40.8139 60.2164 41.1095C60.1809 41.1449 58.683 42.7949 58.3311 43.1818C57.0384 39.5951 52.3595 38.4771 49.6383 41.3861L40.3784 51.2852C40.1403 51.5397 40.0078 51.8752 40.0078 52.2238V68.9758C40.0078 69.7346 40.6231 70.3499 41.3819 70.3499H57.8928C58.6516 70.3499 59.2669 69.7346 59.2669 68.9758V60.6979C59.2669 59.964 59.5492 59.2734 60.0625 58.7502C69.1639 50.4151 68.6891 50.8522 68.7296 50.8117C69.774 49.7671 70.3492 48.3785 70.3492 46.9014V23.4474C70.3492 20.7801 68.1791 18.6101 65.5118 18.6101ZM67.6011 46.9014C67.6011 47.6355 67.3187 48.3259 66.8055 48.8492C57.8223 57.0759 58.1822 56.744 58.1384 56.7878C57.0938 57.8323 56.5188 59.221 56.5188 60.6979V67.6018H42.7559V52.7663L51.6451 43.2633C52.5231 42.3249 53.9461 42.2169 54.9558 43.0125C56.0098 43.8431 56.1912 45.3465 55.4076 46.3988L51.2297 50.9961C50.7193 51.5578 50.7609 52.4267 51.3225 52.9371C51.8839 53.4472 52.753 53.4062 53.2635 52.8443C54.5565 51.4215 60.9446 44.3919 62.2043 43.0058C62.9899 42.1967 63.4226 41.1334 63.4226 40.0032V23.4474C63.4226 22.2954 64.3598 21.3582 65.5118 21.3582C66.6638 21.3582 67.6011 22.2956 67.6011 23.4474V46.9014Z" fill="url(#paint1_linear_665_31788)" />
                    <path d="M46.6262 12.6407H43.4529C42.6942 12.6407 42.0789 13.256 42.0789 14.0148V15.3945C42.0789 18.7309 39.6997 21.5216 36.549 22.1605V15.3888H38.0262C38.785 15.3888 39.4003 14.7735 39.4003 14.0148C39.4003 13.256 38.785 12.6407 38.0262 12.6407H32.4616C31.7028 12.6407 31.0875 13.256 31.0875 14.0148C31.0875 14.7735 31.7028 15.3888 32.4616 15.3888H33.8009V22.1605C30.6502 21.5217 28.271 18.7309 28.271 15.3945V14.0148C28.271 13.256 27.6557 12.6407 26.8969 12.6407H23.7237C22.9649 12.6407 22.3496 13.256 22.3496 14.0148C22.3496 14.7735 22.9649 15.3888 23.7237 15.3888H25.5229V15.3945C25.5229 20.2499 29.1274 24.2776 33.8009 24.9468V27.8169H32.4616C31.7028 27.8169 31.0875 28.432 31.0875 29.1909C31.0875 29.9497 31.7028 30.565 32.4616 30.565H38.0262C38.785 30.565 39.4003 29.9497 39.4003 29.1909C39.4003 28.432 38.785 27.8169 38.0262 27.8169H36.549V24.9468C41.2224 24.2776 44.827 20.2498 44.827 15.3945L46.6262 15.3888C47.3849 15.3888 48.0002 14.7735 48.0002 14.0148C48.0002 13.2559 47.3849 12.6407 46.6262 12.6407Z" fill="url(#paint2_linear_665_31788)" />
                    <path d="M54.8389 19.6616C54.8389 8.81964 46.0183 -0.000732422 35.1763 -0.000732422C24.3343 -0.000732422 15.5137 8.81964 15.5137 19.6616C15.5137 30.5035 24.3342 39.324 35.1763 39.324C46.0184 39.324 54.8389 30.5035 54.8389 19.6616ZM35.1763 36.5758C25.8496 36.5758 18.2618 28.9881 18.2618 19.6615C18.2618 10.3351 25.8496 2.74735 35.1763 2.74735C44.503 2.74735 52.0908 10.3351 52.0908 19.6616C52.0908 28.9882 44.503 36.5758 35.1763 36.5758Z" fill="url(#paint3_linear_665_31788)" />
                    <defs>
                        <linearGradient id="paint0_linear_665_31788" x1="62.4993" y1="6.0873" x2="-0.823707" y2="7.99446" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#211F54" />
                            <stop offset="1" stopColor="#0161AB" />
                        </linearGradient>
                        <linearGradient id="paint1_linear_665_31788" x1="62.4993" y1="6.0873" x2="-0.823707" y2="7.99446" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#211F54" />
                            <stop offset="1" stopColor="#0161AB" />
                        </linearGradient>
                        <linearGradient id="paint2_linear_665_31788" x1="62.4993" y1="6.0873" x2="-0.823707" y2="7.99446" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#211F54" />
                            <stop offset="1" stopColor="#0161AB" />
                        </linearGradient>
                        <linearGradient id="paint3_linear_665_31788" x1="62.4993" y1="6.0873" x2="-0.823707" y2="7.99446" gradientUnits="userSpaceOnUse">
                            <stop stopColor="#211F54" />
                            <stop offset="1" stopColor="#0161AB" />
                        </linearGradient>
                    </defs>
                </svg>

                <h3 className='mt-6 text-lg leading-[22px] font-medium text-center '>Book collectors</h3>

                <p className='mt-3 text-center text-sm font-light leading-5'>Share your love for literature by passing on duplicate or less-loved books to fellow collectors who truly value rare and meaningful finds.</p>
            </div>
        ),
    ]

    return (
        <div ref={ref} className="w-full mx-auto">
            <div className="w-full px-4">
                <Slider ref={sliderRef} sliderSettings={settings} className="custom_slider_dots static-slider">
                    {whoCanSellComponents.map((item, index) => (
                        <div key={index} className="h-[400px] md:h-[500px] overflow-hidden md:px-2.5">
                            <div className="slide-content h-full w-full flex items-center">
                                {item}
                            </div>
                        </div>
                    ))}
                </Slider>
            </div>

        </div>
    );
}

export default ImageSlider;
