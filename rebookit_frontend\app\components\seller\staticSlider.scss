/* Slick Slider- make the center bigger */

@media (min-width: 768px) {
    .slick-slide {
        transition: transform 300ms ease, opacity 300ms ease;
    }

    .slider-slick-center {
        transform: scale(1.2);
        z-index: 1;
        opacity: 1;
        margin: 0 6px;
    }

    .slider-slick-center .slick-custom-item {
        background-color: #211f54;
        color: white;
    }

    .slider-slick-center .slick-custom-item svg path {
        fill: white;
    }

    .slick-slide:not(.slider-slick-center) {
        transform: scale(0.9);
        opacity: 0.8;
    }
}

@media (max-width: 768px) {

    /* Svg color */
    .slider-svg-color-change svg path {
        fill: white;
    }
}


.boxshadow{
    box-shadow: 5px 5px 15px black;
}
/* Slick Slider- make the center bigger */