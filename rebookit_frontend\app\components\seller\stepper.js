"use client";

import {updateUserListing} from "@/app/redux/slices/storeSlice";
import {useEffect, useState} from "react";
import {useDispatch, useSelector} from "react-redux";

export default function Stepper({current, stepsName}) {
  const [currentStep, setCurrentStep] = useState(current || 2);
  const storeData = useSelector((state) => state.storeData);
  const dispatch = useDispatch();
  useEffect(() => {
    setCurrentStep(current);
  }, [current]);
  const changeStepper = (stepCount) => {
    // Only allow navigation to steps up to the highest visited and completed step
    const { visitedStep = 0, completedStep = 0 } = storeData.userListing;
    if (stepCount > visitedStep || stepCount > completedStep) return;
    dispatch(updateUserListing({currentStep: stepCount}));
  };
  return (
    <ol className="flex items-center w-[100%] justify-center">
      {stepsName?.map((name, idx) => {
        const isLast = idx === stepsName.length - 1;
        const isCompleted = idx <= currentStep;

        const liClass = `flex items-center relative ${
          !isLast
            ? "w-full after:content-[''] after:w-full after:h-1 after:border-b after:border-4 after:inline-block after:mx-[10px] after:rounded-[44px] " +
              (isCompleted
                ? "w-full after:border-[#0161AB] dark:after:border-[#0161AB] "
                : "w-auto after:border-gray-200 ")
            : ""
        }`;

        if (isCompleted || idx === currentStep) {
          return (
            <li key={name} className={liClass}>
              <span
                onClick={() => changeStepper(idx)}
                className="cursor-pointer flex items-center justify-center w-10 h-10 bg-[#0161AB] rounded-full lg:h-12 lg:w-12 dark:bg-blue shrink-0"
              >
                <p className="text-white"> {idx + 1} </p>
              </span>
              <p className="absolute top-[60px] left-[0px] text-[10px]">
                {name}
              </p>
            </li>
          );
        } else
          return (
            <li key={name} className={liClass}>
              <span
                onClick={() => changeStepper(idx)}
                className="flex cursor-pointer items-center justify-center w-10 h-10 bg-gray-200 rounded-full lg:h-12 lg:w-12  shrink-0"
              >
                {idx + 1}
              </span>
              <p className="absolute top-[60px] left-[0px] text-[10px]">
                {name}
              </p>
            </li>
          );
      })}
    </ol>
  );
}
