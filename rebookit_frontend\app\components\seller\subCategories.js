import React, {useEffect, useState, useRef} from "react";

import bookCategories from "@/app/static_data/bookSubCategories.json";
import Image from "next/image";

import sellerCss from "./sellerComponent.module.scss";

import {MdArrowRightAlt} from "react-icons/md";
import {useDispatch, useSelector} from "react-redux";
import {
  changeCompletedStep,
  updateUserListing,
} from "@/app/redux/slices/storeSlice";
import {getToken} from "@/app/utils/utils";
import {USER_ROUTES} from "@/app/config/api";
import {getSubCategories, getSubSubCategories} from "@/app/services/profile";
import SubSubCategories from "./SubSubCategory";
import {ItemKindEnum} from "@/app/config/constant";

export default function SubCategories() {
  const dispatch = useDispatch();
  const userListing = useSelector((store) => store.storeData.userListing);
  const [subCategoryState, setsubCategoryState] = useState([]);
  const [isImageLoaded, setisImageLoaded] = useState(false);

  // For instant UI feedback and to avoid delay
  const [pendingSelection, setPendingSelection] = useState(null);
  const [pendingSubSubSelection, setPendingSubSubSelection] = useState(null);

  // Used to prevent double click/fast click issues
  const isHandlingRef = useRef(false);

  // For subcategory selection
  const [selectSubCategory, setselectSubCategory] = useState("");
  // For sub-sub-category selection
  const [selectedSubSubCategory, setSelectedSubSubCategory] = useState("");

  const auth = getToken();

  // Helper: get selected subcategory id (from redux or local state)
  const selectedSubCategoryId =
    pendingSelection !== null
      ? pendingSelection
      : userListing?.subCategory?._id || selectSubCategory;

  // Helper: get selected subsubcat id (from redux or local state)
  const selectedSubSubCategoryId =
    pendingSubSubSelection !== null
      ? pendingSubSubSelection
      : userListing?.subSubCategory?._id || selectedSubSubCategory;

  // Called when a subcategory is selected
  const handleCategoryChange = async (subcategory, idx) => {
    if (isHandlingRef.current) return;
    isHandlingRef.current = true;

    // Instantly update UI for selection
    setPendingSelection(subcategory._id);
    setselectSubCategory(subcategory._id);
    setSelectedSubSubCategory(""); // Reset subsubcat selection on subcat change
    setPendingSubSubSelection(null);

    let updatePayload = {subCategory: subcategory};
    if (subcategory.kind) {
      updatePayload = {...updatePayload, kind: subcategory.kind};
    }

    // Optimistically update Redux for instant UI feedback
    // Do NOT show loader for subcategory list, only for sub-sub-category if needed
    let response;
    let hasSubSubCategories = false;
    try {
      // Fetch sub-sub-categories in background, but don't block UI
      response = await getSubSubCategories(subcategory._id);
      response = response?.data;
      hasSubSubCategories = response?.subSubCategories?.length > 0;
    } catch (e) {
      hasSubSubCategories = false;
    }

    if (!hasSubSubCategories) {
      dispatch(
        updateUserListing({
          ...updatePayload,
          subSubCategory: null,
          currentStep: 2,
          visitedStep: 2,
        })
      );
      setSelectedSubSubCategory("");
      setPendingSubSubSelection(null);
    } else {
      // Remove subSubCategory from redux if switching to a new subcategory
      dispatch(
        updateUserListing({
          ...updatePayload,
          subSubCategory: null,
          visitedStep: 2,
        })
      );
      setSelectedSubSubCategory("");
      setPendingSubSubSelection(null);
    }

    // Remove pending state immediately for snappy UI
    setPendingSelection(null);
    isHandlingRef.current = false;
  };

  // Called when a sub-sub-category is selected (from SubSubCategories)
  const handleSubSubCategorySelect = (subSubCategory) => {
    setPendingSubSubSelection(subSubCategory._id);
    setSelectedSubSubCategory(subSubCategory._id);
    dispatch(
      updateUserListing({
        ...userListing,
        subSubCategory: subSubCategory,
        currentStep: 2,
        visitedStep: 2, // Reset visitedStep on sub-sub-category change
        completedStep: 2, // Reset completedStep on sub-sub-category change
      })
    );
    setTimeout(() => {
      setPendingSubSubSelection(null);
    }, 0);
  };

  // Fetch subcategories for the selected category
  const fetchMasterSubCategory = async (categoryId) => {
    try {
      let response = await getSubCategories(categoryId);
      response = response?.data;
      setsubCategoryState(response?.subCategories || []);
      return response?.data || [];
    } catch (error) {
      console.error("Subcategory fetch error:", error);
      return [];
    }
  };

  // On mount or when category changes, fetch subcategories
  useEffect(() => {
    if (userListing?.category) {
      fetchMasterSubCategory(userListing.category._id);
    }
  }, [userListing?.category?._id]);

  // On mount or when redux subCategory/subSubCategory changes, sync local state for instant UI
  useEffect(() => {
    if (userListing?.subCategory?._id) {
      setselectSubCategory(userListing.subCategory._id);
    }
    if (userListing?.subSubCategory?._id) {
      setSelectedSubSubCategory(userListing.subSubCategory._id);
    }
  }, [userListing?.subCategory?._id, userListing?.subSubCategory?._id]);

  let mappingIcon = {
    Book: "/icons/openBookIcon.png",
    "E-directory": "/icons/openFolderIcon.png",
    "Scholarship & Awards": "/icons/scholerShipIcon.png",
    Events: "/icons/eventIcon.png",
  };

  // Helper: is this subcategory selected? (either by redux, local state, or pending)
  const isSubCategorySelected = (category) => {
    // If a sub-sub-category is selected, highlight only its parent subcategory
    if (selectedSubSubCategoryId && userListing?.subSubCategory?.parentId) {
      return userListing.subSubCategory.parentId === category._id;
    }
    // Otherwise, highlight the selected subcategory
    return selectedSubCategoryId === category._id;
  };

  return (
    <>
      <section className="my-8 md:my-20">
        <header className="max-w-4xl">
          <h2
            id="subcategory-heading"
            className="text-[22px] font-semibold my-4 md:text-4xl lg:text-[40px] "
          >
            Choose a sub category
          </h2>
          {/* <p className="mt-3 text-gray-600 text-sm md:text-base lg:text-lg max-w-3xl ">
            DigiRoad collects this information to better understand and serve
            your business.
          </p> */}
        </header>

        <div className="mt-8 md:mt-12">
          <ul
            className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 md:gap-6"
            role="list"
          >
            {subCategoryState?.length > 0 ? (
              subCategoryState.map((category, idx) => {
                const selected = isSubCategorySelected(category);
                return (
                  <li
                    key={category._id || idx}
                    onClick={() => {
                      if (!pendingSelection && !selected) {
                        handleCategoryChange(category, idx);
                      }
                    }}
                    className="flex flex-col h-full "
                    style={{
                      cursor: pendingSelection ? "not-allowed" : "pointer",
                    }}
                  >
                    <article
                      className={`flex flex-col items-center h-full p-4 bg-white rounded-lg transition-all duration-150`}
                    >
                      {/* Image container - responsive circle */}
                      <div className="bg-[#211F54] rounded-full p-3 w-16 h-16 sm:w-20 sm:h-20 md:w-30 md:h-30 flex items-center justify-center">
                        <img
                          src={
                            category.image ||
                            mappingIcon[userListing.category?.name]
                          }
                          alt={`Cover for ${category.name}`}
                          className={`
                            w-full h-full max-w-[40px] max-h-[40px] 
                            sm:max-w-[50px] sm:max-h-[50px] 
                            md:max-w-[60px] md:max-h-[60px]
                            object-contain transition-opacity duration-300 
                            ${isImageLoaded ? "opacity-100" : "opacity-0"}
                          `}
                          onLoad={() => setisImageLoaded(true)}
                        />
                      </div>

                      {/* Category name - responsive text with consistent height */}
                      <p className="text-center font-medium mt-3 mb-4 text-xs sm:text-sm md:text-base min-h-[3.5em] flex items-center justify-center">
                        {category.text || category.name}
                      </p>

                      {/* Button - fully responsive */}
                      <div className="mt-auto w-full pt-2 sm:pt-3 md:pt-4">
                        <button
                          type="button"
                          disabled={!!pendingSelection}
                          className={`
      w-full flex items-center justify-center
      gap-1                      /* mobile gap */
      sm:gap-2                   /* ≥640px */
      py-2 px-3                 /* mobile padding */
      sm:py-2.5 sm:px-4         /* ≥640px */
      md:py-3 md:px-5           /* ≥768px */
      text-sm                    /* mobile font */
      sm:text-base               /* ≥640px */
      md:text-lg                 /* ≥768px */
      rounded-full
      transition-all duration-200
      whitespace-nowrap
      border
      ${
        selected
          ? "global_linear_gradient text-white"
          : "text-gray-800 hover:bg-gray-200"
      }
      ${pendingSelection && !selected ? "opacity-60 pointer-events-none" : ""}
    `}
                        >
                          <span className="truncate max-w-[80px] sm:max-w-[120px] md:max-w-none">
                            {selected ? "Selected" : "Select"}
                          </span>

                          {!selected && (
                            <MdArrowRightAlt
                              className="
          flex-shrink-0
          w-4 h-4               /* 16×16 mobile */
          sm:w-5 sm:h-5         /* 20×20 ≥640px */
          md:w-6 md:h-6         /* 24×24 ≥768px */
        "
                            />
                          )}
                        </button>
                      </div>
                    </article>
                  </li>
                );
              })
            ) : (
              // Improved loading indicator
              <div className="col-span-full flex justify-center py-10">
                <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
            )}
          </ul>

          <div id="subSubCat" className="mt-10">
            {selectedSubCategoryId && (
              <SubSubCategories
                subCategoryId={selectedSubCategoryId}
                onSelect={handleSubSubCategorySelect}
                selectedSubSubCategoryId={selectedSubSubCategoryId}
              />
            )}
          </div>
        </div>
      </section>
    </>
  );
}
