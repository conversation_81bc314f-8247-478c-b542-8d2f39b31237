import React, {useEffect, useState} from "react";

import bookCategories from "@/app/static_data/bookSubCategories.json";
import Image from "next/image";

import sellerCss from "./sellerComponent.module.scss";

import {MdArrowRightAlt} from "react-icons/md";
import {useDispatch, useSelector} from "react-redux";
import {
  changeCompletedStep,
  updateUserListing,
} from "@/app/redux/slices/storeSlice";
import {getToken} from "@/app/utils/utils";
import {USER_ROUTES} from "@/app/config/api";
import {
  getSubCategories,
  getSubSubCategories,
  getSubSubSubCategories,
} from "@/app/services/profile";
import {mappingWithCategoryIcon} from "@/app/config/constant";

export default function SubSubSubCategories({subsubCategoryId}) {
  const dispatch = useDispatch();
  const userListing = useSelector((store) => store.storeData.userListing);
  const [subsubCategoryState, setsubsubCategoryState] = useState([]);
  const [isLoading, setisLoading] = useState(false);
  // Track selected sub-sub-sub-category by id for highlight and reload persistence
  const [selectedSubSubSubCategoryId, setSelectedSubSubSubCategoryId] =
    useState(userListing?.subSubSubCategory?._id || "");
  const [isImageLoaded, setisImageLoaded] = useState(false);

  const auth = getToken();

  // Sync local selected id with redux on mount and when redux changes (for reload persistence)
  useEffect(() => {
    if (userListing?.subSubSubCategory?._id) {
      setSelectedSubSubSubCategoryId(userListing.subSubSubCategory._id);
    }
  }, [userListing?.subSubSubCategory?._id]);

  // Handle selection and update both redux and local state
  const handleCategoryChange = (subcategory, idx) => {
    setSelectedSubSubSubCategoryId(subcategory._id);
    dispatch(
      updateUserListing({
        currentStep: 2,
        subSubSubCategory: subcategory,
        visitedStep: 3,
        completedStep: 2,
      })
    );
    if (userListing.completedStep > 1) {
      return;
    } else {
      dispatch(changeCompletedStep(1));
    }
  };

  const fetchMasterSubSubCategory = async (subsubCategoryId) => {
    setisLoading(true);
    try {
      let response = await getSubSubSubCategories(subsubCategoryId);
      response = response?.data;
      setsubsubCategoryState(response?.subSubSubCategories || []);
      setTimeout(() => {
        if (response?.subSubSubCategories.length) {
          let element = document.getElementById("SubSubSubCat");
          if (element) {
            element.scrollIntoView({behavior: "smooth", block: "start"});
          }
        }
      }, 1000);
      setisLoading(false);
      return response?.data || [];
    } catch (error) {
      setisLoading(false);
      console.error("Subcategory fetch error:", error);
      return [];
    }
  };

  useEffect(() => {
    if (subsubCategoryId) {
      fetchMasterSubSubCategory(subsubCategoryId);
    }
  }, [subsubCategoryId]);

  // Helper: is this sub-sub-sub-category selected?
  const isSelected = (category) => {
    // Prefer local state, fallback to redux for reload
    return (
      selectedSubSubSubCategoryId === category._id ||
      userListing?.subSubSubCategory?._id === category._id
    );
  };

  // Find the selected category object for display
  const selectedCategoryObj = subsubCategoryState.find((cat) =>
    isSelected(cat)
  );

  return (
    <section className="my-8 md:my-16" id="SubSubSubCat">
      <header className="mb-6 md:mb-10">
        <h2
          id="subsubsubcategory-heading"
          className="text-2xl font-semibold my-4 md:text-3xl lg:text-[40px]"
        >
          Choose a sub-sub-sub category
        </h2>
      </header>

      <ul
        className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 md:gap-6"
        role="list"
      >
        {!isLoading ? (
          subsubCategoryState?.map((category, idx) => {
            const selected = isSelected(category);
            return (
              <li
                key={category._id || idx}
                onClick={() => handleCategoryChange(category, idx)}
                className="flex flex-col h-full"
                style={{cursor: "pointer"}}
              >
                <article
                  className={`flex flex-col items-center h-full p-4 bg-white rounded-lg transition-all duration-150 `}
                >
                  {/* Image container - responsive circle */}
                  <div className="bg-[#211F54] rounded-full p-3 w-16 h-16 sm:w-20 sm:h-20 md:w-30 md:h-30 flex items-center justify-center">
                    <img
                      src={
                        category.image ||
                        mappingWithCategoryIcon[userListing.category?.name]
                      }
                      alt={`Cover for ${category.text || category.name}`}
                      className={`
                        w-full h-full max-w-[40px] max-h-[40px] 
                        sm:max-w-[50px] sm:max-h-[50px] 
                        md:max-w-[60px] md:max-h-[60px]
                        object-contain transition-opacity duration-300 
                        ${isImageLoaded ? "opacity-100" : "opacity-0"}
                      `}
                      onLoad={() => setisImageLoaded(true)}
                      onError={(e) => {
                        e.target.onerror = null;
                        e.target.src = "/path/to/fallback-image.png";
                      }}
                    />
                  </div>

                  {/* Category name - responsive text with consistent height */}
                  {/* <p className="text-center font-medium mt-3 mb-4 text-xs sm:text-sm md:text-base min-h-[3.5em] flex items-center justify-center px-1">
                    {category.text || category.name}
                  </p> */}
                  <p className="text-center font-medium mt-3 mb-4 text-xs sm:text-sm md:text-base min-h-[3.5em] flex items-center justify-center px-1">
                    {String(category.text || category.name).includes("/")
                      ? String(category.text || category.name)
                          .split("/")
                          .map((item, index, arr) => (
                            <React.Fragment key={index}>
                              {item}
                              {index < arr.length - 1 && "/"}
                              <br />
                            </React.Fragment>
                          ))
                      : category.text || category.name}
                  </p>

                  {/* Button - fully responsive */}
                  <div className="mt-auto w-full pt-2 sm:pt-3 md:pt-4">
                    <button
                      type="button"
                      className={`
      w-full flex items-center justify-center
      gap-1               /* mobile gap */
      sm:gap-2            /* ≥640px */
      md:gap-3            /* ≥768px */

      py-2 px-3           /* mobile padding */
      sm:py-2.5 sm:px-4   /* ≥640px */
      md:py-3 md:px-5     /* ≥768px */

      text-sm             /* mobile font */
      sm:text-base        /* ≥640px */
      md:text-lg          /* ≥768px */

      rounded-full
      transition-all duration-200
      whitespace-nowrap
      border              /* use Tailwind’s standard border */

      ${
        selected
          ? "bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white"
          : "text-gray-800 hover:bg-gradient-to-r hover:from-[#211F54] hover:to-[#0161AB] hover:text-white"
      }
    `}
                    >
                      <span className="truncate max-w-[80px] sm:max-w-[120px] md:max-w-none">
                        {selected ? "Selected" : "Select"}
                      </span>
                      {!selected && (
                        <MdArrowRightAlt
                          className="
          flex-shrink-0
          w-4 h-4             /* 16×16 on mobile */
          sm:w-5 sm:h-5       /* 20×20 ≥640px */
          md:w-6 md:h-6       /* 24×24 ≥768px */
        "
                        />
                      )}
                    </button>
                  </div>
                </article>
              </li>
            );
          })
        ) : (
          // Improved loading indicator
          <div className="col-span-full flex justify-center py-10">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        )}
      </ul>
    </section>
  );
}
