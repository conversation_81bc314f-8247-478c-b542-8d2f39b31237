export default function StatusBadge({status}) {
  const labelMap = {
    success: "Paid",
    pending: "Pending",
    failed: "Failed",
    "marked as sold":"marked as sold"
  };

  const colorMap = {
    success: "bg-green-100 text-green-800",
    pending: "bg-yellow-100 text-yellow-800",
    failed: "bg-red-100   text-red-800",
    "marked as sold":"bg-red-400 text-white"
  };

  const label = labelMap[status] || status;
  const classes = colorMap[status] || "bg-gray-100 text-gray-800";

  return (
    <span
      className={`inline-block px-4 py-2 text-xs font-medium rounded-full ${classes}`}
    >
      {label}
    </span>
  );
}
