const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;
// const BASE_URL="http://localhost:4000"
console.log("BASE_URL",BASE_URL)
const USER_BASE_URL = `${BASE_URL}`;
const CHAT_BASE_URL = `${BASE_URL}/chat`;
const ELASTIC_BASE_URL = `${BASE_URL}`;

// const USER_BASE_URL = `http://localhost:4002`;
// const CHAT_BASE_URL = `http://localhost:4001`;
// const ELASTIC_BASE_URL = `http://localhost:4004`;



const USER_ROUTES = {

    //LOGIN ROUTES
    lOGIN: `${USER_BASE_URL}/user/login`,
    REGISTER: `${USER_BASE_URL}/user/register`,
    SEND_OTP: `${USER_BASE_URL}/user/send-otp`,
    VERIFY_OTP: `${USER_BASE_URL}/user/verify-otp`,
    RESEND_OTP: `${USER_BASE_URL}/user/resend-otp`,
    FORGOT_PASSWORD: `${USER_BASE_URL}/user/forgot-password`,
    CREATE_NEW_PASSWORD: `${USER_BASE_URL}/user/create-new-password`,

    //verify user email or number
    VERIIFICATION_USER_DATA: `${USER_BASE_URL}/user/verify-user-data`,

    // book search routes
    ISBN_SEARCH: `${USER_BASE_URL}/api/books/isbn/{{ISBN}}`,
    BOOK_SEARCH_BY_NAME: `${USER_BASE_URL}/api/books/search?q={search}`,

    SEARCH_ITEM_BY_NAME: `${USER_BASE_URL}/api/item/search?name=itemName`,

    // upload
    MULTI_UPLOAD_FILES: `${USER_BASE_URL}/api/admin/multiple-upload`,
    SINGLE_UPLOAD_FILE: `${USER_BASE_URL}/api/admin/single-upload`,



    // USER INFO
    USER_INFO: `${USER_BASE_URL}/api/user`,
    EDIT_USER_INFO: `${USER_BASE_URL}/api/user/edit-profile`,


    // list item
    LIST_ITEM: `${USER_BASE_URL}/item`,

    // get subscription plans     
    GET_ALL_SUBSCRIPTION: `${USER_BASE_URL}/api/admin/subscription/plan`,



    // payment routes
    PAYMENT_INTENT: `${USER_BASE_URL}/api/payment/create-payment-intent`,


    // master data
    MASTER_DATA_CATEGORY: `${USER_BASE_URL}/master/category`, // GET
    MASTER_DATA_SUB_CATEGORY: `${USER_BASE_URL}/master/sub-category`, // GET
    MARK_AS_SOLD:`${USER_BASE_URL}/api/item`
}

const CHAT_ROUTES = {
    ALL_CHATS: `${CHAT_BASE_URL}/all`,
    CHAT: `${CHAT_BASE_URL}`
}

const ELASTIC_DB_ROUTES = {
    SUGGESTION: `${USER_BASE_URL}/api/item/auto-complete`,
    SEARCH: `${USER_BASE_URL}/api/item/search?page={{page}}`,
    SEARCH_BY_ID: `${USER_BASE_URL}/search-book-by-id`
}


module.exports = {
    USER_ROUTES,
    CHAT_ROUTES,
    ELASTIC_DB_ROUTES
}