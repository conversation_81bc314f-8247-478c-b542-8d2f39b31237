const PaymentStatusEnum = Object.freeze({
  PENDING: "pending",
  SUCCESS: "success",
  FAILED: "failed",
});

const ItemListStatusEnum = Object.freeze({
  PENDING: "pending",
  HOLD: "hold",
  ACCEPTED: "accepted",
  REJECTED: "rejected",
  MARKED_AS_SOLD: "marked as sold",
  EXPIRED:"expired"
});

const RebookerListTypeEnum = Object.freeze({
  YEARLY: "yearly",
  MONTHLY: "monthly",
  WEEKLY: "weekly",
  EVERYDAY: "everyday",
});

const ChatTypeEnum = Object.freeze({
  Buyers: "buyers",
  SELLERS: "sellers",
});

const RoleEnum = Object.freeze({
  ADMIN: "admin",
  USER: "user",
  EDITOR: "editor",
  VIEWER: "viewer",
  SUPERADMIN: "superadmin",
});

const PlanMonthsTypeEnum = Object.freeze({
  MONTHLY: "Monthly",
  QUARTERLY: "Quarterly",
  HALF_YEARLY: "Half-Yearly",
  YEARLY: "Yearly",
});

const PlanTypeEnum = Object.freeze({
  FREE: "free",
  PAID: "paid",
});

const PlanStatusEnum = Object.freeze({
  ACTIVE: "active",
  EXPIRED: "expired",
  QUEUED: "queued",
  UPGRADED: "upgraded",
});

const NotificationTypesEnum = Object.freeze({
  EMAIL: "email",
  SMS: "sms",
  WHATSAPP: "whatsapp",
});

const BookConditionEnum = Object.freeze({
  NEW: "new",
  OLD: "old",
});

const EventModeEnum = Object.freeze({
  ONLINE: "online",
  INPERSON: "inperson",
  HYBRID: "hybrid",
});

const eventModeArr = [
  { value: 'online', label: 'Online' },
  { value: 'inperson', label: 'Inperson' },
  { value: 'hybrid', label: 'Hybrid' },

]
const ClassesOffered = Object.freeze({
  KINDERGARDEN: "kindergarden",
  FIRST_TO_FIFTH: "first to fifth",
  UPTO_EIGHTH: "up to eighth",
  UPTO_TENTH: "up to tenth",
  UPTO_TWELFTH: "up to twelfth",
});
const classesOfferedArr = [
  { label: "Kindergarden", value: "kindergarden" },
  { label: "First_To_Fifth", value: "first to fifth" },
  { label: "Up_To_Eighth", value: "up to eighth" },
  { label: "Up_To_Tenth", value: "up to tenth" },
  { label: "Up_To_Twelfth", value: "up to twelfth" }
]

const SchoolTypeEnum = Object.freeze({
  PUBLIC: "public",
  PRIVATE: "private",
  INTERNATIONAL: "intetnamtional",
  CONVENT: "convent",
  GOVERNMENT: "government",
});
const schoolTypes = [
  { label: "Public", value: "public" },
  { label: "Private", value: "private" },
  { label: "International", value: "intetnamtional" },
  { label: "Convent", value: "convent" },
  { label: "Government", value: "government" },
]
const ItemKindEnum = { "BookItem": "BookItem", "TutorItem": "TutorItem", "EventItem": "EventItem", "SchoolItem": "SchoolItem", "ExtracurricularActivityItem": "ExtracurricularActivityItem", "ScholarshipAwardItem": "ScholarshipAwardItem" }
const itemToKind = {
  "BookItem": { "isbn_number": "isbn_number", "authors": "authors", "condition": "condition" },
  "TutorItem": { "highestQualification": "highestQualification", "targetClasses": "targetClasses", "experience": "experience", "website": "website" },
  "SchoolItem": { "schoolType": "schoolType", "classesOffered": "classesOffered", "website": "website" },
  "EventItem": { "eventMode": "eventMode", "eventStartDate": "eventStartDate", "eventEndDate": "eventEndDate", "website": "website" },
  "ExtracurricularActivityItem": { "activityType": "activityType", "frequency": "frequency", "targetStudents": "targetStudents" },
  "ScholarshipAwardItem": { "eligibilityCriteria": "eligibilityCriteria", "scholarshipType": "scholarshipType", "website": "website" }
}

const labelItemToKind = {
  "BookItem": { "isbn_number": "ISBN", "authors": "Authors", "condition": "Condition" },
  "TutorItem": { "highestQualification": "Qualification", "targetClasses": "Target Classes", "experience": "Experience", "website": "Website" },
  "SchoolItem": { "schoolType": "School Type", "classesOffered": "Classes Offered", "website": "Website" },
  "EventItem": { "eventMode": "Event Mode", "eventStartDate": "EventStart Date", "eventEndDate": "EventEnd Date", "website": "Website" },
  "ExtracurricularActivityItem": { "activityType": "Activity Type", "frequency": "Frequency", "targetStudents": "Target Students" },
  "ScholarshipAwardItem": { "eligibilityCriteria": "Eligibility Criteria", "scholarshipType": "Scholarship Type", "website": "Website" }
}

const ActivityTypeEnum = Object.freeze({
  PHYSICAL: "physical",
  CREATIVE: "creative",
  TECHNICAL: "technical",
  LEADERSHIP: "leadership",
  CULTURAL: "cultural",
  OTHERS: "others",
});
const activityTypeArr = [
  { label: "Physical", value: "physical" },
  { label: "Creative", value: "creative" },
  { label: "Technical", value: "technical" },
  { label: "Leadership", value: "leadership" },
  { label: "Cultural", value: "cultural" },
  { label: "Others", value: "others" }
]

const FrequencyEnum = Object.freeze({
  DAILY: "daily",
  WEEKLY: "weekly",
  MONTHLY: "monthly",
  SEASONALLY: "seasonally",
  EVENT_BASED: "event-based",
});

const frequencyArr = [
  { label: "Daily", value: "daily" },
  { label: "Weekly", value: "weekly" },
  { label: "Monthly", value: "monthly" },
  { label: "Seasonally", value: "seasonally" },
  { label: "Event_Based", value: "event-based" }
]
let mappingWithCategoryIcon = {
  "Book": "/icons/openBookIcon.png",
  "E-directory": "/icons/openFolderIcon.png",
  "Scholarship & Awards": "/icons/scholerShipIcon.png",
  "Events": "/icons/eventIcon.png"
}

const ParishesListEnum = Object.freeze({
  KINGSTON: "kingston",
  ST_ANDREW: "saint andrew",
  PORTLAND: "portland",
  ST_THOMAS: "saint thomas",
  ST_CATHERINE: "saint catherine",
  CLARENDON: "clarendon",
  MANCHESTER: "manchester",
  ST_ELIZABETH: "saint elizabeth",
  WESTMORELAND: "westmoreland",
  HANOVER: "hanover",
  ST_JAMES: "saint james",
  TRELAWNY: "trelawny",
  ST_ANN: "saint ann",
  ST_MARY: "saint mary",
});

const TargetClassTypeEnum = Object.freeze([
  { label: "Primary_1_To_6", value: "primary 1 to 6" },
  { label: "Lower_Secondary_7_To_9", value: "lower secondary 7 to 9" },
  { label: "Upper_Secondary_10_To_11", value: "upper secondary 10 to 11" },
  { label: "Sixth_Form_12_To_13", value: "sixth form 12 to 13" },
  { label: "Diploma", value: "diploma" },
  { label: "Associate_Degree", value: "associate degree" },
  { label: "Bachelor", value: "bachelor" },
  { label: "Master", value: "master" },
  { label: "Doctorate", value: "doctorate" },
])
 let bookSubcategories={
    carrabian:[{
        "_id": "6811d7ad7d79fae9362714b4",
        "name": "Early Childhood",
        "image": "",
        "categoryId": "68612cc0ba947189b202a825",
        "createdAt": "2025-07-25T07:32:33.414Z",
        "updatedAt": "2025-07-25T07:32:33.414Z",
        "kind": null
    },
    {
        "_id": "6811d7ccd8e7e0ae74fd55ba",
        "name": "Primary /Preparatory School",
        "image": "",
        "categoryId": "68612cc0ba947189b202a825",
        "createdAt": "2025-07-25T07:32:33.414Z",
        "updatedAt": "2025-07-25T07:32:33.414Z",
        "kind": null
    },
    {
        "_id": "6811d7d4887fa095677d4e11",
        "name": "Secondary / High School",
        "image": "",
        "categoryId": "68612cc0ba947189b202a825",
        "createdAt": "2025-07-25T07:32:33.414Z",
        "updatedAt": "2025-07-25T07:32:33.414Z",
        "kind": null
    },
    {
        "_id": "6811d7dcda1dd17fadff14ae",
        "name": "Tertiary / University / College",
        "image": "",
        "categoryId": "68612cc0ba947189b202a825",
        "createdAt": "2025-07-25T07:32:33.414Z",
        "updatedAt": "2025-07-25T07:32:33.414Z",
        "kind": null
    },
    {
        "_id": "6811d7e3cb24f0b11c79e5a2",
        "name": "Professional Certifications",
        "image": "",
        "categoryId": "68612cc0ba947189b202a825",
        "createdAt": "2025-07-25T07:32:33.414Z",
        "updatedAt": "2025-07-25T07:32:33.414Z",
        "kind": null
    },],

    nonCarrabian: [
    {
        "_id": "6811d81a1680f8a7d435cb7a",
        "name": "Fiction",
        "image": "",
        "categoryId": "68612cc0ba947189b202a825",
        "createdAt": "2025-07-25T07:32:33.414Z",
        "updatedAt": "2025-07-25T07:32:33.414Z",
        "kind": null
    },
    {
        "_id": "6811d821ebe38531a1fe3fee",
        "name": "Non-Fiction",
        "image": "",
        "categoryId": "68612cc0ba947189b202a825",
        "createdAt": "2025-07-25T07:32:33.414Z",
        "updatedAt": "2025-07-25T07:32:33.414Z",
        "kind": null
    }]
  }

let categoriesLocalData = { "book": "Book", "EDirectory": "E-directory", "Scholarship": "Scholarship & Awards", "Events": "Events" }

const QualificationTypeEnum = Object.freeze({
  high_school_diploma: "High School Diploma", // Level 3 — High School Diploma
  skills_certification: "Skills Certification", // Level 4 — Skills Certification
  professional_certification: "Professional Certification", // Level 4 — Professional Certification
  associates_degree: "Associates Degree", // Level 5 — Associate's Degree
  bachelor: "Bachelor", // Level 6 — Bachelor's Degree
  postgrad_diploma: "Postgrad Diploma", // Level 7 — Postgraduate Diploma/Certificate
  masters: "Masters", // Level 8 — Master's Degree
  doctorate: "Doctorate", // Level 9 — Doctoral Degree
});
module.exports = {
  ItemListStatusEnum,
  RebookerListTypeEnum,
  ChatTypeEnum,
  RoleEnum,
  PaymentStatusEnum,
  PlanMonthsTypeEnum,
  PlanTypeEnum,
  PlanStatusEnum,
  NotificationTypesEnum,
  BookConditionEnum,
  ItemKindEnum,
  EventModeEnum,
  itemToKind,
  schoolTypes,
  classesOfferedArr,
  activityTypeArr,
  frequencyArr,
  eventModeArr,
  mappingWithCategoryIcon,
  ParishesListEnum,
  TargetClassTypeEnum,
  categoriesLocalData,
  labelItemToKind,
  bookSubcategories,
  QualificationTypeEnum
};
