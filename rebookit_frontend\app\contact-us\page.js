"use client";
import React, {useState} from "react";
import contactModule from "./contactUs.module.scss";
import dynamic from "next/dynamic";
import Image from "next/image";
import {MdOutlineMail} from "react-icons/md";
import {IoIosCall} from "react-icons/io";
import {IoLocationOutline} from "react-icons/io5";
import {FaTwitter, FaFacebook, FaYoutube, FaInstagram} from "react-icons/fa";
import {useForm} from "react-hook-form";
import {supportRequest} from "../services/profile";
import {toDate} from "date-fns";
import {toast} from "react-toastify";
import SubmitButton from "../components/common/SubmitButton";

const ImageSlider = dynamic(() => import("../components/about/imageSlider"), {
  // ssr: false,
  loading: () => <div>Loading slider...</div>,
});

const BuyAndSellComponent = dynamic(
  () => import("../components/about/BuyAndSellComponent"),
  {
    // ssr: false,
  }
);

// export const metadata = {
//     title: "About Us",
//     description: "Know About Us",
// };

function ContactUs() {
  const {
    register,
    watch,
    formState: {errors},
    getValues,
    handleSubmit,
    setValue,
    reset,
    setError,
    clearErrors,
    control,
  } = useForm({});

  const [isLoading, setisLoading] = useState(false);
  console.log("watch", watch());

  console.log("errors", errors);
  const resetData = () => {
    reset();
  };
  const submitFunction = async (data) => {
    setisLoading(true);
    let payload = {
      email: data.email,
      message: data.message,
      subject: data.subject,
      name: data.name,
    };
    reset();

    let response = await supportRequest(payload);
    console.log("response", response);
    if (response?.status == 200) {
      toast.success(response.data?.message || "");
      reset();
      setisLoading(false);
    }
  };

  const InnerDiv = () => {
    return <div>Submit</div>;
  };
  return (
    <div className={`${contactModule?.aboutContainer}`}>
      <div
        className={`${contactModule?.searchContainer} flex items-start justify-start`}
      >
        <section className="my-auto container-wrapper w-full">
          <header className={`my-9 ${contactModule?.mainHeading}`}>
            <h1 className="text-3xl md:text-5xl font-semibold leading-normal lg:text-6xl">
              Contact Us - We are here to support you.
            </h1>
            <p className="mt-2 text-xl md:text-3xl lg:text-4xl lg:font-bold text-white lg:w-3/4">
              Have a question, suggestion, or need help with something on
              Rebookit.club?
            </p>
          </header>
        </section>
      </div>
      {/* <div className={`${contactModule.searchContainer} flex items-start justify-start`}>
                <section className='container-wrapper w-full'>
                    <header className={`my-9 ${contactModule.mainHeading} text-left`}>
                        <div>
                            <h1 className='text-left md:text-5xl md:font-semibold leading-normal lg:text-6xl'>
                                Contact Us - We are Here to support you
                            </h1>
                        </div>
                        <p className='  mt-[10px] text-[31px] text-white md:text-4xl lg:leading-14 lg:text-5xl lg:font-bold text-left'>
                                                        Have a question, Suggestion, or need help with something on ReBookit?
                        </p>
                    </header>
                </section>
            </div> */}

      <section className="px-4 pb-10 md:px-12 lg:px-24 lg:pb-16 my-12">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6 container-wrapper">
          <div className="bg-[#211F54] text-white rounded-[20px] p-4 lg-col-span-4 md:col-span-2">
            <p className="text-lg font-semibold mb-4">Contact Information</p>
            <div className="flex items-center mb-3">
              <MdOutlineMail />
              <p className="ml-2"><EMAIL></p>
            </div>
            <div className="flex items-center mb-3">
              <IoIosCall />
              <p className="ml-2">+1 (876) 855-9108</p>
            </div>
            <div className="flex items-center mb-3">
              <IoLocationOutline />
              <p className="ml-2">Kingston, Jamaica</p>
            </div>
            <div className="flex items-center space-x-4 mt-4">
              <FaTwitter
                className="cursor-pointer"
                onClick={() => window.open("https://www.x.com", "_blank")}
                size={24}
              />
              <FaFacebook
                size={24}
                className="cursor-pointer"
                onClick={() =>
                  window.open("https://www.facebook.com", "_blank")
                }
              />
              <FaYoutube
                size={24}
                className="cursor-pointer"
                onClick={() => window.open("https://www.youtube.com", "_blank")}
              />{" "}
              <FaInstagram
                className="cursor-pointer"
                onClick={() =>
                  window.open("https://www.instagram.com", "_blank")
                }
                size={24}
              />
            </div>

            <div className="mt-6">
              <img
                className="w-36 opacity-30"
                src="https://rebook-it.s3.us-east-1.amazonaws.com/uploads/9289ef19-2dc4-4eb2-a39a-91574017dc61.png"
              />
            </div>
          </div>

          <div
            className="bg-white rounded-lg p-4 lg-col-span-1 md:col-span-3"
            style={{boxShadow: "0px 4px 12px #EEEEEE80"}}
          >
            <p className="text-2xl md:text-3xl font-bold mb-2">Keep In Touch</p>
            <p className="text-sm md:text-base mb-4">
              We’d love to hear from you — reach out via form, email, or phone,
              and stay connected through our social channels and newsletter.
            </p>
            <form className="space-y-4" onSubmit={handleSubmit(submitFunction)}>
              <div className="relative">
                <input
                  type="text"
                  {...register("name", {required: "Required"})}
                  className="border border-[#EEEEEE] w-full rounded-md p-3"
                  autoComplete="off"
                  placeholder="Name"
                />
                {errors.name && (
                  <p className="text-red-500 text-[12px] mt-1 absolute right-0 bottom-[-20px]">
                    {errors.name?.message}
                  </p>
                )}
              </div>

              <div className="relative mt-5">
                <input
                  type="email"
                  {...register("email", {required: "Required"})}
                  className="border border-[#EEEEEE] w-full rounded-md p-3"
                  autoComplete="off"
                  placeholder="Email"
                />
                {errors.email && (
                  <p className="text-red-500 text-[12px] mt-1 absolute right-0 bottom-[-22px]">
                    {errors.email?.message}
                  </p>
                )}
              </div>

              <div className="relative mt-5">
                <input
                  type="text"
                  {...register("subject", {required: "Required"})}
                  className="border border-[#EEEEEE] w-full rounded-md p-3"
                  autoComplete="off"
                  placeholder="Subject"
                />
                {errors.subject && (
                  <p className="text-red-500 text-[12px] mt-1 absolute right-0 bottom-[-22px]">
                    {errors.subject?.message}
                  </p>
                )}
              </div>

              <div className="relative mt-5">
                <textarea
                  {...register("message", {required: "Required"})}
                  className="border w-full border-[#EEEEEE] rounded-md p-3"
                  autoComplete="off"
                  placeholder="Type here..."
                  rows={5}
                />
                {errors.message && (
                  <p className="text-red-500 text-[12px] mt-1 absolute right-0 bottom-[-22px]">
                    {errors.message?.message}
                  </p>
                )}
              </div>
              {/* btnAction={submitQuestion} */}
              <SubmitButton
                isLoading={isLoading}
                InnerDiv={InnerDiv}
                type={"submit"}
              />

              {/* <button type='submit' className='py-[7.5px] px-[30px] text-[13px] font-semibold leading-[18px] text-center text-white rounded-full global_linear_gradient md:order-2 md:text-base md:leading-[22px]'>Submit</button> */}
            </form>
          </div>
        </div>
      </section>

      <section className="px-4 pb-10 md:px-12 lg:px-24">
        <div className="container-wrapper">
          <BuyAndSellComponent />
        </div>
      </section>
    </div>
  );
}

export default ContactUs;
