import React, {useState} from "react";
import {IoAdd} from "react-icons/io5";
import {FiMinus} from "react-icons/fi";

function QuestionDiv({item}) {
  const [clickstate, setclickstate] = useState(false);
  return (
    <div
      className={`p-4 my-2 border border-[#0161AB] rounded-lg ${
        clickstate ? "max-h-[200px]" : "max-h-[68px]"
      } transition-all duration-500 ease-in-out overflow-hidden`}
    >
      <div className="flex   items-start justify-between">
        <span className="max-w-[90%] text-lg font-medium">{item.question}</span>
        <div className="">
          {!clickstate ? (
            <IoAdd
              className="text-[30px] cursor-pointer"
              onClick={() => {
                setclickstate(!clickstate);
              }}
            />
          ) : (
            <FiMinus
              className="text-[30px] cursor-pointer"
              onClick={() => {
                setclickstate(!clickstate);
              }}
            />
          )}
        </div>
      </div>
      <div className="mt-4"> {item.answer}</div>
    </div>
  );
}

export default QuestionDiv;
