"use client";
import React, {useEffect, useState} from "react";
import aboutComponentCss from "./faq.module.scss";
import dynamic from "next/dynamic";
import Image from "next/image";
import {IoAdd} from "react-icons/io5";
import {MdEmail} from "react-icons/md";
import Select from "react-select";
import {getFaq} from "../services/profile";
import QuestionDiv from "./QuestionDiv";
import {useRouter} from "next/navigation";

const ImageSlider = dynamic(() =>
  import("../components/about/imageSlider", {
    ssr: false,
    loading: () => <div>Loading slider...</div>,
  })
);
const BuyAndSellComponent = dynamic(() =>
  import("../components/about/BuyAndSellComponent", {
    ssr: false,
  })
);

const carouselImages = [
  "/images/carousel_img_1.jpg",
  "/images/carousel_img_2.jpg",
  "/images/carousel_img_3.jpg",
  "/images/carousel_img_4.jpg",
];

function About() {
  const [options, setOptions] = useState([]);
  const [allFaq, setallFaq] = useState([]);
  const router = useRouter();
  useEffect(() => {
    // checkout()
  }, []);
  const checkout = () => {
    const option1 = [
      {value: "chocolate", label: "Chocolate"},
      {value: "strawberry", label: "Strawberry"},
      {value: "vanilla", label: "Vanilla"},
    ];
    setTimeout(() => {
      setOptions(option1);
    }, 3000);
  };

  const getFaqFunc = async () => {
    let response = await getFaq();
    console.log("response getFaqFunc", response);
    if (response.status == 200) {
      setallFaq(response.data.data);
    }
  };
  useEffect(() => {
    getFaqFunc();
  }, []);
  console.log(allFaq, "allFaq");

  return (
    <div className={`${aboutComponentCss.aboutContainer} `}>
      {/* <div className={`${aboutComponentCss.searchContainer} flex items-start `}>
                <section className='my-auto ' >
                    <header className={`my-9 ${aboutComponentCss.mainHeading}`}>

                       
                        <h1 className='md:text-5xl md:font-semibold leading-normal lg:text-6xl'>ReBookit.Club FAQ</h1>
                        <p className='mt-[10px] text-[31px] text-white md:text-4xl lg:leading-14 lg:text-5xl lg:font-bold lg:w-[75%]'>
                            Your guide to buying e-selling Books hassle free
                        </p>
                    </header>
                </section>
            </div> */}

      <div
        className={`${aboutComponentCss.searchContainer} flex items-start justify-start`}
      >
        <section className="container-wrapper w-full">
          <header className={`my-9 ${aboutComponentCss.mainHeading} text-left`}>
            <div>
              <h1 className="text-left md:text-5xl md:font-semibold leading-normal lg:text-6xl">
                ReBookIt.Club FAQ
              </h1>
            </div>
            <p className="  mt-[10px] text-[31px] text-white md:text-4xl lg:leading-14 lg:text-5xl lg:font-bold text-left">
              Your hassle-free guide to buying and selling books.
            </p>
          </header>
        </section>
      </div>

      {/* <section className='mt-10 mb-5 px-2.5 smd:px-[50px] md:text-center md:px-[100px] md:my-[70px] md:text-lg lg:text-[22px] '>
                <section className='container-wrapper'>
                    <article>
                        <p>
                            Your guide to buying e-selling Books hassle free
                        </p>
                    </article>
                </section>
            </section> */}

      <section className="px-2.5 my-7 pb-10 md:px-[50px] lg:px-[100px] lg:pb-[50px]">
        <div className="grid grid-cols-1 md:grid-cols-7 gap-5 container-wrapper">
          {/* Left Side: FAQ Items */}
          <div className="col-span-1 md:col-span-4 lg:col-span-5  w-full ">
            <div className="p-4 border rounded-lg border-[#0161AB]">
              <div className="flex items-center justify-between text-[22px] global_text_linear_gradient">
                <span>ReBookIt.Club FAQ: Your Questions, Answered!</span>
                <IoAdd className="text-[30px]" />
              </div>
              <div className="text-[16px] mt-4">
                Find answers to common questions about buying, selling, orders,
                payments, and more on ReBookIt. Get quick solutions to make your
                book trading experience smooth and hassle-free.
              </div>
            </div>

            {/* Reusable FAQ Items */}
            {allFaq.map((item, i) => (
              <QuestionDiv item={item} />
            ))}
          </div>

          {/* Right Side: Support Box */}
          <div className="col-span-1  md:col-span-3 lg:col-span-2  h-full   ">
            <div className="flex flex-col items-center text-center border border-[#0161AB]  rounded-lg  gap-4 w-full py-10 max-h-[380px]">
              <div className="flex justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="90"
                  height="70"
                  viewBox="0 0 129 106"
                  fill="none"
                  className="md:w-[100px] md:h-[106px]"
                >
                  <path
                    d="M11.7273 52.7727C5.27727 52.7727 0 47.4955 0 41.0455V11.7273C0 5.27727 5.27727 0 11.7273 0H58.6364C65.0864 0 70.3636 5.27727 70.3636 11.7273V41.0455C70.3636 47.4955 65.0864 52.7727 58.6364 52.7727H46.9091V70.3636L29.3182 52.7727H11.7273ZM117.273 87.9545C123.723 87.9545 129 82.6773 129 76.2273V46.9091C129 40.4591 123.723 35.1818 117.273 35.1818H82.0909V41.0455C82.0909 53.9455 71.5364 64.5 58.6364 64.5V76.2273C58.6364 82.6773 63.9136 87.9545 70.3636 87.9545H82.0909V105.545L99.6818 87.9545H117.273Z"
                    fill="url(#paint0_linear_2933_4071)"
                  />
                  <defs>
                    <linearGradient
                      id="paint0_linear_2933_4071"
                      x1="114.605"
                      y1="9.13373"
                      x2="-1.45849"
                      y2="13.4062"
                      gradientUnits="userSpaceOnUse"
                    >
                      <stop stopColor="#211F54" />
                      <stop offset="1" stopColor="#0161AB" />
                    </linearGradient>
                  </defs>
                </svg>
              </div>

              <div>
                <p className="text-[22px] font-semibold">
                  Do You Have More Questions?
                </p>
                <p className="text-[14px] text-gray-600">
                  Find answers to common queries or reach out to our support
                  team for further assistance. We're here to help!
                </p>
              </div>

              <div>
                <button
                  className="flex items-center py-[15.5px] px-[30px] text-[13px] font-semibold leading-[18px] text-white rounded-full global_linear_gradient md:text-base md:leading-[22px]"
                  onClick={() => router.push("/contact-us")}
                >
                  <MdEmail />
                  <span className="ml-2">Email Support</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="px-2.5 pb-10 md:px-[50px] lg:px-[100px] lg:pb-[50px]">
        <section className="container-wrapper">
          <BuyAndSellComponent />
        </section>
      </section>
    </div>
  );
}

export default About;
