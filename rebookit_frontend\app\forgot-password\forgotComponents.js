"use client";
import { useForm } from "react-hook-form";
import dynamic from "next/dynamic";
import forgotPassCss from "./forgotPass.module.scss";

// components
const CustomSlider = dynamic(() => import("@/app/components/common/Slider"));
const BuyAndSellComponent = dynamic(() =>
  import("@/app/components/about/BuyAndSellComponent")
);

import dummyImage from "@/public/test.jpeg";

import Link from "next/link";
import Image from "next/image";

import { RiArrowLeftSLine } from "react-icons/ri";
import { USER_ROUTES } from "../config/api";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { forgotPassword, verifyForgetPassword } from "../services/auth";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { changeforgetPasswordData } from "../redux/slices/storeSlice";
import SubmitButton from "../components/common/SubmitButton";
import login1 from "@/public/images/login1.png";
import login2 from "@/public/images/login2.png";
import { IoEyeSharp } from "react-icons/io5";
import { FaRegEyeSlash } from "react-icons/fa";

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const {
    register,
    watch,
    formState: { errors },
    getValues,
    handleSubmit,
    setValue,
    reset,
    setError,
  } = useForm();

  const Images =  [login1, login2];;
  const [passwordView, setpasswordView] = useState(false);
  const [isOTPSent, setisOTPSent] = useState(false);
  const [timer, setTimer] = useState(0);
  const [intervalId, setIntervalId] = useState(null);

  const dispatch = useDispatch();

  useEffect(() => {
    return () => clearInterval(intervalId);
  }, [intervalId]);

  const startResendTimer = () => {
    if (intervalId) clearInterval(intervalId); // clear any existing timer

    setTimer(60); // set 1 minute (60 seconds)
    const id = setInterval(() => {
      setTimer((prev) => {
        if (prev <= 1) {
          clearInterval(id);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    setIntervalId(id);
  };
  

  const sendOtpFunc = async (email) => {
    let forgetPassword = await forgotPassword({ email: email });
    if (forgetPassword.status == 200) {
      setisOTPSent(true);
      toast.success("Code sent to your email");
      startResendTimer();
    }
  };
  const onSubmit = async (data) => {
    // console.log("Form submitted:", data);
    if (isLoading) return; // prevents multiple calls
    setIsLoading(true);
    try {
      // api call
      if (isOTPSent) {
        let verifyResponse = await verifyForgetPassword({
          email: watch().email,
          otp: watch().password,
        });
        // console.log("verifyResponse", verifyResponse);
        if (verifyResponse.status == 200) {
          toast.success("Verification successfull");

          dispatch(
            changeforgetPasswordData({
              email: watch().email,
              otp: watch().password,
            })
          );
          router.push("/setpassword");
        }
      } else {
        sendOtpFunc(data.email);
      }
      // fetch(USER_ROUTES.FORGOT_PASSWORD, {
      //     method: "POST",
      //     headers: {
      //         "Content-Type": "application/json"
      //     },
      //     body: JSON.stringify(data)
      // }).then(async res => {
      //     const response = await res.json();

      //     if (!response.error) {
      //         toast.success("Code sent to your email")
      //         router.push(`/verify-code?email=${data.email}`)
      //     } else {
      //         toast.error(response.message || "No Email Found!")
      //     }
      // })
    } catch (error) {
      console.log("error", error);
    } finally {
      setIsLoading(false);
    }
  };

  // verifyForgetPassword

  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
  };

  return (
    <div className={`${forgotPassCss.loginContainer}`}>
      <section className="container-wrapper md:flex md:justify-between md:flex-wrap  ">
        <Link href={"/login"} className="block md:hidden">
          {" "}
          <h2 className="flex justify-start items-center text-[#313131]">
            {" "}
            <RiArrowLeftSLine size={35} className="mr-2" /> Back to Login
          </h2>{" "}
        </Link>

        <section className="md:w-6/12 md:min-w-[600px] md:max-w-[700px] md:m-auto">
          <div className={`${forgotPassCss.imageContainer}`}>
            {/* <LoginSlider ImageSlider={Images} /> */}

            <CustomSlider sliderSettings={settings}>
              {Images.map((image, idx) => (
                <Image
                  key={idx}
                  src={image}
                  alt="login page images"
                  className="h-full w-full rounded-2xl p-1"
                />
              ))}
            </CustomSlider>
          </div>
        </section>

        <section className="md:w-5/12 my-5 lg:mt-20 md:min-w-[600px] md:m-auto">
          <Link href={"/login"} className="hidden md:block md:ml-[-10px]">
            {" "}
            <h2 className="flex justify-start items-center text-[#313131]">
              {" "}
              <RiArrowLeftSLine size={35} className="mr-2" /> Back to Login
            </h2>{" "}
          </Link>

          <h2 className="text-[18px] font-semibold md:text-[40px] md:font-semibold">
            Forgot your password?
          </h2>
          <p className="mt-3 font-extralight text-[14px] md:text-[18px] md:w-10/12">
            Don’t worry, happens to all of us. Enter your email below to recover
            your password
          </p>

          <form
            onSubmit={handleSubmit(onSubmit)}
            className="text-[#1C1B1F] my-5"
          >
            {/* Email Input */}
            {!isOTPSent ? (
              <div className="relative py-2 h-[90px] md:h-[100px]">
                <fieldset className={` ${forgotPassCss.fields}`}>
                  <legend className="text-[14px] font-medium px-[7px]">
                    Email
                  </legend>
                  <label htmlFor="email" className="sr-only">
                    Email
                  </label>
                  <input
                    id="email"
                    type="email"
                    autoComplete="off"
                    {...register("email", {
                      required: "Email is required",
                      pattern: {
                        value: /^\S+@\S+\.\S+$/,
                        message: "Invalid Email address",
                      },
                    })}
                    className="w-full text-[13px] md:text-[17px] outline-none border-none"
                    aria-invalid={!!errors.email}
                    aria-describedby="email-error"
                  />
                </fieldset>

                {errors.email && (
                  <p
                    id="email-error"
                    className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0"
                  >
                    {errors.email.message}
                  </p>
                )}
              </div>
            ) : (
              <>
                {isOTPSent && (
                  <p className="mt-1 mb-[-15px] text-right cursor-pointer">
                    <span
                      className="text-[linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);] text-[14px] underline "
                      onClick={() => {
                        setValue("email", "");
                        setisOTPSent(false);
                      }}
                    >
                      {" "}
                      Change Email
                    </span>
                  </p>
                )}
              </>
            )}

            {isOTPSent && (
              <div className="relative h-[90px] md:h-[100px] mt-[14px]">
                <fieldset className={` ${forgotPassCss.fields}`}>
                  <legend className="text-[14px] font-medium px-[7px]">
                    Enter Code
                  </legend>
                  <label htmlFor="password" className="sr-only">
                    Enter Code
                  </label>
                  <input
                    id="password"
                    type={passwordView ? "text" : "password"}
                    autoComplete="off"
                    {...register("password", {
                      required: "Valid Password is Required",
                      maxLength: { value: 6, message: "Max length is 6" },
                    })}
                    className="w-full text-[13px] md:text-[17px] outline-none border-none"
                    aria-invalid={!!errors.password}
                    aria-describedby="password-error"
                  />
                  <div className="mr-2">
                    {passwordView ? (
                      <FaRegEyeSlash className=" cursor-pointer" onClick={() => setpasswordView(false)} size={20} />
                    ) : (
                      <IoEyeSharp className="cursor-pointer" onClick={() => setpasswordView(true)} size={20} />
                    )}
                  </div>
                </fieldset>
                <p className="mt-2">
                  Didn't Receive a Code?
                  {timer > 0 ? (
                    <span className="text-gray-500 ml-2">
                      Resend in {timer}s
                    </span>
                  ) : (
                    <span
                      className="mx-1 text-[#211f54] text-[16px] underline "
                      // className="mx-1 text-[linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);] text-[16px] underline "
                      onClick={() => sendOtpFunc(watch().email)}
                    >
                      Resend
                    </span>
                  )}
                </p>

                {errors.password && (
                  <p
                    id="password-error"
                    className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0"
                  >
                    {errors.password.message}
                  </p>
                )}
              </div>
            )}

            {/* Submit Button */}
            {isOTPSent ? (
              //   <button
              //     type="submit"
              //     className={`${forgotPassCss.submitButton} my-2`}
              //   >
              //     Verify OTP
              //   </button>
              <SubmitButton
                isLoading={isLoading}
                type="submit"
                btnAction={null} // because submit handled by react-hook-form
                InnerDiv={() => (
                  <span className={`${forgotPassCss.submitButton} my-2`}>
                    Verify OTP
                  </span>
                )}
              />
            ) : (
              //   <button
              //     type="submit"
              //     className={`${forgotPassCss.submitButton} my-2`}
              //   >
              //     Send OTP
              //   </button>
              <SubmitButton
                isLoading={isLoading}
                type="submit"
                btnAction={null} // because submit handled by react-hook-form
                InnerDiv={() => (
                  <span className={`${forgotPassCss.submitButton} my-2`}>
                    Send OTP
                  </span>
                )}
              />
            )}
          </form>
        </section>

        <section className="my-5 md:my-0 md:w-12/12">
          <BuyAndSellComponent />
        </section>
      </section>
    </div>
  );
}
