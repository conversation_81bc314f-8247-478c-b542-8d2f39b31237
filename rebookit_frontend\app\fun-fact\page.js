"use client";

import React from "react";
import Image from "next/image";
import owl from "@/public/images/fun-fact/owl.svg";
import owlFade from "@/public/images/fun-fact/owlFade.svg";

const facts = [
  {
    title: "Fact: The World’s Most Overdue Library Book",
    description:
      "In 1955, a library book was returned to the Sidney Sussex College library in Cambridge 221 years late. It was borrowed in 1734 by Colonel <PERSON> (son of Britain’s first Prime Minister) and discovered in his family’s estate. No fine was charged! 😄",
    image: owl,
    fadeImg: owlFade,
  },
  {
    title: "Fact: There’s a Book with No Words",
    description:
      "Codex Seraphinianus (1981) by <PERSON> is a surreal encyclopedia written in an imaginary language, filled with bizarre illustrations of impossible plants, animals, and machines. It’s designed to mimic the feeling of a child encountering books they can’t yet read.",
    image: owl,
    fadeImg: owlFade,
  },
  {
    title: "Fact: The Smallest Book in the World",
    description:
      "<PERSON>y Ted from Turnip Town is a 0.07 mm x 0.10 mm book etched on a silicon wafer using electron beams. It requires an electron microscope to read! Meanwhile, the largest book (by size) is This the Prophet Mohamed in Dubai—16 ft x 26 ft when open.",
    image: owl,
    fadeImg: owlFade,
  },
  {
    title: "Fact: <PERSON>’s Record-Breaking Leak",
    description:
      "<PERSON> and the Deathly Hallows (2007) broke piracy records before release. Photographs of all 759 pages were leaked online 3 days early—yet it still sold 15 million copies in 24 hours!",
    image: owl,
    fadeImg: owlFade,
  },
  {
    title: "Fact: A Book You Can “Unlock” with a Key",
    description:
      "S. by J.J. Abrams and Doug Dorst isn’t just a novel—it’s packaged as a library book filled with handwritten notes, postcards, maps, and even a cipher wheel. To read it fully, you must physically unfold its 22 inserts!",
    image: owl,
    fadeImg: owlFade,
  },
  {
    title: "Fact: The Library of Smell",
    description:
      "In Japan, Ginza no Kusuri published scratch-and-sniff picture books for adults featuring scents like sushi, money, and even concrete after rain! 📚✨",
    image: owl,
    fadeImg: owlFade,
  },
];

const FunFact = () => {
  return (
    <div className="relative w-full overflow-hidden">
      {/* Hero Section */}
      <div
        className="relative w-full min-h-[50vh] md:min-h-[88vh] flex items-center px-4 py-12 md:px-8 lg:px-16"
        style={{
          backgroundImage: `linear-gradient(to right, rgba(0, 0, 0, 1) 2%, rgba(0, 0, 0, 0.6) 50%), url("/images/landing1.png")`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundRepeat: "no-repeat",
        }}
      >
        <div className="max-w-6xl  md:px-12 lg:px-16">
          <div className="max-w-3xl">
            <h1 className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-[#ffb800] font-playfair leading-tight mb-4">
              Fact: Stories Live Longer When Shared
            </h1>
            <p className="text-white text-lg sm:text-xl md:text-2xl lg:text-3xl font-[playfair] font-medium leading-relaxed">
              Every time you pass on a book, you’re not just recycling
              paper—you’re reviving imagination, knowledge, and connections.
            </p>
          </div>
        </div>
      </div>

      {/* Facts Grid */}
      <div className="w-full px-4 py-8 sm:px-6 md:px-8 lg:px-16 xl:px-24">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 md:gap-8">
            {facts.map((fact, index) => (
              <div
                key={index}
                className={`flex flex-col rounded-xl overflow-hidden shadow-lg border transition-all duration-300 hover:scale-[1.02] ${
                  index % 2 === 0
                    ? "bg-white border-gray-200"
                    : "bg-[#211F54] border-gray-700"
                }`}
              >
                <div className="p-6 sm:p-8 md:p-10 flex flex-col h-full">
                  <div className="mb-4">
                    <h3
                      className={`text-xl sm:text-2xl font-bold ${
                        index % 2 === 0 ? "text-[#2c5282]" : "text-white"
                      }`}
                    >
                      {fact.title}
                    </h3>
                  </div>

                  <div className="flex-1 relative mb-4">
                    <p
                      className={`text-base sm:text-lg leading-relaxed ${
                        index % 2 === 0 ? "text-gray-700" : "text-gray-300"
                      }`}
                    >
                      {fact.description}
                    </p>
                  </div>

                  <div className="flex justify-end">
                    <div className="w-24 h-24 sm:w-28 sm:h-28 opacity-40">
                      <Image
                        src={fact.fadeImg}
                        alt="Decorative owl"
                        layout="responsive"
                        width={100}
                        height={100}
                      />
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Decorative elements
      <div className="absolute top-[40vh] -right-20 w-40 h-40 sm:w-60 sm:h-60 opacity-10 rotate-12">
        <Image src={owl} alt="Decorative owl" layout="responsive" />
      </div>
      <div className="absolute top-[45vh] -left-20 w-32 h-32 sm:w-48 sm:h-48 opacity-10 -rotate-12">
        <Image src={owl} alt="Decorative owl" layout="responsive" />
      </div> */}
    </div>
  );
};

export default FunFact;
