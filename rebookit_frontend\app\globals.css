@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");
@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: "Poppins", sans-serif;
  color: #212a30;
}

/* Slider Dots Customized */

.custom_slider_dots .slick-dots li div {
  width: 40px;
  height: 5px;
  background-color: #9ca3af;
  border-radius: 9999px;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.custom_slider_dots li.slick-active div {
  background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
  width: 40px;
  height: 5px;
}

.custom_inside_dots {
  display: flex !important;
  justify-content: center;
  gap: 2px;
}

.custom_inside_dots li {
  margin: 0 !important;
  width: 10px !important;
  height: 10px !important;
}

.custom_inside_dots li button {
  padding: 0 !important;
}

.custom_inside_dots li button::before {
  color: white !important;
  opacity: 0.5;
}

.custom_inside_dots li.slick-active button::before {
  /* color: white !important; */
  opacity: 1;
}

/* Slider Dots Customized */

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
}

/* react select  */

.css-13cymwt-control {
  border: none;
  outline: none;
  width: 100% !important;
}

.css-1im77uy-control {
  width: 100%;
  outline: none;
  border: none !important;
}

/* Chrome, Safari, Edge, Opera */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

button {
  cursor: pointer;
}

/* Linear Gradient */

.global_linear_gradient {
  background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
}

.global_black_linear_gradient {
  background: linear-gradient(268deg, #313131 11.09%, #5a5a5a 98.55%);
}

.global_text_linear_gradient {
  background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Linear Gradient */

/* Scrollbar */

.no_scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

/* Scrollbar */

/* Conatiner Wrapper */

.container-wrapper {
  max-width: 1500px !important;
  margin: auto;
}

/* Conatiner Wrapper */

/* Drop Shadow */

.custom_shadow_first {
  box-shadow: 0px 2.706px 10.824px rgba(217, 217, 217, 0.32);
}

/* Drop Shadow */


.gradient-all-round-border {
  position: relative;
  background: linear-gradient(white, white) padding-box,
    linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%) border-box;
  border-color: transparent;
}


