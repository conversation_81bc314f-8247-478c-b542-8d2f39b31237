// styles/Home.module.scss
.bannerContainer {

  .banner_contain {
    display: flex;
    flex-direction: column;
    // flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 20px;

    // .banner {
    //   // width: 370px;
    //   width: 100%;
    //   height: 236px;
    //   flex-shrink: 0;
    //   border-radius: 52px;
    //   overflow: hidden;

    //   div {
    //     height: 400px;
    //     // background-color: pink;
    //   }

    //   img {
    //     transform: translate(269px, -33px);
    //   }

    //   button {
    //     width: 164.938px;
    //     height: 41.234px;
    //     flex-shrink: 0;
    //     border-radius: 25.375px;
    //     border: 1.586px solid var(--Linear, #211F54);
    //     background: #FFF;
    //     box-shadow: 0px 3.172px 3.172px 0px rgba(255, 255, 255, 0.32) inset;
    //     font-size: 14px;
    //   }
    // }
  }



  @media (min-width: 769px) {
    padding: 0 50px;

    .banner_contain {

      justify-content: space-between;

      .banner {
        // width: 405px;
        // height: 387px;
        width: 394px;
        height: 376px;
        flex-shrink: 0;

        img {
          transform: translate(288px, -32px)scale(1.2);
        }
      }
    }
  }

  @media (min-width: 1024px) {
    padding: 0 100px;
  }

}

.bookTypeContainer {

  background-color: #fff;
  // min-height: 30vh;
  padding: 0 10px;


  .typeContainer {

    display: flex;
    // flex-wrap: wrap;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;


    .bookType {
      // width: 370px;
      width: 100%;
      height: 236px;
      flex-shrink: 0;
      border-radius: 52.612px;
      overflow: hidden;


      // button {
      //   border-radius: 52.612px;
      //   background: #211F54;
      //   border-radius: 19.692px;
      //   border: 1.231px solid var(--Linear, #211F54);
      //   background: #FFF;
      //   box-shadow: 0px 2.462px 2.462px 0px rgba(255, 255, 255, 0.32) inset;
      //   width: 128px;
      //   height: 32px;
      //   flex-shrink: 0;
      // }
    }
  }

  @media (min-width: 769px) {

    padding: 0 50px;

    .typeContainer {

      justify-content: space-between;
      flex-direction: row;
      gap: 40px;

      .bookType {

        // width: 600px;
        width: 48%;
        height: 309px;
        flex-shrink: 0;
        border-radius: 69px;



        button {
          width: 232.856px;
          height: 65.886px;
          flex-shrink: 0;
          font-size: 18px;
          border-radius: 40.537px;
          box-shadow: 0px 5.067px 5.067px 0px rgba(255, 255, 255, 0.32) inset;
          margin-top: 23px;
        }
      }
    }
  }

  @media (min-width: 1200px) {
    padding: 0 100px;

    .typeContainer {

      justify-content: space-between;
      // flex-direction: row;
      // gap: 40px;

      .bookType {

        // width: 600px;
        // width: 48%;
        height: 309px;
        flex-shrink: 0;
        border-radius: 69px;
        padding: 60px 60px;

        &>div {
          gap: 14px;

          h2 {
            font-size: 38px;
            line-height: 48px;
          }

          p {
            font-size: 20px;
          }
        }

        button {
          width: 232.856px;
          padding: 22px 0;
          flex-shrink: 0;
          border-radius: 40.537px;
          font-size: 20px;
          line-height: 20px;
          box-shadow: 0px 5.067px 5.067px 0px rgba(255, 255, 255, 0.32) inset;
          margin-top: 23px;
        }
      }
    }
  }

}

.infoContainer {

  height: 40vh;
  padding: 10px;
  overflow: hidden;

  .logo {
    width: 75.688px;
    height: 150px;
    flex-shrink: 0;
    aspect-ratio: 75.69/116.00;
  }

  .cloudDiv {

    background-image: url("/landing/cloud.svg");
    background-position:
      0;
    background-repeat: no-repeat;
    background-size: 100% 83%;
    height: 26vh;
    padding:
      16px;
    position: absolute;
    top: 104px;
    right: -100px;


  }



  @media (min-width: 650px) {
    display: none;
  }


  @media (min-width: 769px) {
    display: block;
    padding: 40px 100px;
    height: 75vh;
    margin: 20px 0;

    .logo {
      width: 276px;
      height: 423px;
      flex-shrink: 0;
      aspect-ratio: 92/141;
    }

    .cloudDiv {
      background-image: url("/landing/cloud.svg");
      background-position: 7px 13px;
      background-repeat: no-repeat;
      background-size: 98% 99%;
      height: 100%;
      padding: 8px;
      position: absolute;
      top: 128px;
      right: -80px;
      transform: rotate(3deg);

    }
  }

  @media (min-width: 1200px) {

    display: block;
    padding: 40px 100px;
    height: 75vh;
    margin: 20px 0;

    .logo {
      width: 276px;
      height: 423px;
      flex-shrink: 0;
      aspect-ratio: 92/141;
    }

    .cloudDiv {
      background-image: url("/landing/cloud.svg");
      background-position: 7px 13px;
      background-repeat: no-repeat;
      background-size: 80% 89%;
      height: 100%;
      padding: 8px;
      position: absolute;
      top: 128px;
      right: -168px;
      transform: rotate(3deg);

    }
  }
}

// Do you know section

.infoSection {

  &>div {
    .cloudDiv {
      padding: 16px;
      position: absolute;
      // bottom: 0;
      top: 30px;
      left: 95px;
      width: 80vw;
      /* Responsive width */
      max-width: 318px;
      /* Optional: limit max size */
      aspect-ratio: 318 / 173;
      /* Maintain aspect ratio of original */
      z-index: 2;
      display: flex;
      justify-content: center;
      align-items: center;

      &::before {
        content: '';
        background-image: url("/landing/cloud.svg");
        background-position: center;
        background-repeat: no-repeat;
        background-size: contain;
        /* or cover based on visual preference */
        transform: rotate(10deg);
        transform-origin: center;
        /* Prevent overflow when rotating */
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
      }

      @media (max-width: 340px) {
        p {
          font-size: 10px;
        }
      }

      @media (min-width: 768px) {
        &::before {
          transform: rotate(0);
        }
      }
    }
  }
}