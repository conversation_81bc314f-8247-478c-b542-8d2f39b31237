
'use client';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_<PERSON><PERSON> } from "next/font/google";
import "./globals.css";
import { Suspense, useEffect, useState } from 'react';
import dynamic from "next/dynamic";
import { Provider } from "react-redux";
import {store} from "./redux/store";
import {persistor} from "./redux/store"
import "slick-carousel/slick/slick.css";
import "slick-carousel/slick/slick-theme.css";
import LocationUpdater from "@/app/utils/geolocation"
import { ToastContainer } from "react-toastify";


import { getToken } from './utils/utils';
import { usePathname, useRouter } from "next/navigation";
import ScrollToTop from "./components/ScrollToTop";
import { PersistGate } from "redux-persist/integration/react";
import { destroyChatwoot, initChatwoot } from "./utils/ChatwootWidget";

const loginRoutes = [
  '/login',
  "/signup",
  "/verify-code",
  '/create-password',
  '/forgot-password',
  "/verify-email",
  "/setpassword"
]

const publicRoutes = [
  "/",
  "/search",
  '/seller',
  "/about",
  "/contact-us",
  "/faq",
  // "/profile",
  "/book-detail",
  "/privacy-policy",
  "/community"
];

const Header = dynamic(() => import('@/app/components/layout/Header/Header'), {
  suspense: true,
});
const Footer = dynamic(() => import('@/app/components/layout/Footer/Footer'), {
  suspense: true,
});


export default function RootLayout({ children }) {
  const pathname = usePathname();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  const isPublicRoute = publicRoutes.includes(pathname);
  const isLoginRoute = loginRoutes.includes(pathname);

  useEffect(() => {
    const auth = getToken();
    
    // Initialize Chatwoot if auth exists and it's not a login route
    if (auth) {
      const websiteToken = process.env.NEXT_PUBLIC_CHATWOOT_WEBSITE_TOKEN;
      if (websiteToken) {
        const userData = JSON.parse(localStorage.getItem('userData'));
          initChatwoot(websiteToken, userData);
      }
    } else {
      // Remove chatwoot script if it exists
      destroyChatwoot();
    }

    // if auth and public route than don't open login routes
    if (auth && isLoginRoute) {
      router.replace("/")
      setIsLoading(false)
      return;
    }

    // if not auth, and it's a login route, stop loading
    if (!auth && isLoginRoute) {
      setIsLoading(false)
      return;
    }

    // If auth exists or it's a public route, stop loading
    if (auth || isPublicRoute) {
      setIsLoading(false);
      return;
    }

    // If no auth and not public route, redirect
    if (!auth && !isPublicRoute && !isLoginRoute) {
    
      router.replace('/login');
    }
  }, [pathname]);


  if (isLoading) {
    return (
      <html lang="en">
        <body className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4 text-lg">Loading...</p>
          </div>
        </body>
      </html>
    );
  }

  return (
    <html lang="en">
      
      <Provider store={store}>
         <PersistGate loading={null} persistor={persistor}>
        <body className="min-h-[50vh]">
          <ToastContainer className="z-[999] " />
          {/* <LocationUpdater /> */}
          <Header />
          <ScrollToTop />
          {children}
          <Footer />
        </body>
        </PersistGate>
      </Provider>
    </html>
  );
}
