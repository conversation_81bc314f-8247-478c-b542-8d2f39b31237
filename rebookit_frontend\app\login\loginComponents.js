"use client";
import {useForm} from "react-hook-form";
import dynamic from "next/dynamic";
import loginCss from "./login.module.scss";

// components
const CustomSlider = dynamic(() => import("@/app/components/common/Slider"));
const BuyAndSellComponent = dynamic(() =>
  import("@/app/components/about/BuyAndSellComponent")
);

import dummyImage from "@/public/test.jpeg";
import login1 from "@/public/images/login1.png";
import login2 from "@/public/images/login2.png";

import {IoEyeSharp} from "react-icons/io5";
import {FaRegEyeSlash} from "react-icons/fa";
import {useEffect, useState} from "react";
import Link from "next/link";
import Image from "next/image";
import {USER_ROUTES} from "../config/api";
import {useRouter} from "next/navigation";
import {toast} from "react-toastify";
import {getToken, setInLocalStorage, setToken} from "../utils/utils";
import {login} from "../services/auth";
import SubmitButton from "../components/common/SubmitButton";

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const {
    register,
    watch,
    formState: {errors},
    getValues,
    handleSubmit,
    setValue,
    reset,
    setError,
  } = useForm();

  useEffect(() => {
    const token = getToken();
    if (token) {
      router.replace("/profile");
    }
  }, []);

  const Images = [login1, login2];

  const [passwordView, setPasswordView] = useState(false);

  const onSubmit = async (data) => {
    if (isLoading) return; // prevents multiple calls
    setIsLoading(true);
    try {
      data.email = data.email.toLowerCase();

      // api call
      // let loginresponse= login(data)
      // console.log("loginresponse",loginresponse)
      // fetch(USER_ROUTES.lOGIN, {
      //     method: "POST",
      //     headers: {
      //         "Content-Type": "application/json"
      //     },
      //     body: JSON.stringify(data)
      // }).then(async res => {
      //     const response = await res.json();
      //     let authData = response
      //     console.log("response auth",response)
      //     if (authData?.success) {
      //         setToken(authData?.token)
      //         toast.success("Logged in successfully!")
      //         router.push("/")
      //     } else {
      //         toast.error(response?.message || "Incorrect credentials! Try again")
      //     }
      // })
      let response = await login(data);
      console.log("resposen", response);
      response = response.data;
      if (response?.success) {
        setToken(response?.token);
        toast.success("Logged in successfully!");
        // localStorage.setItem("userData",JSON.stringify(response.userData));
        
        setInLocalStorage("userData",response.userData)
        if (window.location.href.includes("redirect")) {
          let url = `${window.location.search}`;
          url = url.replace("?redirect=/", "");

          router.push(url);
        } else {
          router.push("/");
        }
      }
      //    else {
      //     toast.error(
      //       response?.message || "Something Went Wrong Please try again"
      //     );
      //   }
    } catch (error) {
      console.log("error", error);
    } finally {
      setIsLoading(false);
    }
  };

  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
  };

  return (
    <div className={`${loginCss.loginContainer}`}>
      <section className="container-wrapper md:flex md:justify-between md:flex-wrap ">
        <section className="md:w-6/12 md:min-w-[600px] md:max-w-[700px] md:m-auto">
          <div className={`${loginCss.imageContainer}`}>
            {/* <LoginSlider ImageSlider={Images} /> */}

            <CustomSlider sliderSettings={settings}>
              {Images.map((image, idx) => (
                <Image
                  key={idx}
                  src={image}
                  alt="login page images"
                  className="h-full w-full rounded-2xl p-1"
                />
              ))}
            </CustomSlider>
          </div>
        </section>

        <section className="md:w-5/12 lg:mt-20 md:min-w-[600px] md:m-auto">
          <h2 className="text-[18px] font-semibold md:text-[40px] md:font-semibold">
            Login
          </h2>
          <p className="mt-3 font-extralight text-[14px] md:text-[16px]">
            Login to access your <strong>Rebookit </strong> account
          </p>

          <form
            onSubmit={handleSubmit(onSubmit)}
            className="text-[#1C1B1F] my-4"
          >
            {/* Email Input */}
            <div className="relative py-2 h-[90px] md:h-[100px]">
              <fieldset className={` ${loginCss.fields}`}>
                <legend className="text-[14px] font-medium px-[7px]">
                  Email
                </legend>
                <label htmlFor="email" className="sr-only">
                  Email
                </label>
                <input
                  id="email"
                  type="email"
                  autoComplete="off"
                  {...register("email", {
                    required: "Email is required",
                    pattern: {
                      value: /^\S+@\S+\.\S+$/,
                      message: "Invalid Email address",
                    },
                  })}
                  className="w-full text-[13px] md:text-[17px] outline-none border-none"
                  aria-invalid={!!errors.email}
                  aria-describedby="email-error"
                />
              </fieldset>
              {errors.email && (
                <p
                  id="email-error"
                  className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0"
                >
                  {errors.email.message}
                </p>
              )}
            </div>

            {/* Password Input */}
            <div className="relative h-[90px] md:h-[100px]">
              <fieldset className={` ${loginCss.fields}`}>
                <legend className="text-[14px] font-medium px-[7px]">
                  Password
                </legend>
                <label htmlFor="password" className="sr-only">
                  Password
                </label>
                <input
                  id="password"
                  type={passwordView ? "text" : "password"}
                  autoComplete="off"
                  {...register("password", {
                    required: "Valid Password is Required",
                  })}
                  className="w-full text-[13px] md:text-[17px] outline-none border-none"
                  aria-invalid={!!errors.password}
                  aria-describedby="password-error"
                />
                <div
                  className="absolute top-[29px] right-[17px]"
                  onClick={() => setPasswordView(!passwordView)}
                  role="button"
                  tabIndex={0}
                  aria-label={passwordView ? "Hide password" : "Show password"}
                  onKeyDown={(e) =>
                    e.key === "Enter" && setPasswordView(!passwordView)
                  }
                >
                  {passwordView ? (
                    <FaRegEyeSlash className="cursor-pointer" size={20} />
                  ) : (
                    <IoEyeSharp className="cursor-pointer" size={20} />
                  )}
                </div>
              </fieldset>
              {errors.password && (
                <p
                  id="password-error"
                  className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0"
                >
                  {errors.password.message}
                </p>
              )}
            </div>

            {/* Remember Me + Forgot Password */}
            <div className="mb-4 flex items-end justify-end">
              <Link href="/forgot-password">
                <p className="text-[#FF8682]  text-[12px] md:text-[16px] font-medium cursor-pointer">
                  Forgot Password
                </p>
              </Link>
            </div>

            {/* Submit Button */}
            {/* <button type="submit" className={`${loginCss.submitButton}`}>
              Login
            </button> */}
            <SubmitButton
              isLoading={isLoading}
              type="submit"
              btnAction={null} // because submit handled by react-hook-form
              InnerDiv={() => (
                <span className={`${loginCss.submitButton}`}>Login</span>
              )}
            />

            {/* Sign up prompt */}
            <p className="text-center text-[12px] md:text-[16px] my-3">
              Don’t have an account?{" "}
              <Link href="/verify-email">
                <span className="text-[#FF8682] font-medium cursor-pointer">
                  Sign up
                </span>
              </Link>
            </p>
          </form>
        </section>
        <section className="my-5 md:my-0 md:w-12/12">
          <BuyAndSellComponent />
        </section>
      </section>
    </div>
  );
}
