// pages/404.js
"use client"
import Link from 'next/link';
import styles from '@/app/styles/NotFound.module.scss'; // optional CSS
import { RotatingSquare } from 'react-loader-spinner';

export default function Custom404() {
  return (
    <div className={`${styles.container} min-h-[70vh]`}>

      <section className='m-auto w-full flex justify-center'>
        <RotatingSquare
          visible={true}
          height="100"
          width="100"
          color="#4fa94d"
          ariaLabel="rotating-square-loading"
          wrapperStyle={{}}
          wrapperClass=""
        />
      </section>
      <section>

        <h1>404 - Page Not Found</h1>
        <p>Oops! Looks like this page doesn't exist.</p>
        <Link href="/">Go back home</Link>
      </section>

    </div>
  );
}



