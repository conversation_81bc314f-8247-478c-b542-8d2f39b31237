"use client";
import dynamic from "next/dynamic";
import Link from "next/link";

import homeCss from "@/app/home.module.scss";
import Image from "next/image";
// import didYouKnowImage from "@/public/images/didyouknow/didYouKnowImage.svg";
import didYouKnowOwl from "@/public/images/didyouknow/didYouKnowOwl.svg";
import didYouKnowCloud from "@/public/images/didyouknow/cloud.svg";

// components
const BookSearchComponent = dynamic(() =>
  import("./components/landingPage/search/search")
);
const BannerAd = dynamic(() => import("./components/common/AdBanner/Banner"));
const RebookitStory = dynamic(() =>
  import("./components/landingPage/rebookit_story/story")
);
const MemberResource = dynamic(() =>
  import("./components/landingPage/member_Resouce/memberResource")
);
const FeaturedBooks = dynamic(() =>
  import("./components/landingPage/featured_books/featuredBooks")
);
const RebookerMonth = dynamic(() =>
  import("./components/landingPage/rebookerOfMonth/rebookerMonth")
);
const TutorList = dynamic(() =>
  import("./components/landingPage/tutor_list/tutorList")
);
const Subscription = dynamic(() =>
  import("./components/landingPage/subscription/subscription")
);
const Community = dynamic(() =>
  import("./components/landingPage/community/community")
);
const Testimonials = dynamic(() =>
  import("./components/landingPage/testimonials/testimonials")
);
const BuyAndSellComponent = dynamic(() =>
  import("./components/about/BuyAndSellComponent")
);
const BannerList = dynamic(() =>
  import("./components//landingPage/bannerList/bannerList")
);

import doYouKnow from "@/public/landing/doYouKnow.svg";
import logo from "@/public/landing/fine_logo.svg";
import cloud from "@/public/landing/cloud.svg";
import {useDispatch} from "react-redux";
import {bookSubcategories} from "./config/constant";
import {useRouter} from "next/navigation";
import {updateCarrabianNonCarrabianSearch} from "./redux/slices/storeSlice";

// export const metadata = {
//   title: "Rebookit",
//   description: "Resell Your Books",
// };

export default function Home() {
  const dispatch = useDispatch();
  const route = useRouter();
  const saggregateBookType = (text) => {
    if (text == "carrabian") {
      return bookSubcategories.carrabian.map((item) => item._id);
    } else {
      return bookSubcategories.nonCarrabian.map((item) => item._id);
    }
  };

  const bookTypes = [
    {
      heading: "Non-textbooks",
      text: "Connecting readers and sellers.",
      link: "/",
      backgroundColor: "#211F54",
      iconColor: "#C5E5FF",
      searchType: "noncarrabian",
    },
    {
      heading: "Caribbean books",
      text: "Connecting readers and sellers.",
      link: "/",
      backgroundColor: "#4d7906",
      iconColor: "#ffd464",
      searchType: "carrabian",
    },
  ];

  const searchBook = (text) => {
    let bookTypeIdArr = saggregateBookType(text);
    dispatch(
      updateCarrabianNonCarrabianSearch({name: text, value: bookTypeIdArr})
    );
    route.push(`/search?category=68612cc0ba947189b202a825&booktype=${text}`);
  };

  return (
    <div className="min-h-[70vh]">
      <BookSearchComponent />
      <BannerAd position="" page="" className={" "} />

      <MemberResource />

      <RebookitStory />

      {/* banners  */}
      <section className={`${homeCss.bannerContainer} my-[30px] lg:mt-0 `}>
        <section
          className={`${homeCss.banner_contain} container-wrapper px-2.5 !grid !grid-cols-1 lg:!grid-cols-2 xl:!flex xl:!flex-row  xl:!justify-between md:px-[10px] xl:pb-[40px] xl:pt-[60px]`}
        >
          <BannerList />
        </section>
      </section>

      <TutorList />

      <section className={`${homeCss.bookTypeContainer} my-10 lg:my-[70px]`}>
        <section className={`${homeCss.typeContainer} container-wrapper`}>
          {bookTypes?.map((book, idx) => {
            return (
              <article
                key={idx}
                className={`${homeCss.bookType} relative flex items-end py-[46px] px-[16px]`}
                style={{backgroundColor: book.backgroundColor}}
              >
                <div>
                  <h2 className="text-white font-semibold text-[23px] leading-[30px]">
                    {book.heading}
                  </h2>
                  <p className="text-white mt-[5px] leading-normal text-sm">
                    {book.text}
                  </p>

                  <button
                    onClick={() => searchBook(book.searchType)}
                    className="mt-3.5 cursor-pointer gradient-all-round-border w-[128px] rounded-full border-2 py-2.5 text-[10px] font-semibold leading-[11px] text-[#211F54] gradient-all-round-border"
                  >
                    See books
                  </button>
                </div>

                <svg
                  className="absolute top-0 right-0"
                  width="123"
                  height="173"
                  viewBox="0 0 123 173"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M97.9703 -57.5448C109.443 -71.4851 130.787 -71.4851 142.26 -57.5448L148.035 -50.5272C154.104 -43.1527 163.446 -39.2831 172.952 -40.2063L181.998 -41.0848C199.967 -42.83 215.06 -27.7369 213.315 -9.76753L212.436 -0.721741C211.513 8.7843 215.383 18.1262 222.757 24.1952L229.775 29.9703C243.715 41.4426 243.715 62.7874 229.775 74.2597L222.757 80.0348C215.383 86.1038 211.513 95.4457 212.436 104.952L213.315 113.998C215.06 131.967 199.967 147.06 181.998 145.315L172.952 144.436C163.446 143.513 154.104 147.383 148.035 154.757L142.26 161.775C130.787 175.715 109.443 175.715 97.9703 161.775L92.1952 154.757C86.1262 147.383 76.7843 143.513 67.2783 144.436L58.2325 145.315C40.2631 147.06 25.17 131.967 26.9152 113.998L27.7937 104.952C28.7169 95.4457 24.8474 86.1038 17.4728 80.0348L10.4552 74.2597C-3.48506 62.7874 -3.48508 41.4426 10.4552 29.9703L17.4728 24.1952C24.8473 18.1262 28.7169 8.78432 27.7937 -0.721725L26.9152 -9.76752C25.17 -27.7369 40.2631 -42.83 58.2325 -41.0848L67.2783 -40.2063C76.7843 -39.2831 86.1262 -43.1526 92.1952 -50.5272L97.9703 -57.5448Z"
                    fill={book.iconColor}
                  />
                </svg>
              </article>
            );
          })}
        </section>
      </section>

      <FeaturedBooks />
      <BannerAd position="" page="" />

      {/* <section className={`${homeCss.infoContainer}`}>

        <div className="container-wrapper">

          <div className='flex'>

            <div className="flex md:w-10/12">
              <article className="mt-5 flex">
                <figure>
                  <Image src={logo} alt="logo" className={`${homeCss.logo}`} />
                </figure>
                <div className="h-[10px] w-[10px] rounded-full bg-[#0161AB] relative top-15 left-10 md:h-[50px] md:w-[50px] md:left-[14vw] md:top-[11vh]"></div>
                <div className="h-[15px] w-[15px] rounded-full bg-[#0161AB] relative top-20 left-10 md:left-[4vw] md:h-[30px] md:w-[30px]"></div>
              </article>

              <article className="w-9/12 flex flex-col items-end relative">
                <figure>
                  <Image src={doYouKnow} alt="curiosity" className='md:h-[20%] md:W-[100%] scale-50 md:absolute md:left-50 md:scale-200' />
                </figure>

                <div className={`${homeCss.cloudDiv} flex justify-center items-center relative w-full h-full`}>
                  <p className="text-white text-[8px] ml-5 text-center md:w-9/12 lg:w-6/12 md:text-[11px] xl:text-[18px] md:ml-[26px] lg:ml-[-90px] md:text-left md:rotate-[-3deg]">
                    <span className="text-[#FFC72C]">Fact:</span> Printing textbooks involves using large amounts of paper, water, and energy. On average, it takes approximately 2.5 pounds of wood to produce one pound of paper.
                  </p>
                </div>
              </article>
            </div>


            <footer className="hidden lg:flex justify-end my-5 md:w-2/12">
              <Link href="/" aria-label="View all book categories">
                <svg width="178" height="72" viewBox="0 0 178 72" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M171.629 35.7285C171.629 19.31 157.778 6.00018 140.692 6.00018L36.9361 6.00017C19.8503 6.00017 5.99954 19.31 5.99954 35.7285" stroke="#211F54" strokeWidth="11.679" />
                  <path d="M171.629 35.7285C171.629 19.31 157.827 6.00018 140.802 6.00018L37.412 6.00017" stroke="#0161AB" strokeWidth="11.679" />
                  <path d="M6 35.7285C6 52.147 19.8507 65.4569 36.9365 65.4569H140.693C157.779 65.4569 171.629 52.147 171.629 35.7285" stroke="#EFDC2A" strokeWidth="11.679" />
                  <path d="M37.4121 65.4569H140.802C157.827 65.4569 171.629 52.147 171.629 35.7285" stroke="#0161AB" strokeWidth="11.679" />
                  <path d="M140.693 6L36.937 5.99999" stroke="#FF0009" strokeWidth="11.679" />
                  <path d="M140.693 65.457L36.937 65.457" stroke="#4A8B40" strokeWidth="11.679" />
                  <rect x="11.6016" y="7.93848" width="154.01" height="54.6036" rx="27.3018" fill="white" />
                  <text x="50%" y="50%" dominantBaseline="middle" textAnchor="middle" fontSize="20" fill="#211F54" fontFamily="Poppins, sans-serif">
                    View All
                  </text>
                </svg>
              </Link>
            </footer>
          </div>
        </div>


      </section> */}

      {/* Did you Know Images section */}

      {/* <section className="mt-20 mb-10 px-4 md:px-12 lg:px-24 relative">
        <div className="relative flex overflow-hidden min-h-[210px] sm:min-h-[270px] md:min-h-[350px] lg:min-h-[450px]">
          <div className="relative flex-shrink-0 w-16 h-28 md:w-48 md:h-72 overflow-hidden">
            <Image
              src={logo}
              alt="logo"
              className="object-contain"
              fill
              sizes="33w"
            />
          </div>

          <div className=" w-2.5 h-2.5 bg-gradient-to-r from-[#211F54] to-[#0161AB] rounded-full lg:w-5 lg:ml-10 lg:h-5 xl:w-8 xl:h-8" />
          <div className=" mt-3 w-5 h-5 bg-gradient-to-r from-[#211F54] to-[#0161AB] rounded-full lg:ml-10 lg:w-10 lg:h-10 xl:w-16 xl:h-16" />

          <div
            className="absolute
             top-8 left-17 flex items-center justify-center p-4 w-[80vw] max-w-[218px] aspect-[318/173] z-20
                    min-w-[100px] sm:min-w-[450px] sm:max-w-[218px] sm:top-2 sm:min-h-[200px]
                    md:min-w-[550px] md:min-h-[280px] md:left-20
                    lg:min-w-[570px] lg:min-h-[320px] lg:left-88 lg:top-12
                    xl:min-w-[700px] xl:min-h-[370px]"
          >
            <div className="absolute inset-0 bg-[url('/landing/cloud.svg')] bg-center bg-no-repeat bg-contain rotate-12 md:rotate-0 -z-10" />
            <p
              className="w-4/5 text-[9px] leading-tight capitalize text-center text-white
                    md:text-sm md:leading-7
                    lg:text-xl lg:leading-8
                    xl:text-2xl"
            >
              <span className="font-semibold text-[#FFC72C]">Fact:</span>{" "}
              Printing textbooks involves using large amounts of paper, water,
              and energy. On average, it takes approximately 2.5 pounds of wood
              to produce one pound of paper.
            </p>
          </div>
        </div>
        
        <div
          className="absolute overflow-hidden right-0 top-[-40px]
                  w-28 h-20
                  sm:left-44 sm:top-[-64px]
                  md:left-[310px] md:w-36 md:h-28 md:top-[-80px]
                  lg:left-[420px] lg:w-52 lg:h-40 lg:top-[-144px]
                  xl:left-1/3 xl:top-[-120px]"
        >
          <Image
            src={doYouKnow}
            alt="Do you know?"
            className="object-cover"
            fill
            sizes="33w"
          />
        </div>
        <footer className="hidden lg:block absolute right-25 top-[-96px]">
          <Link href="/fun-fact" aria-label="View all book categories">
            <svg
              width="178"
              height="72"
              viewBox="0 0 178 72"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="w-44"
            >
              <Link href="/fun-fact" aria-label="View all book categories">
                <svg
                  width="178"
                  height="72"
                  viewBox="0 0 178 72"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-44"
                >
                  <path
                    d="M171.629 35.7285C171.629 19.31 157.778 6.00018 140.692 6.00018L36.9361 6.00017C19.8503 6.00017 5.99954 19.31 5.99954 35.7285"
                    stroke="#211F54"
                    strokeWidth="11.679"
                  />
                  <path
                    d="M171.629 35.7285C171.629 19.31 157.827 6.00018 140.802 6.00018L37.412 6.00017"
                    stroke="#0161AB"
                    strokeWidth="11.679"
                  />
                  <path
                    d="M6 35.7285C6 52.147 19.8507 65.4569 36.9365 65.4569H140.693C157.779 65.4569 171.629 52.147 171.629 35.7285"
                    stroke="#EFDC2A"
                    strokeWidth="11.679"
                  />
                  <path
                    d="M37.4121 65.4569H140.802C157.827 65.4569 171.629 52.147 171.629 35.7285"
                    stroke="#0161AB"
                    strokeWidth="11.679"
                  />
                  <path
                    d="M140.693 6L36.937 5.99999"
                    stroke="#FF0009"
                    strokeWidth="11.679"
                  />
                  <path
                    d="M140.693 65.457L36.937 65.457"
                    stroke="#4A8B40"
                    strokeWidth="11.679"
                  />
                  <rect
                    x="11.6016"
                    y="7.93848"
                    width="154.01"
                    height="54.6036"
                    rx="27.3018"
                    fill="white"
                  />
                  <text
                    x="50%"
                    y="50%"
                    dominantBaseline="middle"
                    textAnchor="middle"
                    fontSize="20"
                    fill="#211F54"
                    fontFamily="Poppins, sans-serif"
                  >
                    View All
                  </text>
                </svg>
              </Link>
            </svg>
          </Link>
        </footer>
      </section> */}

      <section className="py-16 px-4 sm:px-6 md:px-8 bg-gradient-to-b from-blue-50 to-indigo-50">
        <div className="container-wrapper max-w-6xl mx-auto">
          {/* Heading */}
          <div className="text-center mb-12">
            <h2 className="text-3xl sm:text-4xl md:text-5xl font-bold text-indigo-900 mb-4">
              DID YOU KNOW?
            </h2>
            <div className="w-20 sm:w-24 h-1 bg-gradient-to-r from-blue-500 via-yellow-400 to-green-500 mx-auto rounded-full"></div>
          </div>

          {/* Fact Card with Owl */}
          <div className="flex items-center justify-center">
            <div className="relative bg-white/90 backdrop-blur-sm rounded-2xl p-5 sm:p-6 md:p-8 max-w-xs sm:max-w-sm md:max-w-md shadow-lg">
              {/* Owl on top-right */}
              <div className="absolute -top-7 right-4 sm:-top-11 sm:right-6 w-12 sm:w-16 ">
                <Image src={didYouKnowOwl} alt="Owl character" />
              </div>
              <h3 className="text-lg sm:text-xl font-bold text-indigo-800 mb-2">
                Fact:
              </h3>
              <p className="text-indigo-900 text-base sm:text-lg font-medium leading-relaxed">
                Printing textbooks involves using large amounts of{" "}
                <span className="text-blue-600 font-semibold">
                  paper, water, and energy
                </span>
                . On average, it takes approximately{" "}
                <span className="text-green-600 font-semibold">
                  2.5 pounds of wood
                </span>{" "}
                to produce one pound of paper.
              </p>
            </div>
          </div>

          {/* View All Button */}
          <div className="flex justify-center mt-10">
            <Link href="/fun-fact" className="inline-block" passHref>
              {/* <a aria-label="View all book categories" className="inline-block"> */}
              <svg
                width="178"
                height="72"
                viewBox="0 0 178 72"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="w-40 sm:w-44"
              >
                {/* SVG paths omitted for brevity */}
                <path
                  d="M171.629 35.7285C171.629 19.31 157.778 6.00018 140.692 6.00018L36.9361 6.00017C19.8503 6.00017 5.99954 19.31 5.99954 35.7285"
                  stroke="#211F54"
                  strokeWidth="11.679"
                />
                <path
                  d="M171.629 35.7285C171.629 19.31 157.827 6.00018 140.802 6.00018L37.412 6.00017"
                  stroke="#0161AB"
                  strokeWidth="11.679"
                />
                <path
                  d="M6 35.7285C6 52.147 19.8507 65.4569 36.9365 65.4569H140.693C157.779 65.4569 171.629 52.147 171.629 35.7285"
                  stroke="#EFDC2A"
                  strokeWidth="11.679"
                />
                <path
                  d="M37.4121 65.4569H140.802C157.827 65.4569 171.629 52.147 171.629 35.7285"
                  stroke="#0161AB"
                  strokeWidth="11.679"
                />
                <path
                  d="M140.693 6L36.937 5.99999"
                  stroke="#FF0009"
                  strokeWidth="11.679"
                />
                <path
                  d="M140.693 65.457L36.937 65.457"
                  stroke="#4A8B40"
                  strokeWidth="11.679"
                />
                {/* ...other paths... */}
                <rect
                  x="11.6016"
                  y="7.93848"
                  width="154.01"
                  height="54.6036"
                  rx="27.3018"
                  fill="white"
                />
                <text
                  x="50%"
                  y="50%"
                  dominantBaseline="middle"
                  textAnchor="middle"
                  fontSize="20"
                  fill="#211F54"
                  fontFamily="Poppins, sans-serif"
                >
                  View All
                </text>
              </svg>
              {/* </a> */}
            </Link>
          </div>

          {/* Environmental Impact Stats */}
          <div className="mt-16 bg-white rounded-2xl p-6 sm:p-8 md:p-10 max-w-4xl mx-auto shadow-lg">
            <h3 className="text-xl sm:text-2xl md:text-3xl font-bold text-center text-indigo-900 mb-8">
              Environmental Impact of Textbook Production
            </h3>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
              {/* Card 1 */}
              <div className="bg-blue-50 p-6 rounded-xl text-center border border-blue-100 transform transition-transform hover:scale-105">
                <div className="text-3xl sm:text-4xl md:text-5xl mb-3">📚</div>
                <div className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2">
                  32 Million
                </div>
                <p className="text-sm sm:text-base">
                  Textbooks printed annually in US
                </p>
              </div>
              {/* Card 2 */}
              <div className="bg-green-50 p-6 rounded-xl text-center border border-green-100 transform transition-transform hover:scale-105">
                <div className="text-3xl sm:text-4xl md:text-5xl mb-3">🌳</div>
                <div className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2">
                  20 Million
                </div>
                <p className="text-sm sm:text-base">Trees cut down each year</p>
              </div>
              {/* Card 3 */}
              <div className="bg-amber-50 p-6 rounded-xl text-center border border-amber-100 transform transition-transform hover:scale-105">
                <div className="text-3xl sm:text-4xl md:text-5xl mb-3">💧</div>
                <div className="text-2xl sm:text-3xl md:text-4xl font-bold mb-2">
                  44 Billion
                </div>
                <p className="text-sm sm:text-base">Gallons of water used</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* <RebookerMonth /> */}
      {/* <section className='container-wrapper'> */}
      <div className="px-2.5 py-4 md:p-0">
        <Subscription />
      </div>
      {/* </section> */}
      <Community />
      <Testimonials />
      <section className="px-2.5 pb-10 md:px-[50px] lg:px-[100px] lg:pb-[50px] mt-10 md:mt-auto">
        <div className="container-wrapper">
          <BuyAndSellComponent />
        </div>
      </section>
    </div>
  );
}
