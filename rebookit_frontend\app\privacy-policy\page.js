"use client";

import React, {useState} from "react";
import logo from "@/public/owl_eyes.svg";
import Image from "next/image";
import policy from "./privacyPolicy.json";
import {FiChevronDown, FiChevronUp} from "react-icons/fi";

const PrivacyPolicy = () => {
  const {privacy_notice} = policy;
  const [openSections, setOpenSections] = useState(
    new Set(privacy_notice.sections.map((_, i) => i))
  );

  const toggleSection = (index) => {
    setOpenSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  return (
    <div className="min-h-screen pt-20 pb-16">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Logo */}
        <div className="pt-10 pb-3 text-center">
          <Image
            src={logo}
            alt="Logo"
            width={100}
            height={80}
            className="mx-auto transform transition-transform duration-500 hover:scale-105"
          />
        </div>

        {/* Title */}
        <div className="relative mb-12 text-center">
          <h1 className="text-4xl md:text-5xl font-extrabold text-black tracking-tight ">
            {privacy_notice.title}
          </h1>
          {/* {/* <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 w-32 h-1 bg-amber-500 rounded-full"></div> */}
          {/* <div className="underline transform -translate-x-1/2 w-16 h-1 bg-amber-300 rounded-full -mt-0.5"></div>{" "} */}
        </div>

        {/* Introduction Card */}
        {/* <div className="bg-white/90 backdrop-blur-sm rounded-xl shadow-xl p-6 mb-10 border border-white/30 transition-all duration-300 hover:shadow-2xl">
          <div className="flex items-start">
            <div className="bg-amber-100 p-3 rounded-full mr-4">
              <div className="bg-amber-500 w-12 h-12 rounded-full flex items-center justify-center">
                <span className="text-white text-xl font-bold">i</span>
              </div>
            </div>
            <div>
              <p className="text-gray-800 text-lg">
                This policy outlines how we collect, use, and protect your
                personal information. Please read it carefully to understand our
                practices.
              </p>
              <div className="mt-4 flex items-center text-amber-700 font-medium">
                <svg
                  className="w-5 h-5 mr-2"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z"
                    clipRule="evenodd"
                  />
                </svg>
                Last Updated:{" "}
                {new Date().toLocaleDateString("en-US", {
                  month: "long",
                  day: "numeric",
                  year: "numeric",
                })}
              </div>
            </div>
          </div>
        </div> */}

        {/* Sections with accordion */}
        <div className="space-y-6">
          {privacy_notice.sections.map((section, index) => (
            <div
              key={section.section_id}
              className={`bg-white rounded-xl shadow-lg overflow-hidden transition-all duration-300 ${
                openSections.has(index) ? "ring-1 ring-amber-200" : ""
              }`}
            >
              <div
                className="flex justify-between items-center p-5 cursor-pointer bg-gradient-to-r from-amber-50 to-white"
                onClick={() => toggleSection(index)}
              >
                <div className="flex items-center">
                  <div className="bg-amber-500 w-8 h-8 rounded-full flex items-center justify-center mr-3">
                    <span className="text-white font-bold">
                      {section.section_id}
                    </span>
                  </div>
                  <h2 className="text-xl font-bold text-gray-800">
                    {section.title}
                  </h2>
                </div>
                <button className="text-amber-600 hover:text-amber-800 transition-colors">
                  {openSections.has(index) ? (
                    <FiChevronUp size={24} />
                  ) : (
                    <FiChevronDown size={24} />
                  )}
                </button>
              </div>

              {openSections.has(index) && (
                <div className="p-6 pt-4 border-t border-gray-100">
                  {/* Content Rendering */}
                  {section.content && (
                    <p className="mb-4 text-gray-700 leading-relaxed">
                      {section.content}
                    </p>
                  )}

                  {/* Categories (for section 3) */}
                  {section.categories && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                      {section.categories.map((category, idx) => (
                        <div
                          key={idx}
                          className="bg-amber-50 p-4 rounded-lg border border-amber-100"
                        >
                          <h3 className="font-semibold text-amber-700 mb-1">
                            {category.name}
                          </h3>
                          <p className="text-gray-700">
                            {category.description}
                          </p>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Legal Grounds (section 4) */}
                  {section.legal_grounds && (
                    <div className="mb-4">
                      {section.legal_grounds.map((item, idx) => (
                        <div key={idx} className="flex mb-3">
                          <div className="flex-shrink-0 mr-3 mt-1">
                            <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                          </div>
                          <p className="text-gray-700">{item}</p>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Purposes (section 5) */}
                  {section.purposes && (
                    <div className="mb-4">
                      {section.purposes.map((purpose, idx) => (
                        <div key={idx} className="flex mb-3">
                          <div className="flex-shrink-0 mr-3 mt-1">
                            <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                          </div>
                          <p className="text-gray-700">{purpose}</p>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Circumstances (section 6) */}
                  {section.circumstances && (
                    <div className="mb-4">
                      {section.circumstances.map((item, idx) => (
                        <div key={idx} className="flex mb-3">
                          <div className="flex-shrink-0 mr-3 mt-1">
                            <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                          </div>
                          <p className="text-gray-700">{item}</p>
                        </div>
                      ))}
                    </div>
                  )}

                  {/* Rights (section 8) */}
                  {section.rights && (
                    <div className="mb-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {section.rights.map((right, idx) => (
                          <div
                            key={idx}
                            className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm"
                          >
                            <div className="flex items-center mb-2">
                              <div className="bg-amber-100 p-2 rounded-lg mr-3">
                                <svg
                                  className="w-5 h-5 text-amber-600"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                                  ></path>
                                </svg>
                              </div>
                              <h3 className="font-semibold text-gray-800">
                                {right.split(":")[0]}
                              </h3>
                            </div>
                            <p className="text-gray-700 ml-11">
                              {right.split(":").slice(1).join(":")}
                            </p>
                          </div>
                        ))}
                      </div>
                      {section.note && (
                        <p className="mt-4 text-gray-700 italic bg-amber-50 p-3 rounded-lg">
                          {section.note}
                        </p>
                      )}
                    </div>
                  )}

                  {/* Contact Info (section 13) */}
                  {section.contact_info && (
                    <div className="mt-6 bg-gradient-to-br from-amber-50 to-amber-100 p-5 rounded-xl">
                      <h3 className="text-lg font-bold text-amber-800 mb-4">
                        Contact Information
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="bg-white p-4 rounded-lg shadow-sm">
                          <div className="text-amber-600 font-medium mb-1">
                            Company Name
                          </div>
                          <div className="text-gray-800">
                            {section.contact_info.company_name}
                          </div>
                        </div>
                        <div className="bg-white p-4 rounded-lg shadow-sm">
                          <div className="text-amber-600 font-medium mb-1">
                            Email
                          </div>
                          <a
                            href={`mailto:${section.contact_info.email}`}
                            className="text-blue-600 hover:underline"
                          >
                            {section.contact_info.email}
                          </a>
                        </div>
                        <div className="bg-white p-4 rounded-lg shadow-sm">
                          <div className="text-amber-600 font-medium mb-1">
                            Address
                          </div>
                          <div className="text-gray-800">
                            {section.contact_info.address}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Acknowledgement (section 13) */}
                  {section.acknowledgement && (
                    <div className="mt-6 p-5 bg-gradient-to-r from-amber-500 to-amber-600 rounded-xl text-white">
                      <div className="flex items-start">
                        <svg
                          className="w-6 h-6 mr-3 flex-shrink-0 mt-0.5"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          ></path>
                        </svg>
                        <p className="font-medium">{section.acknowledgement}</p>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Back to Top Button */}
        <div className="mt-12 flex justify-center">
          <button
            onClick={() => window.scrollTo({top: 0, behavior: "smooth"})}
            className="flex items-center px-5 py-3 bg-white text-amber-600 font-medium rounded-full shadow-md hover:bg-amber-50 transition-all hover:-translate-y-0.5"
          >
            <svg
              className="w-5 h-5 mr-2"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
                clipRule="evenodd"
              />
            </svg>
            Back to Top
          </button>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicy;
