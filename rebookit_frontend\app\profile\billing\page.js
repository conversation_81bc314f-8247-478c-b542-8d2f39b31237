"use client";
import {
  updateItemId,
  updateProfileComponentIndex,
} from "@/app/redux/slices/storeSlice";
import {get_bookMarkItems, paymentHistory} from "@/app/services/profile";
import {useEffect, useState} from "react";
import {useDispatch} from "react-redux";
import {toast} from "react-toastify";
import {FaArrowDownLong} from "react-icons/fa6";
import {FaArrowDown} from "react-icons/fa6";
import moment from "moment";
import {subscriptionPlans} from "@/app/services/membership";
import StatusBadge from "@/app/components/statusBadge/StatusBadge";
import NoDataFound from "@/app/components/common/NoDataFound";

export default function Billing() {
  const dispatch = useDispatch();
  const [bookmarkData, setbookmarkData] = useState([]);
  const [currentPlan, setcurrentPlan] = useState({});
  const [billingData, setbillingData] = useState([]);
  const [isLoading, setisLoading] = useState(false);
  let dataTable = [
    {
      name: "lala Land",
      seller: "warisAhmad",
      location: "Kiston london",
      price: "99",
      image: "sample",
    },
    {
      name: "lala Land",
      seller: "warisAhmad",
      location: "Kiston london",
      price: "99",
      image: "sample",
    },
    {
      name: "lala Land",
      seller: "warisAhmad",
      location: "Kiston london",
      price: "99",
      image: "sample",
    },
    {
      name: "lala Land",
      seller: "warisAhmad",
      location: "Kiston london",
      price: "99",
      image: "sample",
    },
    {
      name: "lala Land",
      seller: "warisAhmad",
      location: "Kiston london",
      price: "99",
      image: "sample",
    },
  ];
  const chatNavigationHandler = (e, itemId) => {
    e.stopPropagation();
    const profileIndex = 3;
    dispatch(updateProfileComponentIndex(profileIndex));
    dispatch(updateItemIdd(itemId));
    router.push("/profile");
  };

  const fetchBookMarkItems = async () => {
    try {
      setisLoading(true);
      let data = await get_bookMarkItems();
      console.log("fetchBookMarkItems data", data);
      if (data.data) {
        setbookmarkData(data.data);
        setisLoading(false);
      }
    } catch (err) {
      setisLoading(false);
      console.log("error in fetchBookMarkItems", err);
    }
  };
  const fetchPaymentHistory = async () => {
    const payment = await paymentHistory();
    console.log("payment", payment);
    if (payment.status == 200) {
      setbillingData(payment.data.subscriptions);
    }
  };
  useEffect(() => {
    fetchPaymentHistory();
  }, []);

  console.log("billingData", billingData);

  async function getSubscriptionData() {
    try {
      let subscriptionData = await subscriptionPlans();
      // let subscriptionData = await getsubscriptionPlans()
      if (subscriptionData.status == 200) {
        // setSubscriptionList(subscriptionData?.data.plans)
        setcurrentPlan(subscriptionData?.data?.currentSubscription);
      } else {
        toast.error("Something went wrong");
      }
    } catch (err) {
      toast.error("Something went wrong");
    }
  }
  console.log("currentPlan in billing", currentPlan);
  console.log("bookmarkData", bookmarkData);
  useEffect(() => {
    fetchBookMarkItems();
    getSubscriptionData();
  }, []);

  const paidCompo = (text) => {
    const isPaid = text === "paid";
    const textColor = isPaid ? "#1E9609" : "#A70909";
    const bgColor = isPaid ? "#A5FF9647" : "#FFD6D6";

    return (
      <div
        className="inline-flex items-center px-3 py-1 rounded-full"
        style={{backgroundColor: bgColor, color: textColor}}
      >
        <div
          className="w-[8px] h-[8px] rounded-full mr-2"
          style={{backgroundColor: textColor}}
        ></div>
        {text || "NA"}
      </div>
    );
  };
  let logoMater =
    "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/38fee772-1b93-41b7-a047-140df06dd338.png";
  const Card = () => {
    return (
      <div className="bg-[#4A8B40] text-[#ECECEC] rounded-xl p-6 w-full max-w-2xl shadow-md mt-6">
        <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
          {/* Left: MasterCard Logo */}
          <div className="flex-shrink-0">
            <img
              src={logoMater}
              alt="MasterCard"
              className="w-14 h-auto object-contain"
            />
          </div>

          {/* Middle: Card Details */}
          <div className="flex-1 w-full">
            <div className="text-lg font-semibold mb-1">Johnathan Doe</div>
            <div className="text-xl tracking-widest font-mono mb-1">
              •••• •••• •••• 1234
            </div>
            <div className="text-sm mb-1 flex">
              <span className="font-semibold block uppercase text-[#ECECEC]/80">
                Expires on
              </span>
              <span className="font-semibold ml-3">09/27</span>
            </div>
            <div className="flex items-center text-sm text-[#ECECEC]/90 mt-2">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-4 h-4 mr-2"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
                strokeWidth={2}
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  d="M16 12H8m0 0l-4-4m4 4l-4 4m16-4H8"
                />
              </svg>
              <EMAIL>
            </div>
          </div>

          {/* Right: Change Button */}
          <div className="w-full md:w-auto flex justify-end">
            <button className="bg-white text-[#4A8B40] font-semibold px-5 py-2 rounded shadow-sm w-full md:w-auto">
              Change
            </button>
          </div>
        </div>
      </div>
    );
  };
  return (
    <div className="p-1 md:p-1">
      <h1 className="font-bold text-[20px] md:text-[24px] mb-4">Billing</h1>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Payment Method */}

        {/* Plan Summary */}
        {/* <div className="border border-gray-300 rounded-md overflow-hidden">
                    <div className="bg-gray-200 p-3 flex justify-between items-center text-[16px] md:text-[18px]">
                        <span className="font-medium">Current Plan Summary</span>
                        <button className="flex items-center gap-2 px-4 py-2 w-fit sm:w-[115px] text-sm md:text-[12px] bg-[#5B89FF] rounded text-white justify-center">
                            <span>Upgrade</span>
                        </button>
                    </div>
                    <div className="p-3">
                        <div className="grid grid-cols-3 gap-4 mb-5">
                            <div>
                                <label className="block text-sm mb-1">Plan Name</label>
                                <div className="font-bold text-[18px] md:text-[22px]">{currentPlan?.planId?.planName}</div>
                            </div>
                            <div>
                                <label className="block text-sm mb-1">Billing Cycle</label>
                                <div className="font-bold text-[18px] md:text-[22px]">{currentPlan?.planId?.planMonths}</div>
                            </div>
                            <div>
                                <label className="block text-sm mb-1">Plan Cost</label>
                                <div className="font-bold text-[18px] md:text-[22px]">${currentPlan?.planId?.price}</div>
                            </div>
                        </div>

                        <div className="mt-5">
                            <div className="text-sm">Book Uploaded</div>
                            <div className="font-bold">5 out of 10</div>
                            <div className="mt-3">
                                <div className="relative h-[30px] w-full bg-gray-200 rounded-md overflow-hidden">
                                    <div className="absolute left-0 top-0 h-full w-1/2 bg-gradient-to-r from-[#0161AB] to-[#211F54]"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> */}
      </div>

      {/* Invoices Table */}
      <div className="bg-white p-6 rounded-lg shadow border border-gray-200 overflow-x-auto mt-2">
        <table className="min-w-[600px] w-full text-sm">
          <thead className="bg-gray-200 text-black">
            <tr>
              {[
                "Invoice",
                "Services",
                "Billing Date",
                "Amount",
                "Payment Status",
              ].map((head, idx) => (
                <th
                  key={idx}
                  className="px-6 py-4 text-left text-[14px] font-bold"
                >
                  <div className="flex items-center">
                    <span>{head}</span>
                    <FaArrowDown className="ml-2" />
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white ">
            {!isLoading ? (
              billingData.length > 0 ? (
                billingData.map((item, i) => (
                  <tr
                    key={i}
                    className="border-b border-gray-200 animate-fade-in-down text-[14px] font-medium"
                  >
                    <td className="px-6 py-4 whitespace-nowrap">
                      RBIT-{item.invoiceId || i + 1}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {item?.planId?.planName}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {moment(item.createdAt).format("MMM D, YYYY")}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      J$ {item?.planId.price}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {/* {paidCompo(item.status)} */}
                      <StatusBadge status={item?.paymentId?.status} />
                    </td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="text-center py-4">
                    <div className=" mx-auto min-h-[60vh] col-span-2">
                      <NoDataFound
                        actionFunc={() => router.push("")}
                        title={"No Data Found"}
                        // btntxt={"Bookmark Your Favourites"}
                      />
                    </div>
                  </td>
                </tr>
              )
            ) : (
              <tr>
                <td colSpan={5} className="text-center py-4">
                  <div className="flex h-[60vh] items-center justify-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
}
