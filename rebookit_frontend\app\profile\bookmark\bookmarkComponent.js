"use client";
import {
  updateItemId,
  updateProfileComponentIndex,
} from "@/app/redux/slices/storeSlice";
import { delete_bookMarkItem, get_bookMarkItems } from "@/app/services/profile";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";

import { RxCross2 } from "react-icons/rx";
import { HiOutlineChatBubbleLeftRight } from "react-icons/hi2";
import { MdOutlineDeleteSweep } from "react-icons/md";
import NoDataFound from "@/app/components/common/NoDataFound";
import { userDataFromLocal } from "@/app/utils/utils";

export default function Bookmark() {
  const dispatch = useDispatch();
  const [bookmarkData, setbookmarkData] = useState([]);
  const [isLoading, setisLoading] = useState(false);
  const router = useRouter();
  const [code, setCode] = useState("");
  const [isImageLoaded, setisImageLoaded] = useState(false);
  let dataTable = [
    {
      name: "lala Land",
      seller: "warisAhmad",
      location: "Kiston london",
      price: "99",
      image: "sample",
    },
    {
      name: "lala Land",
      seller: "warisAhmad",
      location: "Kiston london",
      price: "99",
      image: "sample",
    },
    {
      name: "lala Land",
      seller: "warisAhmad",
      location: "Kiston london",
      price: "99",
      image: "sample",
    },
    {
      name: "lala Land",
      seller: "warisAhmad",
      location: "Kiston london",
      price: "99",
      image: "sample",
    },
    {
      name: "lala Land",
      seller: "warisAhmad",
      location: "Kiston london",
      price: "99",
      image: "sample",
    },
  ];
  const chatNavigationHandler = (e, itemId) => {
    e.stopPropagation();
    const profileIndex = 3;
    dispatch(updateProfileComponentIndex(profileIndex));
    dispatch(updateItemId(itemId));
    router.push("/profile/messages");
  };

  const fetchBookMarkItems = async () => {
    try {
      setisLoading(true);
      let data = await get_bookMarkItems();
      console.log("fetchBookMarkItems data", data);
      if (data.data) {
        setbookmarkData(data.data);
        setisLoading(false);
      }
    } catch (err) {
      setisLoading(false);
      console.log("error in fetchBookMarkItems", err);
    }
  };
  console.log("bookmarkData", bookmarkData);
  useEffect(() => {
    fetchBookMarkItems();
  }, []);

  async function deleteItemById(id) {
    let deleteItem = await delete_bookMarkItem(id);
    console.log("deleteItem", deleteItem);
    if (deleteItem.status == 200) {
      fetchBookMarkItems();
      toast.success("Deleted Successfully");
    } else {
    }
  }
  return (
    <div>
      <h1 className="font-bold text-[24px]">Wishlist</h1>

      <div>
        <div class="bg-white  rounded-lg shadow border border-gray-200 overflow-x-auto mt-4">
          <table class=" w-full overflow-x-auto">
            <thead class=" text-black rounded-lg ">
              <tr className="w-full text-[14px] ">
                <td class="bg-gray-200 px-6 py-2 text-left">
                  <div className="my-3 bg-gray-200">Items</div>
                </td>
                <td class="bg-gray-200 px-6 py-2 text-left ">Seller</td>
                <td class="bg-gray-200 px-6 py-2 text-left ">Location</td>
                <td class="bg-gray-200 px-6 py-2 text-left  whitespace-nowrap ">
                  Quoted Price
                </td>
                <td class="bg-gray-200 px-6 py-2 text-left ">Action</td>
              </tr>
            </thead>
            {!isLoading ? (
              <tbody class="bg-white">
                {bookmarkData.length > 0 ? (
                  bookmarkData.map((item) => {
                    return (
                      <tr
                        className="cursor-pointer border-b border-[#e6e7eb] text-[14px]"
                        onClick={(e) => {
                          router.push(`/book-detail?id=${item.itemId?._id}`);
                        }}
                      >
                        <td className="px-6 py-4 inline-flex whitespace-nowrap items-center">
                          <img
                            src={item?.itemId?.images[0]}
                            className={`w-14 h-16 rounded-t-[10px] object-cover  transition-opacity duration-300 ${
                              isImageLoaded ? "opacity-100" : "opacity-0"
                            }`}
                            onLoad={() => setisImageLoaded(true)}
                          />{" "}
                          <div className="flex flex-col">
                            <span
                              title={item?.itemId?.title}
                              className="ml-3 w-[150px] overflow-hidden text-ellipsis  whitespace-nowrap"
                            >
                              {item?.itemId?.title}
                            </span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          {item.itemId?.createdBy?.firstName}
                        </td>
                        <td className="px-6 py-4 inline-flex whitespace-nowrap items-center">
                          <span
                            title={
                              item?.itemId?.address?.formatted_address || ""
                            }
                            className="ml-3 w-[200px] overflow-hidden text-ellipsis  whitespace-nowrap"
                          >
                            {item?.itemId?.address?.formatted_address || ""}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap">
                          J$ {item?.itemId?.price}
                        </td>
                        <td className="px-4 py-4 flex items-center justify-between w-[100px]">
                          <HiOutlineChatBubbleLeftRight
                            className="w-[25px] h-[25px] cursor-pointer"
                            onClick={(e) => {
                              // alert(item.itemId._id)
                              // e.stopPropagation(); // Prevent tr onClick
                              // let userData = userDataFromLocal();
                              // userData = JSON.parse(userData);
                              // console.log("userData in btn", userData);
                              // console.log("item.createdBy", item);
                              chatNavigationHandler(e, item.itemId._id);
                            }}
                          />
                          <MdOutlineDeleteSweep
                            className="cursor-pointer my-auto"
                            size={25}
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent tr onClick
                              deleteItemById(item._id);
                            }}
                          />
                        </td>
                      </tr>
                    );
                  })
                ) : (
                  <tr className="text-center">
                    {/* <td colSpan={5}>No Data Available</td> */}
                    <td colSpan={5}>
                      <div className=" mx-auto min-h-[60vh] col-span-2">
                        <NoDataFound
                          actionFunc={() => router.push("/search")}
                          title={"No Data Found"}
                          btntxt={"Add In WishList"}
                        />
                      </div>
                    </td>
                  </tr>
                )}
              </tbody>
            ) : (
              <tbody>
                <tr>
                  <td colSpan={5} className=" text-center">
                    <div className="flex h-[60vh] items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    </div>
                  </td>
                </tr>
              </tbody>
            )}
          </table>
        </div>
      </div>
    </div>
  );
}
