import React, { useEffect, useState, useMemo } from "react";
import style from "./campaign.module.scss";
import { useDispatch, useSelector } from "react-redux";
import { updateAdListinStep, updateAdManagement } from "@/app/redux/slices/storeSlice";
import CalendarRuleView from "./CalendarRuleView";
import { getAdPlanById } from "@/app/services/profile";

export default function BudgetCalculator() {
  const dispatch = useDispatch();

  // Get adManagement from redux, which will now hold duration and ranges
  const adManagement = useSelector((state) => state.storeData.adManagement);

  // Local state for ad plan and calendar navigation only
  const [selectedAdPlan, setSelectedAdPlan] = useState(null);
  const [calendarMonth, setCalendarMonth] = useState(new Date().getMonth());
  const [calendarYear, setCalendarYear] = useState(new Date().getFullYear());
  const [isLoading, setIsLoading] = useState(true);
console.log(selectedAdPlan,"selectedAdPlan");
  // Get duration and ranges from adManagement (default to 15s and empty array)
  const budgetDuration = adManagement?.budgetDuration || "15s";
  const budgetRanges = adManagement?.budgetRanges || [];

  useEffect(() => {
    if (adManagement?.adPlanId) {
      setIsLoading(true);
      getAdPlanById(adManagement.adPlanId)
        .then((res) => {
          setSelectedAdPlan(res?.data);
        })
        .finally(() => {
          setIsLoading(false);
        });
    }
  }, [adManagement?.adPlanId]);

  // Extract rules and overrides for the selected ad plan
  const rules = selectedAdPlan?.pricingRules || [];
  const dateOverrides = {};
  if (selectedAdPlan?.overrideRules) {
    selectedAdPlan.overrideRules.forEach((override) => {
      const date = new Date(override.date);
      const key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
        2,
        "0"
      )}-${String(date.getDate()).padStart(2, "0")}`;
      dateOverrides[key] = override;
    });
  }
  // Find base price
  const basePrice = rules.find(
    (rule) => rule.name?.toLowerCase() === "base price"
  )?.price;

  // Only non-base rules
  const nonBaseRules = rules.filter(
    (rule) => rule.name?.toLowerCase() !== "base price"
  );

  // Get rule for a date (similar to admin view, but no "recent" logic needed)
  function getRuleForDateWithUpdatedAt(rules, year, month, day) {
    const dateStr = `${year}-${String(month + 1).padStart(2, "0")}-${String(
      day
    ).padStart(2, "0")}`;
    const matching = rules.filter((rule) => {
      if (rule.isActive === false) return false;
      if (!rule.startDate || !rule.endDate) return false;
      const start = formatDate(rule.startDate);
      const end = formatDate(rule.endDate);
      return start <= dateStr && dateStr <= end;
    });
    if (matching.length === 0) return null;
    matching.sort((a, b) => {
      if (Number(b.priority) !== Number(a.priority)) {
        return Number(b.priority) - Number(a.priority);
      }
      return new Date(b.updatedAt) - new Date(a.updatedAt);
    });
    return matching[0];
  }

  function formatDate(dateStr) {
    if (!dateStr) return "";
    const d = new Date(dateStr);
    return (
      d.getFullYear() +
      "-" +
      String(d.getMonth() + 1).padStart(2, "0") +
      "-" +
      String(d.getDate()).padStart(2, "0")
    );
  }

  function formatPrice(currency, price) {
    if (currency === "USD") {
      if (price === undefined || price === null || price === "") return "";
      return (
        "US$" +
        Number(price).toLocaleString("en-US", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        })
      );
    }
    if (price === undefined || price === null || price === "") return "";
    return "J$" + Number(price).toLocaleString("en-JM");
  }

  function buildDateStr(year, monthZeroBased, dayNumber) {
    return `${year}-${String(monthZeroBased + 1).padStart(2, "0")}-${String(
      dayNumber
    ).padStart(2, "0")}`;
  }

  function enumerateDatesSameMonth(startDate, endDate) {
    // assumes same month/year selection
    const [y1, m1, d1] = startDate.split("-").map((v) => Number(v));
    const [, , d2] = endDate.split("-").map((v) => Number(v));
    const list = [];
    for (let d = d1; d <= d2; d++) {
      list.push(buildDateStr(y1, m1 - 1, d));
    }
    return list;
  }

  function getPriceForDate(dateStr) {
    // override wins
    if (dateOverrides[dateStr]) {
      const { price, currency } = dateOverrides[dateStr];
      return { price: Number(price) || 0, currency: currency || "JMD" };
    }
    const [y, m, d] = dateStr.split("-").map((v) => Number(v));
    const rule = getRuleForDateWithUpdatedAt(nonBaseRules, y, m - 1, d);
    if (rule && rule.price != null) {
      return { price: Number(rule.price) || 0, currency: rule.currency || "JMD" };
    }
    return { price: Number(basePrice) || 0, currency: "JMD" };
  }

  function onRangeSelectedFromCalendar({ startDate, endDate }) {
    // compute totals
    // prevent overlap at parent-level too (safety)
    const overlaps = budgetRanges.some(
      (r) => !(endDate < r.startDate || startDate > r.endDate)
    );
    if (overlaps) return;
    const days = enumerateDatesSameMonth(startDate, endDate);
    // If currency could vary, group by currency; for now, keep primary and note mixed
    const totalsByCurrency = days.reduce(
      (acc, dateStr) => {
        const { price, currency } = getPriceForDate(dateStr);
        acc[currency] = (acc[currency] || 0) + price;
        return acc;
      },
      {}
    );
    // Prefer JMD, else first key
    const currencyKeys = Object.keys(totalsByCurrency);
    const primaryCurrency = currencyKeys.includes("JMD")
      ? "JMD"
      : currencyKeys[0] || "JMD";
    const totalCost = totalsByCurrency[primaryCurrency] || 0;

    const range = {
      id: `${startDate}_${endDate}_${Date.now()}`,
      startDate,
      endDate,
      totalDays: days.length,
      totalCost,
      currency: primaryCurrency,
    };
    // Update adManagement in redux with new ranges
    dispatch(
      updateAdManagement({
        ...adManagement,
        budgetRanges: [...budgetRanges, range],
        isStep1Completed: true,
      })
    );
  }

  function removeRange(id) {
    const newRanges = budgetRanges.filter((r) => r.id !== id);
    dispatch(
      updateAdManagement({
        ...adManagement,
        budgetRanges: newRanges,
        isStep1Completed: newRanges.length > 0 ? true : false,
      })
    );
  }

  function handleDurationChange(duration) {
    dispatch(
      updateAdManagement({
        ...adManagement,
        budgetDuration: duration,
      })
    );
  }

  const totalDaysSelected = useMemo(
    () => budgetRanges.reduce((acc, r) => acc + (Number(r.totalDays) || 0), 0),
    [budgetRanges]
  );

  const totalsByCurrencyAll = useMemo(() => {
    const m = {};
    for (const r of budgetRanges) {
      const cur = r.currency || "JMD";
      m[cur] = (m[cur] || 0) + (Number(r.totalCost) || 0);
    }
    return m;
  }, [budgetRanges]);

  // Calculate final totals based on selected duration (for 30s)
  const finalTotalsByCurrency = useMemo(() => {
    if (budgetDuration === "30s") {
      const doubled = {};
      for (const [cur, amt] of Object.entries(totalsByCurrencyAll)) {
        doubled[cur] = amt * 2;
      }
      return doubled;
    }
    return { ...totalsByCurrencyAll };
  }, [totalsByCurrencyAll, budgetDuration]);

  // Re-sync stored range totals whenever the ad plan/rules change
  useEffect(() => {
    // Need an ad plan and at least one stored range to reconcile
    if (!selectedAdPlan || !Array.isArray(budgetRanges) || budgetRanges.length === 0) {
      return;
    }

    // Recompute a single range based on the latest rules/overrides
    const recomputeRangeTotals = (range) => {
      const days = enumerateDatesSameMonth(range.startDate, range.endDate);
      const totalsByCurrency = days.reduce((accumulator, dateStr) => {
        const { price, currency } = getPriceForDate(dateStr);
        accumulator[currency] = (accumulator[currency] || 0) + price;
        return accumulator;
      }, {});

      const currencyKeys = Object.keys(totalsByCurrency);
      const primaryCurrency = currencyKeys.includes("JMD")
        ? "JMD"
        : currencyKeys[0] || "JMD";
      const totalCost = totalsByCurrency[primaryCurrency] || 0;

      return {
        ...range,
        totalDays: days.length,
        totalCost,
        currency: primaryCurrency,
      };
    };

    const updatedRanges = budgetRanges.map(recomputeRangeTotals);

    const hasAnyChange = budgetRanges.some((existing, index) => {
      const updated = updatedRanges[index];
      return (
        Number(existing.totalCost) !== Number(updated.totalCost) ||
        Number(existing.totalDays) !== Number(updated.totalDays) ||
        existing.currency !== updated.currency
      );
    });

    if (hasAnyChange) {
      dispatch(
        updateAdManagement({
          ...adManagement,
          budgetRanges: updatedRanges,
        })
      );
    }
    // We intentionally avoid adding adManagement to deps to prevent loops
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedAdPlan, nonBaseRules, basePrice, JSON.stringify(dateOverrides), budgetRanges]);

  // Show loading spinner or message when loading
  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[300px]">
        <div className="text-lg font-semibold text-gray-500">Loading...</div>
      </div>
    );
  }

  return (
    <div>
      {selectedAdPlan && (
        <div className="flex items-center gap-3 flex-wrap mb-5">
          <h2 className="text-[15px] font-semibold text-[#211F54] capitalize">
            {selectedAdPlan.page}
          </h2>
          <span className="px-3 py-1 rounded-full bg-purple-50 text-purple-600 text-[12px]">
            Type: {String(selectedAdPlan.type || "").charAt(0).toUpperCase() + String(selectedAdPlan.type || "").slice(1)}
          </span>
          <span className="px-3 py-1 rounded-full bg-gray-100 text-gray-700 text-[12px]">
            Position: {String(selectedAdPlan.position || "").charAt(0).toUpperCase() + String(selectedAdPlan.position || "").slice(1)}
          </span>
         
        </div>
      )}
      <div className="flex justify-between">
        <p className="text-[18px] font-semibold">Select duration</p>
      </div>
      <div className=" my-4">
        <div className="flex items-center w-[50%] border border-[#f4f4f4] px-3 py-2 rounded-lg gap-3">
          <button
            className={`w-[50%] py-1 rounded transition-colors duration-300 ${
              budgetDuration === "30s" ? "" : style.linear_gradient
            } `}
            onClick={() => handleDurationChange("15s")}
          >
            15s
          </button>
          <button
            className={`w-[50%] py-1 rounded transition-colors duration-300 ${
              budgetDuration === "30s" ? style.linear_gradient : " "
            } `}
            onClick={() => handleDurationChange("30s")}
          >
            30s
          </button>
        </div>
        {/* Show selected duration */}
        {/* <div className="mt-2">
          <span className="text-sm text-[#211F54]">
            <span className="font-medium">Selected Duration:</span>{" "}
            {budgetDuration === "30s" ? "30 seconds" : "15 seconds"}
          </span>
        </div> */}
      </div>
      <div className="w-full mt-[20px]">
        <p className="text-[18px] font-semibold">Select date range</p>

        {/* Calendar View with month changer, no rule name on calendar date */}
        <div className="bg-white rounded-3xl shadow-2xl p-6 w-full">
          <div className="flex items-center gap-4 mb-6 sticky top-0 z-10 bg-white rounded-t-3xl">
            <span className="font-[poppins] w-[250px] font-bold text-xl bg-white px-4 py-2 rounded shadow-sm border border-gray-200">
              {new Date(calendarYear, calendarMonth).toLocaleString("default", {
                month: "long",
                year: "numeric",
              })}
            </span>
            <button
              className="px-4 py-2 bg-white border border-gray-200 rounded-lg shadow-sm text-xl text-[#211F54]"
              onClick={() =>
                setCalendarMonth((prev) => (prev === 0 ? 0 : prev - 1))
              }
              aria-label="Previous Month"
            >
              {"<"}
            </button>
            <button
              className="px-4 py-2 bg-white border border-gray-200 rounded-lg shadow-sm text-xl text-[#211F54]"
              onClick={() =>
                setCalendarMonth((prev) => (prev === 11 ? 11 : prev + 1))
              }
              aria-label="Next Month"
            >
              {">"}
            </button>
          </div>
          <CalendarRuleView
            rules={nonBaseRules}
            basePrice={basePrice}
            baseCurrency={"JMD"}
            dateOverrides={dateOverrides}
            onCellClick={() => {}} // No-op
            calendarMonth={calendarMonth}
            calendarYear={calendarYear}
            setCalendarMonth={setCalendarMonth}
            setCalendarYear={setCalendarYear}
            getRuleForDate={getRuleForDateWithUpdatedAt}
            onRangeSelected={onRangeSelectedFromCalendar}
            selectedRanges={budgetRanges}
            // You may want to add a prop to CalendarRuleView to hide rule names on calendar cells
            // e.g. showRuleName={false}
          />
        </div>
        {budgetRanges.length > 0 && (
          <div className="mt-4 bg-white rounded-2xl shadow border border-gray-200 p-4">
            <div className="flex items-center justify-between mb-3">
              <p className="font-semibold text-[#211F54]">Selected Ranges</p>
            </div>
            <div className="space-y-3">
              {budgetRanges.map((r) => (
                <div
                  key={r.id}
                  className="flex items-center justify-between bg-[#F8FAFF] border border-[#E6ECFF] rounded-xl px-4 py-3"
                >
                  <div className="flex flex-col sm:flex-row sm:items-center gap-3">
                    <span className="text-sm text-[#211F54]">
                      <span className="font-medium">Start:</span> {r.startDate}
                    </span>
                    <span className="hidden sm:inline">•</span>
                    <span className="text-sm text-[#211F54]">
                      <span className="font-medium">End:</span> {r.endDate}
                    </span>
                    <span className="hidden sm:inline">•</span>
                    <span className="text-sm text-[#211F54]">
                      <span className="font-medium">Days:</span> {r.totalDays}
                    </span>
                  </div>
                  <div className="flex items-center gap-4">
                    <span className="text-sm font-semibold text-[#2563EB]">
                      {formatPrice(r.currency, r.totalCost)}
                    </span>
                    <button
                      className="text-xs text-red-600 hover:text-red-700 border border-red-200 rounded-lg px-3 py-1 bg-white"
                      onClick={() => removeRange(r.id)}
                    >
                      Remove
                    </button>
                  </div>
                </div>
              ))}
              <div className="pt-3 mt-2 border-t border-gray-200">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                  <span className="text-sm text-[#211F54]">
                    <span className="font-medium">Total days:</span> {totalDaysSelected}
                  </span>
                  <div className="flex flex-wrap items-center gap-2">
                    {/* Always show the base total (for 15s) */}
                    {Object.keys(totalsByCurrencyAll).length === 0 && (
                      <span className="text-sm text-[#211F54]">Total: 0</span>
                    )}
                    {Object.entries(totalsByCurrencyAll).map(([cur, amt]) => (
                      <span
                        key={cur}
                        className="text-xs font-semibold text-[#2563EB] bg-blue-50 border border-blue-200 rounded-lg px-2 py-1"
                      >
                        {formatPrice(cur, amt)}
                        {budgetDuration === "30s" && (
                          <span className="text-[10px] text-gray-500 ml-1">(15s)</span>
                        )}
                      </span>
                    ))}
                    {/* If 30s, show the doubled price as "Final (30s)" */}
                    {budgetDuration === "30s" &&
                      Object.entries(finalTotalsByCurrency).map(([cur, amt]) => (
                        <span
                          key={cur + "_30s"}
                          className="text-xs font-semibold text-[#D97706] bg-yellow-50 border border-yellow-200 rounded-lg px-2 py-1 ml-2"
                        >
                          {formatPrice(cur, amt)} <span className="text-[10px] text-[#D97706]">(Final 30s)</span>
                        </span>
                      ))}
                  </div>
                </div>
                {/* Note about final amount and duration, only for 30s */}
                {budgetDuration === "30s" && (
                  <div className="mt-2">
                    <span className="text-xs text-gray-500">
                      <b>Note:</b> The final total amount for 30 seconds is double the 15 seconds price.
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}
        {/* <div className="flex justify-between items-center bg-[#F8F8F8] p-4 mt-4">
          <div>
            <button
              className="border rounded-full px-10 py-2"
              onClick={() => dispatch(updateAdListinStep(0))}
            >
              Back
            </button>
          </div>
          <div>
            <button
              className="global_linear_gradient text-white rounded-full px-10 py-2"
              onClick={() => dispatch(updateAdListinStep(2))}
            >
              Next
            </button>
          </div>
        </div> */}
      </div>
    </div>
  );
}
