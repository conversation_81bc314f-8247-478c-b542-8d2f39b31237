import React, { useMemo, useState } from "react";

const weekDays = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

const formatJMD = (value) => {
  if (value === undefined || value === null || value === "") return "";
  return "J$" + Number(value).toLocaleString("en-JM");
};

function getDaysInMonth(year, month) {
  return new Date(year, month + 1, 0).getDate();
}

function getFirstDayIndex(year, month) {
  // Monday as first day (0=Monday, 6=Sunday)
  const jsDay = new Date(year, month, 1).getDay();
  return jsDay === 0 ? 6 : jsDay - 1;
}

function getCalendarMatrix(year, month) {
  const daysInMonth = getDaysInMonth(year, month);
  const firstDayIdx = getFirstDayIndex(year, month);
  const matrix = [];
  let day = 1 - firstDayIdx;
  for (let row = 0; row < 6; row++) {
    const week = [];
    for (let col = 0; col < 7; col++) {
      week.push(day > 0 && day <= daysInMonth ? day : "");
      day++;
    }
    matrix.push(week);
  }
  return matrix;
}

function normalizeDate(date) {
  if (!date) return "";
  if (/^\d{4}-\d{2}-\d{2}$/.test(date)) return date;
  const d = new Date(date);
  return (
    d.getFullYear() +
    "-" +
    String(d.getMonth() + 1).padStart(2, "0") +
    "-" +
    String(d.getDate()).padStart(2, "0")
  );
}

function getRuleForDateDefault(rules, year, month, day) {
  const dateStr = `${year}-${String(month + 1).padStart(2, "0")}-${String(
    day
  ).padStart(2, "0")}`;
  const matching = rules.filter((rule) => {
    if (rule.isActive === false) return false;
    if (!rule.startDate || !rule.endDate) return false;
    const start = normalizeDate(rule.startDate);
    const end = normalizeDate(rule.endDate);
    return start <= dateStr && dateStr <= end;
  });
  if (matching.length === 0) return null;
  const selected = matching.reduce((a, b) => {
    if (a.priority == null) return b;
    if (b.priority == null) return a;
    return Number(a.priority) > Number(b.priority) ? a : b;
  });
  return selected;
}

function formatPrice(currency, price) {
  if (currency === "USD") {
    // Format as US$ 1,234.56
    if (price === undefined || price === null || price === "") return "";
    return (
      "US$" +
      Number(price).toLocaleString("en-US", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      })
    );
  } else {
    // Default to JMD
    return formatJMD(price);
  }
}

export default function CalendarRuleView({
  rules = [],
  basePrice = "",
  baseCurrency = "JMD",
  dateOverrides = {},
  onCellClick = () => {},
  onRemoveOverride = () => {},
  calendarMonth,
  calendarYear,
  setCalendarMonth,
  setCalendarYear,
  getRuleForDate,
  onRangeSelected = () => {},
  selectedRanges = [],
}) {
  const calendar = useMemo(
    () => getCalendarMatrix(calendarYear, calendarMonth),
    [calendarYear, calendarMonth]
  );
  // Use custom getRuleForDate if provided, else default
  const ruleForDateFn = getRuleForDate || getRuleForDateDefault;

  const [selectionStartDay, setSelectionStartDay] = useState(null);
  const [hoverDay, setHoverDay] = useState(null);

  function buildDateStr(year, monthZeroBased, dayNumber) {
    return `${year}-${String(monthZeroBased + 1).padStart(2, "0")}-${String(
      dayNumber
    ).padStart(2, "0")}`;
  }

  const todayStr = useMemo(() => {
    const now = new Date();
    const y = now.getFullYear();
    const m = String(now.getMonth() + 1).padStart(2, "0");
    const d = String(now.getDate()).padStart(2, "0");
    return `${y}-${m}-${d}`;
  }, []);

  function isDatePast(dateStr) {
    return dateStr < todayStr;
  }

  function isDayInHoverRange(dayNumber) {
    if (selectionStartDay == null) return false;
    if (hoverDay == null) return dayNumber === selectionStartDay;
    const start = Math.min(selectionStartDay, hoverDay);
    const end = Math.max(selectionStartDay, hoverDay);
    const startDate = buildDateStr(calendarYear, calendarMonth, start);
    const endDate = buildDateStr(calendarYear, calendarMonth, end);
    if (isDatePast(startDate) || isDatePast(endDate)) return false;
    if (doesRangeOverlapExisting(startDate, endDate)) return false;
    return dayNumber >= start && dayNumber <= end;
  }

  function isDateInCommittedRanges(dateStr) {
    if (!Array.isArray(selectedRanges) || selectedRanges.length === 0) return false;
    return selectedRanges.some((r) => r.startDate <= dateStr && dateStr <= r.endDate);
  }

  function isCommittedRangeBoundary(dateStr) {
    if (!Array.isArray(selectedRanges) || selectedRanges.length === 0) return null;
    for (const r of selectedRanges) {
      // If single-day range, do not show Start/End label
      if (r.startDate === r.endDate && r.startDate === dateStr) return null;
      if (r.startDate === dateStr) return "start";
      if (r.endDate === dateStr) return "end";
    }
    return null;
  }

  function doesRangeOverlapExisting(startDate, endDate) {
    if (!Array.isArray(selectedRanges) || selectedRanges.length === 0) return false;
    return selectedRanges.some(
      (r) => !(endDate < r.startDate || startDate > r.endDate)
    );
  }

  function handleMouseEnterDay(dayNumber) {
    if (selectionStartDay == null) return;
    setHoverDay(dayNumber);
  }

  function handleClickDay(dayNumber) {
    if (selectionStartDay == null) {
      const startDateCandidate = buildDateStr(
        calendarYear,
        calendarMonth,
        dayNumber
      );
      if (isDatePast(startDateCandidate)) return;
      if (isDateInCommittedRanges(startDateCandidate)) return;
      setSelectionStartDay(dayNumber);
      setHoverDay(dayNumber);
      return;
    }
    const startDay = Math.min(selectionStartDay, dayNumber);
    const endDay = Math.max(selectionStartDay, dayNumber);
    const startDate = buildDateStr(calendarYear, calendarMonth, startDay);
    const endDate = buildDateStr(calendarYear, calendarMonth, endDay);
    if (isDatePast(startDate) || isDatePast(endDate)) return;
    if (doesRangeOverlapExisting(startDate, endDate)) return;
    onRangeSelected({ startDate, endDate });
    setSelectionStartDay(null);
    setHoverDay(null);
  }

  return (
    <div className="bg-white rounded-lg shadow p-0 overflow-x-auto border border-gray-200">
      <table className="min-w-full text-sm font-[poppins]">
        <thead>
          <tr>
            {weekDays.map((day) => (
              <th
                key={day}
                className="p-3 text-center font-semibold text-[#211F54] bg-[#F5F6FA] border-b border-gray-200"
              >
                {day}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {calendar.map((week, rowIdx) => (
            <tr key={rowIdx}>
              {week.map((day, colIdx) => {
                if (!day) return <td key={colIdx} />;
                let isDisabled = false;
                const dateStr = `${calendarYear}-${String(
                  calendarMonth + 1
                ).padStart(2, "0")}-${String(day).padStart(2, "0")}`;
                const isPast = isDatePast(dateStr);
                const isInCommitted = isDateInCommittedRanges(dateStr);
                // Disable past dates and prevent interactions on committed dates
                isDisabled = isPast || isInCommitted;
                const override = dateOverrides[dateStr];
                if (override) {
                  return (
                    <td
                      key={colIdx}
                      className="p-0 text-center align-top "
                      style={{ minWidth: 120, height: 90 }}
                    >
                      <div
                        className={`flex flex-col items-center justify-center h-full py-2 border rounded-lg relative ${
                          isDayInHoverRange(day)
                            ? "bg-blue-500/10 border-blue-300 ring-2 ring-blue-300 backdrop-blur-sm"
                            : isInCommitted
                            ? "bg-blue-400/10 border-blue-300 ring-2 ring-blue-400/60"
                            : "border-gray-200"
                        } ${isPast ? "opacity-40 " : "cursor-pointer"}`}
                        // Remove border: "none" from style so Tailwind border classes work
                          style={{ position: "relative" }}
                        onMouseEnter={() => !isDisabled && handleMouseEnterDay(day)}
                        onClick={() => !isDisabled && handleClickDay(day)}
                      >
                        {selectionStartDay === day && hoverDay !== selectionStartDay && (
                          <span className="absolute top-1 left-1 text-[10px] px-1.5 py-0.5 rounded bg-blue-600 text-white">Start</span>
                        )}
                        {hoverDay === day && selectionStartDay != null && hoverDay !== selectionStartDay && (
                          <span className="absolute top-1 right-1 text-[10px] px-1.5 py-0.5 rounded bg-blue-600 text-white">End</span>
                        )}
                        {isInCommitted && isCommittedRangeBoundary(dateStr) === "start" && (
                          <span className="absolute top-1 left-1 text-[10px] px-1.5 py-0.5 rounded bg-blue-700 text-white">Start</span>
                        )}
                        {isInCommitted && isCommittedRangeBoundary(dateStr) === "end" && (
                          <span className="absolute top-1 right-1 text-[10px] px-1.5 py-0.5 rounded bg-blue-700 text-white">End</span>
                        )}
                        {/* <button
                          className="absolute top-1 right-1 text-xs text-gray-500 hover:text-red-500 bg-white rounded-full p-0.5 border border-gray-300 z-10"
                          style={{ lineHeight: 1, fontSize: 14 }}
                          title="Remove override"
                          onClick={e => {
                            e.stopPropagation();
                            onRemoveOverride(day);
                          }}
                        >
                          ×
                        </button> */}
                          <span className="font-normal text-xl text-center">
                          {String(day).padStart(2, "0")}
                        </span>
                        <div className="flex items-center gap-2 mt-2">
                          <span
                            className={`text-xs font-semibold px-1.5 py-0.5 rounded border ${
                              isDayInHoverRange(day) || isInCommitted
                                ? "border-blue-300 text-[#2563EB]"
                                : "border-gray-200 text-[#888]"
                            }`}
                          >
                            {formatPrice(override.currency, override.price)}
                          </span>
                          {isInCommitted && (
                            <span className="text-[10px] px-1 py-0.5 rounded bg-blue-100 text-blue-700 border border-blue-200">Selected</span>
                          )}
                        </div>
                      </div>
                    </td>
                  );
                }
                const ruleForDay = ruleForDateFn(
                  rules,
                  calendarYear,
                  calendarMonth,
                  day
                );
                const highlightStyle = ruleForDay
                  ? {
                      // color: ruleForDay.color,
                      fontWeight: 600,
                    }
                  : {};
                const priceToShow = ruleForDay ? ruleForDay.price : basePrice;
                const currencyToShow = ruleForDay
                  ? ruleForDay.currency
                  : baseCurrency;
                return (
                  <td
                    key={colIdx}
                    className="p-0 text-center align-top "
                    style={{ minWidth: 120, height: 90 }}
                  >
                    <div
                        className={`flex flex-col items-center justify-center h-full py-2 border rounded-lg relative ${
                          isDayInHoverRange(day)
                            ? "bg-blue-500/10 border-blue-300 ring-2 ring-blue-300 backdrop-blur-sm"
                            : isInCommitted
                            ? "bg-blue-400/10 border-blue-300 ring-2 ring-blue-400/60"
                            : "border-gray-200"
                        } ${isPast ? "opacity-40 cursor-pointer" : "cursor-pointer"}`}
                      style={
                        isDisabled
                          ? {
                              opacity: 0.5,
                              pointerEvents: "none",
                              ...highlightStyle,
                            }
                          : highlightStyle
                      }
                        onMouseEnter={() => !isDisabled && handleMouseEnterDay(day)}
                        onClick={() => !isDisabled && handleClickDay(day)}
                    >
                        {selectionStartDay === day && hoverDay !== selectionStartDay && (
                          <span className="absolute top-1 left-1 text-[10px] px-1.5 py-0.5 rounded bg-blue-600 text-white">Start</span>
                        )}
                        {hoverDay === day && selectionStartDay != null && hoverDay !== selectionStartDay && (
                          <span className="absolute top-1 right-1 text-[10px] px-1.5 py-0.5 rounded bg-blue-600 text-white">End</span>
                        )}
                        {isInCommitted && isCommittedRangeBoundary(dateStr) === "start" && (
                          <span className="absolute top-1 left-1 text-[10px] px-1.5 py-0.5 rounded bg-blue-700 text-white">Start</span>
                        )}
                        {isInCommitted && isCommittedRangeBoundary(dateStr) === "end" && (
                          <span className="absolute top-1 right-1 text-[10px] px-1.5 py-0.5 rounded bg-blue-700 text-white">End</span>
                        )}
                      <span className="font-normal text-xl text-center">
                        {String(day).padStart(2, "0")}
                      </span>
                      <div className="flex items-center gap-2 mt-2">
                        <span
                            className={`text-xs font-semibold px-1.5 py-0.5 rounded border ${
                              isDayInHoverRange(day) || isInCommitted
                                ? "border-blue-300 text-[#2563EB]"
                                : "border-gray-200 text-[#888]"
                            }`}
                          style={
                            ruleForDay
                              ? { color: ruleForDay.color }
                              : { color: "#888" }
                          }
                        >
                          {formatPrice(currencyToShow, priceToShow)}
                        </span>
                          {isInCommitted && (
                            <span className="text-[10px] px-1 py-0.5 rounded bg-blue-100 text-blue-700 border border-blue-200">Selected</span>
                          )}
                      </div>
                    </div>
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
