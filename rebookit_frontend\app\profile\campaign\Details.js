import { updateAdListinStep } from '@/app/redux/slices/storeSlice';
import React, { useRef, useState } from 'react'
import { FaAsterisk } from "react-icons/fa";
import { useDispatch } from 'react-redux';

export default function Details() {
    const inputRef=useRef(null)
    const [imageState,setimageState]=useState(null)
    const dispatch = useDispatch()

    const handleChangeImage =(e)=>{
        let file=e.target.files[0]
        let url=URL.createObjectURL(file)
        setimageState(url)
    }
    console.log("imageState",imageState)
    const openImageHandler =()=>{
        inputRef?.current?.click()
    }
    return (
        <div className='p-4 bg-white border rounded-lg border-gray-100'>
            <div className='border border-gray-300 rounded-lg p-5'>
                <div className='flex justify-between'>
                    <p className='text-[20px] font-semibold'>Get Started with ads manager</p>
                    <div className='flex items-center'><button className='global_linear_gradient px-8 py-2 rounded-full text-white'>Preview</button> <div className='ml-2'> in Draft</div></div>
                </div>

                <section className='bodyPart'>
                    <div className='mt-4'>
                        <lable className="font-semibold text-[18px] flex items-center">Business Type<FaAsterisk size={10} className='ml-1' fill='red' /></lable>
                        <div className='mt-2'>
                            <select className='border-gray w-full border p-2 rounded-md'>
                                <option></option>
                            </select>
                            {/* <input className='border-gray w-full border p-2 rounded-md' placeholder='Book seller' /> */}
                        </div>
                    </div>

                    <div className='mt-4'>
                        <lable className="font-semibold text-[18px] flex items-center">Campaign name <FaAsterisk size={10} className='ml-1' fill='red' /></lable>
                        <div className='mt-2'>
                            <input className=' border-gray w-full border p-2 rounded-md' placeholder='New Awareness Campaign' />
                        </div>
                    </div>

                    <div className='mt-5'>
                        <label className='font-semibold text-[18px]'>Upload Image</label>
                        <div className='flex mt-3 bg-[#F5F5F5] w-fit pr-[3%]'>
                            <img className='h-[170px] w-[300px] rounded-md aspect-[3/4]' src={imageState|| "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/5cc58d8d-b0c1-4687-beaf-14b558c99858.jpg"}></img>
                            <div className='ps-3 flex flex-col justify-center pl-2'>
                                <button className='w-fit border-b-[1px] text-[#211F54]' onClick={openImageHandler}>Change</button>
                                <input ref={inputRef} type='file' className='hidden' onChange={(e)=>handleChangeImage(e)}/>
                                <div className='mt-4 pl-2'>
                                    <p> File Size: up to 5MB</p>
                                    <p>Optimal  dimentions: 600X280px</p>
                                    <p>Supportive  file types: JPG,JPEG,PNG,GIF,WEBP</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div className='mt-4'>
                        <lable className="font-semibold text-[18px]">Url</lable>
                        <div className='mt-2'>
                            <input className='border-gray w-full border p-2 rounded-md' placeholder='www.asdjj.com' />
                        </div>
                    </div>
                </section>

                <div className='flex justify-between items-center bg-[#F8F8F8] py-4 px-1 mt-3'>
                    <div><button className='border rounded-full px-10 py-2'>Back</button></div>
                    <div><button className='global_linear_gradient text-white rounded-full px-10 py-2' onClick={() => dispatch(updateAdListinStep(3))}>Next</button></div>
                </div>
            </div>
        </div>
    )
}
