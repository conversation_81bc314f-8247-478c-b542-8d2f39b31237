import React from 'react'
import style from "./campaign.module.scss"
import { updateAdListinStep } from '@/app/redux/slices/storeSlice'
import { useDispatch } from 'react-redux'
export default function Potentialreach() {

    const dispatch = useDispatch()
    return (
        <div>
            <div>
                <p className='font-semibold text-[20px]'>Homepage-Top</p>
                <p className='text-[#ADADAD] text-[16px] mt-1'>View Campaign parameters and estimated costs</p>
            </div>

            <div className='mt-8'>
                <div className='bg-[#F4F7FD] p-4'>
                    <p className='text-[16px]'>Estimated Views</p>
                    <p className='text-[24px] font-semibold'>12,500</p>
                </div>

                <div className='bg-[#F4F7FD] p-4 mt-3'>
                    <p className='text-[16px]'>Estimated Views</p>
                    <p className='text-[24px] font-semibold'>12,500</p>
                </div>
            </div>

            <div className='mt-8 '>
                <p className='text-[#2D2B32]'>Potential Reach</p>
                <div className='mt-1 grid  grid-cols-1 md:grid-cols-3 gap-4 flex items-center justify-between'>

                    <div className={`${style.gradientBorder}`}>
                        <div className={`p-3  p-4 shadow inner-content  rounded-lg ${style.innerContent}`}>
                            <p className='text-[18px] font-semibold'>Estimated Reach</p>
                            <p>500 - 1500</p>
                        </div>
                    </div>

                    <div className={`${style.gradientBorder}`}>
                        <div className={`p-3  p-4 border-gray shadow rounded-lg ${style.innerContent}`}>
                            <p className='text-[18px] font-semibold'>Average Engagement</p>
                            <p>10%</p>
                        </div>
                    </div>

                    <div className={`${style.gradientBorder}`}>
                        <div className={`p-3  p-4 border-gray shadow rounded-lg ${style.innerContent}`}>
                            <p className='text-[18px] font-semibold'>Click-Through Rate</p>
                            <p>2%</p>
                        </div>
                    </div>
                </div>
            </div>
            <div className='flex justify-between items-center bg-[#F8F8F8] py-4 px-1 mt-3'>
                <div><button className='border rounded-full px-10 py-2'>Back</button></div>
                <div><button className='global_linear_gradient text-white rounded-full px-10 py-2' onClick={() => dispatch(updateAdListinStep(3))}>Next</button></div>
            </div>
        </div>
    )
}
