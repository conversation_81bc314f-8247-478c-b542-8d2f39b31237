import {
    resetAdManagement,
  updateAdListinStep,
  updateAdManagement,
} from "@/app/redux/slices/storeSlice";
import React from "react";
import { useDispatch } from "react-redux";

export default function AdCard(item) {
  const dispatch = useDispatch();

  // Find the base price from pricingRules
  const basePrice = item?.pricingRules?.find(
    (rule) => rule?.name?.toLowerCase() === "base price"
  )?.price;

  const handleCreateAd = () => {
    dispatch(resetAdManagement());
    dispatch(
      updateAdManagement({
        adPlanId: item._id,
        isStep0Completed: true,
      })
    );
    dispatch(updateAdListinStep(1));}

  return (
    <div className="border border-[#f4f4f4] p-2 rounded-lg">
      <div>
        <img
          className="rounded-lg w-full max-h-[200px]"
          src={
            "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/eb76a233-6374-4d4f-b3ea-2906576e3500.png"
          }
        />
      </div>
      <div className="w-full flex justify-between mt-3  md:px-2">
        <p className="text-center">
          {item?.page?.charAt(0).toUpperCase() + item?.page?.slice(1)} -{" "}
          {item.position?.charAt(0).toUpperCase() + item.position?.slice(1)}
        </p>
        <p className="text-center">
          Starting From ${basePrice !== undefined ? basePrice : "--"}
        </p>
      </div>
      <div className="w-full flex justify-between mt-3 md:px-2">
        <p>Impressions</p> <p>50k to 100k</p>
      </div>
      <div className="mt-2">
        <button
          className="w-full global_linear_gradient rounded-full px-4 py-2 text-white"
          onClick={handleCreateAd}
        >
          Create Ad
        </button>
      </div>
    </div>
  );
}
