"use client"
import React from 'react'
import AdCard from './card'
import style from "../campaign.module.scss"

export default function BannerComp({ adPlans = [], isLoading = false, isGrid = false, setIsGrid = () => {} }) {
  return (
    <div className='px-2'>
      <div className=' my-4'>
        <div className='flex items-center border border-[#f4f4f4] px-3 py-2 rounded-lg gap-3'>
          <button
            className={`w-[50%] py-1 rounded transition-colors duration-300 ${isGrid ? "" : style.linear_gradient} `}
            onClick={() => setIsGrid(false)}
          >
            Banner
          </button>
          <button
            className={`w-[50%] py-1 rounded transition-colors duration-300 ${isGrid ? style.linear_gradient : " "} `}
            onClick={() => setIsGrid(true)}
          >
            Grid
          </button>
        </div>
      </div>
      {isLoading ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <div className="text-center py-8">Loading...</div>
        </div>
      ) : (
        <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5'>
          {adPlans && adPlans.length > 0 ? (
            adPlans.map((item, idx) => (
              <div className='col-span-1' key={item._id || idx}>
                <AdCard {...item} />
              </div>
            ))
          ) : (
            <div className="col-span-full text-center py-8">No ad plans found.</div>
          )}
        </div>
      )}
    </div>
  )
}
