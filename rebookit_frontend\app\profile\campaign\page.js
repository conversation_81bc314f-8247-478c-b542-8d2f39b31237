"use client";
import React, { useState, useEffect } from "react";
import NoCampaign from "./NoCampaign";
import style from "./campaign.module.scss";
import BannerComp from "./banner/page";
import { useDispatch, useSelector } from "react-redux";
import BudgetCalculator from "./BudgetCalculator";
import {
  resetAdManagement,
  updateAdListinStep,
} from "@/app/redux/slices/storeSlice";
import Potentialreach from "./Potentialreach";
import Details from "./Details";
import { getAdPlans } from "@/app/services/profile";

export default function Page() {
  const state = useSelector((state) => state.storeData);
  const adManagement = useSelector((state) => state.storeData.adManagement);
  const dispatch = useDispatch();

  // State for ad plans, loading, and grid/banner toggle
  const [adPlans, setAdPlans] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isGrid, setIsGrid] = useState(false);

  // Fetch ad plans when isGrid changes
  useEffect(() => {
    setIsLoading(true);
    getAdPlans({ type: isGrid ? "grid" : "banner", page: 1, limit: 10 })
      .then((res) => {
        setAdPlans(res?.data?.adPlans || []);
      })
      .finally(() => setIsLoading(false));
  }, [isGrid]);

  const isNextEnabled = adManagement?.[`isStep${state.adListingStep}Completed`] ;

  // memberShipTab
  return (
    <div className="h-full flex flex-col gap-4 min-h-[500px] ">
      <p className=" font-semibold text-[18px] ">Create Ad Campaign</p>

      {/* <div className="mb-8 flex items-center gap-4">
          <button
            type="button"
            className={`w-8 h-8 flex items-center justify-center rounded-full font-bold text-sm transition-all duration-200 ${
              !adManagement?.[`isStep${state.adListingStep}Completed`] 
                ? "bg-[#0161AB] text-white"
                : "bg-[#F5F8FE] text-[#0161AB] border border-[#0161AB]"
            }`}
            style={{ outline: "none" }}
            onClick={() => handleStepClick(1)}
            tabIndex={0}
          >
            {isNextEnabled ? (
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx="10" cy="10" r="10" fill="#4D7906" />
                <path
                  d="M6 10.5L9 13.5L14 8.5"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            ) : (
              1
            )}
          </button>
          <span
            className={`font-[poppins] text-sm ${
              !adManagement?.[`isStep${state.adListingStep}Completed`]  ? "text-[#0161AB] font-semibold" : "text-gray-400"
            }`}
          >
            Step 1: Select the plan
          </span>
          <button
            type="button"
            className={`w-8 h-8 flex items-center justify-center rounded-full font-bold text-sm transition-all duration-200 ${
              adManagement?.[`isStep${state.adListingStep}Completed`]  && isNextEnabled
                ? "bg-[#0161AB] text-white"
                : "bg-[#F5F8FE] text-[#0161AB] border border-[#0161AB]"
            } ${isNextEnabled ? "cursor-pointer" : "cursor-not-allowed"}`}
            style={{ outline: "none" }}
            onClick={() => isNextEnabled && handleStepClick(2)}
            tabIndex={isNextEnabled ? 0 : -1}
            disabled={!isNextEnabled}
          >
            2
          </button>
          <span
            className={`font-[poppins] text-sm ${
              adManagement?.[`isStep${state.adListingStep}Completed`]  ? "text-[#0161AB] font-semibold" : "text-gray-400"
            }`}
          >
            Step 2: Calendar Management
          </span>
        </div> */}

      {/* 
      <div className='h-full flex items-center justify-center '>
        <NoCampaign />
      </div> */}

      {state.adListingStep == 0 && (
        <BannerComp
          adPlans={adPlans}
          isLoading={isLoading}
          isGrid={isGrid}
          setIsGrid={setIsGrid}
        />
      )}
      {state.adListingStep == 1 && <BudgetCalculator adPlans={adPlans} />}

      {state.adListingStep == 2 && <Potentialreach />}
      {state.adListingStep == 3 && <Details />}

      <div className="flex items-end justify-between gap-0 px-4 py-4">
        <button
          className="border-2 border-red-500 px-3 py-2 rounded-full min-w-[110px]"
          onClick={() => {
            dispatch(updateAdListinStep(0));
            dispatch(resetAdManagement());
          }}
        >
          Discard
        </button>
        <div className="flex items-end gap-4">
          {state.adListingStep > 0 && (
            <button
              className="global_linear_gradient text-white rounded-full px-10 py-2 min-w-[110px]"
              onClick={() =>
                dispatch(updateAdListinStep(state.adListingStep - 1))
              }
            >
              Back
            </button>
          )}
          <button
            className={`rounded-full px-10 py-2 min-w-[110px] text-white ${
              adManagement?.[`isStep${state.adListingStep}Completed`]
                ? "global_linear_gradient"
                : "bg-gray-300 cursor-not-allowed"
            }`}
            onClick={() =>
              adManagement?.[`isStep${state.adListingStep}Completed`] &&
              dispatch(updateAdListinStep(state.adListingStep + 1))
            }
            disabled={!adManagement?.[`isStep${state.adListingStep}Completed`]}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  );
}
