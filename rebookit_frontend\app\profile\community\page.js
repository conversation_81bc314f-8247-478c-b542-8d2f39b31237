"use client";

import {
  addQuestion,
  allQuestion,
  deleteQuestion,
  myAnswers,
  myquestion,
  usersQuestionAnswer,
} from "@/app/services/community";
import moment from "moment";
import Link from "next/link";
import {useRouter} from "next/navigation";
import {useCallback, useEffect, useState} from "react";
import {HiOutlineChatBubbleLeftRight} from "react-icons/hi2";
import {SlShareAlt} from "react-icons/sl";
import {toast} from "react-toastify";
import {RxDotFilled} from "react-icons/rx";
import {getCategories} from "@/app/services/profile";
import AskQuestion from "@/app/components/common/AskQuestion";
import EditAnswer from "@/app/components/common/EditAnswer";
import ShareCard from "@/app/components/common/ShareCard";
import NoDataFound from "@/app/components/common/NoDataFound";
import {userDataFromLocal} from "@/app/utils/utils";

const CommunityComponent = () => {
  const [active, setactive] = useState(0);
  const [qustions, setQuestion] = useState([]);
  const [userQuestionsList, setuserQuestionsList] = useState([]);
  const [addQuestionInput, setaddQuestionInput] = useState("");
  const [questionWithAnswer, setquestionWithAnswer] = useState({});
  const [userReplied, setuserReplied] = useState([]);
  const [isLoading, setisLoading] = useState(false);
  const [searchText, setsearchText] = useState("");
  const [userAnswerForEdit, setuserAnswerForEdit] = useState("");
  const [modelForAnswer, setmodelForAnswer] = useState(false);
  const [answerObj, setanswerObj] = useState({});
  const [categoryState, setcategoryState] = useState([]);
  const [selectedId, setselectedId] = useState("");
  let router = useRouter();
  const handledebondSearchItem = (value) => {
    setsearchText(value);
  };

  let userData = userDataFromLocal();
  console.log("userData", userData);
  console.log("categoryState", categoryState);
  const fetchMasterCategory = async () => {
    try {
      let getCateogriesData = await getCategories();
      console.log("getCateogriesData", getCateogriesData);
      if (getCateogriesData?.status == 200) {
        if (getCateogriesData?.data?.categories) {
          let state = [
            {name: "", _id: ""},
            ...getCateogriesData?.data?.categories,
          ];
          setcategoryState(state);
        }
      }
      // setisLoading(false)
    } catch (err) {
      // setisLoading(false)
      console.log("Err fetchMasterCategory", err);
    }
  };

  console.log("questionWithAnswer", questionWithAnswer);
  const getQuestionFunc = async () => {
    setisLoading(true);

    let queryParams = ``;
    if (searchText) {
      queryParams = `searchTerm=${searchText}`;
    }
    let questions = await allQuestion(queryParams);
    if (questions.status == 200) {
      setQuestion(questions.data.data);
    }
    setisLoading(false);
  };
  console.log("qustions in community", qustions);

  const usersQuestion = async () => {
    setisLoading(true);
    let queryParams = ``;
    if (searchText) {
      queryParams = `searchTerm=${searchText}`;
    }
    let question = await myquestion(queryParams);
    if (question.status == 200) {
      console.log(question.data.data, "tum bin");
      setuserQuestionsList(question.data.data);
    }
    setisLoading(false);
  };

  function debounce(fn, delay) {
    let timeoutId;
    return function (...args) {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => fn.apply(this, args), delay);
    };
  }
  let deboundHandler = debounce((e) => setsearchText(e.target.value), 500);
  console.log(searchText, "searchText");
  const userAnswersFunc = async () => {
    setisLoading(true);

    let queryParams = ``;
    if (searchText) {
      queryParams = `searchTerm=${searchText}`;
    }
    let answers = await myAnswers(queryParams);
    if (answers.status == 200) {
      setuserReplied(answers.data);
    }
    setisLoading(false);
  };
  console.log(userReplied, "userReplied");
  const usersQuestionAnswerFunc = async () => {
    setisLoading(true);

    let queryParams = ``;
    if (searchText) {
      queryParams = `searchTerm=${searchText}`;
    }
    let anserOfquestion = await usersQuestionAnswer(queryParams);
    if (anserOfquestion.status == 200) {
      setquestionWithAnswer(anserOfquestion.data);
    }
    setisLoading(false);
  };
  // console.log("searchText", searchText)
  useEffect(() => {
    fetchMasterCategory();
  }, []);
  useEffect(() => {
    if (active == 0) {
      getQuestionFunc();
    }
    // else if (active == 1) {
    //   usersQuestionAnswerFunc();
    // }
    else if (active == 1) {
      userAnswersFunc();
    } else if (active === 2) {
      usersQuestion();
    }
  }, [active, searchText]);

  // let tabs = ["All", "Responses", "Answer", "Questions"];
  let tabs = ["All", "Responses Given", "Questions Asked"];
  let categories = [
    {label: "School Books", values: "School Books"},
    {label: "PEP", values: "PEP"},
    {label: "CSEC", values: "CSEC"},
    {label: "CAPE", values: "CAPE"},
    {label: "Others", values: "Others"},
  ];

  const submitQuestion = async () => {
    if (modelForAnswer) {
    } else {
      let submitResponse = await addQuestion({title: addQuestionInput});
      if (submitResponse.status == 200) {
        toast.success("Question Added Successfully");
        let docElement = document
          .getElementById("myModal")
          .classList.add("hidden");
      }
    }
  };
  const sample = {
    question: "What is ReBookIt?",
    date: "18 July 2025",
    author: "Kashish Akansha",
    responses: 15,
  };

  const deleteFunc = async (id) => {
    let response = await deleteQuestion(id);
    if (response.status == 200) {
      toast.success("Deleted Successfully");
      usersQuestion();
    }
  };
  const QuestionOnly = ({title, createdAt, askedBy, totalAnswers, _id}) => {
    return (
      <div className="bg-gray-100 rounded-2xl shadow p-6 flex flex-col justify-between h-full  m-3">
        <div>
          <p className="text-xl md:text-2xl font-semibold mb-2 ">
            {title || ""}
          </p>
          <p className="text-sm text-gray-600">
            {moment(createdAt).format("D/MMMM/YYYY")}
          </p>
          <p className="text-sm text-gray-600">
            Posted by:{askedBy?.firstName} {askedBy?.lastName}{" "}
          </p>
          <p className="text-sm text-gray-600">
            Responses: {totalAnswers || 0}
          </p>
        </div>
        <div className="mt-4 flex gap-3 flex-wrap">
          <Link
            href={`/allresponses?id=${_id}`}
            className="w-full md:w-[120px] flex gap-2 items-center"
          >
            <button className="w-full flex gap-2 items-center py-2 px-4 global_linear_gradient rounded-full text-white justify-center text-[18px] md:text-[15px]">
              View
            </button>
          </Link>

          <button
            onClick={() => deleteFunc(_id)}
            style={{background: "#ca2a30"}}
            className="w-full md:w-[120px] bg-[#ca2a30] flex gap-2 items-center py-2 px-4 global_linear_gradient rounded-full text-white justify-center text-[18px] md:text-[15px]"
          >
            Delete
          </button>
          <button
            onClick={() => {
              let docElement = document
                .getElementById("myShareModal")
                .classList.remove("hidden");
              setselectedId(_id);
            }}
            className="w-full md:w-[150px] border  flex gap-2 items-center py-2 px-4 rounded-full justify-center text-[18px] md:text-[15px]"
          >
            <SlShareAlt className="mr-2" /> Share
          </button>
        </div>
      </div>
    );
  };

  const UserGivenAnswer = () => {
    if (isLoading)
      return (
        <div className=" my-5 text-center font-semibold text-[24px]">
          Loading...
        </div>
      );
    else
      return (
        <div>
          {userReplied?.answers?.length > 0 ? (
            userReplied?.answers?.map((item) => {
              return (
                <div className="relative w-full bg-[#fdfdfd] rounded-lg my-3 px-5 py-4 border-[1px] border-[#EFEFEF]">
                  <div className="absolute right-10 top-3">
                    <button
                      onClick={() => {
                        setmodelForAnswer(true);
                        let docElement = document
                          .getElementById("myAnswerModal")
                          .classList.remove("hidden");
                        setaddQuestionInput(item?.userAnswerDocs.answerText);
                        setuserAnswerForEdit(item?.userAnswerDocs.answerText);
                        setanswerObj(item?.userAnswerDocs);
                      }}
                    >
                      Edit Answer
                    </button>
                  </div>
                  <p
                    className="text-[20px]  font-bold mt-6 md:mt-0"
                    style={{fontWeight: "800"}}
                  >
                    {item?.title}?
                  </p>
                  <p className="text-[16px] mt-4 ml-3 text-justify">
                    {" "}
                    <span className="font-bold" style={{fontWeight: "600"}}>
                      1. Answer 1-
                    </span>
                    {item?.userAnswerDocs.answerText}
                  </p>
                </div>
              );
            })
          ) : (
            <div className=" mx-auto col-span-2">
              <NoDataFound
                actionFunc={() =>
                  document.getElementById("myModal").classList.remove("hidden")
                }
                title={"No Data Found"}
                btntxt={"Ask Your Question"}
              />
            </div>
          )}

          {userReplied?.totalPages > 1 && (
            <div className="flex justify-center">
              <svg
                width="130"
                height="45"
                viewBox="0 0 164 52"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M101.203 5.00005L27.0963 5.00004C14.8929 5.00004 5.00021 14.5064 5.00021 26.2331"
                  stroke="#211F54"
                  strokeWidth="8.34158"
                />
                <path
                  d="M159.299 26.2332C159.299 14.5064 149.441 5.00005 137.281 5.00005L63.436 5.00004"
                  stroke="#0161AB"
                  strokeWidth="8.34158"
                />
                <path
                  d="M5 26.2332C5 37.9599 14.8927 47.4663 27.096 47.4663H101.203"
                  stroke="#EFDC2A"
                  strokeWidth="8.34158"
                />
                <path
                  d="M63.4355 47.4663H137.28C149.441 47.4663 159.298 37.9599 159.298 26.2332"
                  stroke="#0161AB"
                  strokeWidth="8.34158"
                />
                <path
                  d="M121 47.4663L46.8933 47.4663"
                  stroke="#4A8B40"
                  strokeWidth="8.34158"
                />
                <path
                  d="M121 5.00001L46.8933 5"
                  stroke="#FF0009"
                  strokeWidth="8.34158"
                />
                <rect
                  x="9"
                  y="7"
                  width="110"
                  height="39"
                  rx="19.5"
                  fill="white"
                />
                <text
                  x="50%"
                  y="50%"
                  dominantBaseline="middle"
                  textAnchor="middle"
                  fontSize="13"
                  fill="#211F54"
                  fontFamily="Poppins, sans-serif"
                >
                  Load More
                </text>
              </svg>
            </div>
          )}
        </div>
      );
  };

  const AnswersOfUserQuestion = () => {
    if (isLoading)
      return (
        <div className=" my-5 text-center font-semibold text-[24px]">
          Loading...
        </div>
      );
    else
      return (
        <div className="mt-5">
          {questionWithAnswer?.data?.length > 0 ? (
            questionWithAnswer?.data?.map((item) => {
              return (
                <div className=" w-full bg-[#fdfdfd] rounded-lg my-3 px-5 py-4 border-[1px] border-[#EFEFEF]">
                  <p
                    className="text-[20px]  font-bold"
                    style={{fontWeight: "800"}}
                  >
                    {item.title}?
                  </p>
                  <div className="ml-3 mt-3">
                    {item?.answerDocs?.length
                      ? item?.answerDocs?.map((ans, idx) => {
                          return (
                            <p className="text-[16px] my-2 flex items-center">
                              <RxDotFilled />{" "}
                              <span
                                className="font-bold"
                                style={{fontWeight: "600"}}
                              >
                                Answer {idx + 1}-
                              </span>
                              {ans.answerText}
                            </p>
                          );
                        })
                      : "NoBody Answerd yet"}
                  </div>
                </div>
              );
            })
          ) : (
            <div className=" mx-auto col-span-2">
              <NoDataFound
                actionFunc={() =>
                  document.getElementById("myModal").classList.remove("hidden")
                }
                title={"No Data Found"}
                btntxt={"Ask Your Question"}
              />
            </div>
          )}
          {questionWithAnswer?.totalCount >
            questionWithAnswer?.data?.length && (
            <div className="flex justify-center">
              <svg
                width="130"
                height="45"
                viewBox="0 0 164 52"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M101.203 5.00005L27.0963 5.00004C14.8929 5.00004 5.00021 14.5064 5.00021 26.2331"
                  stroke="#211F54"
                  strokeWidth="8.34158"
                />
                <path
                  d="M159.299 26.2332C159.299 14.5064 149.441 5.00005 137.281 5.00005L63.436 5.00004"
                  stroke="#0161AB"
                  strokeWidth="8.34158"
                />
                <path
                  d="M5 26.2332C5 37.9599 14.8927 47.4663 27.096 47.4663H101.203"
                  stroke="#EFDC2A"
                  strokeWidth="8.34158"
                />
                <path
                  d="M63.4355 47.4663H137.28C149.441 47.4663 159.298 37.9599 159.298 26.2332"
                  stroke="#0161AB"
                  strokeWidth="8.34158"
                />
                <path
                  d="M121 47.4663L46.8933 47.4663"
                  stroke="#4A8B40"
                  strokeWidth="8.34158"
                />
                <path
                  d="M121 5.00001L46.8933 5"
                  stroke="#FF0009"
                  strokeWidth="8.34158"
                />
                <rect
                  x="9"
                  y="7"
                  width="110"
                  height="39"
                  rx="19.5"
                  fill="white"
                />
                <text
                  x="50%"
                  y="50%"
                  dominantBaseline="middle"
                  textAnchor="middle"
                  fontSize="13"
                  fill="#211F54"
                  fontFamily="Poppins, sans-serif"
                >
                  Load More
                </text>
              </svg>
            </div>
          )}
        </div>
      );
  };

  // const getQuestionFunc = async (searchText, categoryId) => {
  //     console.log("searched Text", searchText)
  //     setisLoading(true)
  //     let searchQuery = ""
  //     if (searchText) {
  //         searchQuery += `searchTerm=${searchText}`
  //     }
  //     if (categoryId) {
  //         searchQuery += `categoryId=${categoryId}`
  //     }
  //     let questions = await allQuestion(searchQuery)
  //     if (questions.status == 200) {
  //         setQuestion(questions.data.data)
  //     }
  //     setisLoading(false)
  // }
  return (
    <div>
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 py-4 px-4">
        {/* Title */}
        <h1 className="text-lg sm:text-xl font-semibold text-[#211F54]">
          Ask Community
        </h1>

        {/* Controls */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4 w-full sm:w-auto">
          {/* Mobile Search (shown on <md) */}
          <div className="flex md:hidden items-center w-full bg-white rounded-full border border-[#211F54] px-3 h-11">
            <input
              type="text"
              placeholder="Search..."
              onChange={deboundHandler}
              autoComplete="off"
              className="flex-grow px-2 outline-none bg-transparent text-gray-700 text-sm"
            />
            <button className="ml-2 px-3 py-1 text-white text-sm font-medium rounded-full bg-gradient-to-r from-[#211F54] to-[#0161AB]">
              Search
            </button>
          </div>

          {/* Ask Question Button */}
          <div
            className="w-full sm:w-auto h-11 flex-shrink-0 flex items-center justify-center cursor-pointer"
            onClick={() => {
              const modal = document.getElementById("myModal");
              if (modal) modal.classList.remove("hidden");
            }}
          >
            <svg
              width="130"
              height="45"
              viewBox="0 0 164 52"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M101.203 5.00005L27.0963 5.00004C14.8929 5.00004 5.00021 14.5064 5.00021 26.2331"
                stroke="#211F54"
                strokeWidth="8.34158"
              />
              <path
                d="M159.299 26.2332C159.299 14.5064 149.441 5.00005 137.281 5.00005L63.436 5.00004"
                stroke="#0161AB"
                strokeWidth="8.34158"
              />
              <path
                d="M5 26.2332C5 37.9599 14.8927 47.4663 27.096 47.4663H101.203"
                stroke="#EFDC2A"
                strokeWidth="8.34158"
              />
              <path
                d="M63.4355 47.4663H137.28C149.441 47.4663 159.298 37.9599 159.298 26.2332"
                stroke="#0161AB"
                strokeWidth="8.34158"
              />
              <path
                d="M121 47.4663L46.8933 47.4663"
                stroke="#4A8B40"
                strokeWidth="8.34158"
              />
              <path
                d="M121 5.00001L46.8933 5"
                stroke="#FF0009"
                strokeWidth="8.34158"
              />
              <rect
                x="9"
                y="7"
                width="110"
                height="39"
                rx="19.5"
                fill="white"
              />
              <text
                x="50%"
                y="50%"
                dominantBaseline="middle"
                textAnchor="middle"
                fontSize="13"
                fill="#211F54"
                fontFamily="Poppins, sans-serif"
              >
                Ask Question
              </text>
            </svg>
          </div>

          {/* Desktop Search (shown on ≥md) */}
          <div className="hidden md:flex items-center w-full md:w-[300px] bg-white rounded-full border border-[#211F54] px-3 h-11">
            <input
              type="text"
              placeholder="Search..."
              onChange={deboundHandler}
              autoComplete="off"
              className="flex-grow px-2 outline-none bg-transparent text-gray-700 text-sm"
            />
            <button className="ml-2 px-3 py-1 text-white text-sm font-medium rounded-full bg-gradient-to-r from-[#211F54] to-[#0161AB]">
              Search
            </button>
          </div>
        </div>
      </div>
      {/* <div className="flex my-4 items-center w-full bg-white rounded-full border border-[#211F54] px-2 py-1 h-[45px] md:hidden">
        <input
          type="text"
          placeholder="Search..."
          onChange={(e) => handledebondSearchItem(e.target.value)}
          autoComplete="off"
          className="flex-grow px-4 py-1 outline-none bg-transparent text-gray-700 w-full"
        />
        <button
          className="px-4  py-1 text-white text-sm  font-medium rounded-full"
          style={{
            backgroundImage: "linear-gradient(to right, #211F54, #0161AB)",
          }}
        >
          Search
        </button>
      </div> */}

      <div className="flex overflow-x-auto whitespace-nowrap hide-scrollbar text-base sm:text-lg w-full border-b border-gray-300 py-2 px-4">
        {tabs.map((item, idx) => (
          <button
            key={idx}
            onClick={() => setactive(idx)}
            className={`
            mr-6 sm:mr-8 pb-1
            ${
              active === idx
                ? "bg-clip-text text-transparent bg-gradient-to-r from-[#211F54] to-[#0161AB] border-b-2 border-[#211F54]"
                : "text-gray-600 hover:text-[#211F54]"
            }
          `}
          >
            {item}
          </button>
        ))}
      </div>

      <div></div>
      {active == 0 && (
        <div>
          {!isLoading ? (
            qustions?.length > 0 ? (
              qustions.map((item) => {
                return (
                  <div className=" my-3 grid grid-cols-1 md:grid-cols-5 gap-4 p-4 bg-[#fdfdfd] p-5  rounded-[20px] border-[1px] border-[#EFEFEF]   ">
                    {/* Left Section */}
                    <div className="md:col-span-3">
                      <h3 className="text-[20px] font-semibold leading-8 text-justify truncate  break-words">
                        {item.title}
                      </h3>
                      <p className="my-[10px] text-[16px] leading-7 truncate text-justify">
                        {item?.latestAnswer?.answerText}
                      </p>
                      <div className="text-[14px] text-[#7E7E7E] space-y-1">
                        <p className="truncate">
                          Posted By:{item.askedBy.firstName}
                        </p>
                        <div>
                          Date: {moment(item.createdAt).format("DD/MM/YYYY")}
                        </div>
                        <div>Responses: {item.totalAnswers}</div>
                      </div>
                    </div>

                    {/* Right Section */}
                    <div className="md:col-span-2  p-4 flex flex-col items-center justify-center">
                      <Link
                        href={`/allresponses?id=${item._id}`}
                        className="w-full flex justify-center"
                      >
                        <button
                          className="w-full md:w-[250px] flex gap-2 items-center py-2 px-4 global_linear_gradient rounded-full text-white justify-center text-[18px] md:text-[16px]"
                          onClick={() => {}}
                        >
                          <HiOutlineChatBubbleLeftRight />
                          <span>All Responses</span>
                        </button>
                      </Link>
                      <button
                        className="w-full md:w-[250px] border mt-3 flex gap-2 items-center py-2 px-4 rounded-full justify-center text-[18px] md:text-[16px]"
                        onClick={() => {
                          let docElement = document
                            .getElementById("myShareModal")
                            .classList.remove("hidden");
                          setselectedId(item._id);
                        }}
                      >
                        <span className="flex items-center">
                          <SlShareAlt className="mr-2" /> Share
                        </span>
                      </button>
                    </div>
                  </div>
                );
              })
            ) : (
              <div className=" mx-auto min-h-[60vh] col-span-2">
                <NoDataFound
                  actionFunc={() =>
                    document
                      .getElementById("myModal")
                      .classList.remove("hidden")
                  }
                  title={"No Data Found"}
                  btntxt={"Ask Your Question"}
                />
              </div>
            )
          ) : (
            <div className="text-center  font-semibold text-[24px]">
              <div className="flex h-[60vh] items-center justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            </div>
          )}
        </div>
      )}
      {/* {active == 1 && <AnswersOfUserQuestion />} */}
      {active == 1 && <UserGivenAnswer />}
      {active == 2 && (
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-3 ">
          {!isLoading ? (
            userQuestionsList?.length > 0 ? (
              userQuestionsList.map((item) => {
                return <QuestionOnly {...item} />;
              })
            ) : (
              <div className=" mx-auto col-span-2">
                <NoDataFound
                  actionFunc={() =>
                    document
                      .getElementById("myModal")
                      .classList.remove("hidden")
                  }
                  title={"No Data Found"}
                  btntxt={"Ask Your Question"}
                />
              </div>
            )
          ) : (
            <div className="text-center w-full col-span-2 font-semibold text-[24px]">
              <div className="flex justify-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
              </div>
            </div>
          )}
        </div>
      )}

      {/* <!-- Trigger Button --> */}

      {/* <!-- Popup Modal --> */}
      <AskQuestion
        modelForAnswer={modelForAnswer}
        setmodelForAnswer={setmodelForAnswer}
        userAnswerForEdit={userAnswerForEdit}
        functionCallAfterSubmit={usersQuestion}
      />
      <EditAnswer
        userAnswerForEdit={userAnswerForEdit}
        setuserAnswerForEdit={setuserAnswerForEdit}
        answerObj={answerObj}
        userAnswersFunc={userAnswersFunc}
      />
      <ShareCard url={`allresponses?id=${selectedId}`} />
    </div>
  );
};
export default CommunityComponent;
