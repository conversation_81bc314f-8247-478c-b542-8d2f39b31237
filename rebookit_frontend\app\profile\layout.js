import profileCss from "./profile.module.scss";

import SideBar from "./sideBar";
import BuyAndSellComponent from "../components/about/BuyAndSellComponent";

export const metadata = {
  title: "Profile Page",
  description: "Allow users to see their profile",
};

function layout(props) {
  const {children} = props;

  return (
    <section className={`${profileCss.profileContainer}`}>
      <section className="container-wrapper mx-auto">
        <section className={`flex w-full justify-between lg:gap-5 `}>
          <SideBar />
          <div className="w-full lg:w-9/12">{children}</div>
        </section>
        {/* <section className='p-[20px] my-5 md:my-0 md:w-12/12'>
                    <BuyAndSellComponent />
                </section> */}
      </section>
    </section>
  );
}

export default layout;
