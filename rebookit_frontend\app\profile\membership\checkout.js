"use client"

import { useState, useEffect } from 'react';
import { loadStripe } from '@stripe/stripe-js';
import {
    Elements,
    useStripe,
    useElements,
    PaymentElement,
    CardNumberElement,
    CardExpiryElement,
    CardCvcElement,
} from '@stripe/react-stripe-js';
import { toast } from 'react-toastify';
import { getPaymentIntent } from '@/app/services/profile';
import { Router } from 'next/router';
import { useRouter } from 'next/navigation';
import { useSelector } from 'react-redux';
import SubmitButton from '@/app/components/common/SubmitButton';

const stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY);

export default function CheckoutForm({ setisPurchase  ,functionAfterSubmit}) {
    const stripe = useStripe();
    const elements = useElements();
    const [message, setMessage] = useState(null);
    const [isLoading, setIsLoading] = useState(false);
    let storeData = useSelector(state => state.storeData)
    const [selectedMembershipType, setSelectedMembershipType] = useState(0)
    let router = useRouter()
    console.log("functionAfterSubmit",functionAfterSubmit)
    // console.log("setisPurchase",setisPurchase)
    console.log("storeData", storeData)
    useEffect(() => {
        if (!stripe) {
            return;
        }

        const clientSecret = "pi_3RNsi3HBP1s03z5o13Nf0DBA_secret_EU5whTNeZ4Euk4hTdROpa8Z4n"

        if (!clientSecret) {
            return;
        }

        stripe.retrievePaymentIntent(clientSecret).then(({ paymentIntent }) => {
            switch (paymentIntent?.status) {
                case "succeeded":
                    setMessage("Payment succeeded!");
                    break;
                case "processing":
                    setMessage("Your payment is processing.");
                    break;
                case "requires_payment_method":
                    setMessage("Your payment was not successful, please try again.");
                    break;
                default:
                    setMessage("Something went wrong.");
                    break;
            }
        });
    }, [stripe]);
    console.log("storeData.selectedPlanToPurchase._id", storeData.selectedPlanToPurchase._id)
    const handleSubmit = async (e) => {

        // router.push("/success")
        // return
        try {
            
            e.preventDefault();

            if (!stripe || !elements) {
                return;
            }

            setIsLoading(true);

            // Create payment intent on your server

            let response = await getPaymentIntent({ planId: storeData.selectedPlanToPurchase._id })

            if (response.status != 200) {
                toast.error(response.data.message || "Something went wrong")
                return
            }

            const clientSecret = response.data.paymentIntent.client_secret
            const responseCardPayment = await stripe.confirmCardPayment(clientSecret, {
                payment_method: {
                    card: elements.getElement(CardNumberElement),
                    billing_details: {
                        name: 'loopmethod',
                        email: '<EMAIL>',

                    },
                },
            });
            console.log("responseCardPayment", responseCardPayment)

            if (responseCardPayment.error?.type === "card_error" || responseCardPayment.error?.type === "validation_error") {
                setMessage(responseCardPayment.error.message || 'Something went wrong!');
                toast.error(responseCardPayment.error.message || 'Something went wrong!')
            } else {
                setMessage("An unexpected error occurred.");
            }

            if (responseCardPayment?.paymentIntent?.status == "succeeded") {
                toast.success("Payment Is Success")
                setisPurchase(false)
                // router.push("/success")
            } else {

            }
            setIsLoading(false);
            // setTimeout(() => {
                
            // }, timeout);
            functionAfterSubmit()

        }
        catch (err) {
            console.log("err", err)
        }
    };

    const membershipTypeCheckHandler = (val) => {
        setSelectedMembershipType(val)
    }

    const ButtonComponent = () => {
        return <button type='submit' className='mt-4 global_linear_gradient text-white flex justify-center items-center py-2 px-5 rounded-full w-full gap-3.5'>
            {isLoading ? <>
                <svg
                    className="w-4 h-4 animate-spin text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                >
                    <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                    ></circle>
                    <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                    ></path>
                </svg>
                Loading...
            </> : <><svg xmlns="http://www.w3.org/2000/svg" width="11" height="18" viewBox="0 0 11 18" fill="none">
                <path d="M6.92383 0.5L6.00488 6.92969L5.92383 7.5H9.91602C7.97111 10.9012 6.51231 13.4568 5.53809 15.166C5.04678 16.028 4.67896 16.6752 4.43359 17.1064C4.34167 17.268 4.26644 17.399 4.20898 17.5H4.07617L4.99512 11.0703L5.07617 10.5H1.10547C1.11005 10.4931 1.11295 10.4875 1.11426 10.4854C1.11656 10.4816 1.11895 10.4785 1.12012 10.4766C1.12411 10.4698 1.12719 10.463 1.12891 10.46C1.13119 10.4559 1.13347 10.4514 1.13574 10.4473C1.14025 10.439 1.14566 10.4298 1.15039 10.4209C1.15203 10.4178 1.15421 10.4141 1.15625 10.4102C2.42172 8.17396 4.30269 4.8782 6.79102 0.5H6.92383Z" stroke="white" />
            </svg>
                <span className='text-sm leading-normal font-medium'>
                    Pay now
                </span></>}

        </button>

    }
    const InnerDiv=()=>{
        return <><svg xmlns="http://www.w3.org/2000/svg" width="11" height="18" viewBox="0 0 11 18" fill="none">
                <path d="M6.92383 0.5L6.00488 6.92969L5.92383 7.5H9.91602C7.97111 10.9012 6.51231 13.4568 5.53809 15.166C5.04678 16.028 4.67896 16.6752 4.43359 17.1064C4.34167 17.268 4.26644 17.399 4.20898 17.5H4.07617L4.99512 11.0703L5.07617 10.5H1.10547C1.11005 10.4931 1.11295 10.4875 1.11426 10.4854C1.11656 10.4816 1.11895 10.4785 1.12012 10.4766C1.12411 10.4698 1.12719 10.463 1.12891 10.46C1.13119 10.4559 1.13347 10.4514 1.13574 10.4473C1.14025 10.439 1.14566 10.4298 1.15039 10.4209C1.15203 10.4178 1.15421 10.4141 1.15625 10.4102C2.42172 8.17396 4.30269 4.8782 6.79102 0.5H6.92383Z" stroke="white" />
            </svg>
                <span className='text-sm leading-normal font-medium'>
                    Pay now
                </span></>
    }
    return (
        <div className="px-2.5">

            {/* Header */}
            <header>
                <h1 className='text-lg leading-normal font-semibold lg:text-2xl'>Membership Payment</h1>

                <p className='mt-2.5 text-sm leading-normal font-light opacity-75 lg:text-[#84949E] lg:opacity-100'>Get all access and an extra 20% off when you subscribe annually</p>
            </header>

            {/* Payment Form Inputs */}
            <form className='mt-5 flex flex-col gap-5 lg:flex-row lg:gap-16' onSubmit={handleSubmit}>

                {/* Left Section */}
                <div className='flex flex-col gap-5 lg:w-[55%]'>

                    {/* Billed To */}
                    <div>
                        <h3 className='text-sm text-[#606060] leading-normal font-medium'>Billed to</h3>

                        <div className='mt-2.5 flex flex-col gap-2.5'>
                            {/* Name */}

                            <input type='text' autoComplete="off" name='name' className='w-full py-[9px] px-3 rounded-[5px] border border-[#EFEFEF] font-medium text-[#1B252B] focus:outline-none bg-[#FDFDFD] text-sm placeholder:text-[#ADB7BD] leading-normal placeholder:font-light' placeholder='Card Holder Name' />
                            <div>
                                <CardNumberElement className='w-full py-[9px] px-3 rounded-[5px] border border-[#EFEFEF] font-medium text-[#1B252B] focus:outline-none bg-[#FDFDFD] text-sm placeholder:text-[#ADB7BD] leading-normal placeholder:font-light' />
                            </div>
                            {/* Card Number */}
                            {/* <input type='text' name='card_no' className='w-full py-[9px] px-3 rounded-[5px] border border-[#EFEFEF] font-medium text-[#1B252B] focus:outline-none bg-[#FDFDFD] text-sm placeholder:text-[#ADB7BD] leading-normal placeholder:font-light' placeholder='Card Number' /> */}

                            <div className='flex gap-[11px]'>
                                {/* Card Year */}
                                <CardExpiryElement className='w-full py-[9px] px-3 rounded-[5px] border border-[#EFEFEF] font-medium text-[#1B252B] focus:outline-none bg-[#FDFDFD] text-sm placeholder:text-[#ADB7BD] leading-normal placeholder:font-light' />
                                {/* <input type='text' name='card_year' className='w-[70%] py-[9px] px-3 rounded-[5px] border border-[#EFEFEF] font-medium text-[#1B252B] focus:outline-none bg-[#FDFDFD] text-sm placeholder:text-[#ADB7BD] leading-normal placeholder:font-light' placeholder='MM / YY' /> */}

                                {/* CVV */}
                                <CardCvcElement className='w-full py-[9px] px-3 rounded-[5px] border border-[#EFEFEF] font-medium text-[#1B252B] focus:outline-none bg-[#FDFDFD] text-sm placeholder:text-[#ADB7BD] leading-normal placeholder:font-light' />
                                {/* <input type='text' name='cvv' className='w-[30%] py-[9px] px-3 rounded-[5px] border border-[#EFEFEF] font-medium text-[#1B252B] focus:outline-none bg-[#FDFDFD] text-sm placeholder:text-[#ADB7BD] leading-normal placeholder:font-light' placeholder='CVV' /> */}

                            </div>
                        </div>
                    </div>

                    {/* Country */}
                    <div className='lg:mt-1'>
                        <h3 className='text-sm text-[#606060] leading-normal font-medium'>Billed to</h3>

                        <div className='mt-2.5 flex flex-col gap-2.5'>
                            {/* country */}
                            <input type='text' autoComplete="off" name='country' className='w-full py-[9px] px-3 rounded-[5px] border border-[#EFEFEF] font-medium text-[#1B252B] focus:outline-none bg-[#FDFDFD] text-sm placeholder:text-[#ADB7BD] leading-normal placeholder:font-light' placeholder='Country' />

                            {/* ZIP code */}
                            <input type='text' autoComplete="off" name='zip_code' className='w-full py-[9px] px-3 rounded-[5px] border border-[#EFEFEF] font-medium text-[#1B252B] focus:outline-none bg-[#FDFDFD] text-sm placeholder:text-[#ADB7BD] leading-normal placeholder:font-light' placeholder='Zip Code' />
                        </div>
                    </div>

                    <p className='text-sm text-[#5E6C73] font-light leading-normal'>Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>

                    {/* Price */}
                    <div className='flex flex-col gap-[7px] text-sm leading-normal lg:mt-5'>
                        <p className='font-bold lg:text-[22px]'>${storeData.selectedPlanToPurchase.price} / {storeData.selectedPlanToPurchase.planMonths} / User</p>
                        {/* <p className='text-[#0161AB] font-medium'>Details</p> */}
                    </div>

                </div>


                {/* Right Section */}
                <div className='lg:w-[45%]'>
                    <div className='flex flex-col gap-2.5'>
                        <h3 className='text-sm font-medium leading-normal text-[#606060]'>Membership Type</h3>

                        <div className={`py-3.5 px-3 border flex gap-2.5 cursor-pointer rounded-[5px] ${selectedMembershipType === 0 ? "global_linear_gradient border-[#fff] text-white" : "bg-[#FDFDFD] border-[#EFEFEF]"}`} onClick={() => membershipTypeCheckHandler(0)}>
                            {/* <input type='radio' name='membership' className='' value="0" checked={selectedMembershipType === 0} onChange={() => membershipTypeCheckHandler(0)} /> */}

                            <div className="relative self-center">
                                <input
                                    type="radio"
                                    name="membership"
                                    value="0"
                                    autoComplete="off"
                                    className="peer appearance-none w-4 h-4 border border-black rounded-full checked:bg-transparent checked:border-white flex items-center justify-center cursor-pointer"
                                    checked={selectedMembershipType === 0}
                                    onChange={() => membershipTypeCheckHandler(0)}
                                />
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="12"
                                    height="12"
                                    viewBox="0 0 12 12"
                                    fill="none"
                                    className="absolute left-[2.4px] top-[2.3px] hidden peer-checked:block pointer-events-none"
                                >
                                    <path
                                        d="M4.77578 9.00078L1.92578 6.15078L2.63828 5.43828L4.77578 7.57578L9.36328 2.98828L10.0758 3.70078L4.77578 9.00078Z"
                                        fill="white"
                                    />
                                </svg>
                            </div>

                            <div className='flex flex-col gap-1.5'>
                                <label className='text-sm font-semibold leading-normal lg:text-base cursor-pointer'>Pay {storeData?.selectedPlanToPurchase?.planMonths}</label>
                                <label className={`text-sm leading-normal cursor-pointer ${selectedMembershipType === 0 ? "text-white" : "text-[#8A9399]"}`}>${storeData.selectedPlanToPurchase.price} / {storeData.selectedPlanToPurchase.planMonths} Per Member</label>
                            </div>
                        </div>

                        {/* <div className={`py-3.5 px-3 border flex gap-2.5 cursor-pointer rounded-[5px] ${selectedMembershipType === 1 ? "global_linear_gradient border-[#fff] text-white" : "bg-[#FDFDFD] border-[#EFEFEF]"}`} onClick={() => membershipTypeCheckHandler(1)}>
                           

                            <div className="relative self-center">
                                <input
                                    type="radio"
                                    name="membership"
                                    value="1"
                                    className="peer appearance-none w-4 h-4 border border-black rounded-full checked:bg-transparent checked:border-white flex items-center justify-center cursor-pointer"
                                    checked={selectedMembershipType === 1}
                                    onChange={() => membershipTypeCheckHandler(1)}
                                />
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    width="12"
                                    height="12"
                                    viewBox="0 0 12 12"
                                    fill="none"
                                    className="absolute left-[2.4px] top-[2.4px] hidden peer-checked:block pointer-events-none"
                                >
                                    <path
                                        d="M4.77578 9.00078L1.92578 6.15078L2.63828 5.43828L4.77578 7.57578L9.36328 2.98828L10.0758 3.70078L4.77578 9.00078Z"
                                        fill="white"
                                    />
                                </svg>
                            </div>

                            <div className='w-full flex items-center justify-between'>
                                <div className='flex flex-col gap-1.5'>
                                    <label className='text-sm font-semibold leading-normal lg:text-base cursor-pointer'>Pay Annually</label>
                                    <label className={`text-sm leading-normal cursor-pointer ${selectedMembershipType === 1 ? "text-white" : "text-[#8A9399]"}`}>$18 / Month Per Member</label>
                                </div>
                                <span className='text-xs font-semibold leading-normal lg:text-sm'>
                                    Save 20%
                                </span>
                            </div>
                        </div> */}
                    </div>

                    <p className='mt-3.5 text-xs text-[#84949E] leading-normal font-medium'>By Continuing <span className='text-[#0161AB]'>you agree to our terms and conditions.</span></p>

                    {/* Pay now Button */}
                    {/* <ButtonComponent /> */}
                    <SubmitButton isLoading={isLoading} InnerDiv={InnerDiv}/> 
                </div>

            </form>
        </div>
    )
}