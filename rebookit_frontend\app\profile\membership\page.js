"use client";

import { useEffect, useState } from "react";
// import Subscription from "../landingPage/subscription/subscription";
import membershipCss from "./membership.module.scss";
import SubscriptionTable from "./subscriptionTable";
import CheckoutForm from "./checkout";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { subscriptionPlans } from "@/app/services/membership";
import { toast } from "react-toastify";
import Subscription from "@/app/components/landingPage/subscription/subscription";
import moment from "moment";
import { RxCross1 } from "react-icons/rx";

export default function Membership() {
  const [currentScreen, setcurrentScreen] = useState("Membership");
  const listScreen = ["Membership", "YourPlan"];
  const [isPurchase, setisPurchase] = useState(false);
  const [subscriptionList, setSubscriptionList] = useState([]);
  const [currentPlan, setcurrentPlan] = useState({});

  console.log("currentPlan", currentPlan);

  const stripePromise = loadStripe(
    process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY
  );

  async function getSubscriptionData() {
    try {
      let subscriptionData = await subscriptionPlans();

      // let subscriptionData = await getsubscriptionPlans()
      if (subscriptionData.status == 200) {
        setSubscriptionList(subscriptionData?.data.plans);
        setcurrentPlan(subscriptionData?.data?.currentSubscription);
      } else {
        toast.error("Something went wrong");
      }
    } catch (err) {
      toast.error("Something went wrong");
    }
  }
  useEffect(() => {
    getSubscriptionData();
  }, []);
  const consumption = () => {
    let data = {
      consumePostList: 0,
    };
    if (currentPlan?.planId) {
      data.consumePostList =
        currentPlan?.planId.postListing - currentPlan?.remainingPostListing;
    }

    return data;
  };

  const calWidth = () => {
    let str = "w-";
    if (currentPlan?.planId?.postListing) {
      str =
        str +
        `${currentPlan?.planId?.postListing - currentPlan?.remainingPostListing}` +
        `/${currentPlan?.planId?.postListing}`;
    }
    console.log("calWidth", str);
    return str;
  };

  console.log("currentPlan", currentPlan);
  if (isPurchase) {
    return (
      <section
        className={`${membershipCss.tableContainer} w-[95%] mx-auto my-5 overflow-hidden `}
      >
        <Elements stripe={stripePromise}>
          <CheckoutForm
            setisPurchase={setisPurchase}
            functionAfterSubmit={getSubscriptionData}
          />
        </Elements>
      </section>
    );
  } else
    return (
      <section className={membershipCss.subscriptionContainer}>
        <section className="pb-1">
          <header>
            <p className="text-[20px]  md:text-[24px] font-semibold">
              Membership Settings
            </p>
          </header>

          <header className="relative flex gap-10 text-[14px] my-5 after:content-[''] after:absolute after:left-0 after:w-full after:h-[1px] after:bg-gray-300 after:bottom-[-7px]">
            {listScreen?.map((list) => (
              <p
                key={list}
                className={`cursor-pointer ${list == currentScreen && membershipCss.activeList
                  }`}
                onClick={() => setcurrentScreen(list)}
              >
                {list}
              </p>
            ))}
          </header>
          {/*  
                    <div className="md:border md:border-[#ededed] md:px-5 md:pb-3 rounded-lg">
                        <header className="flex justify-between items-center my-4 md:my-7">
                            <p className="text-[16px] md:text-[18px] font-medium">Plan: {currentPlan?.planId?.planName || "NA"}</p>
                        </header>

                        <p className="text-[#8A8A8A] text-[14px]">Take your ads to the next level with more features.</p>
                        <p className="text-[#0161AB] font-semibold">${currentPlan?.planId?.price}/{currentPlan?.planId?.planMonths || "NA"}</p>
                    </div> */}
        </section>

        {currentScreen == "Membership" && (
          <section className="md:mt-5">
            <Subscription
              profileSection={true}
              setisPurchase={setisPurchase}
              subscriptionList={subscriptionList}
              currentPlan={currentPlan}
            />
          </section>
        )}

        {currentScreen == "YourPlan" && (
          <>
            <div className=" gap-4 ">
              <div className="border border-gray-300 rounded-md overflow-hidden ">
                <div className="bg-gray-200 p-3 flex justify-between items-center text-[16px] md:text-[18px]">
                  <span className="font-medium">Current Plan Summary</span>
                  {/* <button className="flex items-center gap-2 px-4 py-2 w-fit sm:w-[115px] text-sm md:text-[12px] global_linear_gradient  rounded-full text-white justify-center">
                    <span>Upgrade</span>
                  </button> */}
                  <button
                    className="rounded-full global_linear_gradient text-[white] text-[14px] px-5 py-1"
                    onClick={() =>
                      document
                        .getElementById("viewAll")
                        .classList.remove("hidden")
                    }
                  >
                    View Inclusions
                  </button>
                </div>
                <div className="p-3">
                  <div className="grid grid-cols-4 gap-4 mb-5">
                    <div>
                      <label className="block text-sm mb-1">Plan Name</label>
                      <div className="font-bold text-[18px] md:text-[22px]">
                        {currentPlan?.planId?.planName}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm mb-1">
                        Billing Cycle
                      </label>
                      <div className="font-bold text-[18px] md:text-[22px]">
                        {currentPlan?.planId?.planMonths}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm mb-1">Plan Cost</label>
                      <div className="font-bold text-[18px] md:text-[22px]">
                        J$ {currentPlan?.planId?.price}
                      </div>
                    </div>
                    <div>
                      <label className="block text-sm mb-1">Expire On</label>
                      <div className="font-bold text-[18px] md:text-[22px]">
                        {moment(currentPlan?.endDate).format(
                          "DD/MM/YYYY"
                        )}
                      </div>
                    </div>
                  </div>
                  <div className="flex justify-between items-center">
                    <p>TOTAL OFFER</p>
                  </div>
                  <div className="mt-5">
                    <div className="text-sm">Item Listed</div>
                    <div className="font-bold">
                      {currentPlan?.remainingListings?.reduce((acc, curr) => acc + (curr.noOfListing || 0), 0)} out of{" "}
                      {currentPlan?.planId?.listings?.reduce((acc, curr) => acc + (curr.noOfListing || 0), 0)}
                    </div>
                    <div className="mt-3">
                      <div className="relative h-[30px] w-full bg-gray-200 rounded-md overflow-hidden">
                        {calWidth() && (
                          <div
                            className={` ${calWidth()}  absolute left-0 top-0 h-full  bg-gradient-to-r from-[#0161AB] to-[#211F54]`}
                          ></div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <section
              className={`${membershipCss.tableContainer} w-[95%] mx-auto my-5 overflow-hidden `}
            >
              <SubscriptionTable />
            </section>
          </>
        )}

        {currentScreen == "paymentPage" && (
          <section
            className={`${membershipCss.tableContainer} w-[95%] mx-auto my-5 overflow-hidden `}
          >
            <Elements stripe={stripePromise}>
              <CheckoutForm functionAfterSubmit={getSubscriptionData} />
            </Elements>
          </section>
        )}

        <div
          id="viewAll"
          className=" text-[black] fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden bg-[#EAEAEA]"
        >
          <div className="bg-[#ffffff]  rounded-[35px] w-full max-w-lg shadow-lg relative p-4">
            <div
              className="flex w-[50px] cursor-pointer  h-[50px] justify-center absolute top-[-10px] right-[-10px] rounded-full bg-[white] "
              onClick={() => {
                let docElement = document
                  .getElementById("viewAll")
                  .classList.add("hidden");
                console.log("docElement", docElement);
                // setaddQuestionInput("")
                // setmodelForAnswer(false)
              }}
            >
              <button className="text-gray-500 hover:text-red-600 text-[20px] font-bold">
                &times;
              </button>
            </div>
            <div className="py-3 bg-white rounded-lg ">
              {/* <h2 className="text-xl font-semibold mb-4  border-b w-fit mx-auto">{modelForAnswer ? "Answer" : "Add Question"}</h2> */}
            </div>

            <div className="border-b border-[#f4f4f4] border-gray flex justify-between py-2 items-center">
              <p className="text-[16px] font-semibold py-2">Remain Listing</p>
              {/* <p>
                {currentPlan?.remainingListings?.reduce((acc,curr)=>acc+(curr.noOfListing || 0),0)}/
                                      {currentPlan?.planId?.listings?.reduce((acc,curr)=>acc+(curr.noOfListing || 0),0)}

              </p> */}

            </div>
            <div>
              {currentPlan?.remainingListings?.map((item,idx) => {
                return <div className="border-b border-[#f4f4f4] border-gray flex justify-between py-2 items-center">
                  <div className="text-[16px]  w-[50%] font-semibold py-2">{item.category.name}</div> : <div className="flex w-[50%] justify-end"><p>{item.noOfListing}</p>/<p>{currentPlan.planId.listings[idx].noOfListing}</p></div>

                </div>
              })}
            </div>
            <div className="border-b border-[#f4f4f4]  border-gray flex justify-between py-2 items-center">
              <p className="text-[16px] font-semibold py-2 w-[50%]">Boost</p>:
              <div className="w-[50%] flex justify-end"> {currentPlan?.planId?.boosts && <p>
                {currentPlan?.remainingBoosts}/
                {currentPlan?.planId?.boosts}
              </p>}
              </div>
            </div>
            {/* <div className="border-b border-[#f4f4f4] flex justify-between py-2 items-center">
              <p className="text-[16px] font-semibold py-2">Feature Listing</p>
              <p>
                {currentPlan?.remainingFeautureListings}/
                {currentPlan?.planId?.featuredListing}
              </p>
            </div> */}
            {/* <!-- Three Textareas --> */}
            <div className="flex mx-4">
              {/* <div><img className="w-[20px] h-[20px] rounded-full" src={userData?.profileImage} /></div> */}
              {/* <div className="ml-3">{modelForAnswer ? "Reply To Question" : "Ask  Question"}</div> */}
            </div>
            <div class=" mx-4 mt-3">
              {/* <textarea rows={8} value={addQuestionInput} onChange={(e) => setaddQuestionInput(e.target.value)} placeholder="Type here..." className="w-full bg-[#F4F4F4] rounded-lg p-2  outline-none"></textarea> */}
              {/* {!modelForAnswer && <p className="text-bold">Select Category</p>}
                            {!modelForAnswer && <select className="w-full p-3 border rounded-lg border-[#EAEAEA] outline-none" onChange={(e) => setselectedCategory(e.target.value)}>
                                {categoryState.map((item) => {
                                    return <option value={item._id}>{item.name}</option>
                                })}
                            </select>} */}
            </div>

            {/* <!-- Action Button --> */}
            <div class="my-2 flex justify-start mx-4">
              {/* <div className='flex gap-3.5 mt-3 items-center justify-center md:flex-col md:justify-center md:h-full md:w-fit md:items-start md:gap-2.5'>
                                            <button className='py-[7.5px] px-[30px] text-[13px] font-semibold leading-[18px] text-center text-white rounded-full global_linear_gradient md:order-2 md:text-base md:leading-[22px]'
                                                onClick={submitQuestion}
                                            >Submit</button>
                
                                        </div> */}
            </div>
          </div>
        </div>
      </section>
    );
}
