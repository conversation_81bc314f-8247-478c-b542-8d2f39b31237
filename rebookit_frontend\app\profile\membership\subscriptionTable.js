import React, {Fragment, useEffect, useState} from "react";
import membershipCss from "./membership.module.scss";

import {MdOutlineArrowUpward} from "react-icons/md";
import {historyOfSubscription} from "@/app/services/membership";
import moment from "moment";
import StatusBadge from "@/app/components/statusBadge/StatusBadge";

export default function SubscriptionTable() {
  const [tableListData, settableListData] = useState([]);
  const [isLoading, setisLoading] = useState(false);

  console.log(tableListData, "tableData");
  useEffect(() => {
    // settableListData([
    //     {
    //         invoice: "101",
    //         billingDate: "May 12 ,2025",
    //         status: "Paid",
    //         amount: "15",
    //         plan: "Basic"
    //     },
    //     {
    //         invoice: "101",
    //         billingDate: "May 12 ,2025",
    //         status: "Cancelled",
    //         amount: "15",
    //         plan: "Basic"
    //     },
    //     {
    //         invoice: "101",
    //         billingDate: "May 12 ,2025",
    //         status: "Paid",
    //         amount: "15",
    //         plan: "Basic"
    //     },
    // ])
  }, []);

  const getHistoryOfSbuscription = async () => {
    setisLoading(true);
    let subscriptionData = await historyOfSubscription();
    console.log("subscriptionData", subscriptionData);
    if (subscriptionData.status == 200) {
      settableListData(subscriptionData.data.subscriptions);
    }

    setisLoading(false);
  };

  console.log("tableListData", tableListData);
  useEffect(() => {
    getHistoryOfSbuscription();
  }, []);

  return (
    <table className="w-full table-fixed">
      <thead className="bg-[#f1f1f1] overflow-hidden">
        <tr>
          <th className="w-1/5 font-bold px-3 py-2 min-w-[50px] text-left text-[13px] md:text-[15px]">
            Invoice
          </th>
          <th className="w-1/5 font-bold px-3 py-2 min-w-[50px] text-left text-[13px]  md:text-[15px] ">
            Plan Name
          </th>
          <th className="w-1/5 font-bold px-3 py-2  min-w-[50px] text-left text-[13px] md:text-[15px] ">
            Billing Date
          </th>

          <th className="w-1/5 font-bold px-3 py-2 min-w-[50px] text-left text-[13px]  md:text-[15px] ">
            Amount
          </th>
          <th className="w-1/5 font-bold px-3 py-2 min-w-[50px] text-left text-[13px]  md:text-[15px] ">
            Payment Status
          </th>
        </tr>
      </thead>

      <tbody className="">
        {!isLoading ? (
          tableListData.length > 0 ? (
            tableListData?.map((data, idx) => (
              <Fragment key={idx} className="">
                <tr
                  className={`text-[11px] md:text-[14px] text-left border-b border-[#ededed]`}
                >
                  <td className=" w-1/5 px-[10px] py-[20px] xs:text-center">
                    {data?.planId?.planName}
                  </td>
                  <td className="w-1/5 px-[10px] py-[20px] xs:text-center">
                    {moment(data?.createdAt).format("MMMM D, YYYY")}
                  </td>
                  <td className="w-1/5 px-[10px] py-[20px] xs:text-center">
                    <b>J$ </b>
                    {data?.planId?.price}
                  </td>
                  <td
                    className="px-3 w-1/5 "
                    // className={`px-[5px] py-[10px]  ${
                    //   data?.status?.toLowerCase() == "paid"
                    //     ? membershipCss.paid
                    //     : membershipCss.cancelled
                    // }`}
                  >
                    {/* <div
                      className={`${
                        data?.status?.toLowerCase() == "paid"
                          ? "h-[5px]  text-[14px] w-[5px] bg-[#1E9609] rounded-full"
                          : "h-[5px] w-[5px] bg-[#A70909] text-[14px] rounded-[24px]"
                      }`}
                    ></div>
                    {data?.status} */}
                    <StatusBadge status={data?.paymentId?.status} />
                  </td>
                  <td className="w-1/5 px-[10px] py-[20px] xs:text-center">
                    {data?.paymentId?._id}
                  </td>
                </tr>
              </Fragment>
            ))
          ) : (
            <tr>
              <td colSpan={5} className="py-4 mx-auto text-center">
                No Data Available
              </td>
            </tr>
          )
        ) : (
          <tr>
            <td colSpan={5} className="py-4 mx-auto text-center">
              ...Loading
            </td>
          </tr>
        )}
      </tbody>
    </table>
  );
}
