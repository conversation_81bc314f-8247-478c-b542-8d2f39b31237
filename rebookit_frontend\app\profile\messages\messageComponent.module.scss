// my messages

.myMessagesContainer {

    // .borderGradient {
    //     border: 1px solid;
    //     border-image-slice: 1;
    //     border-width: 1px;
    //     border-image-source: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
    // }


    .borderGradient {
        position: relative;
        border-radius: 27px 27px 0 0;
        /* Your custom radius */
        background: white;
        /* Match your background */
        // padding: 1px;
        /* Adjust if needed */
        z-index: 0;

        &::before {
            content: "";
            position: absolute;
            inset: 0;
            padding: 1.3px;
            /* Border width */
            border-radius: inherit;
            background: linear-gradient(268deg, #211F54 11.09%, #0161AB 98.55%);
            -webkit-mask:
                linear-gradient(#fff 0 0) content-box,
                linear-gradient(#fff 0 0);
            -webkit-mask-composite: xor;
            mask-composite: exclude;
            pointer-events: none;
            z-index: -1;
        }
    }
}