"use client";

import React, { useEffect, useRef, useState } from "react";
import messagesComponentScss from "./messageComponent.module.scss";
import { Circles, Discuss } from "react-loader-spinner";
import Link from "next/link";
import { IoIosArrowDown, IoMdSearch } from "react-icons/io";
import { HiOutlineDotsVertical } from "react-icons/hi";
import Image from "next/image";
import { LuClock3 } from "react-icons/lu";
import { IoArrowBack } from "react-icons/io5";
import { FaMapMarkerAlt } from "react-icons/fa";
import { MdKeyboardArrowDown } from "react-icons/md";
import { BiLogoTelegram } from "react-icons/bi";
import predefinedChats from "./predefinedChats.json";
import { socket } from "@/app/socket";
import { useDispatch, useSelector } from "react-redux";
import { CHAT_ROUTES, USER_ROUTES } from "@/app/config/api";
import {
  Debounce,
  formatRelativeDate,
  formatTo12HourTime,
  getToken,
  userDataFromLocal,
} from "@/app/utils/utils";
import { toast } from "react-toastify";
import {
  deleteChatById,
  getAllChat,
  getBooksById,
  getChatById,
  uploadPhotoSingle,
} from "@/app/services/profile";
import { update_userInfo_api, userInfo_api } from "@/app/services/auth";
import { MdDeleteOutline } from "react-icons/md";
import { FaPlusCircle } from "react-icons/fa";
import {
  nullItemId,
  updateChatId,
  updateItemId,
} from "@/app/redux/slices/storeSlice";
import { useRouter } from "next/navigation";
import { ItemListStatusEnum } from "@/app/config/constant";
import { createInitialsAvatar } from "@/app/components/InitialAvatar/CreateInitialAvatar";

const chatSuggestions = {
  admin: [
    "Thank you for your interest! Would you like to buy it?",
    "Hi! How can I help you with this item?",
    "Yes, it’s still available. Would you like to buy it?",
    "Sorry, it’s no longer available.",
    "Yes, What you like to offer!",
  ],
  client: [
    "Hi, I’m interested!",
    "Is this item still available?",
    "When would it be available for pick-up/delivery?",
    "Do you offer delivery?",
    "Are there any defect or issues I should know about?",
    "Is the price negotiable?",
  ],
};

function myMessages() {
  let userData = userDataFromLocal();
  console.log("userData", userData);
  const itemId = useSelector((x) => x?.storeData?.itemId);
  const storeData = useSelector((x) => x?.storeData);
  const fileInputRef = useRef();
  const menuRef = useRef(null);
  const chatRef = useRef(null);
  const messagesEndRef = useRef(null);
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [openChat, setOpenChat] = useState(false); // For Mobile purpose
  const [openFilter, setOpenFilter] = useState(false);
  const [chatToggle, setChatToggle] = useState("chats"); // chats , make offer
  const [openChatTemplate, setOpenChatTemplate] = useState(true);
  const [predefinedChatsList, setPredefinedChatsList] = useState([
    "Hey! Yes, it’s still available 😊",
    "No, it's not available",
  ]);
  const [chatList, setChatList] = useState([]);
  const [chatId, setChatId] = useState(null);
  const [chatCount, setChatCount] = useState(0);
  const [selectedChat, setSelectedChat] = useState({
    itemId: null,
    userId: userData?._id,
    userName: "",
    backCoverImage: "",
    profileImage: "",
    itemPrice: null,
    isSeller: null,
    status: ""
  });

  const [messages, setMessages] = useState([]);
  const [sender, setSender] = useState(null);
  const [searchText, setSearchText] = useState(null);
  const [filter, setFilter] = useState("all"); // all, buyers, sellers
  const [chatListLoading, setChatListLoading] = useState(false);
  const [makeOfferValue, setMakeOfferValue] = useState("");
  const [sellerMakeOfferShownComponent, setSellerMakeOfferShownComponent] =
    useState("offer_pitch"); //  seller -> offer_pitch, rejected_offer, accepted_offer
  const [buyerMakeOfferShownComponent, setBuyerMakeOfferShownComponent] =
    useState("send_offer"); // buyer -> send_offer, edit_offer, counter_offer, rejected_counter_offer, offer_accepted,
  const [itemOfConversation, setitemOfConversation] = useState({});
  const debouncedFetchRef = useRef();
  const [isImageLoaded, setisImageLoaded] = useState(false);
  const [currentPosition, setCurrentPosition] = useState({});
  const [showOptionThreeDot, setshowOptionThreeDot] = useState(false);
  const menuBtnRef = useRef(null)
  const dispatch = useDispatch();
  const queryParams = new URLSearchParams(window.location.search);
  let urlItemId = queryParams.get("itemId")
  console.log("urlItemId", urlItemId)
  // const [showChatBodyCondition,setshowChatBodyCondition]=useState(false)
  console.log("itemOfConversation", itemOfConversation);
  const showMessageCondition =
    chatId || itemId || chatList?.length > 0 || searchText?.length;
  let showChatBodyCondition = chatId || itemId;
  console.log("showMessageCondition", showMessageCondition);
  console.log("itemId", itemId);
  console.log("storeData", storeData);
  console.log("chatListLoading", chatListLoading);
  console.log("chatList", chatList);
  console.log("showOptionThreeDot", showOptionThreeDot);

  // Scroll Behavior
  console.log("sender", sender)
  useEffect(() => {
    if (showMessageCondition) {
      // Scroll to the bottom when messages update
      chatRef.current?.scrollIntoView({ behavior: "smooth" });
    }

    const container = messagesEndRef.current;
    if (container) {
      container.scrollTo({
        top: container.scrollHeight,
        behavior: "smooth",
      });
    }
  }, [messages]);

  // function to open chat in mobile and update the chat id
  const openChatHandler = (chatId) => {
    setChatId(chatId);
    fetchChatById(chatId);
    setOpenChat(true);
  };

  const closeChatHandler = () => {
    setOpenChat(false);
  };

  // add the predefined list from the chat
  useEffect(() => {
    if (messages.length < 1) return;
    // console.log("PredefinedChats", PredefinedChats)
    const char = messages.toReversed().find((m) => {
      if (m?.sender !== sender?._id) return m;
    });
    // console.log("char", char)
    const list = predefinedChats[char?.text];

    if (list?.length > 0) {
      setPredefinedChatsList(list);
    }
  }, [messages]);

  // fetch user profile

  const fetchCurrentLocation = () => {
    let position = navigator.geolocation.getCurrentPosition((position) => {
      console.log("position", position);
      setCurrentPosition({
        lat: position.coords.latitude,
        long: position.coords.longitude,
      });
    });
  };

  console.log(currentPosition, "currentPosition");

  const fetchProfile = async () => {
    try {
      let userToken = getToken();
      let userData = userDataFromLocal();
      let responseUserInfo = await userInfo_api();

      setSender(userData);
      // fetch(USER_ROUTES.USER_INFO, {
      //     method: "get",
      //     headers: {
      //         "Authorization": `Bearer ${userToken}`
      //     }
      // }).then(async res => {
      //     const response = await res.json();
      //     console.log("response is", response)
      //     if (!response.error) {
      //         let userData = response.data;
      //         setSender(userData)
      //     } else {
      //         toast.error(response.message || "No Info Found")
      //     }
      // })
    } catch (error) {
      console.log(error);
      toast.error(error || "Internal Server Error");
    }
  };

  // fetch item data
  const fetchItemData = async () => {
    try {
      let userToken = getToken();
      let searchData = await getBooksById(itemId);
      console.log("searchData fetchItemData", searchData)
      if (searchData.status == 200) {
        setitemOfConversation(searchData.data);

        setSelectedChat((prev) => ({
          ...prev,
          userId: searchData?.data?.createdBy?._id,
          itemId: searchData?.data?._id,
          backCoverImage:
            searchData?.data?.images.length && searchData?.data?.images[0],
          profileImage: searchData?.data?.createdBy?.profileImage,
          userName: `${searchData?.data?.createdBy?.firstName} ${searchData?.data?.createdBy?.lastName}`,
          isSeller: userData._id == searchData.data?.createdBy?._id,
          status: searchData?.data.status
        }));
        // setChatToggle(userData._id == searchData.data?.createdBy?._id?"chats":"make offer")
        setSelectedChat((prev) => {
          return { ...prev, itemPrice: searchData.data.price };
        });
        setMakeOfferValue(`${searchData.data.price}`);
        // setSelectedChat(searchData.data?.coverImage)
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  // fetch chat list in the left side [ search and filters ]
  const fetchChatList = async (searchTerm, filterText) => {
    try {
      setChatListLoading(true);
      let userToken = getToken();

      const payloadData = {};
      if (searchTerm) payloadData["searchTerm"] = searchTerm;
      if (filterText) payloadData["chatType"] = filterText;
      let allChat = await getAllChat(payloadData);

      console.log("allChat", allChat);
      if (allChat.status == 200) {
        let chatIdToOpen = ""
        let nonDeletedData = allChat?.data?.filter(
          (item) => {
            if (itemId === item.sellerItemId) {
              chatIdToOpen = item._id
            }
            return !item.deletedBy.includes(userData._id)
          }
        );
        console.log("chatIdToOpen", chatIdToOpen)
        if (chatIdToOpen) {
          fetchChatById(chatIdToOpen)
        }
        setChatList(nonDeletedData);
        if (itemId) {
          for (let index = 0; index < nonDeletedData.length; index++) {
            if (storeData.chatId == nonDeletedData[index]._id) {
              // fetchChatById(nonDeletedData[index]._id)
            }
          }
        }
        setLoading(false);
      } else {
        toast.error(allChat?.message || "Internal Server Error");
        setLoading(false);
      }
      setChatListLoading(false);
      // fetch(CHAT_ROUTES.ALL_CHATS, {
      //     method: "POST",
      //     headers: {
      //         "Content-Type": "application/json",
      //         "Authorization": `Bearer ${userToken}`
      //     },
      //     body: JSON.stringify(payloadData)
      // }).then(async res => {
      //     const response = await res.json();
      //     let chatData = response
      //     if (!chatData?.error) {
      //         console.log("chat list Data", chatData)
      //         setChatList(chatData?.data)
      //         setLoading(false)
      //         setChatListLoading(false)
      //     } else {
      //         toast.error(response?.message || "Internal Server Error")
      //         setLoading(false)
      //         setChatListLoading(false)
      //     }
      // })
    } catch (error) {
      console.log("error", error);
      setLoading(false);
      setChatListLoading(false);
    }
  };
  console.log("compare", selectedChat?.userId, "userId", userData?._id);
  // fetch the selected chat data like conversation, participants and messages data
  const fetchChatById = async (chatId) => {
    try {
      // getAllChat()
      let userToken = getToken();
      let chatData = await getChatById(chatId);
      console.log("chatData getChatById", chatData);
      if (chatData.status == 200) {
        setitemOfConversation(chatData.data.conversation[0].item[0]);
        setChatCount(chatData?.data?.count);
        const conversation = chatData?.data?.conversation?.[0];

        const participant1 = conversation?.participants_docs?.[0];
        const participant2 = conversation?.participants_docs?.[1];
        console.log("participant1", participant1);
        console.log("participant2", participant2);

        const receiver =
          userData?._id == participant1._id
            ? participant2
            : participant1;

        console.log("receiver fetch", receiver);

        setSelectedChat((prev) => ({
          ...prev,
          conversationId: conversation?._id,
          itemId: conversation?.sellerItemId,
          userId: receiver?._id,
          userName: receiver?.firstName + " " + receiver?.lastName,
          profileImage: receiver?.profileImage,
          backCoverImage:
            conversation?.item?.[0]?.images.length &&
            conversation?.item?.[0]?.images[0],
          itemPrice: conversation?.item?.[0]?.price,
          isSeller: conversation?.item?.[0]?.createdBy === userData._id,
          status: conversation?.item?.[0]?.status
        }));
        // setChatToggle(conversation?.item?.[0]?.createdBy === userData._id?"chats":"make offer")
        // messages
        setMessages(chatData?.data?.messages);
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  // update user profile of chat types consistency
  const updateProfile = async (chatType) => {
    try {
      const token = getToken();
      const payload = {};
      if (chatType === "sellers") payload["chatType"] = chatType;
      else return;
      await update_userInfo_api({ selectedChatFilters: payload });
    } catch (error) {
      console.log("error", error);
    }
  };

  // fetching data's
  useEffect(() => {
    // fetch item data only when the user came from the search path and there is no chat with that user
    if (itemId) {
      fetchItemData();
      setOpenChat(true)
    }

    fetchCurrentLocation();
    fetchChatList();
    fetchProfile();
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setshowOptionThreeDot(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Initialize debounced function once
  useEffect(() => {

    debouncedFetchRef.current = Debounce(fetchChatList, 500);
    const openCloseMenu = (e) => {
      if (menuBtnRef.current && !menuBtnRef.current.contains(e.target)) {
        setOpenFilter(false)
      }

    }
    document.addEventListener("click", openCloseMenu)
    return () => {
      debouncedFetchRef.current?.cancel();
      document.removeEventListener("click", openCloseMenu)

      // dispatch(nullItemId())
    };
  }, []);

  // filter or search chat list
  useEffect(() => {
    if (debouncedFetchRef.current) {
      debouncedFetchRef.current(searchText, filter);
    }
  }, [searchText, filter]);

  // socket connection check
  useEffect(() => {
    console.log("socket", socket);
    socket.on("connect", () => {
      console.log("Connected to socket");
      console.log("socket inside", socket);
    });
    socket.on("disconnect", () => {
      console.log("disconnected to socket");
    });
    socket.on("connect_error", (err) => {
      console.log("Connect error:", err.message); // Example: "xhr poll error", "WebSocket is closed before the connection is established"
    });
  }, []);

  // socket message receive
  useEffect(() => {
    const handleMessage = (data) => {
      console.log("data on message", data);
      // appending the message in the message array
      setMessages((prev) => [...prev, data.message]);
      let nonDeletedData = data?.chats?.filter(
        (item) => {
          return !item.deletedBy.includes(userData._id)
        }
      );
      setChatList(nonDeletedData);
    };

    socket.on("on-message", handleMessage);
    socket.on("connect_error", (err) => {
      console.error("Socket connection error:", err.message);
    });
    socket.on("connect", () => {
      console.log("Connected to socket server:", socket.id);
    });

    // Cleanup the listener on unmount or rerun
    return () => {
      socket.off("on-message", handleMessage);
    };
  }, []);
  const avatarUrl = (userName)=>{
    return createInitialsAvatar(`${userName}`, {
    bgColor: "#3f51b5",
    textColor: "#ffffff",
  });}
  console.log("itemId", itemId);
  console.log("selectedChat", selectedChat);
  console.log("sender", sender);
  console.log("messages", messages);

  // socket event to send message
  const sendMessage = (message, mediaType, mediaUrl) => {
    
    if (!message || message.trim().length === 0) return;

    let data = {
      recipientId: selectedChat.userId || itemOfConversation.createdBy._id,
      sellerItemId: selectedChat?.itemId || itemId,
      message: {
        text: message.trim(),
      },
    };

    if (mediaUrl) {
      data.message[mediaType] = mediaType;
      data.message[mediaUrl] = mediaUrl;
    }
    socket.emit("send-message", data);
  };

  const imageSend = (mediaUrl) => {
    let data = {
      recipientId: selectedChat.userId || itemOfConversation.createdBy._id,
      sellerItemId: selectedChat?.itemId || itemId,
      message: {
        mediaType: "image",
        mediaUrl: mediaUrl,
        isMedia: true,
      },
    };
    socket.emit("send-message", data);
  };

  const locationSend = (mediaUrl) => {
    console.log("inside locationSend");
    navigator.permissions?.query({ name: "geolocation" }).then(function (result) {
      console.log("Permission status:", result.state);
    });
    let position = navigator.geolocation.getCurrentPosition((position) => {
      console.log("position", position);
      if (!position.coords) {
        return;
      }
      setCurrentPosition({
        lat: position.coords.latitude,
        long: position.coords.longitude,
      });
      let data = {
        recipientId: selectedChat.userId || itemOfConversation.createdBy._id,
        sellerItemId: selectedChat?.itemId || itemId,
        message: {
          mediaType: "location",
          // "mediaUrl": mediaUrl,
          // "isMedia": true,
          text: JSON.stringify({
            lat: position.coords.latitude,
            long: position.coords.longitude,
          }),
        },
      };
      socket.emit("send-message", data);
    });
  };

  // sending message
  const sendMessageHandler = (e) => {
    e.preventDefault();

    // Send message
    sendMessage(e.target.message.value);

    // clear the message
    e.target.message.value = "";
  };

  const filterHandler = (filterText) => {
    setFilter(filterText);
    updateProfile(filterText);
    setOpenFilter(false);
    setChatId(null);
    setOpenChat(false); // close chat for mobile
  };

  // chat template open handler
  const chatTemplateOpenHandler = () => {

    setOpenChatTemplate(!openChatTemplate);
    const container = messagesEndRef.current;
    // if (container) {
    //   container.scrollTo({
    //     top: container.scrollHeight,
    //     behavior: "smooth",
    //   });
    // }
  };

  let prefix = "J$ "; // default prefix
  // handling the default prefix will not get send, without price cannot send something
  const makeOfferInputHandler = (e) => {
    let inputValue = e.target.value;
    // Remove prefix if user tries to type it again
    if (inputValue.startsWith(prefix)) {
      inputValue = inputValue.slice(prefix.length);
    } else {
      // If user tries to delete the prefix entirely, ignore that change
      inputValue = inputValue.replace(/[^0-9]/g, "");
    }

    // Allow only digits
    const numeric = inputValue.replace(/[^0-9]/g, "");

    setMakeOfferValue(inputValue);
  };
  console.log("makeOfferValue", makeOfferValue);
  // Send Offer price by buyer
  const sendMakeOfferHandler = (e) => {
    e.preventDefault();
    let sliceData;
    if (makeOfferValue.includes(prefix)) {
      sliceData = makeOfferValue.slice(prefix.length);
    } else {
      sliceData = makeOfferValue;
    }
    console.log(sliceData, "sliceData");
    // if (sliceData < selectedChat.itemPrice) {
    //     toast.error("Can Not Quote Less Then Price")
    //     return
    // }

    if (makeOfferValue === prefix) return;
    let sendOfferText = "J$" + makeOfferValue
    sendMessage(sendOfferText);
    // setMakeOfferValue("J$ ")
  };

  // handling the make offer component shown to the buyer or seller
  // useEffect(() => {
  //     if (selectedChat?.isSeller) {
  //         // last message of price
  //         const priceRegex = new RegExp(`^${prefix}/[^0-9]/g`);

  //         const lastPriceMessageByBuyer = messages?.toReversed().find(m => {
  //             const text = m.text || ""; // assuming messages have a `text` field
  //             console.log("text", text)
  //             const isPrice = /^J\$ \d+(\.\d{1,2})?$/.test(text.trim())
  //             console.log("isPrice", isPrice)
  //             if (isPrice) return m;
  //         });

  //         console.log("lastPriceMessageByBuyer", lastPriceMessageByBuyer)

  //         // To extract the actual price if found
  //         const price = lastPriceMessageByBuyer
  //             ? parseFloat(lastPriceMessageByBuyer.text.trim().match(priceRegex)[1])
  //             : null;

  //         console.log("price", price)
  //     }
  // }, [messages])

  const handshakeSvg = (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="28"
      height="15"
      viewBox="0 0 34 20"
      fill="none"
      className="md:w-[34px] md:h-5"
    >
      <path
        d="M22.6324 0H18.1601C17.7436 0 17.3427 0.156193 17.0355 0.437341L11.9176 5.12314C11.9123 5.12835 11.9071 5.13876 11.9019 5.14397C11.0377 5.95617 11.0533 7.25258 11.7926 8.05958C12.4538 8.78327 13.8439 8.97591 14.7134 8.20015C14.7186 8.19494 14.729 8.19494 14.7342 8.18974L18.8942 4.37862C19.2326 4.07144 19.7637 4.09227 20.0708 4.43069C20.3832 4.7691 20.3572 5.29495 20.0188 5.60734L18.6599 6.85168L26.2405 13.0057C26.3915 13.1307 26.5268 13.266 26.6518 13.4066V3.33212L23.8091 0.489406C23.5019 0.177019 23.075 0 22.6324 0ZM28.3231 3.34254V14.9998C28.3231 15.9213 29.0676 16.6658 29.9891 16.6658H33.3212V3.34254H28.3231ZM30.8222 14.9998C30.364 14.9998 29.9891 14.6249 29.9891 14.1667C29.9891 13.7086 30.364 13.3337 30.8222 13.3337C31.2803 13.3337 31.6552 13.7086 31.6552 14.1667C31.6552 14.6249 31.2803 14.9998 30.8222 14.9998ZM0 16.6606H3.33212C4.25367 16.6606 4.99819 15.9161 4.99819 14.9946V3.34254H0V16.6606ZM2.49909 13.3337C2.95726 13.3337 3.33212 13.7086 3.33212 14.1667C3.33212 14.6249 2.95726 14.9998 2.49909 14.9998C2.04093 14.9998 1.66606 14.6249 1.66606 14.1667C1.66606 13.7034 2.04093 13.3337 2.49909 13.3337ZM25.194 14.3021L17.4208 7.99189L15.8588 9.42367C14.3125 10.8346 11.9436 10.6992 10.5639 9.19458C9.16334 7.66389 9.27268 5.29495 10.793 3.89963L15.0518 0H10.6888C10.2463 0 9.82456 0.177019 9.51217 0.489406L6.66425 3.33212V14.9894H7.61703L12.3289 19.2534C13.7554 20.4145 15.8536 20.1958 17.0147 18.7692L17.0251 18.7588L17.957 19.5658C18.7849 20.2427 20.0084 20.1125 20.68 19.2847L22.3148 17.275L22.596 17.5041C23.3093 18.082 24.361 17.9779 24.9389 17.2594L25.4335 16.6502C26.0166 15.9317 25.9073 14.8852 25.194 14.3021Z"
        fill="url(#paint0_linear_2930_11490)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_2930_11490"
          x1="0"
          y1="10"
          x2="33.3212"
          y2="10"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#E1020C" />
          <stop offset="1" stopColor="#4D7906" />
        </linearGradient>
      </defs>
    </svg>
  );

  const defaultSellerComponent = (
    title,
    price,
    para,
    btn1,
    btn2,
    rejected = false,
    rejectedPrice = "",
    showBtn = true,
    showIcon = false
  ) => (
    <div className="flex w-full flex-col justify-center items-center pt-[11px] pb-[7px] px-5">
      {rejected && (
        <div className="flex flex-row gap-[5px] mb-[5px]">
          <p className="text-xs leading-normal uppercase">
            Buyer’s offer: J${rejectedPrice}
          </p>
          <span className="text-xs leading-normal uppercase text-[#FF2929]">
            Rejected
          </span>
        </div>
      )}

      <p className="text-xs leading-normal uppercase">{title}</p>

      <div className="flex justify-center items-center gap-2.5">
        <p className="my-[1px] text-[28px] capitalize font-bold leading-normal global_text_linear_gradient">
          J$ {price}
        </p>
        {showIcon && handshakeSvg}
      </div>

      <p className="capitalize text-xs leading-normal">{para}</p>

      {showBtn && (
        <div className="w-full flex justify-between gap-2 mt-[5px]">
          <button className="py-[13px] px-2.5 md:px-5 w-[48%] md:w-[45%] uppercase global_linear_gradient text-white text-[10px] leading-normal md:text-sm tracking-[0.7px] rounded-full">
            {btn1}
          </button>

          <button className="py-[13px] px-2.5 md:px-5 w-[48%] md:w-[45%] uppercase global_linear_gradient text-white text-[10px] leading-normal md:text-sm tracking-[0.7px] rounded-full">
            {btn2}
          </button>
        </div>
      )}
    </div>
  );

  const offerInput = (
    <form onSubmit={sendMakeOfferHandler} className="flex justify-between">
      <div className="w-[50%]">
        <div className="w-full flex items-stretch my-2 ">
          {/* Left Prefix */}
          <div className="bg-[#C0DEFB] pl-2  text-[12px] rounded-l-md flex items-center text-sm font-medium">
            J$
          </div>

          {/* Input Field */}
          <input
            type="text"
            autoComplete="off"
            className="w-full  bg-[#C0DEFB] pr-2 py-2 text-xs font-medium leading-normal rounded-r-md focus:outline-none"
            value={makeOfferValue}
            placeholder="Enter Price"
            min={itemOfConversation.price}
            onChange={makeOfferInputHandler}
          />
        </div>

        <div className="w-full flex justify-between gap-2.5 items-center">
          {makeOfferValue < selectedChat.itemPrice ? (
            <div className=" w-full bg-[#f00711] py-[3px] px-[15px] rounded-sm">
              <p className="text-xs md:text-sm text-white font-medium leading-normal">
                Too low to offer
              </p>
              <p className="text-[10px] md:text-xs leading-normal text-[white]">
                Less chance of seller’s reply.
              </p>
            </div>
          ) : (
            <div className="w-full global_linear_gradient py-[3px] px-[15px] rounded-sm">
              <p className="text-xs md:text-sm text-white font-medium leading-normal">
                Very good offer
              </p>
              <p className="text-[10px] md:text-xs leading-normal text-[#D8D8D8]">
                High chance of seller’s reply.
              </p>
            </div>
          )}
        </div>
      </div>
      <div className="flex flex-col justify-end align-end">
        <button
          type="submit"
          className="uppercase text-xs md:text-sm text-white py-2.5 px-[30px] global_linear_gradient rounded-full"
        >
          SEND
        </button>
      </div>
    </form>
  );

  const makeOfferComponents = {
    buyer: {
      // Send Offer with input
      send_offer: (
        <div className="w-full py-2 px-5 md:px-7">
          <div className="overflow-x-auto flex gap-3.5">
            {new Array(4).fill(0).map((_, idx) => {
              const basePrice = selectedChat?.itemPrice ?? 0;
              const price = Math.round(basePrice * Math.pow(1.2, idx));

              return (
                <span
                  key={idx}
                  className="w-fit whitespace-nowrap py-1.5 px-2.5 text-[8px] leading-normal text-center rounded-sm text-white gradient-all-round-border border-2 lg:py-[6.5px] lg:px-[22px] lg:text-sm font-semibold cursor-pointer active:scale-95"
                  onClick={() => sendMessage(`J$ ${price}`)}
                >
                  <p className="global_text_linear_gradient">J$ {price}</p>
                </span>
              );
            })}
          </div>

          {offerInput}
        </div>
      ),

      // Edit Offer
      edit_offer: (
        <div className="pt-2 pb-1 px-5 flex flex-col justify-center items-center w-full">
          <p className="uppercase text-xs leading-normal">YOUR OFFER</p>
          <p className="py-[1px] text-[28px] capitalize font-bold global_text_linear_gradient">
            J$ 440
          </p>
          <p className="text-xs capitalize leading-normal">
            waiting for seller’s response.
          </p>
          <button className="uppercase text-sm leading-normal py-2.5 md:py-[13px] px-5 global_linear_gradient rounded-full w-full text-white mt-2.5 tracking-[0.7px]">
            Edit offer
          </button>
        </div>
      ),

      // counter offer
      counter_offer: (
        <div className="flex w-full flex-col justify-center items-center pt-[11px] pb-[7px] px-5">
          <p className="text-xs leading-normal uppercase">
            Seller’s counter offer
          </p>
          <p className="my-[1px] text-[28px] capitalize font-bold leading-normal global_text_linear_gradient">
            J$ 440
          </p>
          <p className="capitalize text-xs leading-normal">
            Buyer waiting for your response
          </p>

          <div className="w-full flex justify-between gap-2 mt-[5px]">
            <button className="py-[13px] px-2.5 md:px-5 w-[48%] md:w-[45%] uppercase global_linear_gradient text-white text-[10px] leading-normal md:text-sm tracking-[0.7px] rounded-full">
              Make new offer
            </button>

            <button className="py-[13px] px-2.5 md:px-5 w-[48%] md:w-[45%] uppercase global_linear_gradient text-white text-[10px] leading-normal md:text-sm tracking-[0.7px] rounded-full">
              Let’s go ahead
            </button>
          </div>
        </div>
      ),

      // Rejected than make offer
      rejected_counter_offer: (
        <div className="py-2 px-5 md:px-7 w-full">
          <div className="flex gap-2 md:gap-3.5 w-full">
            <div className="global_linear_gradient py-[7px] px-2.5 md:px-4 text-center text-xs md:text-lg leading-normal font-semibold text-white rounded-sm w-[52%]">
              J$ 435{" "}
              <span className="text-[#FF2929] font-medium">Rejected</span>
            </div>

            <div className="global_linear_gradient py-[7px] px-2.5 md:px-4 text-center text-xs md:text-lg leading-normal font-semibold text-white rounded-sm w-1/2">
              Seller’s Offer J$ 440
            </div>
          </div>

          {offerInput}
        </div>
      ),

      // offer accepted
      offer_accepted: (
        <div className="flex flex-col items-center pt-2.5 pb-2 px-5 md:px-8">
          <p className="text-xs leading-normal uppercase">offer accepted</p>
          <div className="flex gap-2.5 items-center">
            <p className="text-[22px] md:text-[28px] leading-normal capitalize font-bold global_text_linear_gradient">
              J$ 440
            </p>
            {handshakeSvg}
          </div>
          <p className="text-xs leading-normal capitalize">
            now you’re one step closer to thr final deal.
          </p>

          <button className="global_linear_gradient mt-2.5 rounded-full border border-[#0161AB] w-full text-white py-[13px] px-5 uppercase text-sm tracking-[0.7px] leading-normal">
            let’s meet
          </button>
        </div>
      ),
    },

    seller: {
      offer_pitch:
        // <div className='flex w-full flex-col justify-center items-center pt-[11px] pb-[7px] px-5'>
        defaultSellerComponent(
          "Buyer’s Offer",
          440,
          "Buyer waiting for your response",
          "Make new offer",
          "Let’s go ahead"
        ),
      // </div>

      rejected_offer: defaultSellerComponent(
        "Your Counter offer",
        440,
        "waiting for buyer’s response",
        "",
        "",
        true,
        440,
        false
      ),

      accepted_offer: defaultSellerComponent(
        "offer accepted",
        440,
        "now you’re one step closer to thr final deal.",
        "Let’s meet",
        "Ask contact",
        false,
        "",
        true,
        true
      ),
    },
  };
  console.log("showChatBodyCondition", showChatBodyCondition);
  async function deleteConversation(id) {
    let deleteResponse = await deleteChatById(id);
    console.log("deleteResponse", deleteResponse);
    if (deleteResponse.status == 200) {
      toast.success("Deleted Successfully");
      // showChatBodyCondition=false
      dispatch(updateItemId(""));
      setChatId(null);
      fetchChatList();
    } else {
      toast.error(deleteResponse?.data?.message || "Something Went Wrong");
    }
  }

  const handleButtonClick = () => {
    fileInputRef.current?.click();
  };

  let str = "image/png";
  console.log(str.includes("image"), "includes");
  const handleFileChange = async (e) => {
    console.log("event", e.target.files);

    const file = e.target.files[0];
    if (file) {
      let isImage = file.type.includes("image");
      if (!isImage) {
        toast.error("Only Images Are Allowed");
        return;
      }
      // setLoading(true)
      const formData = new FormData();
      formData.append("file", file);
      try {
        const data = await uploadPhotoSingle(formData);
        console.log("data uploadPhotoSingle", data);
        if (data.status == 200) {
          imageSend(data.data.url);
        }
      } catch (err) {
        console.log("err in file chjange", err);
      }
    }
  };

  const ChatMessage = ({ lat, long }) => {
    if (
      typeof lat !== "number" ||
      typeof long !== "number" ||
      isNaN(lat) ||
      isNaN(long)
    ) {
      console.error("Invalid coordinates:", lat, long);
      return null;
    }
    console.log("ChatMessage", lat, long);
    const mapUrl = `https://www.google.com/maps?q=${lat},${long}&z=15&output=embed`;

    return (
      <div
        className="w-full max-w-[150px] h-[150px] rounded overflow-hidden border shadow"
        onClick={() => window.open(mapUrl)}
      >
        <iframe
          src={mapUrl}
          width="100%"
          height="100%"
          allowFullScreen=""
          loading="lazy"
        ></iframe>
      </div>
    );

    // For normal text messages
  };

  let mediaType = { image: "image", location: "location" };
  const RenderMessage = (message) => {
    if (mediaType.image == message.mediaType) {
      return (
        <img
          className={`w-[100px] h-[100px] rounded-md object-cover  transition-opacity duration-300 ${isImageLoaded ? "opacity-100" : "opacity-0"
            }`}
          src={message.mediaUrl}
          onLoad={() => setisImageLoaded(true)}
        />
      );
    } else if (mediaType.location == message.mediaType) {
      console.log();
      return ChatMessage(JSON.parse(message.text));
    } else {
      return (
        <p className="py-4 px-6 global_linear_gradient rounded-tl-[32px] rounded-tr-[32px] rounded-bl-[32px] text-xs leading-normal text-white w-fit hyphens-auto wrap-anywhere overflow-hidden">
          {message?.text}
        </p>
      );
    }
  };

  let lastMessageText = (chat) => {
    if (chat.lastMessage.mediaType == mediaType.location) {
      return (
        <p className="mt-1 text-sm leading-normal text-[#636363] w-[70%] line-clamp-2 lg:mt-[9px] lg:w-[90%]">
          Map
        </p>
      );
    } else if (chat.lastMessage.mediaType == mediaType.image) {
      return (
        <p className="mt-1 text-sm leading-normal text-[#636363] w-[70%] line-clamp-2 lg:mt-[9px] lg:w-[90%]">
          Image
        </p>
      );
    } else {
      return (
        <p className="mt-1 text-sm leading-normal text-[#636363] w-[70%] line-clamp-2 lg:mt-[9px] lg:w-[90%]">
          {chat?.lastMessage?.text}
        </p>
      );
    }
  };
  console.log("makeOfferValue", makeOfferValue);
  console.log("openChat", openChat);
  let elseImage =
    "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/03df84d0-59d5-4991-977a-f9ab67fae5eb.jpg";

  if (loading) {
    return (
      <div className="h-full w-full flex justify-center items-center bg-white ">
        <Circles
          height="80"
          width="80"
          color="#4fa94d"
          ariaLabel="circles-loading"
          wrapperStyle={{}}
          wrapperClass=""
          visible={true}
        />
      </div>
    );
  } else
    return (
      <div className={`${messagesComponentScss.myMessagesContainer} w-full`}>
        <div>
          {/* Header */}
          <div
            className={` ${openChat ? "hidden" : "flex"
              } lg:flex justify-between items-center px-2.5`}
          >
            <h3 className="text-lg md:text-2xl font-semibold leading-normal">
              My Messages
            </h3>

            <footer className="flex justify-center items-center">
              <Link href="/search" aria-label="View all book categories">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="91"
                  height="34"
                  viewBox="0 0 91 34"
                  fill="none"
                  className="lg:hidden"
                >
                  <path
                    d="M87.5156 17C87.5156 9.26817 80.448 3.00029 71.7296 3.00029L18.7857 3.00028C10.0673 3.00028 2.99966 9.26817 2.99966 17"
                    stroke="#211F54"
                    strokeWidth="5.49989"
                  />
                  <path
                    d="M87.5156 17C87.5156 9.26817 80.473 3.00029 71.7854 3.00029L19.0285 3.00028"
                    stroke="#0161AB"
                    strokeWidth="5.49989"
                  />
                  <path
                    d="M3 17C3 24.7318 10.0676 30.9997 18.786 30.9997H71.7299C80.4483 30.9997 87.516 24.7318 87.516 17"
                    stroke="#EFDC2A"
                    strokeWidth="5.49989"
                  />
                  <path
                    d="M19.0293 30.9997H71.7861C80.4737 30.9997 87.5164 24.7318 87.5164 17"
                    stroke="#0161AB"
                    strokeWidth="5.49989"
                  />
                  <path
                    d="M71.7305 3L18.7866 3"
                    stroke="#FF0009"
                    strokeWidth="5.49989"
                  />
                  <path
                    d="M71.7305 31L18.7866 31"
                    stroke="#4A8B40"
                    strokeWidth="5.49989"
                  />
                  <rect
                    x="5.85938"
                    y="3.91406"
                    width="78.5871"
                    height="25.714"
                    rx="12.857"
                    fill="white"
                  />

                  <text
                    x="50%"
                    y="50%"
                    dominantBaseline="middle"
                    textAnchor="middle"
                    fontSize="12"
                    fill="#211F54"
                    fontWeight="500"
                    fontFamily="Poppins, sans-serif"
                  >
                    Explore
                  </text>
                </svg>

                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="132"
                  height="54"
                  viewBox="0 0 132 54"
                  fill="none"
                  className="hidden lg:inline"
                >
                  <path
                    d="M127.527 26.9922C127.527 14.8465 117.281 5.00045 104.642 5.00045L27.8874 5.00044C15.2481 5.00044 5.00188 14.8465 5.00188 26.9922"
                    stroke="#211F54"
                    strokeWidth="8.63962"
                  />
                  <path
                    d="M127.527 26.9922C127.527 14.8465 117.317 5.00045 104.723 5.00044L28.2395 5.00044"
                    stroke="#0161AB"
                    strokeWidth="8.63962"
                  />
                  <path
                    d="M5.00391 26.9922C5.00391 39.1379 15.2501 48.9839 27.8894 48.9839H104.644C117.283 48.9839 127.529 39.1379 127.529 26.9922"
                    stroke="#EFDC2A"
                    strokeWidth="8.63962"
                  />
                  <path
                    d="M28.2422 48.9839H104.725C117.32 48.9839 127.53 39.1379 127.53 26.9922"
                    stroke="#0161AB"
                    strokeWidth="8.63962"
                  />
                  <path
                    d="M104.645 5L27.8901 4.99999"
                    stroke="#FF0009"
                    strokeWidth="8.63962"
                  />
                  <path
                    d="M104.645 48.9844L27.8901 48.9844"
                    stroke="#4A8B40"
                    strokeWidth="8.63962"
                  />
                  <rect
                    x="9.14844"
                    y="6.43359"
                    width="113.93"
                    height="40.3934"
                    rx="20.1967"
                    fill="white"
                  />

                  <text
                    x="50%"
                    y="50%"
                    dominantBaseline="middle"
                    textAnchor="middle"
                    fontSize="14"
                    fill="#211F54"
                    fontWeight="500"
                    fontFamily="Poppins, sans-serif"
                  >
                    Explore
                  </text>
                </svg>
              </Link>
            </footer>
          </div>

          {/* Body */}
          <div
            className={`mt-[5px] lg:mt-[30px] flex ${showMessageCondition ? "" : "flex-col"
              }`}
          >
            {/* chats list */}
            <div
              className={` ${openChat ? "hidden" : ""} lg:block w-full ${showMessageCondition ? "lg:w-[40%]" : ""
                }`}
            >
              <div className="px-2.5 flex justify-between items-center py-4 lg:px-6 w-full border-b border-[#AFB8CF] lg:border-r">
                <div className="flex gap-2 items-center relative w-[200px] justify-between">
                  <p className="text-base leading-normal font-semibold lg:leading-[35px]">
                    {filter === "sellers"
                      ? "All Seller Messages"
                      : filter === "buyers"
                        ? "All Buyer Messages"
                        : "All Messages"}
                  </p>
                  <IoIosArrowDown
                    ref={menuBtnRef}
                    className={`w-[18px] h-[18px] cursor-pointer transition ease-in-out duration-300 ${openFilter ? "-rotate-180" : ""
                      }`}
                    onClick={() => setOpenFilter(!openFilter)}
                  />

                  {openFilter && (
                    <div
                      className={`flex flex-col z-50 absolute top-full right-0 bg-white shadow-md rounded-md`}
                    >
                      <span
                        className="text-sm font-medium py-2.5 px-8 hover:bg-blue-100 rounded-t-md border-b border-[#80808026] text-center cursor-pointer"
                        onClick={() => filterHandler("all")}
                      >
                        All
                      </span>
                      <span
                        className="text-sm font-medium py-2.5 px-8 hover:bg-blue-100 border-b border-[#80808026] text-center cursor-pointer"
                        onClick={() => filterHandler("buyers")}
                      >
                        Buyer
                      </span>
                      <span
                        className="text-sm font-medium py-2.5 px-8 hover:bg-blue-100 rounded-b-md text-center cursor-pointer"
                        onClick={() => filterHandler("sellers")}
                      >
                        Seller
                      </span>
                    </div>
                  )}
                </div>

                {/* <HiOutlineDotsVertical className='cursor-pointer lg:w-5 lg:h-5' /> */}
              </div>

              {/* Search */}
              {showMessageCondition ? (
                <div className="border-b border-[#AFB8CF] py-4 px-2.5 lg:border-r">
                  <div className="relative w-full">
                    <input
                      autoComplete="off"
                      type="search"
                      className="focus:outline-none bg-[#F3FAFF] py-2 pl-[44px] pr-4 w-full rounded-lg text-sm placeholder:font-light leading-normal placeholder:text-[#9FA7BE]"
                      placeholder="Search or start a new chat"
                      onChange={(e) => setSearchText(e.target.value)}
                    />
                    <IoMdSearch className="w-5 h-5 absolute top-[25%] left-4" />
                  </div>
                </div>
              ) : (
                ""
              )}

              {/* Chats */}
              {!chatListLoading ? (
                showMessageCondition ? (
                  <div className=" flex flex-col max-h-[60vh] pb-2.5 overflow-auto no_scrollbar">
                    {chatList.length>0?[...chatList]?.map((chat, idx) => {
                      console.log("chat?.participantData receiver", chat?.participantData)
                      const receiver = chat?.participantData?.find(
                        (participant) => participant?._id !== userData?._id
                      );
                      const isSeller =
                        receiver?._id === chat?.item_doc?.[0]?.createdBy?._id;
                      console.log("receiver", receiver);
                      return (
                        <div
                          key={`chat-list-${idx}`}
                          className={`cursor-pointer py-4 px-2.5 border-b flex gap-4 border-[#AFB8CF] ${chatId?.toString() === chat._id.toString()
                            ? "border-r-0 bg-[#F3FAFF]"
                            : "lg:border-r"
                            } `}
                          onClick={() => {
                            dispatch(updateChatId(chat._id));
                            dispatch(updateItemId(chat.sellerItemId))
                            openChatHandler(chat._id);
                          }}
                        >
                          <div className="relative overflow-hidden aspect-auto w-14 min-w-14 h-16">
                            <img
                              src={
                                chat?.item_doc?.images?.length
                                  ? chat?.item_doc?.images[0]
                                  : avatarUrl(selectedChat.userName)
                              }
                              alt="Book Img"
                              className="object-cover rounded-lg w-14 h-16"
                            />
                          </div>

                          <div className="w-[90%]">
                            <h3 className="text-base leading-normal font-semibold line-clamp-1">{`${receiver?.firstName} ${receiver?.lastName}`}</h3>

                            
                            {lastMessageText(chat)}
                            <p className="mt-2.5 text-[#0161AB] text-sm leading-normal flex flex-row items-center relative">
                              <LuClock3 className="stroke-[#0161AB]" />
                              {chat?.lastMessage?.createdAt
                                ? formatRelativeDate(
                                  chat?.lastMessage?.createdAt
                                )
                                : "Today"}
                              <span className="mx-1  bg-[#0161AB] w-[1px] h-[8px] scale-200 rounded-full relative -top-[2px]"></span>
                              {formatTo12HourTime(
                                chat?.lastMessage?.createdAt ?? new Date()
                              )}
                            </p>
                          </div>
                          <div>
                            <MdDeleteOutline
                              size={25}
                              onClick={() => deleteConversation(chat._id)}
                            />
                          </div>
                        </div>
                      );
                    }):<div className="text-center py-4 px-2 bg-gray-50 mt-2 rounded-md">No Data Found</div>}
                  </div>
                ) : (
                  <div className="w-full  flex justify-center items-center"></div>
                )
              ) : (
                <div className=" w-full flex justify-center items-center">
                  
                </div>
              )}
            </div>

            {/* ---------------------------------------- Chat screen ----------------------------------------------------------------------------------------------------------------------------------------------------------------------- */}
            <div className={`  ${openChat?"w-[100%]":"w[0px]"} lg:w-[60%] relative p-0 m-0`}>
              {showChatBodyCondition ? (
                <div
                  ref={chatRef}
                  style={{ filter: ItemListStatusEnum.MARKED_AS_SOLD == selectedChat.status ? `blur(5px)` : "" }}
                  className={`${openChat ? "flex " : "hidden"
                    } w-full px-2.5 z-[5] lg:flex flex-col lg:p-0  ${ItemListStatusEnum.MARKED_AS_SOLD == selectedChat.status ? "backdrop-blur-md bg-white/30" : ""}`}
                >
                  {showChatBodyCondition && (
                    <div className="border-b border-[#AFB8CF] flex justify-between py-2.5 lg:py-4 lg:px-6">
                      <div className="flex items-center gap-3.5">
                        <IoArrowBack
                          className="cursor-pointer lg:hidden"
                          onClick={closeChatHandler}
                        />

                        <div className="flex items-center gap-1.5 lg:gap-2">
                          <div className="relative w-6 h-6 lg:w-[40px] lg:h-[40px] rounded-lg">
                            <img
                              src={
                                selectedChat?.backCoverImage ||
                                avatarUrl(selectedChat.userName)
                              }
                              alt="Book Image"
                              className="object-cover rounded-lg w-full h-full"
                            />
                            <span className="absolute -bottom-[5px] -right-[5px] rounded-full w-3 h-3 lg:w-[17px] lg:h-[17px]">
                              <img
                                src={
                                  selectedChat?.profileImage ||
                                 avatarUrl(selectedChat.userName)
                                }
                                alt="Profile"
                                className="object-cover rounded-full z-10 w-full h-full"
                              />
                            </span>
                          </div>
                          <p className="text-[11px] font-semibold leading-normal text-[#636363] lg:text-base">
                            {selectedChat?.userName}
                          </p>
                        </div>
                      </div>

                      <div
                        ref={menuRef}
                        className="relative cursor-pointer py-0.5 px-1 flex justify-center items-center custom_shadow_first rounded-lg lg:px-2"
                      >
                        <HiOutlineDotsVertical
                          onClick={() => setshowOptionThreeDot(true)}
                        />
                        {showOptionThreeDot && (
                          <div className="absolute left-[-100px] cursor-pointer py-[5px] px-1 flex justify-center items-center custom_shadow_first rounded-md lg:px-2">
                            <button
                              onClick={() =>
                                router.push(
                                  `/book-detail?id=${selectedChat.itemId}`
                                )
                              }
                            >
                              View More
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="lg:pl-6">
                    <div
                      ref={messagesEndRef}
                      className={`flex h-[60vh] py-10  flex-col gap-8 pt-11 transition-all linear duration-300 ${!openChatTemplate
                        ? "pb-10"
                        : chatToggle === "chats"
                          ? "pb-35 md:pb-45"
                          : "pb-[220px] md:pb-[270px]"
                        } overflow-auto h-[75vh] no_scrollbar`}
                    >
                      {messages?.map((message, idx) =>
                        sender?._id?.toString() ===
                          message?.sender?.toString() ? (
                          //  right side message
                          <div
                            key={`message-${idx}`}
                            className="flex flex-col items-end self-end gap-1 w-[70%] pr-2"
                          >
                            {RenderMessage(message)}
                          </div>
                        ) : (
                          // left side message
                          <div
                            key={`message-${idx}`}
                            className="flex flex-col items-start self-start gap-1 w-[70%]"
                          >
                            {RenderMessage(message)}
                          </div>
                        )
                      )}
                    </div>
                  </div>

                  {ItemListStatusEnum.MARKED_AS_SOLD !== selectedChat.status && showChatBodyCondition && (
                    <div className="relative z-[20] border">
                      <div className="absolute bottom-full w-full">
                        <div
                          className={`flex gap-2 items-center bg-[#F9F9F9] ${messagesComponentScss.borderGradient}`}
                        >
                          <div
                            className={`!absolute -top-[80%] left-1/2 -translate-x-1/2 translate-y-1/2 w-[41px] h-[22px] flex justify-center items-end cursor-pointer !rounded-t-full lg:w-[61px] lg:h-[33px] lg:top-[-110%] ${messagesComponentScss.borderGradient
                              } ${openChatTemplate ? "" : ""}`}
                            onClick={chatTemplateOpenHandler}
                          >
                            <svg width="0" height="0">
                              <linearGradient
                                id="blue-gradient"
                                x1="100%"
                                y1="100%"
                                x2="0%"
                                y2="0%"
                              >
                                <stop stopColor="#211f54" offset="11%" />
                                <stop stopColor="#0161ab" offset="98%" />
                              </linearGradient>
                            </svg>
                            <MdKeyboardArrowDown
                              style={{ fill: "url(#blue-gradient)" }}
                              className={`lg:w-7 lg:h-7 transition-transform duration-200 ${openChatTemplate ? "" : "-rotate-180"
                                }`}
                            />
                          </div>

                          <div
                            className={`flex items-center ${selectedChat.isSeller ? "w-full" : "w-1/2"
                              } justify-center gap-2 text-[9.5px] leading-[13px] pt-3 pb-2 cursor-pointer lg:text-sm lg:leading-[19px]  ${chatToggle !== "chats"
                                ? "text-[#A0A0A0]"
                                : "global_text_linear_gradient"
                              }`}
                            onClick={() => setChatToggle("chats")}
                          >
                            {chatToggle !== "chats" ? (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="13"
                                viewBox="0 0 22 18"
                                fill="none"
                                className="lg:w-[22px] lg:h-[18px]"
                              >
                                <path
                                  d="M2 9C0.9 9 0 8.1 0 7V2C0 0.9 0.9 0 2 0H10C11.1 0 12 0.9 12 2V7C12 8.1 11.1 9 10 9H8V12L5 9H2ZM20 15C21.1 15 22 14.1 22 13V8C22 6.9 21.1 6 20 6H14V7C14 9.2 12.2 11 10 11V13C10 14.1 10.9 15 12 15H14V18L17 15H20Z"
                                  fill="#A0A0A0"
                                />
                              </svg>
                            ) : (
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="13"
                                viewBox="0 0 22 18"
                                fill="none"
                                className="lg:w-[22px] lg:h-[18px]"
                              >
                                <path
                                  d="M2 9C0.9 9 0 8.1 0 7V2C0 0.9 0.9 0 2 0H10C11.1 0 12 0.9 12 2V7C12 8.1 11.1 9 10 9H8V12L5 9H2ZM20 15C21.1 15 22 14.1 22 13V8C22 6.9 21.1 6 20 6H14V7C14 9.2 12.2 11 10 11V13C10 14.1 10.9 15 12 15H14V18L17 15H20Z"
                                  fill="url(#paint0_linear_2021_12436)"
                                />
                                <defs>
                                  <linearGradient
                                    id="paint0_linear_2021_12436"
                                    x1="19.5451"
                                    y1="1.55769"
                                    x2="-0.248734"
                                    y2="2.28634"
                                    gradientUnits="userSpaceOnUse"
                                  >
                                    <stop stopColor="#211F54" />
                                    <stop offset="1" stopColor="#0161AB" />
                                  </linearGradient>
                                </defs>
                              </svg>
                            )}
                            Chats
                          </div>
                          {!selectedChat.isSeller && (
                            <div
                              className={`flex items-center w-1/2 justify-center gap-2 text-[9.5px] leading-[13px] pt-3 pb-2 cursor-pointer lg:text-sm lg:leading-[19px] ${chatToggle === "make offer"
                                ? "global_text_linear_gradient"
                                : "text-[#A0A0A0]"
                                } `}
                              onClick={() => setChatToggle("make offer")}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="15"
                                height="15"
                                viewBox="0 0 20 20"
                                fill="none"
                                className="lg:w-[18px] lg:h-[18px]"
                              >
                                <path
                                  d="M19.41 9.58L10.41 0.58C10.05 0.22 9.55 0 9 0H2C0.9 0 0 0.9 0 2V9C0 9.55 0.22 10.05 0.59 10.42L9.59 19.42C9.95 19.78 10.45 20 11 20C11.55 20 12.05 19.78 12.41 19.41L19.41 12.41C19.78 12.05 20 11.55 20 11C20 10.45 19.77 9.94 19.41 9.58ZM3.5 5C2.67 5 2 4.33 2 3.5C2 2.67 2.67 2 3.5 2C4.33 2 5 2.67 5 3.5C5 4.33 4.33 5 3.5 5Z"
                                  fill={`${chatToggle === "make offer"
                                    ? "url(#paint0_linear_2021_16128)"
                                    : "#A0A0A0"
                                    } `}
                                />
                                <defs>
                                  <linearGradient
                                    id="paint0_linear_2021_16128"
                                    x1="17.7683"
                                    y1="1.73077"
                                    x2="-0.234176"
                                    y2="2.27298"
                                    gradientUnits="userSpaceOnUse"
                                  >
                                    <stop stopColor="#211F54" />
                                    <stop offset="1" stopColor="#0161AB" />
                                  </linearGradient>
                                </defs>
                              </svg>
                              Make Offer
                            </div>
                          )}
                        </div>

                        <div
                          className={`global_linear_gradient text-white text-[9.5px] leading-normal py-3 px-5 lg:py-4 lg:px-8 lg:text-sm transition-all duration-300 ${openChatTemplate
                            ? "max-h-[200px] opacity-100"
                            : "max-h-0 opacity-0 !p-0"
                            } `}
                        >
                          Don't buy nuh 'puss in a bag'!
                          <br />
                          Ask about the item's condition, request additional
                          photos, and confirm payment and pick-up/delivery options
                          before you close the sale.
                        </div>

                        <div
                          className={` w-full bg-white transition-all duration-300 ${openChatTemplate
                            ? "max-h-[200px] opacity-100"
                            : "max-h-0 opacity-0"
                            }`}
                        >
                          {chatToggle === "chats" ? (
                            <div className="flex gap-3.5 pt-1 px-5 lg:py-[14px] pb-2.5 overflow-auto no_scrollbar">
                              {userData?._id == itemOfConversation?.createdBy
                                ? chatSuggestions.admin?.map((chat, idx) => (
                                  <span
                                    key={`predefined - chats - ${idx} `}
                                    className={`w-fit whitespace-nowrap py-1.5 px-2.5 text-[8px] leading-normal text-center rounded-t-xl rounded-bl-xl gradient-all-round-border border-2 lg:py-2 lg:px-3.5 lg:text-xs lg:rounded-t-[18px] lg:rounded-bl-[18px] cursor-pointer active:scale-95`}
                                    onClick={() => sendMessage(chat)}
                                  >
                                    <p className="global_text_linear_gradient font-medium">
                                      {chat}
                                    </p>
                                  </span>
                                ))
                                : chatSuggestions.client?.map((chat, idx) => (
                                  <span
                                    key={`predefined - chats - ${idx} `}
                                    className={`w-fit whitespace-nowrap py-1.5 px-2.5 text-[8px] leading-normal text-center rounded-t-xl rounded-bl-xl gradient-all-round-border border-2 lg:py-2 lg:px-3.5 lg:text-xs lg:rounded-t-[18px] lg:rounded-bl-[18px] cursor-pointer active:scale-95`}
                                    onClick={() => sendMessage(chat)}
                                  >
                                    <p className="global_text_linear_gradient font-medium">
                                      {chat}
                                    </p>
                                  </span>
                                ))}
                            </div>
                          ) : (
                            // make offer components render based on seller and buyer
                            // selectedChat?.isSeller ?
                            // makeOfferComponents.seller[sellerMakeOfferShownComponent]
                            // :
                            makeOfferComponents.buyer[
                            buyerMakeOfferShownComponent
                            ]
                          )}
                        </div>
                      </div>

                      <div className="relative w-full">
                        <div className="w-full  flex gap-3 py-2.5 pr-4 bg-[#F3F3F3] items-center lg:py-4 lg:pr-6">
                          <div className="flex justify-center items-center  mx-2">
                            <FaMapMarkerAlt
                              className="lg:w-5 lg:h-[27px] fill-[#575757] cursor-pointer"
                              onClick={locationSend}
                            />
                          </div>

                          <form
                            className="w-full   flex bg-white rounded-full"
                            onSubmit={sendMessageHandler}
                          >
                            <input
                              type="text"
                              autoComplete="off"
                              name="message"
                              className="w-full focus:outline-none text-[10px] leading-normal py-1.5 pl-3 pr-[50px] rounded-full placeholder:text-[#9FA7BE] lg:py-2.5 lg:pl-[21px] lg:text-[15px]"
                              placeholder="Type your message here ..."
                            //   autoComplete="off"
                            />
                            <div className="flex  items-center justify-between mr-3 ">
                              <input
                                autoComplete="off"
                                type="file"
                                ref={fileInputRef}
                                onChange={handleFileChange}
                                className="hidden"
                              />
                              <button
                                type="button"
                                className=" cursor-pointer flex justify-center items-center px-2  rounded-full"
                                onClick={handleButtonClick}
                              >
                                <FaPlusCircle size={25} />
                              </button>
                              <button
                                type="submit"
                                className=" cursor-pointer flex justify-center items-center p-1 lg:p-1.5 lg:px-1.5 bg-[#575757] rounded-full"
                              >
                                <BiLogoTelegram size={25} className="fill-white w-[14px] h-[14px] lg:w-3.5 lg:h-3.5" />
                              </button>
                            </div>
                          </form>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div
                  className={`${showMessageCondition ? "hidden lg:w-[60%]" : "flex"
                    } lg:flex flex-col w-full justify-center items-center min-h-[70vh] md:min-h-[75vh] gap-[60px]`}
                >
                  <p className="text-xs">No, messages,yet?</p>

                  <div>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="90"
                      height="70"
                      viewBox="0 0 129 106"
                      fill="none"
                      className="md:w-[129px] md:h-[106px]"
                    >
                      <path
                        d="M11.7273 52.7727C5.27727 52.7727 0 47.4955 0 41.0455V11.7273C0 5.27727 5.27727 0 11.7273 0H58.6364C65.0864 0 70.3636 5.27727 70.3636 11.7273V41.0455C70.3636 47.4955 65.0864 52.7727 58.6364 52.7727H46.9091V70.3636L29.3182 52.7727H11.7273ZM117.273 87.9545C123.723 87.9545 129 82.6773 129 76.2273V46.9091C129 40.4591 123.723 35.1818 117.273 35.1818H82.0909V41.0455C82.0909 53.9455 71.5364 64.5 58.6364 64.5V76.2273C58.6364 82.6773 63.9136 87.9545 70.3636 87.9545H82.0909V105.545L99.6818 87.9545H117.273Z"
                        fill="url(#paint0_linear_2933_4071)"
                      />
                      <defs>
                        <linearGradient
                          id="paint0_linear_2933_4071"
                          x1="114.605"
                          y1="9.13373"
                          x2="-1.45849"
                          y2="13.4062"
                          gradientUnits="userSpaceOnUse"
                        >
                          <stop stopColor="#211F54" />
                          <stop offset="1" stopColor="#0161AB" />
                        </linearGradient>
                      </defs>
                    </svg>
                  </div>
                  <p className="text-xs leading-normal">
                    We’ll keep messages for any item you’re selling in here
                  </p>
                </div>
              )
              }
              {ItemListStatusEnum.MARKED_AS_SOLD == selectedChat.status && <div className="bg-white   max-w-full h-[30%] absolute bottom-[0%] flex items-cemter justify-center">
                <div className="w-fit  flex flex-col items-center justify-center">
                  <img className="mx-auto" src={"/icons/bookEmpty.png"} />
                  <p className="md:w-[60%] text-center mx-auto">The chat has been removed because the seller mark the book as sold</p>
                </div>
              </div>}
            </div>

          </div>
        </div>
      </div>
    );
}

export default myMessages;
