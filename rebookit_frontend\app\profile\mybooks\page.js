"use client";
// `use strict`
import { USER_ROUTES } from "@/app/config/api";
import {
  DecideItemAction,
  editItemPayload,
  getToken,
  userDataFromLocal,
} from "@/app/utils/utils";
import { useEffect, useRef, useState } from "react";
import { FaArrowRightLong } from "react-icons/fa6";
import styles from "./mybooks.module.scss";
import Link from "next/link";
import { deleteMyBooks, getMyBooks, searchItemByName } from "@/app/services/profile";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { markAsSold } from "@/app/services/bookDetails";
import {
  ItemKindEnum,
  ItemListStatusEnum,
  itemToKind,
} from "@/app/config/constant";
import { BsThreeDotsVertical } from "react-icons/bs";
import { editListingPrefillData } from "@/app/redux/slices/storeSlice";
import { useDispatch } from "react-redux";
import NoDataFound from "@/app/components/common/NoDataFound";
import MenuList from "@/app/components/common/MenuList";
import { FaArrowDown } from "react-icons/fa";
import moment from "moment";
import StatusBadge from "@/app/components/statusBadge/StatusBadge";
import Pagination from "@/app/components/common/Pagination";

export default function MyBooks() {
  const [bookData, setBookData] = useState([]);
  console.log("bookdata", bookData);

  const [searchedText, setsearchedText] = useState("");
  const [isLoading, setisLoading] = useState(false);
  const router = useRouter();
  const menuRef = useRef(null);
  const [isMenuOpen, setisMenuOpen] = useState({});
  const dispatch = useDispatch();
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  console.log("isloading", isLoading);

  const getAllBooksOfuser = async () => {
    try {
      setisLoading(true);
      let payload = {
        filters: {},
        sort: {},
      };
      let queryString = `?page=${currentPage}&pageSize=${pageSize}`;
      const response = await getMyBooks(payload, queryString);
      if (response.status == 200) {
        console.log("response getAllBooksOfuser", response);
        setBookData(response.data.data);
        setTotalItems(response.data.totalCount);
        setTotalPages(response.data.totalPages);
        // setCurrentPage(response.data.page)
        // setPageSize(pageSize)
      }
      setisLoading(false);
    } catch (err) {
      console.log("err", err);
      setisLoading(false);
      toast.error("Something Went Wrong");
    }
  };

  useEffect(() => {
    const handleClickOutside = (e) => {
      if (menuRef.current && !menuRef.current.contains(e.target)) {
        setisMenuOpen({});
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const markAsSoldFun = async (item,status) => {
    try {
      const response = await markAsSold(`${USER_ROUTES.MARK_AS_SOLD}/${item._id}`, {
        status: status,
        kind: item.__t
      });
      if (response.status == 200) {
        toast.success(response.data.message || status);
        getAllBooksOfuser();
      }
    } catch (error) {
      toast.error("Failed to mark as sold");
      console.error(error);
    }
  };
 

  const editBook = (item) => {
    const payloadToSet = editItemPayload(item);
    dispatch(editListingPrefillData(payloadToSet));
    router.push("/become-seller");
  };

  const MenuListComp = ({ item, closeMenu, id, kind }) => {
    const actionPayload = DecideItemAction(item.status);

    const deleteBook = async (id, kind) => {
      try {
        const response = await deleteMyBooks(id, {
          "status": "deleted",
          "kind": kind
        });
        if (response && response.status === 200) {
          toast.success(response.data.message || "Book deleted successfully");
          getAllBooksOfuser()
        }
      } catch (error) {
        toast.error("Failed to delete book");
        console.error(error);
      }
    };

    return (
      <div
        ref={menuRef}
        className="z-[200] p-2 absolute right-[20px] bg-white rounded-md shadow-lg border border-gray-200"
      >
        {actionPayload.edit && (
          <button
            className="p-2 cursor-pointer hover:bg-gray-100 w-full rounded-md text-left"
            onClick={() => {
              editBook(item);
              closeMenu();
            }}
          >
            Edit
          </button>
        )}
        {ItemKindEnum.BookItem === item.__t && actionPayload.mark_as_sold && (
          <button
            className="p-2 cursor-pointer hover:bg-gray-100 w-full rounded-md text-left"
            onClick={() => {
              markAsSoldFun(item,"marked as sold");
              closeMenu();
            }}
          >
            Mark As Sold
          </button>
        )}
        {item.__t!==ItemKindEnum.BookItem&& actionPayload.remove && (
          <button
            className="p-2 cursor-pointer hover:bg-gray-100 w-full rounded-md text-left text-red-600"
            onClick={() => {
              // toast.info("Remove functionality coming soon");
              markAsSoldFun(item,"deleted");
              closeMenu();
            }}
          >
            Remove
          </button>
        )}
      </div>
    );
  };

  function debounce(func, delay) {
    let timer;
    return function (...args) {
      clearTimeout(timer);
      timer = setTimeout(() => {
        func.apply(this, args);
      }, delay);
    };
  }

  const getItemsByName = async (text) => {
    try {
      if (text) {
        let payload = {
          filters: { keyword: text },
          sort: {},
        };
        let queryString = `?page=${currentPage}&pageSize=${pageSize}`;

        const response = await searchItemByName(payload, queryString);
        if (response.status == 200) {
          setBookData(response.data.data);
          setTotalItems(response.data.totalCount);
          setTotalPages(response.data.totalPages);
        }
      } else {
        getAllBooksOfuser();
      }
    } catch (err) {
      console.log("err", err);
    }
  };

  useEffect(() => {
    getAllBooksOfuser();
  }, [currentPage, pageSize]);

  const handledebondSearchItem = debounce(getItemsByName, 500);

  let userData = userDataFromLocal();
  // userData = JSON.parse(userData);

  let staticComments = {
    [ItemListStatusEnum.ACCEPTED]: "This Item is currently Live",
    [ItemListStatusEnum.MARKED_AS_SOLD]: "The Item Was Sold",
    [ItemListStatusEnum.REJECTED]: "Your Item is rejected due to",
    [ItemListStatusEnum.HOLD]: "Item Is on hold",
    [ItemListStatusEnum.PENDING]:
      "Your Item is pending approval. We'll notify you once it's live",
    [ItemListStatusEnum.EXPIRED]:
      "This Item was Expired. If you sold it Please mark as sold",
  };
  console.log("staticComments", staticComments["rejected"]);
  const rejectedText = (item) => {
    if (item.status == ItemListStatusEnum.REJECTED) {
      if (item?.reason?.length) {
        return staticComments[item.status] + `(${item.reason})`;
      } else {
        return "Your Item is rejected";
      }
    } else {
      return staticComments[item.status];
    }
  };
  return (
    <div className="mybookContainer">
      <div className="flex items-center justify-between flex-wrap gap-4 py-4">
        <h1 className="text-xl font-semibold text-[#211F54]">
          {userData?.firstName}'s Listings
        </h1>

        <div className="flex items-center gap-4 w-full md:w-[40%]">
          <div className="flex items-center w-full bg-white rounded-full border border-[#211F54] px-2 py-1 h-[45px]">
            <input
              type="text"
              autoComplete="off"
              placeholder="Search..."
              onChange={(e) => handledebondSearchItem(e.target.value)}
              className="flex-grow px-4 py-1 outline-none bg-transparent text-gray-700 w-full"
            />
            <button
              className="px-4 py-1 text-white text-sm font-medium rounded-full"
              style={{
                backgroundImage: "linear-gradient(to right, #211F54, #0161AB)",
              }}
            >
              Search
            </button>
          </div>
        </div>
      </div>

      <div className="w-full ">
        <div className="w-full rounded-lg bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white px-4 py-2 text-lg font-semibold">
          All
        </div>

        <div className="bg-white p-6 rounded-lg shadow border border-gray-200 overflow-x-auto mt-2">
          <table className="min-w-full text-sm">
            <thead className="bg-gray-200 text-black">
              <tr className="rounded-lg overflow-hidden">
                {["Name", "Comment", "Listed Date", "Status", "Action"].map(
                  (head, idx) => (
                    <th
                      key={idx}
                      className={`px-6 py-4 text-left text-[14px] font-medium ${
                        idx === 0
                          ? "rounded-tl-lg"
                          : idx === 4
                          ? "rounded-tr-lg"
                          : ""
                      }`}
                    >
                      <div className="flex items-center">
                        <span>{head}</span>
                      </div>
                    </th>
                  )
                )}
              </tr>
            </thead>
            <tbody className="bg-white">
              {!isLoading ? (
                bookData.length > 0 ? (
                  bookData.map((item, i) => (
                    <tr
                      key={i}
                      className="border-b border-gray-200 animate-fade-in-down text-[14px] font-medium relative"
                    >
                      <td
                        className="px-6 py-4 whitespace-nowrap cursor-pointer"
                        onClick={() =>
                          router.push(`/book-detail?id=${item._id}`)
                        }
                      >
                        <div className="flex items-center gap-2 ">
                          <img className="w-14 h-16" src={item.images[0]} />
                          <div>
                            <p
                              title={item.title}
                              className="overflow-hidden text-ellipsis whitespace-nowrap w-[200px]"
                            >
                              {item?.title}
                            </p>
                            <p className="text-[#9A9A9A] overflow-hidden  text-ellipsis whitespace-nowrap w-[200px]">
                              Price: J${item.price}
                            </p>
                            <p className="text-[#9A9A9A] overflow-hidden text-ellipsis whitespace-nowrap w-[200px]">
                              Category: {item.categoryDoc.name}
                            </p>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <p
                          title={item.reason || "NA"}
                          className="overflow-hidden  max-h-[80px] overflow-none  line-clamp-2 text-ellipsis whitespace-normal w-[200px]"
                        >
                          {rejectedText(item)}
                        </p>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {moment(item.createdAt).format("MMM D, YYYY")}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <StatusBadge
                          status={
                            item?.status
                              ? item.status.charAt(0).toUpperCase() +
                                item.status.slice(1)
                              : ""
                          }
                        />
                      </td>
                      <td className="px-6 py-4">
                        <div
                          className="cursor-pointer"
                          onClick={(e) => {
                            e.stopPropagation();
                            // Toggle menu for this item
                            setisMenuOpen((prev) =>
                              prev._id === item._id ? {} : item
                            );
                          }}
                        >
                          <BsThreeDotsVertical size={16} />
                        </div>
                        {isMenuOpen._id === item._id && (
                          <MenuListComp
                            item={item}
                            closeMenu={() => setisMenuOpen({})}
                            id={item?._id}
                            kind={item.__t}
                          />
                        )}
                      </td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={5} className="text-center py-4">
                      <div className=" mx-auto min-h-[60vh] col-span-2">
                        <NoDataFound
                          actionFunc={() => router.push("/become-seller")}
                          title={"No Data Found"}
                          btntxt={"List Your Book"}
                        />
                      </div>
                    </td>
                  </tr>
                )
              ) : (
                <tr>
                  <td colSpan={5} className="text-center py-4">
                    <div className="flex justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      <div className="">
        <Pagination
          setPageSize={setPageSize}
          setCurrentPage={setCurrentPage}
          getListing={getAllBooksOfuser}
          currentPage={currentPage}
          totalPages={totalPages}
          totalItems={totalItems}
          pageSize={pageSize}
        />
      </div>
    </div>
  );
}
