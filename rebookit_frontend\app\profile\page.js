// "use client"

// import React from 'react'
// import dynamic from 'next/dynamic';

// const UserInfo = dynamic(() => import('@/app/components/profile/userInfo'));
// const MyMessages = dynamic(() => import('@/app/components/profile/myMessages'));
// const CheckoutPage = dynamic(() => import("@/app/components/profile/checkout"))

// import profileCss from "./profile.module.scss"
// import { useSelector } from 'react-redux';
// import Membership from '../components/profile/membership';
// import Stripe from '../components/profile/stripe';
// import MyBooks from '../components/profile/mybooks';
// import useResetIndexOnLeave from './useResetIndexOnLeave';
// import Bookmark from '../components/bookmark/bookmarkComponent';
// import Billing from "../components/billing/billingComponent"
// import CommunityPage from '../components/askCommunity/page';

// function Profile() {
//     useResetIndexOnLeave();
//     const activIndex = useSelector(x => x?.storeData?.currentProfileComponentIndex)
//     console.log("activIndex", activIndex)

//     function getScreen(idx) {

//         switch (idx) {
//             case 0:
//                 return <MyBooks />
//             case 1:
//                 return <Membership />

//             case 3:
//                 return <MyMessages />
//             case 4:
//                 return <Bookmark />
//             case 5:
//                 return <CommunityPage />
//             case 6:
//                 // return <CheckoutPage />
//                 return <Billing />

//             case 7:
//                 return <UserInfo />

//             default:
//                 return "No Component Developed"

//         }
//     }

//     return (

//         <section className={`${profileCss.screens}`}>
//             {getScreen(activIndex)}
//         </section>
//     )
// }

// export default Profile;
"use client";

import React, {useEffect, useRef, useState} from "react";
import profileCss from "./profileComponent.module.scss";
import Image from "next/image";
import {useForm} from "react-hook-form";

import dummyImage from "@/public/test.jpeg";
import {getToken} from "@/app/utils/utils";
import {USER_ROUTES} from "@/app/config/api";
import {toast} from "react-toastify";

import {SlUser} from "react-icons/sl";
import {Circles} from "react-loader-spinner";
import {update_userInfo_api, userInfo_api} from "@/app/services/auth";
import {uploadPhotoSingle} from "@/app/services/profile";
import {createInitialsAvatar} from "../components/InitialAvatar/CreateInitialAvatar";

export default function UserInfo() {
  const {
    register,
    watch,
    formState: {errors},
    getValues,
    handleSubmit,
    setValue,
    reset,
    setError,
  } = useForm();

  const fileInputRef = useRef(null);
  const [selectedImage, setSelectedImage] = useState();
  const [btnDisabled, setBtnDisabled] = useState(true);
  const [userName, setUserName] = useState("");
  const [loading, setLoading] = useState(false);

  const getUserInfoFunc = async () => {
    try {
      let responseUserInfo = await userInfo_api();
      console.log("responseUserInfo", responseUserInfo);
      if (responseUserInfo.data) {
        let userData = responseUserInfo.data;
        setValue("firstName", userData?.firstName);
        setValue("lastName", userData?.lastName);
        setValue("number", userData?.mobileNumber);
        setValue("email", userData?.email);
        setValue("address", userData?.address);
        setValue("description", userData?.aboutMe);
        setSelectedImage(userData?.profileImage);

        setUserName(`${userData?.firstName} ${userData?.lastName || ""}`);
      }
    } catch (err) {
      toast.error("Something Went Wrong");
      console.log("getUserInfoFunc err", err);
    }
  };

  useEffect(() => {
    try {
      let userToken = getToken();
      setLoading(true);
      getUserInfoFunc();
      // fetch(USER_ROUTES.USER_INFO, {
      //     method: "get",
      //     headers: {
      //         "Authorization": `Bearer ${userToken}`
      //     }
      // }).then(async res => {
      //     const response = await res.json();
      //     console.log("response is", response)
      //     if (!response.error) {
      //         let userData = response.data;
      //         setValue("firstName", userData?.firstName)
      //         setValue("lastName", userData?.lastName)
      //         setValue("number", userData?.mobileNumber)
      //         setValue("email", userData?.email)
      //         setValue("address", userData?.address)
      //         setValue("description", userData?.aboutMe)
      //         setSelectedImage(userData?.profileImage)
      //     } else {
      //         toast.error(response?.message || "No Info Found")
      //     }
      // })
    } catch (error) {
      console.log(error);
      toast.error(error || "Internal Server Error");
    } finally {
      setLoading(false);
    }
  }, []);

  const handleImageChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      setLoading(true);
      const formData = new FormData();
      formData.append("file", file);
      try {
        const data = await uploadPhotoSingle(formData);
        // const response = await fetch(USER_ROUTES.SINGLE_UPLOAD_FILE, {
        //     method: "POST",
        //     headers: {
        //         "Authorization": `Bearer ${userToken}`
        //     },
        //     body: formData,
        // });

        // if (!response.status) {
        //     throw new Error("Upload failed");
        // }

        if (data.status == 200) {
          console.log("Upload response:", data);
          toast.success("File Uploaded..");
          const imageUrl = data.data.url || URL.createObjectURL(file);
          setSelectedImage(imageUrl);
        }
      } catch (err) {
        console.error("Image upload error:", err);
        toast.error(err.message || "Internal Server Error");
      } finally {
        setLoading(false);
      }
    }
  };

  const onSubmit = async (data) => {
    try {
      setLoading(true);
      // console.log("data called", data)
      let userToken = getToken();

      let userPayload = {
        firstName: data?.firstName,
        lastName: data.lastName,
      };

      if (data.description) userPayload.aboutMe = data.description;
      if (data.address) userPayload.address = data.address;
      if (data.number) userPayload.mobileNumber = data.number;
      if (selectedImage) userPayload.profileImage = selectedImage;

      let updatedData = await update_userInfo_api(userPayload);
      console.log("updatedData", updatedData);
      if (updatedData.status == 200) {
        toast.success(updatedData.data.message);
        setBtnDisabled(true);
      }
      // fetch(USER_ROUTES.EDIT_USER_INFO, {
      //     method: "put",
      //     headers: {
      //         "Authorization": `Bearer ${userToken}`,
      //         "Content-Type": "application/json"

      //     },
      //     body: JSON.stringify(userPayload)

      // }).then(async res => {
      //     const response = await res.json();
      //     if (!response.error) {
      //         toast.success(response.message)
      //         setBtnDisabled(true)
      //     } else {
      //         response.message?.map(x => toast.error(x))
      //         toast.error(response.message || "No Info Found")
      //     }
      // })
    } catch (error) {
      console.log(error);
      toast.error(error || "Internal Server Error");
    } finally {
      setLoading(false);
    }
  };

  const avatarUrl = createInitialsAvatar(`${userName}`, {
    bgColor: "#3f51b5",
    textColor: "#ffffff",
  });

  return (
    <section className={`${profileCss.profileContainer} relative`}>
      {loading && (
        <div className="z-50 h-full w-full bg-white opacity-80 absolute">
          <Circles
            height="80"
            width="80"
            color="#4fa94d"
            ariaLabel="circles-loading"
            wrapperStyle={{}}
            wrapperClass="absolute top-[40%] left-[45%] "
            visible={true}
          />
        </div>
      )}
      <h2 className="text-[18px] font-medium md:text-[24px] md:font-bold">
        Profile
      </h2>
      <hr className="mt-2" />

      <section className="flex justify-between  my-5 md:justify-start ">
        <div className="w-2/12 md:w-[11%] text-center">
          <div
            className="inline-block cursor-pointer w-full h-full"
            onClick={() => {
              !btnDisabled && fileInputRef.current.click();
            }}
          >
            {selectedImage ? (
              <img
                src={selectedImage}
                className={`ring-3 ring-[#A5A58D] rounded-full h-[56] w-[56] md:h-[101] md:w-[101] ${
                  btnDisabled && "cursor-not-allowed"
                }`}
                alt="user profile"
              />
            ) : (
              // <SlUser
              //   className={`ring-3 ring-[#A5A58D] rounded-full h-[56] w-[56] md:h-[101] md:w-[101] p-3 ${
              //     btnDisabled && "cursor-not-allowed"
              //   }`}
              //   color="#000"
              // />
              <div
                className="user-avatar rounded-full h-[56] w-[56] md:h-[100] md:w-[100] p-3"
                style={{
                  backgroundImage: `url(${avatarUrl})`,
                  // width: "50px",
                  // height: "50px",
                  borderRadius: "50%",
                }}
              />
            )}
          </div>

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageChange}
            style={{display: "none"}}
            autoComplete="off"
          />
        </div>

        <div className="w-9/12 ml-4 md:flex md:flex-wrap md:w-full md:justify-between md:items-center">
          <section>
            <p className="text-[19px] font-semibold md:text-[28px]">
              {watch("firstName") + " " + watch("lastName")}
            </p>
            <p className="text-[15px] text-[#6C6C6C] md:text-[22px]">
              {watch("email")}
            </p>
          </section>
          {btnDisabled ? (
            <button
              className={`${profileCss.editProfile} mt-[16px]`}
              onClick={() => setBtnDisabled(false)}
            >
              Edit Profile
            </button>
          ) : (
            <button
              className={`${profileCss.saveButton}`}
              disabled={btnDisabled}
              onClick={handleSubmit(onSubmit)}
            >
              Save
            </button>
          )}
        </div>
      </section>

      {/* <hr className="h-[1px] bg-[#E3E3E3] absolute w-[111%] left-[-24px] md:w-full md:left-[0] md:my-5" /> */}

      <section className="my-4 md:flex md:flex-wrap md:justify-between">
        <div
          className={`${profileCss.inputContainer} my-5 md:w-5/12 relative `}
        >
          <label className="">First Name</label>
          <input
            type="text"
            {...register("firstName", {
              required: "First Name is required",
            })}
            placeholder="Your First Name"
            disabled={btnDisabled}
            className={`${
              btnDisabled && "!bg-gray-200 text-gray-400 !cursor-not-allowed"
            }`}
            autoComplete="off"
          />

          {errors?.firstName && (
            <p
              id="firstName-error"
              className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-[-13px]"
            >
              {errors?.firstName.message}
            </p>
          )}
        </div>

        <div className={`${profileCss.inputContainer} my-5 md:w-5/12 relative`}>
          <label className="">Last Name</label>
          <input
            type="text"
            {...register("lastName", {
              required: "Last Name is required",
            })}
            placeholder="Your Last Name"
            autoComplete="off"
            className={`${
              btnDisabled && "!bg-gray-200 text-gray-400 !cursor-not-allowed"
            }`}
            disabled={btnDisabled}
          />

          {errors.lastName && (
            <p
              id="lastName-error"
              className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-[-13px]"
            >
              {errors.lastName.message}
            </p>
          )}
        </div>

        {/* <div className={`${profileCss.inputContainer} my-5 md:w-5/12 relative`}>
                    <label className=''>Contact Number</label>
                    <input
                        type='text'
                        {...register('number', {
                            required: 'Contact Number is required',
                        })}
                        placeholder='Your Contact number'
                        autoComplete="off"
                        disabled={btnDisabled}
                    />

                    {errors.number && (
                        <p id="number-error" className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-[-13px]">
                            {errors.number.message}
                        </p>
                    )}
                </div> */}

        <div className={`${profileCss.inputContainer} my-5 md:w-5/12 relative`}>
          <label className="">Email</label>
          <input
            type="email"
            {...register("email", {
              required: "Email is required",
            })}
            placeholder="Your Email"
            // className={`${
            //   btnDisabled && "!bg-gray-200 text-gray-400 !cursor-not-allowed"
            // }`}
            className="!bg-gray-200 text-gray-400 !cursor-not-allowed"
            disabled
            autoComplete="off"
          />

          {errors.email && (
            <p
              id="email-error"
              className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-[-13px] "
            >
              {errors.email.message}
            </p>
          )}
        </div>

        {/* <div className={`${profileCss.inputContainer} my-5 md:w-12/12 relative`}>
                    <label className=''>Address</label>
                    <input
                        type='text'
                        {...register('address', { 
                        })}
                        placeholder='Your Address'
                        disabled={btnDisabled}
                        autoComplete="off"
                    />

                    {errors.address && (
                        <p id="address-error" className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-[-13px]">
                            {errors.address.message}
                        </p>
                    )}
                </div> */}

        <div
          className={`${profileCss.inputContainer} my-5 md:w-12/12 relative`}
        >
          <label className="">About (Optional)</label>
          <textarea
            placeholder="Let us about yourself"
            {...register("description", {})}
            rows={4}
            className={`${
              btnDisabled && "!bg-gray-200 text-gray-400 !cursor-not-allowed"
            }`}
            disabled={btnDisabled}
          />

          {errors.description && (
            <p
              id="description-error"
              className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-[-13px]"
            >
              {errors.description.message}
            </p>
          )}
        </div>
      </section>
    </section>
  );
}
