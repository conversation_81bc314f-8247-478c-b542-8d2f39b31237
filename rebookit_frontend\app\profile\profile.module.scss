.profileContainer {
  min-height: 50vh;
  overflow: hidden;
  margin-top: 75px;

  .sidebar {
    display: none;
  }

  // .screens {
  //     padding: 10px;
  // }

  hr {
    border: 1px solid #e3e3e3;
  }

  @media (min-width: 769px) {
    padding: 25px 5vw;
    overflow: hidden;
    margin-top: 75px;

    .sidebar {
      display: block;
      padding: 16px 0;
      // height: 550px;
      // height: fit-content;
      border-radius: 10px;
      border: 1px solid #e5e7eb;
      background: var(--Gray-00, #fff);
      //   box-shadow: 0px 8px 40px 0px rgba(0, 0, 0, 0.08);
      // min-width: 325px;
      min-width: 294px;

      .listItem {
        cursor: pointer;
        color: #5f6c72;
        font-family: Poppins;
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px;
        display: flex;
        justify-content: start;
        align-items: center;
        padding: 10px 24px;
        transition: all ease-in-out 0.3s;
      }

      .active {
        background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
        color: #fff;
        // transform: scale(1.06);
        transition: all ease-in-out 0.3s;

        svg {
          path {
            stroke: white;
          }
        }
      }
    }
  }

  @media (max-width: 769px) {
    padding: 0px 5px;
    overflow: hidden;
    margin-top: 10px;

    // .sidebar {
    //     display: block;
    //     padding: 16px 0;
    //     // height: 550px;
    //     height: fit-content;
    //     border-radius: 4px;
    //     border: 1px solid #F5F5F5;
    //     background: var(--Gray-00, #FFF);
    //     box-shadow: 0px 8px 40px 0px rgba(0, 0, 0, 0.08);
    //     // min-width: 325px;
    //     min-width: 294px;

    //     .listItem {
    //         cursor: pointer;
    //         color: #5F6C72;
    //         font-family: Poppins;
    //         font-size: 14px;
    //         font-style: normal;
    //         font-weight: 400;
    //         line-height: 20px;
    //         display: flex;
    //         justify-content: start;
    //         align-items: center;
    //         padding: 10px 24px;
    //         transition: all ease-in-out 0.3s;

    //     }

    //     .active {
    //         background: linear-gradient(268deg, #211F54 11.09%, #0161AB 98.55%);
    //         color: #fff;
    //         // transform: scale(1.06);
    //         transition: all ease-in-out 0.3s;

    //         svg {
    //             path {
    //                 stroke: white;
    //             }
    //         }
    //     }

    // }
  }
}
