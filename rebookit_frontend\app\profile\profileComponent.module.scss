.profileContainer {


    .editProfile {
        color: #fff;
        cursor: pointer;
        background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
        border-radius: 55px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;
        width: 138px;
        height: 40px;
        display: flex;
        font-size: 14px;
    }




    .inputContainer {

        min-height: 70px;
        font-size: 14px;

        input,
        textarea {
            display: flex;
            padding: 9px 12px;
            align-items: center;
            gap: 10px;
            align-self: stretch;
            border-radius: 5px;
            border: 1px solid #EFEFEF;
            background: #FDFDFD;
            margin: 10px 0;
            width: 100%;
            outline: none;


            &:disabled {
                cursor: not-allowed;
            }
        }

    }


    .saveButton {
        display: flex;
        width: 100%;
        height: 37px;
        padding: 6px 92px;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        gap: 10px;
        flex-shrink: 0;
        border-radius: 55px;
        background: linear-gradient(268deg, #211F54 11.09%, #0161AB 98.55%);
        cursor: pointer;
        color: #fff;

        &:disabled {
            cursor: not-allowed;
            opacity: 0.5;
        }
    }
}

@media (min-width: 769px) {

    .profileContainer {
        padding: 5px 25px;

        .editProfile {
            width: 187px;
            height: 50px;
        }


        .inputContainer {

            label {
                font-size: 18px;
            }

            input {
                width: 100%;
                height: 49px;
                font-size: 14px;
            }

            textarea {
                height: 150px;
            }
        }


        .saveButton {
            width: 211.385px;
            height: 58.594px;
            flex-shrink: 0;
        }
    }
}