"use client";

import React, {useState} from "react";

import profileCss from "./profile.module.scss";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  PiNotebookBold,
  PiStackBold,
  PiStorefrontBold,
} from "react-icons/pi";
import {IoSettingsOutline} from "react-icons/io5";
import {clearLocalStorge, removeToken} from "../utils/utils";
import {usePathname, useRouter} from "next/navigation";
import {toast} from "react-toastify";
import {useDispatch, useSelector} from "react-redux";
import {
  setToDefault,
  updateProfileComponentIndex,
} from "../redux/slices/storeSlice";
import {BiCart} from "react-icons/bi";
import {MdLogout} from "react-icons/md";
import Popup from "../components/popup/popup";

export default function SideBar() {
  const [logoutConfirm, setLogoutConfirm] = useState(false);
  const router = useRouter();
  const activeListIndex = useSelector(
    (x) => x?.storeData?.currentProfileComponentIndex
  );
  const pathname = usePathname().split("/");

  const dispatch = useDispatch();
  // console.log("pathname",pathname.split("/"))

  const handleLogout = () => {
    toast.success("Logged out");
    dispatch(setToDefault());
    clearLocalStorge();
    dispatch(setToDefault());
    // dispatch(updateProfileComponentIndex(idx))
    removeToken();
    window.open("/login", "_self");
    // router.push("/login");
  };

  const profileItems = [
    {
      name: "My Listings",
      routeName: "mybooks",
      icon: <PiStackBold className="w-5 h-5" />,
      clickFn: (idx) => {
        router.push("/profile/mybooks");
        dispatch(updateProfileComponentIndex(idx));
      },
    },
    {
      name: "Buy Membership",
      routeName: "membership",
      icon: <PiStorefrontBold className="w-5 h-5" />,
      clickFn: (idx) => {
        router.push("/profile/membership");

        dispatch(updateProfileComponentIndex(idx));
      },
    },
    {
      name: "Ad Campaign",
      routeName: "campaign",

      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="20"
          viewBox="0 0 20 20"
          fill="none"
        >
          <path
            d="M4.375 18.125H15.625"
            stroke="#5F6C72"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M16.25 8.125C16.25 13.75 10 18.125 10 18.125C10 18.125 3.75 13.75 3.75 8.125C3.75 6.4674 4.40848 4.87769 5.58058 3.70558C6.75269 2.53348 8.3424 1.875 10 1.875C11.6576 1.875 13.2473 2.53348 14.4194 3.70558C15.5915 4.87769 16.25 6.4674 16.25 8.125V8.125Z"
            stroke="#5F6C72"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M10 10.625C11.3807 10.625 12.5 9.50571 12.5 8.125C12.5 6.74429 11.3807 5.625 10 5.625C8.61929 5.625 7.5 6.74429 7.5 8.125C7.5 9.50571 8.61929 10.625 10 10.625Z"
            stroke="#5F6C72"
            strokeWidth="1.5"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      ),
      clickFn: (idx) => {
        router.push("/profile/campaign");

        dispatch(updateProfileComponentIndex(idx));
      },
    },
    {
      name: "My Messages",
      routeName: "messages",
      icon: <BiCart className="w-5 h-5" />,
      clickFn: (idx) => {
        router.push("/profile/messages");

        dispatch(updateProfileComponentIndex(idx));
      },
    },
    {
      name: "Wishlist",
      routeName: "bookmark",
      icon: <PiHeart className="w-5 h-5 stroke-[1.5px]" />,
      clickFn: (idx) => {
        router.push("/profile/bookmark");

        dispatch(updateProfileComponentIndex(idx));
      },
    },
    {
      name: "Ask Community",
      routeName: "community",
      icon: <PiNotebookBold className="w-5 h-5" />,
      clickFn: (idx) => {
        router.push("/profile/community");

        dispatch(updateProfileComponentIndex(idx));
      },
    },
    // {
    //   name: "Billing",
    //   routeName: "billing",
    //   icon: <PiNotebookBold className="w-5 h-5 " />,
    //   clickFn: (idx) => {
    //     router.push("/profile/billing");

    //     dispatch(updateProfileComponentIndex(idx));
    //   },
    // },
    {
      name: "Profile",
      routeName: "profile",
      icon: <IoSettingsOutline className="w-5 h-5 stroke-[1.5px]" />,
      clickFn: (idx) => {
        router.push("/profile");

        dispatch(updateProfileComponentIndex(idx));
      },
    },
    {
      name: "Log-out",
      icon: <MdLogout className="w-5 h-5 " />,
      clickFn: () => {
        setLogoutConfirm(true);
      },
    },
  ];

  return (
    <div className={`w-2/12 shadow ${profileCss.sidebar}`}>
      <ul className="flex flex-col">
        {profileItems.map((item, idx) => (
          <li
            key={idx}
            className={`flex ${profileCss.listItem} ${
              pathname[pathname.length - 1] == item.routeName &&
              profileCss.active
            } hover:bg-gray-100`}
            onClick={() => item.clickFn(idx)}
          >
            {item.icon}
            <span className="ml-3">{item.name}</span>
          </li>
        ))}
      </ul>
      <Popup
        isOpen={logoutConfirm}
        title="Confirm Logout"
        message="Are you sure you want to log out?"
        confirmText="Log Out"
        cancelText="Cancel"
        onConfirm={handleLogout}
        onCancel={() => setLogoutConfirm(false)}
      />
    </div>
  );
}
