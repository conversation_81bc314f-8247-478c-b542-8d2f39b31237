'use client';

import { usePathname } from 'next/navigation';
import { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { updateProfileComponentIndex } from '../redux/slices/storeSlice';

export default function useResetIndexOnLeave() {
    const pathname = usePathname();
    const dispatch = useDispatch();

    useEffect(() => {
        return () => {
            // only reset if leaving /profile or a subroute of it
            if (pathname.startsWith('/profile')) {
                dispatch(updateProfileComponentIndex(0));
            }
        };
    }, [pathname, dispatch]);
}
