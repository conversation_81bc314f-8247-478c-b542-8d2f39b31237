import { USER_ROUTES } from '@/app/config/api';
import { getToken } from '@/app/utils/utils';
import { createSlice } from '@reduxjs/toolkit';

let defaultListing = {
  currentStep: 0,
  completedStep: 0,
  membershipStep: 0,
  category: { _id: "", name: "" },
  subCategory: { _id: "", name: "" },
  subSubCategory: { _id: "", name: "" },
  subSubSubCategory: { _id: "", name: "" },
  isEdit: false,
  Kind: "",
  showVerifyScreen: "",
  listData: {
    tags: [],
    ISBN: "",
    title: "",
    desciption: "",
    bookAuthor: "",
    price: "",
    bookCondition: "",
    quantity: 0,
    address: "",
    locationCoordinates: {
      lat: 1, lng: 1
    },
    OTP: "",
    isVerified: false,
    website: "",
    bookImages: {}
  },
  visitedStep: 0,
}

// New: Default structure for ad management data (multi-step ad creation form)
let defaultAdManagement = {
};

const initialState = {
  otp: null,
  token: null,
  chatId: "",
  adListingStep: 0,
  carrabianNonCarrabian: { name: "", values: [] },
  userCoordinates:[],
  verficationData: {
    allListingItems: [],
    createdAt: "2025-06-11T04:13:58.629Z",
    email: "<EMAIL>",
    isEmailVerified: true,
    updatedAt: "2025-06-11T04:13:58.629Z",
    _id: "684902867150353186c4b6c2"
  },
  forgetPasswordData: {
    "email": "",
    "otp": "",
  },
  userListing: defaultListing,

  userId: null,
  selectedPlanToPurchase: {},
  currentProfileComponentIndex: 0,

  itemId: null,

  // New: Ad management state for multi-step ad creation
  adManagement: { ...defaultAdManagement }
};

const storeSlice = createSlice({
  name: 'Rebookit store',
  initialState,
  reducers: {

    setOtp: (state, action) => {
      state.otp = action.payload
    },
    planToPurchase: (state, action) => {
      state.selectedPlanToPurchase = action.payload
    },
    clearOtp: (state) => {
      state.otp = null;
    },
    updateUserListing: (state, action) => {
      state.userListing = { ...state.userListing, ...action.payload }
      // Ensure visitedStep is always at least as high as currentStep
      if (action.payload.currentStep && (state.userListing.visitedStep === undefined || action.payload.currentStep > state.userListing.visitedStep)) {
        state.userListing.visitedStep = action.payload.currentStep;
      }
    },
    updateUserListingData: (state, action) => {
      state.userListing.listData = { ...state.userListing.listData, ...action.payload }
    },
    updateProfileComponentIndex: (state, action) => {
      state.currentProfileComponentIndex = action.payload
    },

    updateItemId: (state, action) => {
      state.itemId = action.payload;
    },
    isVerifiedChange: (state, action) => {
      state.userListing.listData.isVerified = action.payload
    },
    setToDefault: () => initialState,
    setVerificationData: (state, action) => {
      state.verficationData = action.payload
    },
    changeCompletedStep: (state, action) => {
      state.userListing.completedStep = action.payload
    },
    changeforgetPasswordData: (state, action) => {
      state.forgetPasswordData = action.payload
    },
    resetListingData: (state, action) => {
      state.userListing = defaultListing
    },
    editListingPrefillData: (state, action) => {
      console.log(action.payload, "action.payload")
      state.userListing = action.payload
    },
    nullItemId: (state, action) => {
      state.itemId = null
    },
    updateChatId: (state, action) => {
      state.chatId = action.payload
    },
    updateAdListinStep: (state, action) => {
      state.adListingStep = action.payload
    },
    updateCarrabianNonCarrabianSearch: (state, action) => {
      state.carrabianNonCarrabian = action.payload
    },

    updateUserCoordinates:(state,action)=>{
      
      console.log("action",action)
      
      state.userCoordinates=action.payload
    },

    // New: Update ad management data for ad creation steps
    updateAdManagement: (state, action) => {
      // Merge new data into adManagement state
      state.adManagement = { ...state.adManagement, ...action.payload }
    },
    // New: Reset ad management data (e.g., on discard)
    resetAdManagement: (state) => {
      state.adManagement = { ...defaultAdManagement }
    }
  },
});

export const {
  setOtp,
  clearOtp,
  updateUserListing,
  updateUserListingData,
  updateProfileComponentIndex,
  updateItemId,
  setUserId,
  planToPurchase,
  isVerifiedChange,
  setToDefault,
  setVerificationData,
  changeCompletedStep,
  changeforgetPasswordData,
  resetListingData,
  editListingPrefillData,
  nullItemId,
  updateChatId,
  updateAdListinStep,
  updateCarrabianNonCarrabianSearch,
  updateAdManagement,      // Export new action
  resetAdManagement,        // Export new action,
  updateUserCoordinates
} = storeSlice.actions;

export default storeSlice.reducer;
