import { useEffect, useRef, useState } from "react";
import { useLoadScript } from "@react-google-maps/api";
import { FaChevronDown } from "react-icons/fa6";
import { ParishesListEnum } from "../config/constant";
import { RxCross2 } from "react-icons/rx";
import { useSelector } from "react-redux";
import { getCurrentLocationAndAddress } from "../utils/utils";

const libraries = ["places"];

const CustomAutocomplete = ({ searchCoordinates, setsearchCoordinates, setselectedParish, selectedParish }) => {
  const { isLoaded } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_NEW_API_KEY,
    libraries,
  });
  const [isFocused, setisFocused] = useState(false)
  const filterRef = useRef(null)
  const [inputValue, setInputValue] = useState("");
  const [suggestions, setSuggestions] = useState([]);
  const [expanded, setexpanded] = useState(false)
  const storeData=useSelector(state=>state.storeData)

  const autocompleteServiceRef = useRef(null);

  useEffect(() => {
    if (isLoaded && !autocompleteServiceRef.current && window.google) {
      autocompleteServiceRef.current = new window.google.maps.places.AutocompleteService();
    }
  }, [isLoaded]);

  console.log("storeData in google",storeData)
  const getLocationFunc =async()=>{
   let address= await  getCurrentLocationAndAddress()
    console.log("address",address)
    if(address.address?.length){
      let splittledArr=address.address.split(",")
      console.log("splittledArr",splittledArr)
      setInputValue(splittledArr[splittledArr.length-1])
    }

  }
  useEffect(() => {
    getLocationFunc()
    
    const outSideClickHandle = (e) => {
      if (filterRef.current && !filterRef.current.contains(e.target)) {
        // setFocued(false);
        setisFocused(false)
        setexpanded(false)
      }
    };
    let addListner = document.addEventListener("click", outSideClickHandle);
    return () => {
      document.removeEventListener("click", addListner);
    };
  }, []);

  const handleInputChange = (e) => {
    const value = e.target.value;
    setInputValue(value);

    if (value && autocompleteServiceRef.current) {
      autocompleteServiceRef.current.getPlacePredictions(
        { input: value },
        (predictions, status) => {
          if (
            status === window.google.maps.places.PlacesServiceStatus.OK &&
            predictions
          ) {
            setSuggestions(predictions);
          } else {
            setSuggestions([]);
          }
        }
      );
    } else {
      setSuggestions([]);
      if (!selectedParish) {
        setsearchCoordinates(null)
      }
    }
  };

  const getAddressFromCoordinates = (lat, lng, callback) => {
    if(window.google){

      const geocoder = new window.google.maps.Geocoder();
      const latlng = { lat: parseFloat(lat), lng: parseFloat(lng) };
      
      geocoder.geocode({ location: latlng }, (results, status) => {
        if (status === "OK") {
          if (results[0]) {
            const formattedAddress = results[0].formatted_address;
            
            console.log("formattedAddress",results)
            callback(null, formattedAddress);
          } else {
            callback("No results found", null);
          }
        } else {
          callback(`Geocoder failed due to: ${status}`, null);
        }
      });
    }
  };

  const handleSelect = (prediction) => {
    setisFocused(false)
    setexpanded(false)
    setselectedParish(null)
    const geocoder = new window.google.maps.Geocoder();
    geocoder.geocode({ placeId: prediction.place_id }, (results, status) => {
      if (
        status === "OK" &&
        results &&
        results[0] &&
        results[0].geometry?.location
      ) {
        const location = results[0].geometry.location;
        setsearchCoordinates({
          lat: location.lat(),
          lng: location.lng(),
        });
        setInputValue(prediction.description);
        setSuggestions([]);
      }
    });
  };
  const resetFunction = () => {
    setselectedParish("")
    setsearchCoordinates(null)
    setisFocused(false)
    setexpanded(false)
    setInputValue("")
  }

  return (
    <div className="md:ms-2 absolute z-[10]  overflow-hidden     w-full top-[10px]  ">
      {/* <div>{selectedParish}// {JSON.stringify(searchCoordinates)}</div> */}
      <div ref={filterRef} className={` border rounded-xl bg-white mb-3 border-gray-200 transition-all duration-500 ease-in-out  ${expanded ? "h-[300px]" : ""}`}>
        <div className="flex items-center relative px-2"><input
          type="text"
          placeholder="Search for a location "
          className="w-full  p-2  outline-none cursor-pointer"
          value={inputValue}
          onFocus={() => {
            setisFocused(true)
            setexpanded(true)
            // if(selectedParish){

            // }
            // setInputValue("")
            // setselectedParish(null)
          }}
          //  onBlur={() => setisFocused(false)} 
          onChange={handleInputChange}
        />
          <div className="flex items-center">
            <RxCross2 className="cursor-pointer mr-2" onClick={() => {
              resetFunction()
            }} />
            <FaChevronDown className={`transform transition-transform duration-300 ${expanded ? "rotate-180" : ""
              }`} />
          </div>

        </div>
        {isFocused && (suggestions.length > 0 ? (
          <ul className=" z-10 bg-white w-full mt-1  max-h-[250px] overflow-auto">
            {suggestions.map((prediction) => (
              <li
                key={prediction.place_id}
                className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                onClick={() => handleSelect(prediction)}
              >
                {prediction.description}
              </li>
            ))}
          </ul>
        ) : <div className="absolute top-[40] z-10 bg-white w-full mt-1 max-h-[250px] overflow-auto">
          <div className="px-4 py-2 text-[18px] mt-2">Popular Location</div>
          {Object.values(ParishesListEnum).map((item) => (

            <div
              key={item}
              value={item}
              onClick={() => {
                debugger
                setexpanded(false)
                setisFocused(false)
                setInputValue(item)
                setselectedParish(item)
                setsearchCoordinates(null)
              }}
              className={`${selectedParish == item ? "bg-gray-100" : ""}   px-4 py-2 hover:bg-gray-100 cursor-pointer`}
            >
              {item}
            </div>
          ))}</div>)}
      </div>
    </div>
  );
};

export default CustomAutocomplete;
