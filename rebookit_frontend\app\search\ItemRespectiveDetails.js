import React from 'react'
import { ItemKindEnum, labelItemToKind } from '../config/constant'
import moment from 'moment'

export default function ItemRespectiveDetails({ item }) {
    if(item.__t==ItemKindEnum.BookItem){ return (
        <div>
            <p
                className={`mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`}
            >
                Type: {item?.condition || ""}
            </p>
            <span className="line-clamp-1 mt-2 max-w-[60%] md:max-w-[80%]">
                Author:{" "}
                <span
                    title={item?.authors}
                    className="font-medium"
                >
                    {item?.authors}
                </span>
            </span>
        </div>
    )}else if(item.__t==ItemKindEnum.TutorItem){
       return <div>
             <p
                className={`mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`}
            >
                {[labelItemToKind.TutorItem.experience]}: <span className='font-medium'>{item?.experience || ""}</span>
            </p>
            {/* <p
                className={`mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`}
            >
                {[labelItemToKind.TutorItem.highestQualification]}: <span className='font-medium'>{item?.highestQualification || ""}</span>
            </p> */}
            <p
                className={`mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`}
            >
                {[labelItemToKind.TutorItem.targetClasses]}: <span className='font-medium'>{item?.targetClasses || ""}</span>
            </p>

        </div>
    }
    else if(item.__t==ItemKindEnum.EventItem){
       return <div>
             <p
                className={`mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`}
            >
                {[labelItemToKind.EventItem.eventStartDate]}: <span className='font-medium'>{moment(item?.eventStartDate).format("DD-MMM-YYYY") || ""}</span>
            </p>
            <p
                className={`mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`}
            >
                {[labelItemToKind.EventItem.eventMode]}: <span className='font-medium'>{item.eventMode || ""}</span>
            </p>
        </div>
    }
    else if(item.__t==ItemKindEnum.SchoolItem){
       return <div>
             <p
                className={`mt-2 leading-normal line-clamp-1  md:mt-[10px] md:leading-5 capitalize`}
            >
                {[labelItemToKind.SchoolItem.classesOffered]}: <span className='font-medium'>{item?.classesOffered || ""}</span>
            </p>
            <p
                className={`mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`}
            >
                {[labelItemToKind.SchoolItem.schoolType]}: <span className='font-medium'>{item?.schoolType || ""}</span>
            </p>
        </div>
    }
    else if(item.__t==ItemKindEnum.ScholarshipAwardItem){
       return <div>
             <p
                className={`mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`}
            >
                {[labelItemToKind.ScholarshipAwardItem.eligibilityCriteria]}: <span className='font-medium'>{item?.eligibilityCriteria || ""}</span>
            </p>
            <p
                className={`mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`}
            >
                {[labelItemToKind.ScholarshipAwardItem.scholarshipType]}: <span className='font-medium'>{item?.scholarshipType || ""}</span>
            </p>
          
        </div>
    }
    else if(item.__t==ItemKindEnum.ExtracurricularActivityItem){
       return <div>
             <p
                className={`mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`}
            >
                {[labelItemToKind.ExtracurricularActivityItem.targetStudents]}: <span className='font-medium'>{item?.targetStudents || ""}</span>
            </p>
            <p
                className={`mt-2 leading-normal md:mt-[10px] md:leading-5 capitalize`}
            >
                {[labelItemToKind.ExtracurricularActivityItem.activityType]}: <span className='font-medium'>{item?.activityType || ""}</span>
            </p>
          
        </div>
    }
}
