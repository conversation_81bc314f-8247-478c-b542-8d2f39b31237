import React, { useCallback, useEffect, useRef, useState } from "react";
import { GrLocation } from "react-icons/gr";
import { Debounce } from "../utils/utils";
import { ParishesListEnum } from "../config/constant";
import dynamic from "next/dynamic";
import { useMarkerRef } from "@vis.gl/react-google-maps";
const GoogleSearch = dynamic(() => import("@/app/search/GoogleMapSearch.js"))
import { MdMyLocation } from "react-icons/md";
import { FaLocationDot } from "react-icons/fa6";
import { FaChevronDown } from "react-icons/fa6";

export default function LocationSearch({ setselectedParish, setsearchCoordinates, selectedParish }) {
  const locationRef = useRef(null);
  const [focused, setFocued] = useState(false);
  const [expanded, setexpanded] = useState(false)
  const filterRef = useRef(null)
  const [suggestions, setSuggestions] = useState([]);

  useEffect(() => {
    const outSideClickHandle = (e) => {
      if (filterRef.current && !filterRef.current.contains(e.target)) {
        setFocued(false);
        setexpanded(false)
      }
    };
    let addListner = document.addEventListener("click", outSideClickHandle);
    return () => {
      document.removeEventListener("click", addListner);
    };
  }, []);
  const expandDiv = () => {
    // filterRef.current.scrollHeight="240px"
    // setexpanded(expanded);
    setexpanded(true);

  }

  const GoogleSearchFunc = useCallback(GoogleSearch, [])

  return (
    <div className="md:ms-2 absolute z-[10]  overflow-hidden    bg-white w-full top-[10px]  ">
      <div className="flex">
        <div ref={filterRef} className={`cursor pointer border w-full border-[#ccc] rounded-xl transition-all duration-300  ${expanded ? "h-[300px]" : "h-[45px]"
          }`} onClick={expandDiv}>
          <div className="mt-2 px-3 flex justify-between items-center cursor-pointer">Location <FaChevronDown className={`transform transition-transform duration-300 ${expanded ? "rotate-180" : ""
            }`} /></div>

          <div className="mt-3 overflow-scroll">
            <div className="  ms-2  ">
              <GoogleSearchFunc setsearchCoordinates={setsearchCoordinates} setselectedParish={setselectedParish} setSuggestions={setSuggestions} />
            </div>
            {/* <div className="py-2 cursor-pointer hover:bg-gray-100 text-lg"><div className=" flex items-center  p-1.5"><MdMyLocation className="pr-1" />Use Location</div></div>
            <div className="pl-2">Popular location</div>
            <div className=" mt-2 max-h-[200px]">
              {Object.values(ParishesListEnum).map((item) => (
                <div className={selectedParish == item ? " bg-gray-100" : "hover:bg-gray-100"} ><div
                  key={item}
                  value={item}
                  onClick={() => {
                    setselectedParish(selectedParish == item ? null : item)
                  }
                  }
                  className="p-1.5 flex items-center w-full cursor-pointer text-lg font-light  text-[#757575]"
                ><FaLocationDot className="mr-1" />
                  {item}
                </div>
                </div>
              ))}
            </div> */}
          </div>
          {/* <select
          className="w-full pl-3 pr-3 p-[9px] placeholder:text-sm text-[#757575] placeholder:text-[#757575] placeholder:font-light outline-none"
          // value={selectedParish} // You need this state set
          onChange={(e) => setselectedParish(e.target.value)}
        >
          <option
            value=""
            className="outline-none font-light text-sm text-[#757575]"
          >
            Location
          </option>
          {Object.values(ParishesListEnum).map((item) => (
            <option
              key={item}
              value={item}
              className="w-full cursor-pointer text-sm font-light  text-[#757575]"
            >
              {item}
            </option>
          ))}
        </select> */}


        </div>


      </div>
    </div>
  );
}
