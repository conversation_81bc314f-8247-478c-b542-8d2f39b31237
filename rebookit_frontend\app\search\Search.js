import React, { useState } from 'react'
import { IoIosSearch, IoMdStar } from "react-icons/io";
import { ELASTIC_DB_ROUTES } from '../config/api';
import { bookSuggestion } from '../services/bookDetails';
import { useDebounce } from '../components/common/hooks/useDebounce';
import { Debounce } from '../utils/utils';
import { useRouter } from 'next/navigation';

export default function Search() {
    const [searchSuggestions, setSearchSuggestions] = useState([])
    const router=useRouter()
    const [isLoading,setisLoading]=useState(false)
    const [changeSearchText,setchangeSearchText]=useState("")
    const fetchSearchData = async (event) => {
            setisLoading(true)
        try {
            console.log("event in search data", event.target.value)
            let text = event.target.value
            setchangeSearchText(text)
            if (text.length) {

                let urlString = `${ELASTIC_DB_ROUTES.SUGGESTION
                    }?searchTerm=${text.trim()}&page=1`;
                let searchData = await bookSuggestion(urlString);
                console.log("searchData items", searchData);
                if (searchData.status == 200) {
                    if (searchData?.data?.data.length) {
                        setSearchSuggestions(searchData.data.data);
                    } else {
                        setSearchSuggestions([]);
                    }
                } else {
                    setSearchSuggestions([]);
                }
            }else{
                    setSearchSuggestions([]);
            }
            setisLoading(false)
        } catch (error) {
            console.log("error", error);
        }
    };
    console.log("searchSuggestions", searchSuggestions)
    console.log("setchangeSearchText",changeSearchText)
    //  border-[#D9E2E8]
    let defaultHeight = 'full'
    const debounceHandle = Debounce(fetchSearchData, 700)
    return (
        <div className={`absolute z-[1] bg-white border border-[#ccc] ml-auto mr-4  rounded-xl w-full  top-[10px] ${searchSuggestions.length ? "h-fit" : "defaultHeight"}`} >
            <div className="flex">
                <input
                    className="w-full pl-4 pr-3 p-[9px] placeholder:text-sm text-[#757575] placeholder:text-[#757575] placeholder:font-light outline-none"
                    type="text"
                    onChange={debounceHandle}
                    placeholder="Search by Books, E-Directory, Events, Scholarship & Awards."
                />

                <button
                    className="m-2 shadow px-2.5 hover:bg-[#0161ab] hover:text-white items-center gap-1 rounded-full"
                    type="submit"
                    onClick={() => {
                        // alert("hello");
                    }}
                >
                    <IoIosSearch />
                    {/* Search */}
                </button>
            </div>

            <div>
                { searchSuggestions.length?searchSuggestions.map((item) => {
                    return <div className='flex items-center justify-between px-3 py-1 cursor-pointer hover:bg-[#211F54] hover:text-white' onClick={()=>router.push(`/book-detail?id=${item._id}`)}>
                        <div>{item.title}</div>
                        <div><img className='h-[30] w-[25px] rounded-md' src={item.images[0]} /></div>
                    </div>
                }):<div className='px-3 text-center'>{!isLoading &&changeSearchText?"No data found":""}</div>}
            </div>
        </div>

    )
}
