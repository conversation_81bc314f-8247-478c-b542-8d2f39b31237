"use client";

import React, {use<PERSON><PERSON>back, useEffect, useRef, useState} from "react";
import bookListingScss from "./book-listing.module.scss";
import {MdKeyboardArrowDown} from "react-icons/md";
import ToggleSwitch from "@/app/components/common/Switch";
import {FaListUl} from "react-icons/fa6";
import {LuLayoutGrid} from "react-icons/lu";
import {IoIosSearch, IoMdStar} from "react-icons/io";
import Image from "next/image";
import dynamic from "next/dynamic";
import {HiOutlineHeart} from "react-icons/hi";
import {PiShareFatFill} from "react-icons/pi";
import {useRouter, useSearchParams} from "next/navigation";
import {ELASTIC_DB_ROUTES, USER_ROUTES} from "../config/api";
import {GrLocation} from "react-icons/gr";
import {FaStar} from "react-icons/fa";
import {CiStar} from "react-icons/ci";

import {
  Debounce,
  getToken,
  globleBookmarkFunc,
  RedirectToLoginIfNot,
  userDataFromLocal,
} from "../utils/utils";
import {toast} from "react-toastify";
import moment from "moment";
import {useDispatch, useSelector} from "react-redux";
import {
  updateItemId,
  updateProfileComponentIndex,
} from "../redux/slices/storeSlice";
import {RxCross2} from "react-icons/rx";
import No_result_found_search_page from "@/public/images/No_result_found_search_page.svg";
import {
  bookMarkItem,
  delete_bookMarkItem,
  getCategories,
  getSubCategories,
} from "../services/profile";
import ShareCard from "../components/common/ShareCard";
import {bookSearch} from "../services/bookDetails";
import {FaHeart} from "react-icons/fa";
import ItemRespectiveDetails from "./ItemRespectiveDetails";
import Search from "./Search";
import LocationSearch from "./LocationSearch";

const CustomSlider = dynamic(() => import("@/app/components/common/Slider"));
const MapView = dynamic(() => import("@/app/components/book-listing/Mapview"));
const GoogleSearch = dynamic(() => import("@/app/search/GoogleMapSearch.js"));
const BuyAndSellComponent = dynamic(() =>
  import("@/app/components/about/BuyAndSellComponent")
);

function BookListing() {
  const router = useRouter();
  const storedata = useSelector((state) => state.storeData);
  const searchParams = useSearchParams();
  const bookName = searchParams.get("book");
  const category = searchParams.get("category");
  const subCategory = searchParams.get("subCategory");
  const booktype = searchParams.get("booktype");
  const [bookmarkedItems, setBookmarkedItems] = useState({});
  const [location, setLocation] = useState("");
  const dispatch = useDispatch();
  const filterRef = useRef(null);
  const isListCheck = searchParams.get("isList");
  console.log("isListCheck", isListCheck);
  const [enabled, setEnabled] = useState(false);
  const [isList, setIsList] = useState(isListCheck ? false : true);
  const [notificationChecked, setNotificationChecked] = useState(false);
  const [searchData, setSearchData] = useState();
  const [loading, setLoading] = useState(false);
  const [filterOccurred, setFilterOccurred] = useState(false);
  const [selectedId, setselectedId] = useState("");
  const [searchedText, setsearchedText] = useState("");
  console.log("searchParams.get(book)", searchParams.get("book"));
  const [numberFilter, setnumberFilter] = useState(null);
  const [selectedParish, setselectedParish] = useState("");
  const [searchCoordinates, setsearchCoordinates] = useState(null);

  const [filterList, setFilterList] = useState([
    {
      icon: <MdKeyboardArrowDown className="order-2" />,
      removeIcon: (
        <RxCross2
          className="order-2"
          onClick={() => {
            filterListReset(0);
            filterListHandler("filtersList", [], 1);
            closeAllFilterDropdowns();
          }}
        />
      ),
      name: "Category",
      filtersList: [],
      openDropdown: false,
    },
    {
      icon: <MdKeyboardArrowDown className="order-2" />,
      removeIcon: (
        <RxCross2
          className="order-2"
          onClick={() => {
            filterListReset(1);
            closeAllFilterDropdowns();
          }}
        />
      ),
      name: "Sub-Category",
      filtersList: [],
      openDropdown: false,
    },
    // {
    //     icon: <MdKeyboardArrowDown className='order-2' />,
    //     name: "Latest",
    //     filtersList: [],
    //     openDropdown: false
    // },

    // {
    //   icon: <MdKeyboardArrowDown className="order-2" />,
    //   removeIcon: (
    //     <RxCross2 className="order-2" onClick={() => filterListReset(2)} />
    //   ),
    //   name: "Price Range",
    //   filtersList: [
    //     { id: "0-100", name: "J$0 - J$100" },
    //     { id: "101-500", name: "J$101 - J$500" },
    //     { id: "501-1000", name: "J$501 - J$1000" },
    //   ],
    //   openDropdown: false,
    // },
    // {
    //   icon: <MdKeyboardArrowDown className="order-2" />,
    //   removeIcon: (
    //     // <RxCross2 className="order-2" />
    //     <RxCross2 className="order-2" onClick={() => filterListReset(3)} />
    //   ),
    //   name: "Location",
    //   filtersList: [
    //     { id: "0", name: "Kingston" },
    //     { id: "1", name: "Falmouth" },
    //     { id: "2", name: "Runaway Bay" },
    //   ],
    //   openDropdown: false,
    // },
  ]);

  console.log("storedata", storedata);
  useEffect(() => {
    // if (searchParams.get("book")) {
    //     // setsearchedText(searchParams.get("book"))
    //     fetchSearchData(searchParams.get("book"))
    // }
    if (category) {
      fetchSearchDataForRedirection();
    } else if (searchParams.get("book")) {
      fetchSearchData(searchParams.get("book"));
    } else {
      fetchSearchData();
    }
  }, [category, subCategory, selectedParish, searchCoordinates]);

  // useEffect(() => {
  //     fetchSearchData()
  // }, [searchCoordinates])

  // Fetching Books [Filter]
  //  GoogleSearch
  let GoogleSearchBack = useCallback(GoogleSearch, []);

  console.log(searchedText, "searchedText");
  let userData = userDataFromLocal();

  const fetchSearchDataForRedirection = async () => {
    try {
      setLoading(true);
      let payload = {
        filters: {
          // "keyword": searchedText,
          // "minPrice": 1,
          // "maxPrice": 1000,
          ...(category && {category: [category]}),
          ...(subCategory && {subCategory: [subCategory]}),
          ...(filterList[2]?.name !== "Price Range" && {
            priceRange: filterList[2]?.selectedValue,
          }),
          // "authors": []
        },
        sort: {
          // price: -1,
        },
      };

      let userToken = getToken();
      let userData = userDataFromLocal();
      let urlstring = "";
      urlstring = ELASTIC_DB_ROUTES.SEARCH?.replace("{{page}}", 1);

      if (userData) {
        urlstring = urlstring + `&userId=${userData._id}`;
      }

      if (booktype) {
        payload.filters = {...payload.filters, category: [category]};
        payload.filters = {
          ...payload.filters,
          subCategory: storedata.carrabianNonCarrabian.value,
        };
      }
      let searchData = await bookSearch(urlstring, payload);
      if (!searchData?.error) {
        console.log("search Data", searchData);
        if (searchData?.data?.data) {
          setSearchData(searchData?.data?.data);
        }
      } else {
        // toast.error(response?.message || "Internal Server Error")
      }
      setLoading(false);
    } catch (error) {
      console.log("error", error);
      setLoading(false);
    }
  };
  const fetchSearchData = async (text) => {
    try {
      setLoading(true);
      let payload = {
        filters: {
          // "keyword": searchedText,
          // "minPrice": 1,
          // "maxPrice": numberFilter,
          ...(filterList[0]?.name !== "Category" && {
            category: [filterList[0]?.categoryId],
          }),
          ...(filterList[1]?.name !== "Sub-Category" && {
            subCategory: [filterList[1]?.subCategoryId],
          }),
          // "authors": []
        },
        sort: {
          // price: -1,
        },
      };
      if (storedata.userCoordinates || searchCoordinates) {
        payload.filters = {
          ...payload.filters,
          coordinates: [
            searchCoordinates?.lng || storedata.userCoordinates.lng,
            searchCoordinates?.lat || storedata.userCoordinates.lat,
          ],
          maxDistanceInKm: 100,
        };
      }
      if (booktype) {
        payload.filters = {...payload.filters, category: category};
        payload.filters = {
          ...payload.filters,
          subCategory: storedata.carrabianNonCarrabian.value,
        };
      }
      if (selectedParish) {
        payload.filters = {...payload.filters, parish: [selectedParish]};
      }
      if (text) {
        payload.filters = {keyword: text || searchedText};
      }
      if (numberFilter) {
        payload.filters = {...payload.filters, maxPrice: Number(numberFilter)};
      }
      let userToken = getToken();
      let userData = userDataFromLocal();
      console.log();
      let urlstring = "";
      urlstring = ELASTIC_DB_ROUTES.SEARCH?.replace("{{page}}", 1);

      if (userData) {
        urlstring = urlstring + `&userId=${userData._id}`;
        // urlstring = urlstring;
      }

      if (booktype) {
        payload.filters = {...payload.filters, category: category};
        payload.filters = {
          ...payload.filters,
          subCategory: storedata.carrabianNonCarrabian.value,
        };
      }

      let searchData = await bookSearch(urlstring, payload);
      if (!searchData?.error) {
        console.log("search Data", searchData);
        if (searchData?.data?.data) {
          setSearchData(searchData?.data?.data);
        }
      } else {
        // toast.error(response?.message || "Internal Server Error")
      }
      setLoading(false);
    } catch (error) {
      console.log("error", error);
      setLoading(false);
    }
  };
  console.log("filterlist", filterList);
  // Fetch categories and subcategories
  const fetchMasterCategory = async () => {
    try {
      let getCateogries = await getCategories();
      console.log("getCateogries", getCateogries);
      if (getCateogries?.status == 200) {
        //  setBookCategories(getCateogries?.data?.data)
        setFilterList((prev) =>
          prev.map((p, idx) =>
            idx === 0
              ? {...p, filtersList: getCateogries?.data?.categories || []}
              : p
          )
        );
        // redirecCategoryFetch()
        // if(getCateogries?.data?.categories.filter((item)=>item._id==category))
        if (category) {
          let categoryData = getCateogries?.data?.categories?.filter(
            (item) => item._id == category
          );
          filterListHandler(
            "name",
            categoryData[0]?.name,
            0,
            categoryData[0]?._id
          );
        }
      }
    } catch (error) {
      console.error("Category fetch error:", error);
    }
  };

  const fetchMasterSubCategory = async (categoryId) => {
    try {
      // const res = await fetch(`${USER_ROUTES.MASTER_DATA_SUB_CATEGORY}/${categoryId}`, {
      //     method: "GET",
      // })
      // const response = await res.json();
      // console.log("subcategory response is", response?.data)
      let response = await getSubCategories(categoryId);
      response = response?.data;
      setFilterList((prev) =>
        prev.map((p, idx) =>
          idx === 1 ? {...p, filtersList: response?.subCategories || []} : p
        )
      );

      if (subCategory) {
        let categoryData = response?.subCategories?.filter(
          (item) => item._id == subCategory
        );

        filterListHandler(
          "name",
          categoryData[0]?.name,
          1,
          categoryData[0]?._id
        );
      }
      // return response?.data || [];
    } catch (error) {
      console.error("Subcategory fetch error:", error);
      // return [];
    }
  };
  console.log("searchData", searchData);

  useEffect(() => {
    if (filterOccurred) {
      fetchSearchData();
      router.replace("/search", undefined, {shallow: true});
    }
    setFilterOccurred(false);
  }, [filterOccurred]);

  useEffect(() => {
    if (category) {
      fetchMasterSubCategory(category);
    }

    if (subCategory) {
      if (filterList[1].filtersList.length) {
        let filterDataCat = filterList[0].filtersList?.filter(
          (item) => item._id == category
        );
        let filterDataSubCat = filterList[1].filtersList?.filter(
          (item) => item._id == subCategory
        );
        console.log("filterDataCat", filterDataCat);
        filterListHandler(
          "name",
          filterDataCat[0]?.name,
          0,
          filterDataCat[0]?._id
        );
        filterListHandler(
          "name",
          filterDataSubCat[1]?.name,
          1,
          filterDataSubCat[1]?._id
        );
        setFilterOccurred(true);
      }
    } else if (category) {
      if (filterList[0].filtersList.length) {
        let filterData = filterList[0].filtersList?.filter(
          (item) => item._id == category
        );
        filterListHandler("name", filterData[0]?.name, 0, filterData[0]?._id);
        filterListHandler("name", filtersName[1], 1, "");

        setFilterOccurred(true);
      }
    }
  }, [category, subCategory]);

  useEffect(() => {
    fetchMasterCategory();
  }, []);

  useEffect(() => {
    if (typeof window === "undefined") return;

    // Modern Navigation Timing API
    const navEntries = window.performance.getEntriesByType("navigation");
    const navType = navEntries.length > 0 ? navEntries[0].type : null;

    // Fallback for older browsers
    console.log("navEntries", navEntries);
    const legacyNav =
      window.performance.navigation && window.performance.navigation.type === 1;

    if (navType === "navigate" || legacyNav) {
      const hasQuery = Object.keys(router.query || {}).length > 0;
      if (hasQuery) {
        // replace URL to "/search" without a full reload
        router.replace("/search", undefined, {shallow: true});
      }
    }
  }, [router]);

  const redirecCategoryFetch = () => {
    if (category && filterList[0].filtersList?.length) {
      let selectedCategory = filterList[0]?.filtersList?.filter(
        (item) => item._id == category
      );
      console.log("selectedCategory", selectedCategory);
      if (selectedCategory[0]?.name) {
        filterListHandler(
          "name",
          selectedCategory[0]?.name,
          0,
          selectedCategory[0]?._id
        );
      } else {
        // filterListHandler("name", category, 0)
      }
      // filterListHandler("name", category, 0,)
      // setFilterOccurred(true)

      // if (subCategory) {
      //     filterListHandler("name", subCategory, 1)
      // }
    }
  };
  // useEffect(() => {
  //     redirecCategoryFetch()
  // }, [category])

  const settings = {
    dots: true,
    dotsClass: "custom_inside_dots slick-dots !bottom-4.5 md:!bottom-6",
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    arrows: false,
    adaptiveHeight: true,
  };

  const notificationInputHandler = () => {
    setNotificationChecked(!notificationChecked);
  };

  // Navigation of chat button
  const chatNavigationHandler = (e, itemId) => {
    e.stopPropagation();

    const profileIndex = 3;
    dispatch(updateProfileComponentIndex(profileIndex));
    dispatch(updateItemId(itemId));

    let redirectpath =
      `${window.location.pathname}` + `${window.location.search}`;
    console.log("redirectpath", redirectpath);
    if (getToken()) {
      router.push("/profile/messages");
      return;
    }
    RedirectToLoginIfNot(redirectpath, router);
    // router.push("/profile/messages");
  };

  // Dropdown Open Handler
  const openDropdownHandler = (index) => {
    setFilterList((prev) =>
      prev.map((p, idx) =>
        idx === index ? {...p, openDropdown: !p.openDropdown} : p
      )
    );

    if (index === 1 && filterList[0]?.name === "Category") {
      // setFilterList("filtersList", [], 1)
      toast.warn("Kindly select a category first.");
    }
  };

  // Filter Update Handler
  const filterListHandler = (name, value, index, categoryId) => {
    setFilterList((prev) =>
      prev.map((p, idx) =>
        idx === index
          ? {
              ...p,
              [name]: value,
              [index ? "subCategoryId" : "categoryId"]: categoryId,
            }
          : p
      )
    );
  };

  console.log("filterList", filterList);

  // When Selecting a filter
  const selectFilterHandler = (name, value, index, categoryId = "") => {
    filterListHandler(name, value, index, categoryId);
    setFilterOccurred(true);

    console.log("categoryId", categoryId);

    if (index === 0) {
      filterListReset(1);
      fetchMasterSubCategory(categoryId);
    }
  };

  // Closing All Filter Dropdowns
  const closeAllFilterDropdowns = () => {
    setFilterList((prev) => prev.map((p) => ({...p, openDropdown: false})));
  };

  const filtersName = {
    0: "Category",
    1: "Sub-Category",
    // 2: "Latest",
    2: "Price Range",
    3: "Location",
  };
  // Reset all filters Name
  const filterListReset = (index) => {
    filterListHandler("name", filtersName[index], index);
    setFilterOccurred(true);
  };

  // Handle click outside to close all filters
  useEffect(() => {
    function handleClickOutside(event) {
      if (filterRef.current && !filterRef.current.contains(event.target)) {
        closeAllFilterDropdowns();
      }
    }
    // Bind the event listener
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      // Unbind the event listener on clean up
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [filterRef]);

  // const bookTheItemMark = async (id) => {
  //   try {
  //     let bookMarkResponse = await bookMarkItem({itemId: id});
  //     console.log("bookMarkResponse", bookMarkResponse);
  //     if (bookMarkResponse.data?._id) {
  //       toast.success("Item added");
  //     }
  //   } catch (err) {
  //     console.log("err bookTheItemMark", err);
  //   }
  // };

  const bookTheItemMark = async (id) => {
    try {
      setBookmarkedItems((prev) => ({
        ...prev,
        [id]: !prev[id], // Toggle bookmark state
      }));

      let bookMarkResponse = await bookMarkItem({itemId: id});

      if (bookMarkResponse.data?._id) {
        toast.success(
          bookmarkedItems[id]
            ? "Item removed from Wishlist"
            : "Item Added In Wishlist"
        );
      }
      fetchSearchData();
    } catch (err) {
      setBookmarkedItems((prev) => ({
        ...prev,
        [id]: !prev[id],
      }));
      toast.error("Failed to update Wishlist");
    }
  };

  console.log("isList", isList);
  console.log("window.location", window.location);

  const returnCategorySubCat = (item) => {
    let str = item?.categoryDoc?.name;
    if (item?.subcategoryDoc?.name) {
      str = str + `/${item?.subcategoryDoc?.name}`;
    }
    return str;
  };

  useEffect(() => {
    if ("geolocation" in navigator) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const {latitude, longitude} = position.coords;
          setLocation({lat: latitude, lng: longitude});
        },
        (error) => {
          console.error("Error getting location:", error);
        }
      );
    } else {
      console.log("Geolocation is not supported");
    }
  }, []);

  console.log(location, "location in wev");
  console.log(numberFilter, "numberFilter");
  console.log("bookmarkedItems", bookmarkedItems);

  const deleteBookMarkItem = async (id) => {
    let response = await delete_bookMarkItem(id);
    if (response.status == 200) {
      fetchSearchData();
      toast.success("Removed Wishlist Item");
    }
  };
  console.log("selectedParish", selectedParish);
  const debouncePriceHandler = Debounce((e) => {
    setFilterOccurred(true);
    setnumberFilter(e.target.value);
  }, 800);
  console.log("searchCoordinates", searchCoordinates);
  return (
    <div
      className={`${bookListingScss.bookListingContainer} py-5  md:px-[50px] lg:px-[100px]`}
    >
      <section className="container-wrapper">
        <div className={`mt-2.5 mb-10 md:mt-[50px] lg:mt-[100px] `}>
          {/* Filters */}
          <section className="">
            <div className="grid grid-cols-3 mx-3 ">
              <div className="col-span-3  md:col-span-1 flex items-center gap-3   ">
                <h1 className="text-4xl font-semibold">Items</h1>
                <p className="text-sm font-semibold mb-1 text-gray-400">
                  ({searchData?.length || 0} Results)
                </p>
              </div>

              <div className="col-span-3 md:col-span-1 w-full relative flex items-center  h-[70px] ">
                {/* <div className="absolute z-[100] bg-white border ml-auto mr-4 border-[#D9E2E8] rounded-xl w-full  top-[10px]  ">
                  <div className="flex">
                    <input
                      className="w-full pl-4 pr-3 p-[9px] placeholder:text-sm text-[#757575] placeholder:text-[#757575] placeholder:font-light outline-none"
                      type="text"
                      placeholder="Search by Books, E-Directory, Events, Scholarship & Awards."
                    />

                    <button
                      className="m-2 shadow px-2.5 hover:bg-[#0161ab] hover:text-white items-center gap-1 rounded-full"
                      type="submit"
                      onClick={() => {
                        alert("hello");
                      }}
                    >
                      <IoIosSearch />
                    </button>
                  </div>
                </div> */}
                <Search />
              </div>

              <div className="col-span-3 md:col-span-1 flex items-center relative w-full h-[70px] ">
                {/* <div className="ms-2 absolute  border-1 border-[#D9E2E8] rounded-lg bg-white w-full top-[10px]">
                  <div className=" flex items-center gap-1   ">
                    <input
                      type="text"
                      className="w-full pr-3 p-[9px] pl-4 placeholder:text-sm text-[#757575] placeholder:text-[#757575] placeholder:font-light outline-none"
                      placeholder="Search by location"
                    />
                    <button
                      className="m-2 shadow px-2.5 py-1.5 hover:bg-[#0161ab] hover:text-white items-center gap-1 rounded-full"
                      type="submit"
                      onClick={() => {
                        alert("hello");
                      }}
                    >
                      <GrLocation />
                    </button>
                  </div>
                </div> */}
                <GoogleSearchBack
                  setsearchCoordinates={setsearchCoordinates}
                  searchCoordinates={searchCoordinates}
                  setselectedParish={setselectedParish}
                  selectedParish={selectedParish}
                />
                {/* <LocationSearch setselectedParish={setselectedParish} selectedParish={selectedParish} setsearchCoordinates={setsearchCoordinates} /> */}
              </div>
            </div>

            {/* <div className="flex items-center mt-5 gap-2.5 ">
              <div className="w-[59.2%] flex border border-[#D9E2E8] rounded-full">
                  <input
                    className="w-full px-3 p-[9px] text-[#757575] placeholder:text-[#757575] outline-none"
                    type="text"
                    placeholder="Search by Title, Author, ISBN, Publisher, Grade Level, Subject..."
                  />

                  <button
                    className="m-2 shadow px-2.5 hover:bg-[#0161ab] hover:text-white items-center gap-1 rounded-full"
                    type="submit"
                    onClick={() => {
                      alert("hello");
                    }}
                  >
                    <IoIosSearch />
                    Search
                  </button>
                </div>
            </div> */}

            <div className=" h-full md:flex justify-between items-center gap-[9px]">
              <div className="mt-5 md:flex justify-between items-center gap-[9px] w-full">
                <div
                  className="flex gap-2 mx-[10px]  no_scrollbar"
                  ref={filterRef}
                >
                  {filterList.map((item, itemIdx) => (
                    <button
                      key={`filter-btn-${itemIdx}`}
                      className={`flex flex-row items-center gap-1.5 bg-[#E1020C] rounded-sm py-1.5 px-3 md:py-2 text-white relative transition-all ease-in-out duration-350 ${
                        item?.openDropdown ? "rounded-b-none" : ""
                      }`}
                      onClick={() => openDropdownHandler(itemIdx)}
                    >
                      {item.name === filtersName[itemIdx]
                        ? item.icon
                        : item.removeIcon}

                      <span className="text-xs leading-normal md:w-[200px] max-w-[150px] line-clamp-1">
                        {item.name}
                      </span>

                      <div
                        className={`absolute top-full left-0 bg-[#E1020C] w-full overflow-y-auto overflow-x-hidden h-fit flex flex-col no_scrollbar z-9 rounded-b-sm transition-all duration-300 linear ${
                          item?.openDropdown
                            ? "max-h-[300px] md:max-h-[400px]"
                            : "max-h-0"
                        }`}
                      >
                        {item?.filtersList?.map((filterItem, idx) => (
                          <>
                            <span
                              key={`filter-category-${filterItem?.name + idx}`}
                              className="py-1 px-2.5 text-xs text-white hover:bg-[#211F54] after:border-b after:border-[#ffffff80]"
                              title={filterItem?.name}
                              onClick={() =>
                                selectFilterHandler(
                                  "name",
                                  filterItem?.name,
                                  itemIdx,
                                  filterItem?._id
                                )
                              }
                            >
                              {filterItem?.name}
                            </span>

                            {item?.filtersList?.length - 1 !== idx && (
                              <hr className="w-[90%] mx-auto my-1 text-[#FFFFFF80]" />
                            )}
                          </>
                        ))}
                      </div>
                    </button>
                  ))}

                  {/* <div className="bg-[#E1020C]">
                    <input type="number" onChange={debouncePriceHandler} placeholder="Enter Price" className="h-full w-full ps-2 placeholder:text-white text-white outline-none" />
                  </div> */}
                </div>

                <div className="hidden md:flex gap-[8px] md:ml-auto">
                  <button
                    className={`border-[0.7px] border-[#EFEFEF] w-[46px] h-[46px] flex items-center justify-center transition-colors duration-200 ease-in-out ${
                      isList ? `bg-[#211F54]` : "bg-white"
                    } `}
                    onClick={() => setIsList(true)}
                  >
                    <FaListUl
                      className="w-3.5 h-3.5"
                      fill={isList ? "#ffffff" : "#858585"}
                    />
                  </button>

                  <button
                    className={`border-[0.7px] border-[#EFEFEF] w-[46px] h-[46px] flex items-center justify-center transition-colors duration-200 ease-in-out ${
                      !isList ? `bg-[#211F54]` : "bg-white"
                    }`}
                    onClick={() => setIsList(false)}
                  >
                    <LuLayoutGrid
                      className="w-3.5 h-3.5"
                      stroke={!isList ? "#ffffff" : "#858585"}
                    />
                  </button>
                </div>
              </div>

              {/* <div className="hidden w-[48%] md:flex justify-end items-center border-l border-gray-400">
                  <div className='font-semibold  text-xs leading-normal text-[#192024] pr-3 relative after:content-[""] after:absolute after:right-0 after:h-[70%] after:border-r after:border-[#D9E2E8] after:top-1/2 after:translate-y-[-50%]'>
                    {searchData?.length || 0} Results
                  </div>

                  <div className="flex gap-[5px] pr-1.5 pl-3">
                    <span className="text-xs leading-normal text-[#192024]">
                      Sort by <b>Recommended</b>
                    </span>
                    <MdKeyboardArrowDown />
                  </div>
                </div> */}
            </div>

            <div className="mt-2.5 flex mx-[10px] justify-between md:hidden">
              {/* <div className="flex justify-between  w-full items-center">
                <span className="font-semibold text-xs leading-normal text-[#192024]">
                  63 Results
                </span>

                <div className="flex gap-[5px] pr-1.5">
                  <span className="text-xs leading-normal text-[#192024]">
                    Sort by <b>Recommended</b>
                  </span>
                  <MdKeyboardArrowDown />
                </div>
              </div> */}

              {/* <div className='flex gap-[5px]'>
                            <span className={`border-[0.7px] border-[#EFEFEF] w-7 h-7 flex items-center justify-center transition-colors duration-200 ease-in-out ${isList ? `bg-[#211F54]` : "bg-white"} `} onClick={() => setIsList(true)}>
                                <FaListUl className='w-3.5 h-3.5' fill={isList ? "#ffffff" : "#858585"} />
                            </span>

                            <span className={`border-[0.7px] border-[#EFEFEF] w-7 h-7 flex items-center justify-center transition-colors duration-200 ease-in-out ${!isList ? `bg-[#211F54]` : "bg-white"}`} onClick={() => setIsList(false)} >
                                <LuLayoutGrid className='w-3.5 h-3.5' stroke={!isList ? "#ffffff" : "#858585"} />
                            </span>
                        </div> */}
            </div>
          </section>

          {/* list of books */}
          {isList ? (
            <section className="px-2.5 pb-10 mt-5 flex flex-col lg:flex-row gap-5 md:pb-[70px]">
              {/* List View */}
              <div className="flex flex-col w-full lg:w-[65%] xl:w-[60%] gap-5 lg:gap-[30px] order-2 lg:order-1">
                {searchData?.length > 0
                  ? searchData.map((item, idx) => (
                      <div
                        key={`book-list-${idx}`}
                        className="border border-[#EAEAEA] p-4 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 h-fit cursor-pointer"
                        onClick={() =>
                          router.push(`/book-detail?id=${item._id}`)
                        }
                      >
                        {/* Images with action buttons */}
                        <div className="col-span-1 w-full aspect-[3/4] sm:aspect-[2/3] md:aspect-auto overflow-hidden bg-amber-100  rounded relative">
                          {/* Bookmark and Share Buttons (top-right corner) */}
                          <div className="absolute top-2 right-2 z-1 flex gap-2">
                            {userData?._id && (
                              <button
                                className={`p-2 rounded-full shadow-md ${
                                  item.hasBookmarked
                                    ? "bg-white text-red-500"
                                    : "global_linear_gradient text-white hover:bg-white"
                                } transition-colors`}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (
                                    item.hasBookmarked &&
                                    item.bookmarkDoc[0].itemId
                                  ) {
                                    deleteBookMarkItem(item.bookmarkDoc[0]._id);
                                  } else {
                                    globleBookmarkFunc(
                                      item.createdByDoc._id,
                                      () => bookTheItemMark(item._id)
                                    );
                                  }
                                }}
                              >
                                <FaHeart size={16} />
                              </button>
                            )}
                            <button
                              className="p-2 rounded-full bg-white/80 global_linear_gradient text-white hover:bg-white transition-colors"
                              onClick={(e) => {
                                e.stopPropagation();
                                setselectedId(item._id);
                                document
                                  .getElementById("myShareModal")
                                  ?.classList.remove("hidden");
                              }}
                            >
                              <PiShareFatFill size={16} />
                            </button>
                          </div>

                          <CustomSlider
                            sliderSettings={settings}
                            className="h-full "
                          >
                            {item.images.map((src, i) => (
                              <div
                                key={i}
                                className="relative w-full h-full flex items-center  justify-center"
                              >
                                <div className="w-full h-0 pb-[133.33%] relative">
                                  <img
                                    src={src || "/images/book_1.jpg"}
                                    alt={`book image ${i + 1}`}
                                    className="absolute inset-0 object-cover w-full h-full rounded"
                                    sizes="(max-width: 640px) 50vw, (max-width: 1024px) 33vw, 25vw"
                                  />
                                </div>
                              </div>
                            ))}
                          </CustomSlider>
                        </div>

                        {/* Details */}
                        <div className="col-span-1 sm:col-span-1 md:col-span-3 flex flex-col justify-between">
                          <div>
                            <h3 className="text-base md:text-xl font-semibold leading-tight line-clamp-2">
                              {item.title || "Untitled"}
                            </h3>
                            <p className="mt-1 text-sm md:text-base capitalize">
                              Category:{" "}
                              <span className="font-medium">
                                {returnCategorySubCat(item)}
                              </span>
                            </p>
                            <ItemRespectiveDetails item={item} />
                            <div className="mt-2 flex flex-wrap items-center text-xs md:text-sm gap-2">
                              <span className="truncate max-w-[60%]">
                                Seller:{" "}
                                <span className="font-medium">
                                  {item.createdByDoc?.firstName}
                                </span>
                              </span>
                              {
                                <div className="flex items-center gap-1">
                                  {item?.reviewDoc[0]?.averageRating && (
                                    <span className="bg-[#14884C] text-white text-[10px] px-2 py-[2px] rounded-sm">
                                      {item.reviewDoc[0]?.averageRating}
                                    </span>
                                  )}
                                  <div className="flex">
                                    <div className="flex items-center">
                                      {item?.reviewDoc[0]?.averageRating
                                        ? Array.from({
                                            length:
                                              item?.reviewDoc[0]?.averageRating,
                                          }).map((item) => {
                                            return (
                                              <div>
                                                <FaStar
                                                  size={12}
                                                  fill={"#14884C"}
                                                  className={
                                                    item?.reviewDoc[0]
                                                      ?.averageRating
                                                      ? "text-yellow-600"
                                                      : "text-gray-300"
                                                  }
                                                />
                                              </div>
                                            );
                                          })
                                        : Array.from({
                                            length: 5,
                                          }).map((item) => {
                                            return (
                                              <div>
                                                <FaStar
                                                  size={12}
                                                  fill={"gray"}
                                                  className={
                                                    item?.reviewDoc[0]
                                                      ?.averageRating
                                                      ? "text-yellow-600"
                                                      : "text-gray-300"
                                                  }
                                                />
                                              </div>
                                            );
                                          })}
                                      {/* {item?.reviewDoc[0]?.averageRating ? "" : "(No Review)"} */}
                                    </div>
                                  </div>
                                </div>
                              }
                            </div>
                            <p
                              className="mt-2 text-xs md:text-sm truncate"
                              title={
                                item.address?.formatted_address || item.address
                              }
                            >
                              Location:{" "}
                              {item.address?.formatted_address || item.address}
                            </p>

                            <div className="text-xs leading-normal flex mt-2.5 md:mt-2 md:text-base md:leading-[23.5px]">
                              <span
                                title={item?.address}
                                className="line-clamp-1"
                              >
                                Parish: {item?.address?.parish || "NA"}
                              </span>
                            </div>
                            <p className="mt-2 text-xs md:text-sm">
                              Date:{" "}
                              <span className="font-medium">
                                {moment(item.createdAt).format("DD-MM-YYYY")}
                              </span>
                            </p>
                          </div>

                          {/* Price and Chat Button */}
                          <div className="mt-4 flex flex-wrap justify-between items-center gap-2">
                            <p className="text-base md:text-3xl font-bold truncate">
                              J$ {item.price}
                            </p>
                            <button
                              className="py-2 px-4 text-xs md:text-sm font-semibold rounded-full text-white global_linear_gradient"
                              onClick={(e) => {
                                e.stopPropagation();
                                let user = userDataFromLocal() || "{}";

                                if (item.createdByDoc?._id === user._id) {
                                  toast.error("You cannot message yourself");
                                } else {
                                  chatNavigationHandler(e, item._id);
                                }
                              }}
                            >
                              Chat Now
                            </button>
                          </div>
                        </div>
                      </div>
                    ))
                  : !loading && (
                      <div className="flex flex-col items-center justify-center space-y-5">
                        <Image
                          src={No_result_found_search_page}
                          alt="No Result Found"
                          width={300}
                          height={300}
                          objectFit="cover"
                        />
                        <div className="text-center space-y-2">
                          <h4 className="text-2xl font-medium">
                            No Results Found
                          </h4>
                          <p className="text-lg text-[#838282]">
                            Nothing on our list yet.
                            <br />
                            It's never too late to change it 😊
                          </p>
                        </div>
                        <button
                          className="mt-4 py-3 px-8 text-base font-medium text-white rounded-full global_linear_gradient"
                          onClick={() => router.push("/become-seller")}
                        >
                          Create A New Listing
                        </button>
                      </div>
                    )}
              </div>

              {/* Map */}
              <div className="w-full  hidden md:block lg:w-[35%] xl:w-[40%] order-1 lg:order-2">
                <MapView
                  width="100%"
                  height="500px"
                  data={searchData}
                  searchCoordinates={searchCoordinates}
                />
              </div>
            </section>
          ) : (
            // Grid View
            <section className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-[33px] mt-5 pb-10 md:pb-[70px]">
              {/* book card */}

              {searchData?.length > 0 ? (
                searchData?.map((item, index) => (
                  <div
                    key={`grid-card-${index}`}
                    className="relative p-[13px] pb-4 border border-[#EAEAEA]"
                  >
                    <div className="absolute top-0 left-[-8] z-10">
                      {/* <div className="global_linear_gradient text-white text-xs font-bold px-3 py-1 rounded-tr-md shadow-md relative">
                        Featured
                        <div className="absolute -bottom-2 left-0 w-0 h-0 border-t-[8px] border-t-blue-700 border-l-[8px] border-l-transparent"></div>
                      </div> */}
                    </div>

                    <CustomSlider sliderSettings={settings} className={""}>
                      {
                        // [
                        //   item?.backCoverImage || item?.coverImage,
                        //   item?.frontCoverImage,
                        //   item?.middlePageImage,
                        //   item?.spineImage,
                        // ]
                        item?.images.map((src, imageIdx) => (
                          <div
                            key={`image-${imageIdx}`}
                            // className="relative  h-[218px] aspect-[3/4] bg-[#73877B] py-2.5 !flex justify-center items-center box-border"
                            className="relative  h-[218px] aspect-[3/4] bg-[#FFFBF6] py-2.5 !flex justify-center items-center box-border"
                          >
                            {userData?._id && (
                              <span
                                className={`p-2.5 rounded-full absolute top-12 right-2.5 z-1 bg-[#fff6] cursor-pointer ${bookListingScss.boxShadow}`}
                                onClick={(e) => {
                                  // e.preventDefault()
                                  // e.stopPropagation()
                                  if (userData?._id && !item.hasBookmarked) {
                                    globleBookmarkFunc(
                                      item?.createdBy?._id,
                                      () => bookTheItemMark(item._id)
                                    );
                                  } else {
                                    if (item.bookmarkDoc.length) {
                                      deleteBookMarkItem(
                                        item.bookmarkDoc[0]._id
                                      );
                                    }
                                  }
                                }}
                              >
                                <HiOutlineHeart
                                  stroke="#5F5F5F"
                                  fill={
                                    item.hasBookmarked ? "#e4000d" : "white"
                                  }
                                  width="43"
                                  height="43"
                                />
                                {/* <FaHeart fill='#e60513' /> */}
                              </span>
                            )}
                            <button
                              className={`p-2.5 rounded-full absolute top-2 right-2.5 z-1 bg-[#fff6] cursor-pointer ${bookListingScss.boxShadow}`}
                              onClick={(e) => {
                                e.stopPropagation();
                                setselectedId(item._id);
                                document
                                  .getElementById("myShareModal")
                                  ?.classList.remove("hidden");
                              }}
                            >
                              <PiShareFatFill size={16} />
                            </button>
                            <div
                              className="relative  h-full flex items-center justify-center cursor-pointer"
                              onClick={(e) => {
                                e.stopPropagation();
                                router.push(`/book-detail?id=${item?._id}`);
                              }}
                            >
                              <img
                                // src={item?.coverImage || item?.backCoverImage}
                                src={src}
                                alt={`book image-${imageIdx}`}
                                // fill
                                className="object-cover w-[90%] h-[80%] rounded "
                              />
                            </div>
                          </div>
                        ))
                      }
                    </CustomSlider>

                    {/* content */}
                    <div className="mt-[11px]">
                      <div
                        className="cursor-pointer"
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/book-detail?id=${item?._id}`);
                        }}
                      >
                        <h1
                          className="text-xl leading-normal font-semibold line-clamp-1 overflow-hidden"
                          title={item?.title}
                        >
                          {item?.title}
                        </h1>

                        <p className="mt-0.5 text-[15px] font-light leading-normal line-clamp-1">
                          Category: {returnCategorySubCat(item)}
                        </p>
                        <ItemRespectiveDetails item={item} />

                        <div className="text-xs leading-normal flex mt-2.5 md:mt-2 md:text-base md:leading-[23.5px]">
                          <span title={item?.address} className="line-clamp-1">
                            Location:{" "}
                            {item?.address.formatted_address || item?.address}
                          </span>
                        </div>
                        <div className="text-xs leading-normal flex mt-2.5 md:mt-2 md:text-base md:leading-[23.5px]">
                          <span title={item?.address} className="line-clamp-1">
                            Parish: {item?.address.parish || "NA"}
                          </span>
                        </div>

                        <div className="text-xs leading-normal flex mt-2.5 md:mt-2 md:text-base md:leading-[23.5px]">
                          Date:&nbsp;
                          <span className="font-medium">
                            {moment(item?.createdAt).format("DD-MM-YYYY")}
                          </span>
                        </div>

                        <div className="flex flex-wrap gap-2 mt-[11.3px] text-base leading-5 items-center">
                          <span className="line-clamp-1 max-w-[60%] md:max-w-[80%]">
                            Seller:{" "}
                            <span className="font-medium">
                              {item?.createdByDoc?.firstName +
                                " " +
                                item?.createdByDoc?.lastName}
                            </span>
                          </span>
                          {
                            <div className="flex items-center gap-1">
                              {item?.reviewDoc[0]?.averageRating && (
                                <span className="bg-[#14884C] text-white text-[10px] px-2 py-[2px] rounded-sm">
                                  {item.reviewDoc[0]?.averageRating}
                                </span>
                              )}
                              <div className="flex">
                                <div className="flex items-center">
                                  {item?.reviewDoc[0]?.averageRating
                                    ? Array.from({
                                        length:
                                          item?.reviewDoc[0]?.averageRating,
                                      }).map((item) => {
                                        return (
                                          <div>
                                            <FaStar
                                              size={12}
                                              fill={"#14884C"}
                                              className={
                                                item?.reviewDoc[0]
                                                  ?.averageRating
                                                  ? "text-yellow-600"
                                                  : "text-gray-300"
                                              }
                                            />
                                          </div>
                                        );
                                      })
                                    : Array.from({
                                        length: 5,
                                      }).map((item) => {
                                        return (
                                          <div>
                                            <FaStar
                                              size={12}
                                              fill={"gray"}
                                              className={
                                                item?.reviewDoc[0]
                                                  ?.averageRating
                                                  ? "text-yellow-600"
                                                  : "text-gray-300"
                                              }
                                            />
                                          </div>
                                        );
                                      })}
                                  {/* {
                                    Array.from({
                                      length: item?.reviewDoc[0]?.averageRating ,
                                    }).map((item) => {
                                          
                                      return <div><FaStar size={12} fill={item?.reviewDoc[0]?.averageRating?"#14884C":"gray"}  className={item?.reviewDoc[0]?.averageRating ? "text-yellow-600" : "text-gray-300"} /></div>
                                    })} */}
                                  {/* {item?.reviewDoc[0]?.averageRating ? "" : "(No Review)"} */}
                                </div>
                              </div>
                            </div>
                          }
                        </div>
                        <div className="mt-[11px] text-[15px] leading-normal flex gap-[11px] items-center"></div>
                      </div>
                      <div className="mt-4 flex gap-[34px] justify-between items-center">
                        <button
                          className="py-[9px] w-[120px] px-[20px] text-base font-semibold leading-6 text-center text-white rounded-full global_linear_gradient"
                          onClick={(e) => {
                            e.stopPropagation();

                            let userData = userDataFromLocal();

                            if (item?.createdByDoc?._id == userData?._id) {
                              toast.error(
                                "You can not send message to your self"
                              );
                            } else {
                              chatNavigationHandler(e, item?._id);
                            }
                          }}
                          // chatNavigationHandler(e, item?._id)}}
                        >
                          Chat Now
                        </button>
                        <p
                          title={item?.price}
                          className="text-xl font-semibold leading-normal line-clamp-1 truncate max-w-[180px] md:max-w-[120px] text-ellipsis"
                        >
                          J${item?.price}
                        </p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                // No Data Found
                <div className="flex flex-col col-span-full justify-center items-center">
                  <div className="relative overflow-hidden w-[372px] h-[372px]">
                    <Image
                      src={No_result_found_search_page}
                      alt="No Result Found"
                      fill
                      objectFit="cover"
                      sizes="100w"
                    />
                  </div>

                  <div className="flex flex-col gap-2.5 mt-5 justify-center items-center">
                    <h4 className="text-2xl leading-normal font-medium">
                      No Result Found
                    </h4>
                    <p className="text-center text-lg leading-normal text-[#838282]">
                      Nothing on our list yet.
                      <br />
                      It's never too late to change it 😊
                    </p>
                  </div>

                  <button
                    type="button"
                    className="mt-[30px] global_linear_gradient text-white py-3 px-[30px] rounded-full text-[15px] font-medium -tracking-[0.3px] leading-[21px]"
                    onClick={() => router.push("/become-seller")}
                  >
                    Create A New Listing
                  </button>
                </div>
              )}
            </section>
          )}
          <ShareCard url={`/book-detail?id=${selectedId}`} />

          <div className="px-2.5">
            <BuyAndSellComponent />
          </div>
        </div>
      </section>
    </div>
  );
}

export default BookListing;
