import React, { Suspense } from 'react';
import dynamic from 'next/dynamic';

const BookListingComponent = dynamic(() => import('./bookListingComponent'), {
    suspense: true,
});

export const metadata = {
    title: "Search Page",
    description: "Browse and search books.",
};

export default function Search() {
    return (
        // <Suspense fallback={<div>Loading books...</div>}>
        <BookListingComponent />
        // </Suspense>
    );
}
