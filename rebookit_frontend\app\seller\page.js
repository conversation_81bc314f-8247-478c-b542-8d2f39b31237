"use client";
import React from "react";
import dynamic from "next/dynamic";
import SellerModuleScss from "./seller.module.scss";
import Image from "next/image";

import SellerStepIcon1 from "@/public/icons/seller_step_1.svg";
import SellerStepIcon2 from "@/public/icons/seller_step_2.svg";
import SellerStepIcon3 from "@/public/icons/seller_step_3.svg";
import SellerStepIcon4 from "@/public/icons/seller_step_4.svg";
import {useRouter} from "next/navigation";

const WhoCanSellSlider = dynamic(() =>
  import("@/app/components/seller/staticSlider")
);
const BannerAd = dynamic(() =>
  import("@/app/components/common/AdBanner/Banner")
);
const Testimonials = dynamic(() =>
  import("@/app/components/landingPage/testimonials/testimonials")
);
const BuyAndSellComponent = dynamic(() =>
  import("@/app/components/about/BuyAndSellComponent")
);
const SellerButton = dynamic(() =>
  import("@/app/components/seller/sellerButton")
);

// export const metadata = {
//     title: "Seller Page",
//     description: "Discover and sell a wide range of books—from bestsellers to rare finds—all in one dedicated space for book lovers.",
// };

const whySellOnRebookIt = [
  {
    title: "Collect 100% of your Earnings",
    summary: "No hidden charges, commissions or fees on your book sales.",
    class: "bg-[#211F54] pt-[26px] px-7",
  },
  {
    title: "Quick Upload",
    summary:
      "Easily upload your book by adding key details like title, author, condition, and price. Take a clear photo and publish in minutes.",
    class: "bg-[#0161AB] pt-[26px] px-7",
  },
  {
    title: "Direct Chat",
    summary:
      "Connect instantly with buyers through ReBookIt’s secure messaging system. Ask questions, negotiate pricing, share delivery details, and finalize deals-all in one place.",
    background: "#C70007",
    class: "bg-[#C70007] pt-[26px] px-7",
  },
  {
    title: "Eco-Friendly",
    summary:
      "By reselling your books, you help reduce waste and promote conscious reading. Every reused book lowers paper demand and supports a circular economy.",
    class: "bg-[#457E42] pt-[26px] px-7",
  },
];

const howToSellRebookItSection = [
  {
    title: "Register Your Account",
    summary:
      "Create your ReBookIt account using your Email & mobile number and complete your seller profile in minutes.",
    icon: SellerStepIcon1,
  },
  {
    title: "Upload Book Info",
    summary:
      "Add a title, condition, price, and good photos to make your book's listing stand out.",
    icon: SellerStepIcon2,
  },
  {
    title: "Set Your Price",
    summary:
      "Use ReBookIt’s smart pricing tips or decide your own price based on book's popularity and condition.",
    icon: SellerStepIcon3,
  },
  {
    title: "Connect with Buyers",
    summary:
      "Chat directly on the website, answer questions, agree on price, and complete your book sale easily.",
    icon: SellerStepIcon4,
  },
];

function Seller() {
  const router = useRouter();
  return (
    <div className={`${SellerModuleScss.sellerContainer}`}>
      {/* Header */}
      <div
        className={`${SellerModuleScss.sellerHeader} flex items-center p-2.5 min-h-[38vh] md:!min-h-[82vh] md:px-[50px] lg:px-[100px]`}
      >
        <header
          className={`my-11  ${SellerModuleScss.sellerMainHeading} container-wrapper`}
        >
          <h1 className="text-[22px] w-[98%] leading-normal font-semibold md:w-[90%] md:text-[40px] lg:text-6xl ">
            Turn Your Learnings Into Earnings
          </h1>
          <p className="mt-2.5 text-[31px]  text-white md:mt-[5px] md:text-[31px] md:w-[90%] lg:text-[46px] lg:leading-14 lg:font-bold lg:w-[80%]">
            Sell your used books on ReBookIt.Club and help someone discover
            affordable reads.
          </p>

          <SellerButton />
        </header>
      </div>

      {/* Why Sell on ReBookIt.Club? */}
      <section className="container-wrapper w-full">
        <section className="mt-10 mb-[50px] px-2.5 md:px-[50px] lg:px-[100px] lg:my-[70px] ">
          <h2 className="text-[22px] font-semibold leading-normal uppercase lg:text-[44px] lg:font-semibold lg:leading-[62px] lg:text-center">
            Why Sell on ReBookIt.Club?
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 mt-5 gap-2.5 lg:gap-5">
            {whySellOnRebookIt.map((item, idx) => (
              <div
                key={`why-sell-${idx}`}
                className={`h-[152px] lg:h-[240px] text-white rounded-[18px] shadow-[0px_0px_26px_0px_rgba(24,24,24,0.10)] ${item.class}`}
              >
                <h5 className="text-lg leading-6 font-semibold lg:text-[24px] lg:leading-[40px]">
                  {item.title}
                </h5>
                <p className="mt-1.5 leading-5 text-sm lg:text-[19px] lg:leading-[28px] lg:font-normal">
                  {item.summary}
                </p>
              </div>
            ))}
          </div>
        </section>
      </section>

      {/* How to Sell on ReBookIt.Club */}
      <section className="bg-[#211F54] text-white pt-5 pb-7 px-2.5 md:px-[50px] lg:py-[61px] lg:px-[100px]">
        <section className="container-wrapper">
          <h2 className="text-[22px] font-semibold leading-normal uppercase md:text-[30px] md:text-center md:leading-[50px] lg:text-[44px] lg:leading-[61px]">
            How to Sell on ReBookIt.Club
          </h2>

          <div className="mt-5 grid grid-cols-1 gap-2.5 md:mt-[30px] md:grid-col-2 lg:grid-cols-4 lg:gap-[42px]">
            {howToSellRebookItSection.map((item, idx) => (
              <div
                key={`how-to-sell-step-${idx + 1}`}
                className="flex gap-5 md:flex-col md:items-center"
              >
                <div className="flex justify-center items-center relative overflow-hidden min-w-[108px] max-w-[108px] min-h-[100px] max-h-[100px] w-full h-full lg:min-w-[200px] lg:max-w-[240px] lg:min-h-[240px] lg:max-h-[240px]">
                  <Image
                    src={item.icon}
                    alt={`Step_icon_${idx + 1}`}
                    fill
                    className="object-contain "
                    sizes="(max-width: 768px) 100px, (max-width: 1024px) 180px, 240px"
                  />
                </div>

                <div className="px-[5px]">
                  <h5 className="text-[#FFC72C] text-lg font-medium leading-normal md:text-xl md:text-center md:leading-[30px] lg:text-2xl lg:leading-[34px]">
                    {item.title}
                  </h5>
                  <p className="mt-1.5 text-xs leading-normal md:mt-4 md:text-center md:text-sm md:leading-5 lg:text-lg lg:mt-6 lg:leading-7">
                    {item.summary}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </section>
      </section>

      {/* Who can sell */}
      <section className="container-wrapper">
        <section className="mt-[50px] px-2.5 mb-[40px]  lg:my-[70px] ">
          <h2 className="text-[22px] font-semibold capitalize leading-normal md:text-center md:text-4xl md:leading-[52px] lg:text-[44px] lg:leading-[62px]">
            Who can sell?
          </h2>

          <div className="mt-2.5 mb-[44px] md:mt-[30px] lg:mb-[50px]">
            <WhoCanSellSlider />
          </div>

          <div className="flex justify-center">
            <button
              onClick={() => router.push("/become-seller")}
              className="rounded-full global_linear_gradient text-xs font-medium -tracking-[0.2px] leading-[15px] py-3 px-8 text-white lg:py-6 lg:px-[70px] lg:text-[25px] lg:leading-[31px]"
            >
              Start Selling
            </button>
          </div>
        </section>
      </section>

      {/* Banners */}
      <BannerAd position="" page="" />

      {/* Testimonials */}
      <Testimonials />

      {/* Buy And Sell */}
      <section className=" container-wrapper">
        <div className="mt-[40px] mb-[40px] px-2.5 md:px-[50px] lg:px-[100px] ">
          <BuyAndSellComponent />
        </div>
      </section>
    </div>
  );
}

export default Seller;
