import { USER_ROUTES } from "../config/api";
import { axiosErrorHandler } from "../utils/axiosError.handler";
import { getToken } from "../utils/utils";
import instance from "./axios";


const uri = {
    login: "/user/login",
    userInfo: "/user",
    editProfile:"/user/edit-profile",
    verifyOtp:"user/verify-otp",
    registerUser: "/user/register",
    forgetPassword: "user/forgot-password",
    createNewPassword: "user/create-new-password",
    sendOtp: "user/send-otp",
    verifyForgetPassword: "/user/verify-forgot-otp",
    resetPassword:"/user/reset-password"
};



// fetch( {
//     method: "POST",
//     headers: {
//         "Content-Type": "application/json"
//     },
//     body: JSON.stringify(data)
// }).then(async res => {
//     const response = await res.json();
//     let authData = response
//     console.log("response auth", response)
//     if (authData?.success) {
//         setToken(authData?.token)
//         toast.success("Logged in successfully!")
//         router.push("/")
//     } else {
//         toast.error(response?.message || "Incorrect credentials! Try again")
//     }
// })


// const login = async (data) => {
//     try {
//         let userToken = getToken()
//         let response = await RestCall({
//             method: "get",
//             url: `${USER_ROUTES.LIST_ITEM}/${id}`,
//             headers: {
//                 "Content-Type": "application/json",
//             }, data: data
//         })
//         return response
//     } catch (err) {
//         return { message: err.message, code: err.statusCode }
//     }
// }


export const login = async (payload, guestId) => {
    let response = await instance
        .post(`${uri.login}`,payload)
        .catch(axiosErrorHandler);
        console.log("login test response",response)
        return response 
};
export const userInfo_api = async () => {
    if (getToken()) {
    try{
        let response = await instance
            .get(`${uri.userInfo}`)
            .catch(axiosErrorHandler);
            console.log("login test response",response)
            return response 
    }catch(err){
        console.log("err in userInfo_api",err)
    }}
};
export const update_userInfo_api = async (userPayload) => {
    try{
        let response = await instance
            .put(`${uri.editProfile}`,userPayload)
            .catch(axiosErrorHandler);
            console.log("login test response",response)
            return response 
    }catch(err){
        console.log("err in userInfo_api",err)
    }
};

export const verifyOtp = async (payload) => {
    try{
        let response = await instance
            .post(`${uri.verifyOtp}`,payload)
            .catch(axiosErrorHandler);
            console.log("verifyOtp test response",response)
            return response 
    }catch(err){
        console.log("err in userInfo_api",err)
    }
};

export const registerUser = async (payload) => {
    try{
        let response = await instance
            .post(`${uri.registerUser}`,payload)
            .catch(axiosErrorHandler);
            console.log("registerUser test response",response)
            return response 
    }catch(err){
        console.log("err in userInfo_api",err)
    }
};



export const forgotPassword = async (payload) => {
    try{
        let response = await instance
            .post(`${uri.forgetPassword}`,payload)
            .catch(axiosErrorHandler);
            console.log("registerUser test response",response)
            return response 
    }catch(err){
        console.log("err in userInfo_api",err)
    }
};


export const createNewPassword = async (payload) => {
    try{
        let response = await instance
            .post(`${uri.forgetPassword}`,payload)
            .catch(axiosErrorHandler);
            console.log("registerUser test response",response)
            return response 
    }catch(err){
        console.log("err in userInfo_api",err)
    }
};

export const sendOTP = async (payload) => {
    try{
        let response = await instance
            .post(`${uri.sendOtp}`,payload)
            .catch(axiosErrorHandler);
            console.log("sendOTP test response",response)
            return response 
    }catch(err){
        console.log("err in userInfo_api",err)
    }
};

export const verifyForgetPassword = async (payload) => {
    try{
        let response = await instance
            .post(`${uri.verifyForgetPassword}`,payload)
            .catch(axiosErrorHandler);
            console.log("sendOTP test response",response)
            return response 
    }catch(err){
        console.log("err in userInfo_api",err)
    }
};


export const resetPassword = async (payload) => {
    try{
        let response = await instance
            .post(`${uri.resetPassword}`,payload)
            .catch(axiosErrorHandler);
            console.log("resetPassword test response",response)
            return response 
    }catch(err){
        console.log("err in userInfo_api",err)
    }
};




// fetch(USER_ROUTES.EDIT_USER_INFO, {
//                 method: "put",
//                 headers: {
//                     "Authorization": `Bearer ${userToken}`,
//                     "Content-Type": "application/json"

//                 },
//                 body: JSON.stringify(userPayload)

//             }).then(async res => {
//                 const response = await res.json();
//                 if (!response.error) {
//                     toast.success(response.message)
//                     setBtnDisabled(true)
//                 } else {
//                     response.message?.map(x => toast.error(x))
//                     toast.error(response.message || "No Info Found")
//                 }
//             })


