const { default: axios } = require("axios");
const { getToken } = require("../utils/utils");

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;

const instance = axios.create({
  baseURL: BASE_URL+"/api" ,

  // Lets keep a check as default is 0 millisecond i.e. never
  // Note: timeout is only for server response not network i.e. server reachability
  timeout: 100000,

  // Lets keep a check as default bytes- 2k
  maxContentLength: 1000,

  // Lets keep a check as default 5 seems high
  maxRedirects: 2,
});

instance.interceptors.request.use(
  (config) => {
    const token = getToken();
    console.log("token", token)
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    // Rate limiting: only fire a request every 2 sec from lodash.debounce
    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });
    return Promise.resolve(config);
  },
  function (error) {
    const response = handleLogError(error); // log them

    return Promise.reject(error);
  }
  // multiple options as to when and how to apply these interceptors
  // , { synchronous: true, runWhen: onGetCall }
);


module.exports = instance;