const { default: axios } = require("axios")
const { default: RestCall } = require("../utils/restCall")
const { ELASTIC_DB_ROUTES } = require("../config/api")
const { getToken } = require("../utils/utils")
const instance = require("./axios")
const { axiosErrorHandler } = require("../utils/axiosError.handler")

const bookDetails = async (data) => {
    try {
        let userToken = getToken()
        let response = await RestCall({
            method:"get",
            url: `${USER_ROUTES.LIST_ITEM}/${id}`,
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${userToken}`
            }, data: data
        })
        return response
    } catch (err) {
        return { message: err.message, code: err.statusCode }
    }
}

const bookSearch=async(url,data)=>{
    
    try{
     let userToken = getToken()
        let response = await <PERSON>Call({
            method:"POST",
            url: url,
            headers: {
                "Content-Type": "application/json",
                "Authorization": `Bearer ${userToken}`
            }, data: data
        })
        return response
    }catch(err){
        return { message: err.message, code: err.statusCode }
    }
}


// const bookSuggestion =async(url)=>{
//      try{
//      let userToken = getToken()
//         let response = await RestCall({
//             method:"get",
//             url: url,
//             headers: {
//                 "Content-Type": "application/json",
//                 "Authorization": `Bearer ${userToken}`
//             }
//         })
//         return response
//     }catch(err){
//         return { message: err.message, code: err.statusCode }

//     }
// }

// const markAsSold=async(url,data)=>{
//      try{
//      let userToken = getToken()
//         let response = await RestCall({
//             method:"put",
//             url: url,
//             headers: {
//                 "Content-Type": "application/json",
//                 "Authorization": `Bearer ${userToken}`
//             },
//             data
//         })
//         return response
//     }catch(err){
//         return { message: err.message, code: err.statusCode }

//     }
// }
 const bookSuggestion = async (url) => {
    let response = await instance
        .get(url)
        .catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response
}

 const markAsSold = async (url,data) => {
    let response = await instance
        .put(url,data)
        .catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response
}
module.exports = {
    bookDetails,
    bookSearch,
    bookSuggestion,
    markAsSold
}