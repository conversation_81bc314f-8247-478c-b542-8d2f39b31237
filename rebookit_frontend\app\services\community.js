import { axiosErrorHandler } from "../utils/axiosError.handler";
import instance from "./axios";

let uri={
    allQuestion:"/community/questions",
    addQuestion:"/community/question",
    userQuestions:"/community/user-questions-with-answer",
    submitAnswer:"/community/answer",
    myquestions: "community/user-questions",
    userAnswers: "/community/user-answers"
    
    
}

export const allQuestion = async (query) => {
    let response = await instance
        .get(`${uri.allQuestion}${query?"?"+query:""}`)
        .catch(axiosErrorHandler);
    console.log("allQuestion response", response)
    return response
}
export const usersQuestionAnswer = async (payload) => {
    let response = await instance
        .get(`${uri.userQuestions}?${payload}`)
        .catch(axiosErrorHandler);
    console.log("allQuestion response", response)
    return response
}

export const addQuestion = async (payload) => {
    let response = await instance
        .post(`${uri.addQuestion}`,payload)
        .catch(axiosErrorHandler);
    console.log("addQuestion response", response)
    return response
}

export const allAnswerOfQuestion = async (id) => {
    let response = await instance
        .get(`${uri.addQuestion}/${id}`)
        .catch(axiosErrorHandler);
    console.log("allQuestion response", response)
    return response
}

export const submitAnswer = async (payload) => {
    let response = await instance
        .post(`${uri.submitAnswer}`,payload)
        .catch(axiosErrorHandler);
    console.log("addQuestion response", response)
    return response
}

export const myquestion = async (payload) => {
    let response = await instance
        .get(`${uri.myquestions}?${payload}`)
        .catch(axiosErrorHandler);
    console.log("allQuestion response", response)
    return response
}
export const myAnswers = async (payload) => {
    let response = await instance
        .get(`${uri.userAnswers}?${payload}`)
        .catch(axiosErrorHandler);
    console.log("allQuestion response", response)
    return response
}

export const deleteQuestion = async (id) => {
    let response = await instance
        .delete(`${uri.addQuestion}/${id}`)
        .catch(axiosErrorHandler);
    return response
}


export const editAnswer = async (id,payload) => {
    let response = await instance
        .put(`${uri.submitAnswer}?answerId=${id}`,payload)
        .catch(axiosErrorHandler);
    return response
}


