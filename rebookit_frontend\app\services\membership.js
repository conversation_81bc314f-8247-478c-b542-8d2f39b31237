import { axiosErrorHandler } from "../utils/axiosError.handler";
import instance from "./axios";

let uri={
    plans:"/user/plan",
    history:"/payment/history"
}


export const subscriptionPlans = async (payload) => {
    let response = await instance
        .get(`${uri.plans}`,payload)
        .catch(axiosErrorHandler);
    console.log("login test response", response)
    return response
}

export const historyOfSubscription = async (payload) => {
    let response = await instance
        .get(`${uri.history}`,payload)
        .catch(axiosErrorHandler);
    console.log("login test response", response)
    return response
}
