import { USER_ROUTES } from "../config/api";
import { axiosErrorHandler } from "../utils/axiosError.handler";
import instance from "./axios";


const uri = {
    login: "/user/login",
    userInfo: "/user",
    editProfile: "/user/edit-profile",
    

    item_by_name:`item/search`,
    subscriptionPlan:"/admin/subscription/plan",
    fetch_category:"/master/category",
    fetchSubCategory:"master/sub-category",
    fetchSubSubCategory:"master/Sub-Sub-category",
    fetchSubSubSubCategory:"master/sub-sub-sub-category",
    getPaymentIntent:"/payment/payment-intent",
    verifyUserData:"user/verify-otp",
    searchISBN:"/books/isbn/{{ISBN}}",
    searchByName:"/books/search?q={search}",
    
    bookMarkItem_id:"/item/bookmark",
    get_bookMark_by_user:"/item/bookmarks",
    getItems: "/item/search/current-user",
    getItemById:"/item",
    createItem:"/item",
    editItem:"/item",
    deleteItemById:"/item",
    itemBySeller:"/item/user",
    addReview:"/item/addReview",
    userReviews:"/item/{:id}/reviews",

    uploadPhoto: "admin/single-upload",
    history:"/payment/history",
    supportRequest: "/user/support-request",
    boostItem:"/item/boost",
    getTestimonials: "/user/testimonials",
    getFaq:"/faqs/filter-search",

    // ad-management
    getAdPlans : "ad-management/getAdPlans",
    getAdPlanById : "ad-management/getAdPlanById",
    
};
const chat={
    chat:"/chat/all",
    chatById:"/chat"

}
// let data = await fetch(USER_ROUTES.LIST_ITEM, {
//             headers: {
//                 "Content-Type": "application/json",
//                 "Authorization": `Bearer ${userToken}`
//             }
//         },)
//         let response = await data.json()
//         // console.log("data getAllBookOfUser", await data.json())
//         if (response.data) {
//             setBookData(response.data)
//         }


export const listItem = async (payload) => {
    let response = await instance
        .post(`${uri.createItem}`,payload)
        .catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response
}

export const getAdPlans = async ({ type, position, page = 1, limit = 10 } = {}) => {
    let response = await instance
        .get(`${uri.getAdPlans}`, {
            params: {
                ...(type && { type }),
                ...(position && { position }),
                page,
                limit
            }
        })
        .catch(axiosErrorHandler);
    return response;
}

export const getAdPlanById = async (id) => {
    let response = await instance
        .get(`${uri.getAdPlanById}/${id}`)
        .catch(axiosErrorHandler);
    return response;
}

export const editItem = async (payload,id) => {
    let response = await instance
        .put(`${uri.editItem}/${id}`,payload)
        .catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response
}

export const addReviewForSeller = async (payload) => {
    let response = await instance
        .post(`${uri.addReview}`,payload)
        .catch(axiosErrorHandler);
    console.log("login test response", response)
    return response
}
export const getMyBooks = async (data,queryString) => {
    let response = await instance
        .post(`${uri.getItems}`+queryString,data)
        .catch(axiosErrorHandler);
    console.log("login test response", response)
    return response
}

export const deleteMyBooks = async (id, data) => {
    let response = await instance
        .put(`${uri.deleteItemById}/${id}`, data)
        .catch(axiosErrorHandler);
    return response
}

export const getBooksById = async (id, userId) => {
    let response = await instance
        .get(`${uri.getItemById}/${id}`, {
            params: { userId }
        })
        .catch(axiosErrorHandler);
    console.log("login test response", response)
    return response
}
export const getItemBySeller = async (id) => {
    
    let response = await instance
        .get(`${uri.itemBySeller}/${id}`)
        .catch(axiosErrorHandler);
    console.log("login test response", response)
    return response
}

export const searchItemByName = async (data ,queryString) => {
    let response = await instance
        .post(`${uri.item_by_name}`+queryString,data)
        .catch(axiosErrorHandler);
    console.log("login test response", response)
    return response
}

export const getsubscriptionPlans = async (text) => {
    let response = await instance
        .get(`${uri.subscriptionPlan}`)
        .catch(axiosErrorHandler);
    console.log("login test response", response)
    return response
}

export const getCategories= async (text) => {
    let response = await instance
        .get(`${uri.fetch_category}`)
        .catch(axiosErrorHandler);
    console.log("login test response", response)
    return response
}

export const getSubCategories= async (text) => {
    let response = await instance
        .get(`${uri.fetchSubCategory}/${text}`)
        .catch(axiosErrorHandler);
    console.log("getSubCategories response", response)
    return response
}

export const getSubSubCategories= async (text) => {
    let response = await instance
        .get(`${uri.fetchSubSubCategory}/${text}`)
        .catch(axiosErrorHandler);
    console.log("getSubCategories response", response)
    return response
}
export const getSubSubSubCategories= async (text) => {
    let response = await instance
        .get(`${uri.fetchSubSubSubCategory}/${text}`)
        .catch(axiosErrorHandler);
    console.log("getSubCategories response", response)
    return response
}

export const getAllChat= async (payloadData) => {
    let response = await instance
        .post(`${chat.chat}`,payloadData)
        .catch(axiosErrorHandler);
    console.log("getSubCategories response", response)
    return response
}


export const getChatById= async (id) => {
    let response = await instance
        .get(`${chat.chatById}/${id}`)
        .catch(axiosErrorHandler);
    console.log("getSubCategories response", response)
    return response
}
export const deleteChatById= async (id) => {
    let response = await instance
        .delete(`${chat.chatById}/${id}`)
        .catch(axiosErrorHandler);
    // console.log("getSubCategories response", response)
    return response
}

export const bookMarkItem=async(data)=>{
    
    let response = await instance
        .post(`${uri.bookMarkItem_id}`,data)
        .catch(axiosErrorHandler);
    // console.log("getSubCategories response", response)
    return response
}

export const getReviewsOfUser=async(id)=>{
    let response = await instance
        .get(`${uri.userReviews}`.replace("{:id}",id))
        // .catch(axiosErrorHandler);
    console.log("getSubCategories response", response)
    return response
}


export const delete_bookMarkItem=async(id)=>{
    let response = await instance
        .delete(`${uri.bookMarkItem_id}/${id}`)
        .catch(axiosErrorHandler);
    console.log("getSubCategories response", response)
    return response
}

export const get_bookMarkItems=async()=>{
    let response = await instance
        .get(`${uri.get_bookMark_by_user}`)
        .catch(axiosErrorHandler);
    console.log("getSubCategories response", response)
    return response
}

export const getPaymentIntent =async(body)=>{
    let response = await instance
        .post(`${uri.getPaymentIntent}`,body)
        .catch(axiosErrorHandler);
    console.log("getSubCategories response", response)
    return response
}

export const verifyUserData =async(body)=>{
    let response = await instance
        .post(`${uri.verifyUserData}`,body)
        .catch(axiosErrorHandler);
    console.log("verifyUserData response", response)
    return response
}

export const searchISBN =async(ISBN)=>{
    let response = await instance
        .get(`${uri.searchISBN}`.replace("{{ISBN}}",ISBN))
        .catch(axiosErrorHandler);
    console.log("verifyUserData response", response)
    return response
}

export const searchByName =async(search)=>{
    let response = await instance
        .get(`${uri.searchByName}`.replace("{search}",search))
        .catch(axiosErrorHandler);
    console.log("verifyUserData response", response)
    return response
}

export const uploadPhotoSingle =async(data)=>{
    let response = await instance
        .post(`${uri.uploadPhoto}`,data)
        .catch(axiosErrorHandler);
    console.log("verifyUserData response", response)
    return response
}

export const paymentHistory =async(data)=>{
    let response = await instance
        .get(`${uri.history}`,data)
        .catch(axiosErrorHandler);
    return response
}

export const supportRequest =async(data)=>{
    let response = await instance
        .post(`${uri.supportRequest}`,data)
        .catch(axiosErrorHandler);
    console.log("verifyUserData response", response)
    return response
}


export const boostItem =async(id)=>{
    let response = await instance
        .put(`${uri.boostItem}/`+id)
        .catch(axiosErrorHandler);
    return response
}

export const getTestimonials =async(id)=>{
    let response = await instance
        .get(`${uri.getTestimonials}`)
        .catch(axiosErrorHandler);
    return response
}

export const getFaq =async(data)=>{
    let response = await instance
        .post(`${uri.getFaq}`)
        .catch(axiosErrorHandler);
    return response
}



// let data = await fetch(USER_ROUTES.SEARCH_ITEM_BY_NAME.replace("itemName", text), {
//                 headers: {
//                     "Content-Type": "application/json",
//                     "Authorization": `Bearer ${userToken}`
//                 },
//             },)
//             let response = await data.json()
//             // console.log("data getAllBookOfUser", await data.json())
//             if (response.data) {
//                 setBookData(response.data)
//             }
// export const login = async (payload, guestId) => {
//     let response = await instance
//         .post(`${uri.login}`,payload)
//         .catch(axiosErrorHandler);
//         console.log("login test response",response)
//         return response
// };
