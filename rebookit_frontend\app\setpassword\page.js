"use client"

import { useEffect } from 'react'
import { useForm } from "react-hook-form"
import signUpCss from "./setpassword.module.scss"

import dummyImage from "@/public/test.jpeg"


const CustomSlider = dynamic(() => import('@/app/components/common/Slider'));
const BuyAndSellComponent = dynamic(() => import('@/app/components/about/BuyAndSellComponent'));




import dynamic from 'next/dynamic'
import Image from "next/image"
import Link from "next/link"
import { useRouter } from 'next/navigation'
import { useState } from "react"
import { FaRegEyeSlash } from "react-icons/fa"
import { IoEyeSharp } from "react-icons/io5"
import { toast } from 'react-toastify'
import { USER_ROUTES } from '../config/api'
import { clearLocalStorge, getToken, setToken } from '../utils/utils'
import { registerUser, resetPassword } from '../services/auth'
import { useDispatch, useSelector } from 'react-redux'
import { changeforgetPasswordData, setVerificationData } from '../redux/slices/storeSlice'
import { persistor } from '../redux/store'




export default function SignUpComponent() {
    const router = useRouter()


    const {
        register,
        watch,
        formState: { errors },
        getValues,
        handleSubmit,
        setValue,
        reset,
        setError,


    } = useForm()

    const Images = [dummyImage, dummyImage, dummyImage]

    const [passwordView, setPasswordView] = useState(false)
    const [confirm_passwordView, setconfirm_passwordView] = useState(false)
    const [location, setLocation] = useState(null);
    const storeData = useSelector(state => state.storeData)
    const dispatch = useDispatch()
    console.log("storeData", storeData)
    const handleGetLocation = () => {
        if (!navigator.geolocation) {
            alert("Geolocation is not supported by your browser.");
            return;
        }

        navigator.geolocation.getCurrentPosition(
            (position) => {
                setLocation({
                    lat: position.coords.latitude,
                    lng: position.coords.longitude,
                });
            },
            (err) => {

                if (err.code === err.PERMISSION_DENIED) {
                    // alert("Location access was blocked. Please enable it in your browser settings.");
                    console.log("err", err)
                } else {
                    // alert("Unable to retrieve location. Please try again.");
                    console.log("err", err)
                }
            }
        );
    };


    useEffect(() => {
        const token = getToken();
        if (token) {
            router.replace('/profile');
        }
    }, []);


    useEffect(() => handleGetLocation(), [])

    const onSubmit = async (data) => {

        console.log('Form submitted:', data);
        handleGetLocation();

        // let passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
        // if (!passwordRegex.test(data.password)) {
        //     toast.error("In password Need 1 upper case 1 speacial Character and 1 number atleast")
        //     return
        // }

        if (!location) {
            // console.log("location blocked")
            // alert("Please allow location to continue.")
            // return;
            setLocation({
                lat: 0,
                lng: 0,
            });

        }

        try {
            let payload = {
                "email": storeData.forgetPasswordData.email,
                "otp": storeData.forgetPasswordData.otp,
                "newPassword": watch().password
            }

           let resetResponse= await resetPassword(payload)
           console.log("resetResponse",resetResponse)
           if(resetResponse.status==200){
                if(resetResponse.data.success){
                    toast.success("Password Is Reset, Please Login")
                    router.push("/login")
                    dispatch(changeforgetPasswordData({}))
                    clearLocalStorge()
                    persistor.purge()
                    setValue("password","")
                }
           }
        } catch (error) {
            console.log("error", error)
        }

    };


    const settings = {
        dots: true,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 3000
    };

    console.log("errors", errors)

    return (
        <section className={`${signUpCss.signupContainer}`}>

            <section className='container-wrapper w-full'>

                <section className="md:flex flex-col lg:flex-row lg:justify-between justify-center xs:items-center">

                    <section className="lg:w-1/2 lg:min-w-[600px] lg:max-w-[700px]">
                        <div className={`${signUpCss.imageContainer}`}>
                            {/* <LoginSlider ImageSlider={Images} /> */}

                            <CustomSlider
                                sliderSettings={settings}
                            >
                                {
                                    Images.map((image, idx) =>
                                        <div className={`relative overflow-hidden ${signUpCss.imageElem}`}>
                                            <Image key={idx} src={image} alt="login page images" className="h-full w-full rounded-2xl p-1" />
                                        </div>
                                    )
                                }
                            </CustomSlider>
                        </div>

                    </section>



                    <section className="md:w-5/12 md:mt-20 md:min-w-[600px]">

                        <h2 className="text-[18px] font-semibold md:text-[40px] md:font-semibold">Set A Password</h2>
                        <p className="mt-3 font-extralight text-[14px] md:text-[14px] w-10/12">Your previous password has been reseted. Please set a new password for your account.</p>


                        <form onSubmit={handleSubmit(onSubmit)} className="text-[#1C1B1F] my-4">
                            {/* FirstName Input */}

                            {/* Password Input */}
                            <div className="relative h-[90px] md:h-[100px]">
                                <fieldset className={` ${signUpCss.fields}`}>
                                    <legend className="text-[14px] font-medium px-[7px]">Create Password</legend>
                                    <label htmlFor="password" className="sr-only">Create Password</label>
                                    <input
                                        id="password"
                                        type={passwordView ? "text" : "password"}
                                        autoComplete="off"
                                        {...register('password', {
                                            required: 'Valid Password is Required',
                                            maxLength: { value: 70, message: "Maximum 70 letter allowed" }

                                        })}
                                        className="w-[90%]  text-[13px] md:text-[17px] outline-none "
                                        aria-invalid={!!errors.password}
                                        aria-describedby="password-error"
                                    />
                                    <div
                                        className="absolute top-[29px] right-[17px]"
                                        onClick={() => setPasswordView(!passwordView)}
                                        role="button"
                                        tabIndex={0}
                                        aria-label={passwordView ? "Hide password" : "Show password"}
                                        onKeyDown={(e) => e.key === 'Enter' && setPasswordView(!passwordView)}
                                    >
                                        {passwordView ? (
                                            <FaRegEyeSlash className="cursor-pointer" size={20} />
                                        ) : (
                                            <IoEyeSharp className="cursor-pointer" size={20} />
                                        )}
                                    </div>
                                </fieldset>
                                {errors.password && (
                                    <p id="password-error" className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0">
                                        {errors.password.message}
                                    </p>
                                )}
                            </div>

                            <div className="relative h-[90px] md:h-[100px]">
                                <fieldset className={` ${signUpCss.fields}`}>
                                    <legend className="text-[14px] font-medium px-[7px]">Re-Enter Password</legend>
                                    <label htmlFor="password" className="sr-only">Re-Enter Password</label>
                                    <input
                                        id="confirm-password"
                                        type={confirm_passwordView ? "text" : "password"}
                                        autoComplete="off"
                                        {...register('Confirm_Password', {
                                            required: 'Valid Password is Required',
                                            validate: (value) =>
                                                value === watch("password") || "Passwords do not match"
                                        })}
                                        className="w-[90%] text-[13px] md:text-[17px] outline-none border-none"
                                        aria-invalid={!!errors.Confirm_Password}
                                        aria-describedby="password-error"
                                    />
                                    <div
                                        className="absolute top-[29px] right-[17px]"
                                        onClick={() => setconfirm_passwordView(!confirm_passwordView)}
                                        role="button"
                                        tabIndex={0}
                                        aria-label={confirm_passwordView ? "Hide password" : "Show password"}
                                        onKeyDown={(e) => e.key === 'Enter' && setconfirm_passwordView(!confirm_passwordView)}
                                    >
                                        {confirm_passwordView ? (
                                            <FaRegEyeSlash className="cursor-pointer" size={20} />
                                        ) : (
                                            <IoEyeSharp className="cursor-pointer" size={20} />
                                        )}
                                    </div>
                                </fieldset>
                                {errors.Confirm_Password && (
                                    <p id="password-error" className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0">
                                        {errors.Confirm_Password.message}
                                    </p>
                                )}
                            </div>



                            {/* Submit Button */}
                            <button type="submit" className={`${signUpCss.submitButton}`}>
                                Set Password
                            </button>


                        </form>


                    </section>

                </section>

                <section className='my-5 md:my-0 md:w-12/12 '>
                    <BuyAndSellComponent />
                </section>
            </section>


        </section>
    )
}
