"use client";

import { useEffect } from "react";
import { useForm } from "react-hook-form";
import signUpCss from "./signup.module.scss";

import dummyImage from "@/public/test.jpeg";
import login1 from "@/public/images/login1.png";
import login2 from "@/public/images/login2.png";

const CustomSlider = dynamic(() => import("@/app/components/common/Slider"));
const BuyAndSellComponent = dynamic(() =>
  import("@/app/components/about/BuyAndSellComponent")
);

import dynamic from "next/dynamic";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { FaRegEyeSlash } from "react-icons/fa";
import { IoEyeSharp } from "react-icons/io5";
import { toast } from "react-toastify";
import { USER_ROUTES } from "../config/api";
import { getToken, setInLocalStorage, setToken } from "../utils/utils";
import { registerUser } from "../services/auth";
import { useDispatch, useSelector } from "react-redux";
import { setVerificationData } from "../redux/slices/storeSlice";
import SubmitButton from "../components/common/SubmitButton";

export default function SignUpComponent() {
  const router = useRouter();

  const {
    register,
    watch,
    formState: { errors },
    getValues,
    handleSubmit,
    setValue,
    reset,
    setError,
  } = useForm();

  const Images = [login1, login2];

  const [passwordView, setPasswordView] = useState(false);
  const [confirm_passwordView, setconfirm_passwordView] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [location, setLocation] = useState(null);
  const storeData = useSelector((state) => state.storeData);
  const dispatch = useDispatch();
  console.log("storeData", storeData);
  const handleGetLocation = () => {
    if (!navigator.geolocation) {
      alert("Geolocation is not supported by your browser.");
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        setLocation({
          lat: position.coords.latitude,
          lng: position.coords.longitude,
        });
      },
      (err) => {
        if (err.code === err.PERMISSION_DENIED) {
          // alert("Location access was blocked. Please enable it in your browser settings.");
          console.log("err", err);
        } else {
          // alert("Unable to retrieve location. Please try again.");
          console.log("err", err);
        }
      }
    );
  };

  useEffect(() => {
    const token = getToken();
    if (token) {
      router.replace("/profile");
    }
  }, []);

  useEffect(() => handleGetLocation(), []);

  const onSubmit = async (data) => {
      debugger
    console.log("Form submitted:", data);
    handleGetLocation();

    // let passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/
    // if (!passwordRegex.test(data.password)) {
    //     toast.error("In password Need 1 upper case 1 speacial Character and 1 number atleast")
    //     return
    // }

    if (!location) {
      // console.log("location blocked")
      // alert("Please allow location to continue.")
      // return;
      setLocation({
        lat: 0,
        lng: 0,
      });
    }

    try {
      const payload = {
        ...data,
        email: storeData.verficationData.email,
        location: {
          type: "Point",
          coordinates: Object.values(location),
        },
      };

      delete payload.Confirm_Password;
      delete payload.agreed;
      let authData = await registerUser(payload);
      console.log("authData", authData);
      if (authData.status == 200) {
        console.log("authData?.token", authData?.token);
        setToken(authData?.data.token);
        toast.success("Signed Up successfully!");
        setInLocalStorage("userData", authData?.data.user);
        dispatch(setVerificationData({}));
        router.push("/");
      } else {
        Array.isArray(authData.message)
          ? authData.message?.map((x) => toast.error(x))
          : toast.error(
            authData.message || "Error signing up! Please try again"
          );
      }
    } catch (error) {
      console.log("error", error);
    }
  };

  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
  };

  console.log("errors", errors);

  return (
    <section className={`${signUpCss.signupContainer}`}>
      <section className="container-wrapper w-full">
        <section className="md:flex flex-col lg:flex-row lg:justify-between justify-center xs:items-center">
          <section className="lg:w-1/2 lg:min-w-[600px] lg:max-w-[700px]">
            <div className={`${signUpCss.imageContainer}`}>
              {/* <LoginSlider ImageSlider={Images} /> */}

              <CustomSlider sliderSettings={settings}>
                {Images.map((image, idx) => (
                  <div
                    className={`relative overflow-hidden ${signUpCss.imageElem}`}
                  >
                    <Image
                      key={idx}
                      src={image}
                      alt="login page images"
                      className="h-full w-full rounded-2xl p-1"
                    />
                  </div>
                ))}
              </CustomSlider>
            </div>
          </section>

          <section className="md:w-5/12 md:mt-20 md:min-w-[600px]">
            <h2 className="text-[18px] font-semibold md:text-[40px] md:font-semibold">
              Sign Up
            </h2>
            <p className="mt-3 font-extralight text-[14px] md:text-[14px] w-10/12">
              Let’s get you all set up so you can access your personal account.
            </p>

            <form
              onSubmit={handleSubmit(onSubmit)}
              className="text-[#1C1B1F] my-4"
            >
              {/* FirstName Input */}
              <div className="md:flex justify-between items-center">
                <div className="relative py-2 h-[90px] md:h-[100px] md:w-[47%]">
                  <fieldset className={` ${signUpCss.fields}`}>
                    <legend className="text-[14px] font-medium px-[7px]">
                      FirstName
                    </legend>
                    <label htmlFor="firstName" className="sr-only">
                      FirstName
                    </label>
                    <input
                      id="firstName"
                      type="text"
                      autoComplete="off"
                      {...register("firstName", {
                        required: "firstName is required",
                        maxLength: {
                          value: 70,
                          message: "Maximum 70 letter Allowed",
                        },
                      })}
                      className="w-full text-[13px] md:text-[17px] outline-none border-none"
                      aria-invalid={!!errors?.firstName}
                      aria-describedby="email-error"
                    />
                  </fieldset>

                  {errors?.firstName && (
                    <p
                      id="email-error"
                      className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0"
                    >
                      {errors?.firstName.message}
                    </p>
                  )}
                </div>

                <div className="relative py-2 h-[90px] md:h-[100px] md:w-[47%]">
                  <fieldset className={` ${signUpCss.fields}`}>
                    <legend className="text-[14px] font-medium px-[7px]">
                      LastName
                    </legend>
                    <label htmlFor="lastName" className="sr-only">
                      LastName
                    </label>
                    <input
                      id="lastName"
                      type="text"
                      autoComplete="off"
                      {...register("lastName", {
                        required: "lastName is required",
                        maxLength: {
                          value: 70,
                          message: "Maximum 70 letter allowed",
                        },
                      })}
                      className="w-full text-[13px] md:text-[17px] outline-none border-none"
                      aria-invalid={!!errors.lastName}
                    // aria-describedby="email-error"
                    />
                  </fieldset>
                  {errors.lastName && (
                    <p
                      id="email-error"
                      className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0"
                    >
                      {errors.lastName.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Password Input */}
              <div className="relative h-[90px] md:h-[100px]">
                <fieldset className={` ${signUpCss.fields}`}>
                  <legend className="text-[14px] font-medium px-[7px]">
                    Password
                  </legend>
                  <label htmlFor="password" className="sr-only">
                    Password
                  </label>
                  <input
                    id="password"
                    type={passwordView ? "text" : "password"}
                    autoComplete="off"
                    // autoComplete="current-password"
                    {...register("password", {
                      required: "Valid Password is Required",
                      maxLength: {
                        value: 70,
                        message: "Maximum 70 letter allowed",
                      },
                    })}
                    className="w-[90%]  text-[13px] md:text-[17px] outline-none "
                    aria-invalid={!!errors.password}
                    aria-describedby="password-error"
                  />
                  <div
                    className="absolute top-[29px] right-[17px]"
                    onClick={() => setPasswordView(!passwordView)}
                    role="button"
                    tabIndex={0}
                    aria-label={
                      passwordView ? "Hide password" : "Show password"
                    }
                    onKeyDown={(e) =>
                      e.key === "Enter" && setPasswordView(!passwordView)
                    }
                  >
                    {passwordView ? (
                      <FaRegEyeSlash className="cursor-pointer" size={20} />
                    ) : (
                      <IoEyeSharp className="cursor-pointer" size={20} />
                    )}
                  </div>
                </fieldset>
                {errors.password && (
                  <p
                    id="password-error"
                    className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0"
                  >
                    {errors.password.message}
                  </p>
                )}
              </div>

              <div className="relative h-[90px] md:h-[100px]">
                <fieldset className={` ${signUpCss.fields}`}>
                  <legend className="text-[14px] font-medium px-[7px]">
                    Confirm Password
                  </legend>
                  <label htmlFor="password" className="sr-only">
                    Confirm Password
                  </label>
                  <input
                    id="confirm-password"
                    type={confirm_passwordView ? "text" : "password"}
                    autoComplete="off"
                    {...register("Confirm_Password", {
                      required: "Valid Password is Required",
                      validate: (value) =>
                        value === watch("password") || "Passwords do not match",
                    })}
                    className="w-[90%] text-[13px] md:text-[17px] outline-none border-none"
                    aria-invalid={!!errors.Confirm_Password}
                    aria-describedby="password-error"
                  />
                  <div
                    className="absolute top-[29px] right-[17px]"
                    onClick={() =>
                      setconfirm_passwordView(!confirm_passwordView)
                    }
                    role="button"
                    tabIndex={0}
                    aria-label={
                      confirm_passwordView ? "Hide password" : "Show password"
                    }
                    onKeyDown={(e) =>
                      e.key === "Enter" &&
                      setconfirm_passwordView(!confirm_passwordView)
                    }
                  >
                    {confirm_passwordView ? (
                      <FaRegEyeSlash className="cursor-pointer" size={20} />
                    ) : (
                      <IoEyeSharp className="cursor-pointer" size={20} />
                    )}
                  </div>
                </fieldset>
                {errors.Confirm_Password && (
                  <p
                    id="password-error"
                    className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0"
                  >
                    {errors.Confirm_Password.message}
                  </p>
                )}
              </div>

              {/* Remember Me + Forgot Password */}
              <div className="mb-4 flex items-center justify-between relative">
                <div className="mb-4 flex items-center">
                  <input
                    id="agree"
                    type="checkbox"
                    autoComplete="off"
                    className="mr-2 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                    {...register("agreed", {
                      required: "Agree to the terms and policies",
                    })}
                  />
                  <label className="text-[12px] md:text-[16px] text-gray-700">
                    I agree to all the{" "}
                    <span className="text-[#FF8682]">Terms</span> and{" "}
                    <a
                      href="/privacy-policy"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-[#FF8682] cursor-pointer"
                    >   <span className="text-[#FF8682] cursor-pointer" >Privacy Policies</span></a>
                  </label>
                  {errors.agreed && (
                    <p
                      id="password-error"
                      className="text-red-500 text-[11px] mt-1 absolute left-0 bottom-0"
                    >
                      {errors.agreed.message}
                    </p>
                  )}
                </div>
              </div>

              {/* Submit Button */}
              {/* <button
                isLoading={isLoading}
                type="submit"
                className={`${signUpCss.submitButton}`}
              >
                Create Account
              </button> */}

              <SubmitButton
                isLoading={isLoading}
                type="submit"
                btnAction={null} // because submit handled by react-hook-form
                InnerDiv={() => (
                  <span className={`${signUpCss.submitButton}`}>
                    Create Account
                  </span>
                )}
              />

              {/* Sign up prompt */}
              <p className="text-center text-[12px] md:text-[16px] my-3">
                Already have an account?{" "}
                <Link href="/login">
                  <span className="text-[#FF8682] font-medium cursor-pointer">
                    Login
                  </span>
                </Link>
              </p>
            </form>
          </section>
        </section>

        <section className="my-5 md:my-0 md:w-12/12 ">
          <BuyAndSellComponent />
        </section>
      </section>
    </section>
  );
}
