"use client";

import { io } from "socket.io-client";
import { getToken } from "./utils/utils";

export const socket = io(process.env.NEXT_PUBLIC_BASE_URL, {
    extraHeaders: {
        authorization: `<PERSON><PERSON> ${getToken()}`
    },
    // auth: {
    //     token: `Bear<PERSON> ${getToken()}`
    // },
    // transports: ['websocket'],
    // autoConnect: false,
    // reconnection: true,
    // reconnectionAttempts: 5,
    // reconnectionDelay: 1000,
})

socket.on("connect", () => {
    console.log("Connected to socket server:", socket.id);
});

socket.on("connect_error", (err) => {
    console.log("connecction err",err)
    console.error("Socket connection error:", err.message);
});
