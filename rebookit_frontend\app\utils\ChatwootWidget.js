export function initChatwoot(websiteToken, userData) {
  if (typeof window === "undefined" || window.chatwootSDK) return;

  window.chatwootSettings = {
    hideMessageBubble: false,
    position: "right",
    locale: "en",
    type: "expanded_bubble",
    launcherTitle: "Chat with us",
  };

  (function (d, t) {
    var BASE_URL = "https://app.chatwoot.com";
    var g = d.createElement(t),
      s = d.getElementsByTagName(t)[0];
    g.src = BASE_URL + "/packs/js/sdk.js";
    g.async = true;
    s.parentNode.insertBefore(g, s);
    g.onload = function () {
      window.chatwootSDK.run({
        websiteToken,
        baseUrl: BASE_URL,
      });
      // Attach listener to send user data
      window.addEventListener("chatwoot:ready", function () {
        if (window.$chatwoot && userData?._id) {
          window.$chatwoot.setUser(userData._id, {
            email: userData.email,
            name: userData.firstName,
            avatar_url: userData.profileImage,
            phone_number: userData.mobileNumber,
            // add other fields if required
          });
        }
      });
    };
  })(document, "script");
}

export function destroyChatwoot() {
  if (typeof window === "undefined") return;

  // Remove Chatwoot widget container if it exists
  const widgetContainer = document.getElementById("chatwoot-widget-container");
  if (widgetContainer) widgetContainer.remove();

  const widgetHolder = document.getElementById("cw-widget-holder");
  if (widgetHolder) widgetHolder.remove();

  // Remove any Chatwoot iframes that might linger
  const iframes = document.querySelectorAll("iframe");
  iframes.forEach((iframe) => {
    if (iframe.src && iframe.src.includes("chatwoot")) {
      iframe.remove();
    }
  });

  // Remove the Chatwoot SDK script
  const scripts = document.querySelectorAll('script[src*="chatwoot"]');
  scripts.forEach((script) => script.remove());

  // Remove Chatwoot widget bubbles (fail-safe)
  const bubbles = document.getElementsByClassName("woot-widget-bubble");
  while (bubbles.length > 0) {
    bubbles[0].parentNode.removeChild(bubbles[0]);
  }

  // Remove any references on window (globals)
  if (window.$chatwoot) delete window.$chatwoot;
  if (window.chatwootSDK) delete window.chatwootSDK;
  if (window.chatwootSettings) delete window.chatwootSettings;
}
