import { toast } from "react-toastify";
import { ItemListStatusEnum, itemToKind } from "../config/constant";


export const setToken = (token) => {
    if (typeof window !== "undefined") {
        localStorage.setItem('token', token);
    }
};

export const getToken = () => {
    if (typeof window !== "undefined") {
        return localStorage.getItem('token');
    }
    return null;
};

export const removeToken = () => {
    if (typeof window !== "undefined") {
        return localStorage.removeItem("token");
    }
};

export const clearLocalStorge = () => {
    if (typeof window !== "undefined") {
        localStorage.clear();
    }
};

export const userDataFromLocal = () => {
    if (typeof window !== "undefined") {
        return JSON.parse(localStorage.getItem("userData"));
    }
    return null;
};
export const globleBookmarkFunc = (userId, func) => {
    if (typeof window !== "undefined") {
        let userData = JSON.parse(localStorage.getItem("userData"))
        if (userData._id == userId) {
            toast.error("You can not boomark your book")
        } else {
            func()
        }
    }
    return null;
}

export const setInLocalStorage = (name,data) => {
    if (typeof window !== "undefined") {
        return localStorage.setItem(name, JSON.stringify(data))
    }
    return null;
};


export const editItemPayload = (bookState) => {

    let payloadToSet = {
        currentStep: 2,
        completedStep: 1,
        isEdit: true,
        editItemId: bookState._id,
        kind: bookState.__t,
        subSubCategories: bookState.subSubCategoryId,
        subSubSubCategories: bookState.subSubSubCategoryId,
        category: { _id: bookState.categoryId._id, name: bookState.categoryId.name || "" },
        subCategory: { _id: bookState?.subCategoryId?._id || "", name: bookState?.subCategoryId?.name || "" },
        listData: {
            tags: bookState.tags || [],
            ISBN: bookState.isbn_number || "",
            title: bookState.title || "",
            desciption: bookState.description || "",
            bookAuthor: bookState?.authors?.length ? bookState?.authors[0] : "",
            price: bookState.price || "",
            bookCondition: bookState.bookCondition || "",
            quantity: bookState.quantity || 0,
            address: bookState.address || {},
            OTP: "",
            isVerified: false,
            bookCondition: bookState.condition,
            quantity: bookState.qty,
            bookImages: bookState.images,
            [itemToKind.EventItem.eventEndDate]: bookState.eventEndDate,
            [itemToKind.EventItem.eventStartDate]: bookState.eventStartDate,
            [itemToKind.EventItem.eventMode]: bookState.eventMode,
            [itemToKind.EventItem.website]: bookState.eventMode,
            [itemToKind.SchoolItem.classesOffered]: bookState.classesOffered,
            [itemToKind.SchoolItem.schoolType]: bookState.schoolType,
            [itemToKind.SchoolItem.website]: bookState.website,
            [itemToKind.TutorItem.experience]: bookState.experience,
            [itemToKind.TutorItem.highestQualification]: bookState.highestQualification,
            [itemToKind.TutorItem.targetClasses]: bookState.targetClasses,
            [itemToKind.ExtracurricularActivityItem.activityType]: bookState.activityType,
            [itemToKind.ExtracurricularActivityItem.frequency]: bookState.frequency,
            [itemToKind.ExtracurricularActivityItem.targetStudents]: bookState.targetStudents,
            [itemToKind.ScholarshipAwardItem.eligibilityCriteria]:bookState.eligibilityCriteria,
            [itemToKind.ScholarshipAwardItem.scholarshipType]:bookState.scholarshipType
            
        }
    }
    return payloadToSet
}
// export const Debounce = (fn, timeout = 400) => {
//     let timer;

//     return (...args) => {
//         clearTimeout(timer);
//         timer = setTimeout(() => {
//             fn(...args);
//         }, timeout);
//     };
// };


export function Debounce(fn, delay) {
    let timeoutId;

    const debounced = (...args) => {
        if (timeoutId) clearTimeout(timeoutId);

        timeoutId = setTimeout(() => {
            fn(...args);
        }, delay);
    };

    debounced.cancel = () => {
        if (timeoutId) clearTimeout(timeoutId);
    };

    return debounced;
}


export const Throttle = (fn, limit = 300) => {
    let lastCall = 0;

    return (...args) => {
        const now = Date.now();

        if (now - lastCall >= limit) {
            lastCall = now;
            fn(...args);
        }
    };
};

// format date into time format, ex -> 04:45 PM
export const formatTo12HourTime = (isoString) => {
    const date = new Date(isoString);
    const options = {
        hour: '2-digit',
        minute: '2-digit',
        hour12: true,
    };
    return date.toLocaleTimeString('en-US', options);
}

// format date into string and x days ago format, ex-> today, yesterday, x days ago and date[DD-MM-YY]
export function formatRelativeDate(dateString) {
    const inputDate = new Date(dateString);
    const now = new Date();

    // Strip time from both dates for accurate day difference
    const dateOnly = new Date(inputDate.getFullYear(), inputDate.getMonth(), inputDate.getDate());
    const nowOnly = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    const diffTime = nowOnly - dateOnly;
    const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 0) return "Today";
    if (diffDays === 1) return "Yesterday";
    if (diffDays <= 30) return `${diffDays} days ago`;

    // Format as dd-mm-yy
    const day = String(inputDate.getDate()).padStart(2, "0");
    const month = String(inputDate.getMonth() + 1).padStart(2, "0");
    const year = String(inputDate.getFullYear()).slice(-2);
    return `${day}-${month}-${year}`;
}

export function RedirectToLoginIfNot(redirectRouteAfterLogin, router) {

    let token = getToken()
    if (token) {
        router.push(redirectRouteAfterLogin)
    } else {
        router.push(`/login?redirect=${redirectRouteAfterLogin}`)
    }
}

export function isWithin10Km(lat1, lon1, lat2, lon2) {
    console.log("lat long", lat1, lon1, lat2, lon2)
    const toRad = (value) => (value * Math.PI) / 180;

    const R = 6371; // Radius of the Earth in km
    const dLat = toRad(lat2 - lat1);
    const dLon = toRad(lon2 - lon1);

    const a =
        Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(toRad(lat1)) *
        Math.cos(toRad(lat2)) *
        Math.sin(dLon / 2) *
        Math.sin(dLon / 2);

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    console.log("rse", R, "cse", c, "distance", distance)
    return distance <= 10;
}

export function DecideItemAction(state) {
    let token = getToken()
    let actionPayload = {
        edit: false,
        boost: false,
        mark_as_sold: false,
        remove: false,
        loggedIn: false
    }
    if (token) {
        actionPayload.loggedIn = true
    }
    switch (state) {
        case ItemListStatusEnum.PENDING:
            actionPayload.edit = true
            actionPayload.boost = false
            actionPayload.remove = true
            actionPayload.mark_as_sold = false
            break;
        case ItemListStatusEnum.ACCEPTED:
            actionPayload.edit = true
            actionPayload.boost = true
            actionPayload.remove = true
            actionPayload.mark_as_sold = true
            break;
        case ItemListStatusEnum.REJECTED:
            actionPayload.edit = true
            actionPayload.boost = false
            actionPayload.remove = true
            actionPayload.mark_as_sold = false
            break;
        case ItemListStatusEnum.MARKED_AS_SOLD:
            actionPayload.edit = false
            actionPayload.boost = false
            actionPayload.remove = true
            actionPayload.mark_as_sold = false
            break;
        default:
            actionPayload.edit = false
            actionPayload.boost = false
            actionPayload.remove = true
            actionPayload.mark_as_sold = false
            break;
    }
    return actionPayload
}



export const getCurrentLocationAndAddress = async (apiKey) => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject("Geolocation is not supported by your browser.");
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        const { latitude, longitude } = position.coords;

        try {
          const response = await fetch(
            `https://maps.googleapis.com/maps/api/geocode/json?latlng=${latitude},${longitude}&key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_NEW_API_KEY}`
          );
          
          const data = await response.json();
        //   consolelog("data location",data)
          if (data.status === "OK" && data.results.length > 0) {
            resolve({
              lat: latitude,
              lng: longitude,
              address: data.results[0].formatted_address,
            });
          } else {
            reject("No address found for your location.");
          }
        } catch (error) {
          reject("Error fetching address: " + error);
        }
      },
      (error) => {
        reject("Error getting geolocation: " + error.message);
      }
    );
  });
};
