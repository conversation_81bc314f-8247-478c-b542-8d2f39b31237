"use client"
import { useForm } from "react-hook-form"
import dynamic from "next/dynamic";
import verifyCodeCss from "./verify-code.module.scss"


// components 
const CustomSlider = dynamic(() => import('@/app/components/common/Slider'));
const BuyAndSellComponent = dynamic(() => import('@/app/components/about/BuyAndSellComponent'));




import dummyImage from "@/public/test.jpeg"

import Link from "next/link";
import Image from "next/image";


import { RiArrowLeftSLine } from "react-icons/ri";
import { USER_ROUTES } from "../config/api";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "react-toastify";
import { useDispatch } from "react-redux";
import { setOtp } from "@/app/redux/slices/storeSlice";
import { verifyOtp } from "../services/auth";



export default function LoginPage() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const email = searchParams.get("email");
    const dispatch = useDispatch();
    const {
        register,
        watch,
        formState: { errors },
        getValues,
        handleSubmit,
        setValue,
        reset,
        setError,


    } = useForm()

    const Images = [dummyImage, dummyImage, dummyImage]


    const onSubmit = async (data) => {
        console.log('Form submitted:', data);
        try {
            const payload = {
                ...data,
                email: email,
                verifyType: "forgot_pass"
            }

            // api call 
            let verifyresponse = await verifyOtp()
            if (verifyresponse.status == 200) {
                dispatch(setOtp({ value: data.otp }))
                toast.success("Email Verified")
                router.push("/create-password")
            } else {
                toast.error(response.message || "Wrong Otp")
            }

            // fetch(USER_ROUTES.VERIFY_OTP, {
            //     method: "POST",
            //     headers: {
            //         "Content-Type": "application/json"
            //     },
            //     body: JSON.stringify(payload)
            // }).then(async res => {
            //     const response = await res.json();

            //     if (!response.error) {
            //         dispatch(setOtp({ value: data.otp }))
            //         toast.success("Email Verified")
            //         router.push("/create-password")
            //     } else {
            //         toast.error(response.message || "Wrong Otp")
            //     }

            // })
        } catch (error) {
            console.log("error", error)
        }

    };



    const handleResendCode = async () => {

    }


    const settings = {
        dots: true,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 3000
    };

    return (
        <div className={`${verifyCodeCss.loginContainer}`}>

            <section className="container-wrapper md:flex md:flex-wrap md:justify-between justify-center xs:items-center w-full">


                <Link href={"/login"} className="block md:hidden">  <h2 className="flex justify-start items-center text-[#313131]"> <RiArrowLeftSLine size={35} className="mr-2" /> Back to Login</h2> </Link>

                <section className="md:w-6/12 md:min-w-[600px] md:max-w-[700px]">
                    <div className={`${verifyCodeCss.imageContainer}`}>
                        {/* <LoginSlider ImageSlider={Images} /> */}

                        <CustomSlider
                            sliderSettings={settings}
                        >
                            {
                                Images.map((image, idx) =>
                                    <Image key={idx} src={image} alt="login page images" className="h-full w-full rounded-2xl p-1" />
                                )
                            }
                        </CustomSlider>
                    </div>

                </section>


                <section className="md:w-5/12 my-5 md:mt-20  md:min-w-[600px]">

                    <Link href={"/login"} className="hidden md:block md:ml-[-10px]">  <h2 className="flex justify-start items-center text-[#313131]"> <RiArrowLeftSLine size={35} className="mr-2" /> Back to Login</h2> </Link>

                    <h2 className="text-[18px] font-semibold md:text-[40px] md:font-semibold">Verify Code</h2>
                    <p className="mt-3 font-extralight text-[14px] md:text-[18px] md:w-10/12">An authentication code has been sent to your email.</p>


                    <form onSubmit={handleSubmit(onSubmit)} className="text-[#1C1B1F] my-5">
                        {/* Email Input */}
                        <div className="relative py-2 h-[90px] md:h-[90px]">
                            <fieldset className={` ${verifyCodeCss.fields}`}>
                                <legend className="text-[14px] font-medium px-[7px]">Enter Code</legend>
                                <label htmlFor="email" className="sr-only">Enter Code</label>
                                <input
                                    id="Enter Code"
                                    type="number"
                                    autoComplete="off"
                                    {...register('otp', {
                                        required: 'Code is required',

                                    })}
                                    className="w-full text-[13px] md:text-[17px] outline-none border-none"
                                    aria-invalid={!!errors.otp}
                                    aria-describedby="email-error"
                                />
                            </fieldset>
                            {errors.otp && (
                                <p id="email-error" className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0">
                                    {errors.otp.message}
                                </p>
                            )}
                        </div>

                        <p className="text-left text-[12px] md:text-[14px] my-3">
                            Didn’t receive a code? {' '}

                            <span className="text-[#FF8682] font-medium cursor-pointer" onClick={handleResendCode}>Resend</span>

                        </p>

                        {/* Submit Button */}
                        <button type="submit" className={`${verifyCodeCss.submitButton} my-2 md:my-5`}>
                            Verify
                        </button>

                    </form>


                </section>
            </section>
            <section className='my-16 md:my-0 md:w-12/12'>
                <BuyAndSellComponent />
            </section>


        </div>
    )
}
