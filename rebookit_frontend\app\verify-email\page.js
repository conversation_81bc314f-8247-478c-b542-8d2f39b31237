"use client";
import {useForm} from "react-hook-form";
import dynamic from "next/dynamic";
import loginCss from "./login.module.scss";

// components
const CustomSlider = dynamic(() => import("@/app/components/common/Slider"));
const BuyAndSellComponent = dynamic(() =>
  import("@/app/components/about/BuyAndSellComponent")
);

// import dummyImage from "@/public/test.jpeg";
import login1 from "@/public/images/login1.png";
import login2 from "@/public/images/login2.png";

import {IoEyeSharp} from "react-icons/io5";
import {FaRegEyeSlash} from "react-icons/fa";
import {useEffect, useState} from "react";
import Link from "next/link";
import Image from "next/image";
import {USER_ROUTES} from "../config/api";
import {useRouter} from "next/navigation";
import {toast} from "react-toastify";
import {getToken, setToken} from "../utils/utils";
import {login, sendOTP, verifyOtp} from "../services/auth";
import {useDispatch} from "react-redux";
import {setVerificationData} from "../redux/slices/storeSlice";
import SubmitButton from "../components/common/SubmitButton";

export default function VerifyEmail() {
  const [isLoading, setIsLoading] = useState(false);
  const [isOTPSent, SetIsOTPSent] = useState(false);
  const [isOTPSendingDisabled, setIsOTPSendingDisabled] = useState(false);
  const [otpTimer, setOtpTimer] = useState(60); // countdown from 60 seconds
  const dispatch = useDispatch();
  const router = useRouter();
  const {
    register,
    watch,
    formState: {errors},
    getValues,
    handleSubmit,
    setValue,
    reset,
    setError,
  } = useForm();

  const Images = [login1, login2];

  const [passwordView, setPasswordView] = useState(false);

  const onSubmit = async (data) => {
    if (isLoading) return; // prevents multiple calls
    setIsLoading(true);
    try {
      if (isOTPSent) {
        let response = await verifyOtp({
          email: watch().email,
          codeType: "verification",
          otp: watch().password,
        });

        if (response.status == 200) {
          if (response.data.isOtpVerified) {
            toast.success("OTP Verified");
            dispatch(setVerificationData(response.data.user));
            router.push("/signup");
          }
        }
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setIsLoading(false);
    }
  };

  const startOtpTimer = () => {
    setOtpTimer(60); // reset timer
    const interval = setInterval(() => {
      setOtpTimer((prev) => {
        if (prev <= 1) {
          clearInterval(interval);
          setIsOTPSendingDisabled(false); // re-enable button
          return 0;
        }
        return prev - 1;
      });
    }, 999);
  };

  const sendOtpFunc = async () => {
    const email = watch().email;

    if (!email || errors.email) {
      toast.error("Please enter a valid email before sending OTP");
      return;
    }

    setIsOTPSendingDisabled(true);
    let response = await sendOTP({
      email: watch().email,
      codeType: "verification",
    });
    console.log("response", response);

    if (response?.status == 200) {
      SetIsOTPSent(true);
      toast.success(response.data.message);
      startOtpTimer(); // start countdown
    } else {
      setIsOTPSendingDisabled(false);
    }
  };

  const verifyOtpfunc = async () => {};

  const settings = {
    dots: true,
    infinite: true,
    speed: 500,
    slidesToShow: 1,
    slidesToScroll: 1,
    autoplay: true,
    autoplaySpeed: 3000,
  };
  console.log("error", errors);
  return (
    <div className={`${loginCss.loginContainer}`}>
      <section className="container-wrapper md:flex md:justify-between md:flex-wrap ">
        <section className="md:w-6/12 md:min-w-[600px] md:max-w-[700px] md:m-auto">
          <div className={`${loginCss.imageContainer}`}>
            {/* <LoginSlider ImageSlider={Images} /> */}

            <CustomSlider sliderSettings={settings}>
              {Images.map((image, idx) => (
                <Image
                  key={idx}
                  src={image}
                  alt="login page images"
                  className="h-full w-full rounded-2xl p-1"
                />
              ))}
            </CustomSlider>
          </div>
        </section>

        <section className="md:w-5/12 lg:mt-20 md:min-w-[600px] md:m-auto">
          <h2 className="text-[18px] font-semibold md:text-[40px] md:font-semibold">
            Verify Email
          </h2>
          <p className="mt-3 font-extralight text-[14px] md:text-[16px]">
            Let’s get you all st up so you can access your personal account.{" "}
            <strong>Rebookit </strong> account
          </p>

          <form
            onSubmit={handleSubmit(onSubmit)}
            className="text-[#1C1B1F] my-4"
          >
            <div className="grid grid-cols-1 gap-4 md:grid-cols-5 w-full  rounded-md ">
              {/* Email Input */}
              <div className="md:col-span-3 w-full relative">
                <fieldset className={`${loginCss.fields}`}>
                  <legend className="text-[14px] font-medium px-[7px]">
                    Email
                  </legend>
                  <label htmlFor="email" className="sr-only">
                    Email
                  </label>
                  <input
                    id="email"
                    type="email"
                    autoComplete="off"
                    {...register("email", {
                      required: "Email is required",
                      pattern: {
                        value: /^\S+@\S+\.\S+$/,
                        message: "Invalid Email address",
                      },
                    })}
                    className="w-full text-[13px] md:text-[17px] outline-none border-none"
                    aria-invalid={!!errors.email}
                    aria-describedby="email-error"
                  />
                </fieldset>

                {errors.email && (
                  <p
                    id="email-error"
                    className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-[-18px]"
                  >
                    {errors.email.message}
                  </p>
                )}
              </div>

              {/* Submit Button */}
              <div className="md:col-span-2 w-full flex items-center ">
                <button
                  type="button"
                  disabled={isOTPSendingDisabled}
                  onClick={() => sendOtpFunc()}
                  //   className={`w-full mt-2 py-[18.5px] px-[30px] text-[13px] font-semibold leading-[18px] text-center text-white rounded-md global_linear_gradient md:order-2 md:text-base md:leading-[22px]`}
                  className={`w-full mt-2 py-[18.5px] px-[30px] text-[13px] font-semibold leading-[18px] text-center text-white rounded-md md:order-2 md:text-base md:leading-[22px] ${
                    isOTPSendingDisabled
                      ? "bg-gray-400 cursor-not-allowed"
                      : "global_linear_gradient"
                  }`}
                >
                  {/* Send Code */}
                  {isOTPSendingDisabled
                    ? `Resend in ${otpTimer}s`
                    : "Send Code"}{" "}
                </button>
              </div>
            </div>

            {/* Password Input */}
            {isOTPSent && (
              <>
                <div className="relative h-[90px] md:h-[100px] mt-[30px]">
                  <fieldset className={` ${loginCss.fields}`}>
                    <legend className="text-[14px] font-medium px-[7px]">
                      Enter Code
                    </legend>
                    <label htmlFor="password" className="sr-only">
                      Enter Code
                    </label>
                    <input
                      id="password"
                      type={passwordView ? "text" : "password"}
                      autoComplete="off"
                      maxLength={6}
                      {...register("password", {
                        required: "Valid Password is Required",
                        maxLength: {value: 6, message: "Max length is 6"},
                      })}
                      className="w-full text-[13px] md:text-[17px] outline-none border-none"
                      aria-invalid={!!errors.password}
                      aria-describedby="password-error"
                    />
                     {passwordView ? (
                      <FaRegEyeSlash className="cursor-pointer mr-3 " size={20}  onClick={()=>setPasswordView(!passwordView)} />
                    ) : (
                      <IoEyeSharp className="cursor-pointer  mr-3" size={20} onClick={()=>setPasswordView(!passwordView)} />
                    )}
                  </fieldset>
                  {errors.password && (
                    <p
                      id="password-error"
                      className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0"
                    >
                      {errors.password.message}
                    </p>
                  )}
                </div>

                {/* Submit Button */}
                {/* <button type="submit" className={`${loginCss.submitButton}`}>
                  Verify Code
                </button> */}

                <SubmitButton
                  isLoading={isLoading}
                  type="submit"
                  btnAction={null} // because submit handled by react-hook-form
                  InnerDiv={() => (
                    <span className={`${loginCss.submitButton}`}>
                      Verify Code
                    </span>
                  )}
                />
              </>
            )}
            {/* Sign up prompt */}
            <p className="text-center text-[12px] md:text-[16px] my-3">
              Already have an account?{" "}
              <Link href="/login">
                <span className="text-[#FF8682] font-medium cursor-pointer">
                  Login
                </span>
              </Link>
            </p>
          </form>
        </section>
        <section className="my-5 md:my-0 md:w-12/12">
          <BuyAndSellComponent />
        </section>
      </section>
    </div>
  );
}
