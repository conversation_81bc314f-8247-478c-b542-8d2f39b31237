{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@react-google-maps/api": "^2.20.6", "@reduxjs/toolkit": "^2.6.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.0", "@vis.gl/react-google-maps": "^1.5.2", "axios": "^1.9.0", "google-map-react": "^2.2.5", "joi": "^17.13.3", "moment": "^2.30.1", "next": "15.3.0", "nodemailer": "^7.0.5", "quagga": "^0.12.1", "react": "^18.2.0", "react-confetti": "^6.4.0", "react-datepicker": "^8.4.0", "react-dom": "^18.2.0", "react-fast-marquee": "^1.6.5", "react-google-autocomplete": "^2.7.5", "react-hook-form": "^7.56.0", "react-icons": "^5.5.0", "react-loader-spinner": "^6.1.6", "react-loading-skeleton": "^3.5.0", "react-redux": "^9.2.0", "react-select": "^5.10.1", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "redux": "^5.0.1", "redux-persist": "^6.0.0", "sass": "^1.86.3", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1"}, "devDependencies": {"@tailwindcss/postcss": "^4", "tailwindcss": "^4"}}