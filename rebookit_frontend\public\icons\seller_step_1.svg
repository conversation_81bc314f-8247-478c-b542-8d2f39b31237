<svg width="109" height="101" viewBox="0 0 109 101" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="54.2871" cy="50.5" r="49.8688" fill="white" stroke="url(#paint0_linear_851_4128)" stroke-width="1.2625"/>
<circle cx="54.2872" cy="50.501" r="43.7667" fill="#E4A321"/>
<g filter="url(#filter0_d_851_4128)">
<circle cx="54.287" cy="50.499" r="31.9833" fill="white"/>
</g>
<circle cx="104.156" cy="50.2898" r="3.7875" fill="white" stroke="url(#paint1_linear_851_4128)" stroke-width="1.2625"/>
<circle cx="4.41875" cy="50.2898" r="3.7875" fill="white" stroke="url(#paint2_linear_851_4128)" stroke-width="1.2625"/>
<path d="M51.2363 52.3774C50.4833 52.3774 49.7982 52.2046 49.181 51.859C48.5638 51.5072 48.0299 51.0226 47.5793 50.4054C47.1349 49.7882 46.7923 49.0753 46.5516 48.2667C46.3109 47.452 46.1905 46.5817 46.1905 45.6559C46.1905 44.7301 46.3109 43.8629 46.5516 43.0543C46.7923 42.2396 47.1349 41.5236 47.5793 40.9064C48.0299 40.2891 48.5638 39.8077 49.181 39.4621C49.7982 39.1102 50.4833 38.9343 51.2363 38.9343C51.9893 38.9343 52.6745 39.1102 53.2917 39.4621C53.9089 39.8077 54.4366 40.2891 54.8749 40.9064C55.3193 41.5236 55.6618 42.2396 55.9025 43.0543C56.1494 43.8629 56.2729 44.7301 56.2729 45.6559C56.2729 46.5817 56.1494 47.452 55.9025 48.2667C55.6618 49.0753 55.3193 49.7882 54.8749 50.4054C54.4366 51.0226 53.9089 51.5072 53.2917 51.859C52.6745 52.2046 51.9893 52.3774 51.2363 52.3774ZM51.2363 50.1184C51.8968 50.1184 52.443 49.9085 52.8751 49.4888C53.3071 49.0691 53.6281 48.5198 53.8379 47.8409C54.0478 47.1619 54.1527 46.4336 54.1527 45.6559C54.1527 44.835 54.0416 44.0881 53.8194 43.4154C53.5972 42.7364 53.2701 42.1964 52.838 41.7952C52.406 41.394 51.8721 41.1934 51.2363 41.1934C50.7364 41.1934 50.3012 41.3168 49.9309 41.5637C49.5667 41.8044 49.2643 42.1346 49.0236 42.5543C48.7829 42.9741 48.6039 43.4524 48.4866 43.9894C48.3693 44.5202 48.3107 45.0757 48.3107 45.6559C48.3107 46.4274 48.4156 47.1557 48.6255 47.8409C48.8415 48.5198 49.1655 49.0691 49.5976 49.4888C50.0358 49.9085 50.5821 50.1184 51.2363 50.1184ZM59.1983 52.1367V41.4341H57.5689L58.1799 39.1751H61.4574V52.1367H59.1983Z" fill="#2E2E2E"/>
<path d="M47.2095 61.6123C46.8925 61.6123 46.5867 61.5632 46.2921 61.4651C45.9975 61.3641 45.7394 61.221 45.5178 61.0358C45.2989 60.8506 45.1432 60.6304 45.0506 60.3751L45.7113 60.1268C45.7675 60.2839 45.8713 60.4214 46.0228 60.5392C46.1771 60.6542 46.358 60.744 46.5656 60.8086C46.7732 60.8731 46.9879 60.9053 47.2095 60.9053C47.462 60.9053 47.6963 60.8647 47.9123 60.7833C48.1311 60.6991 48.3079 60.5827 48.4426 60.434C48.5772 60.2853 48.6446 60.1114 48.6446 59.9122C48.6446 59.7074 48.5744 59.5404 48.4341 59.4114C48.2939 59.2795 48.1143 59.1757 47.8955 59.1C47.6766 59.0214 47.448 58.9611 47.2095 58.919C46.8027 58.8517 46.438 58.7563 46.1153 58.6328C45.7955 58.5066 45.5416 58.3312 45.3536 58.1068C45.1685 57.8824 45.0759 57.5864 45.0759 57.2188C45.0759 56.8766 45.1741 56.5778 45.3705 56.3225C45.5697 56.0672 45.832 55.8694 46.1574 55.7291C46.4829 55.5888 46.8336 55.5187 47.2095 55.5187C47.5209 55.5187 47.8225 55.5678 48.1143 55.666C48.4089 55.7614 48.6684 55.9016 48.8928 56.0868C49.1173 56.272 49.2786 56.4978 49.3768 56.7643L48.7077 57.0084C48.6516 56.8485 48.5464 56.711 48.3921 56.596C48.2406 56.4782 48.061 56.3884 47.8534 56.3267C47.6486 56.2622 47.434 56.2299 47.2095 56.2299C46.9598 56.2271 46.727 56.2678 46.5109 56.3519C46.2949 56.4361 46.1196 56.5525 45.9849 56.7012C45.8502 56.8499 45.7829 57.0225 45.7829 57.2188C45.7829 57.4545 45.8446 57.6355 45.9681 57.7617C46.0943 57.8852 46.2654 57.9777 46.4815 58.0395C46.6975 58.1012 46.9402 58.1559 47.2095 58.2036C47.5911 58.2681 47.9446 58.3705 48.27 58.5108C48.5955 58.6483 48.8564 58.832 49.0528 59.0621C49.252 59.2922 49.3516 59.5755 49.3516 59.9122C49.3516 60.2545 49.252 60.5532 49.0528 60.8086C48.8564 61.0639 48.5955 61.2617 48.27 61.4019C47.9446 61.5422 47.5911 61.6123 47.2095 61.6123ZM54.5477 55.6113V56.3183H52.5866V61.5029H51.8796V56.3183H49.9228V55.6113H54.5477ZM55.3926 61.5029V55.6113H59.0076V56.3183H56.0996V57.9932H58.4815V58.7002H56.0996V60.7959H59.0076V61.5029H55.3926ZM59.979 61.5029V55.6113H61.9233C62.2403 55.6113 62.5251 55.6884 62.7776 55.8427C63.0329 55.997 63.2349 56.206 63.3836 56.4698C63.5351 56.7307 63.6108 57.0225 63.6108 57.3451C63.6108 57.6733 63.5337 57.9693 63.3794 58.2331C63.2279 58.494 63.0231 58.7016 62.765 58.8559C62.5097 59.0074 62.2291 59.0831 61.9233 59.0831H60.686V61.5029H59.979ZM60.686 58.3761H61.8602C62.0509 58.3761 62.2249 58.3298 62.382 58.2373C62.5419 58.1419 62.6682 58.0142 62.7607 57.8543C62.8561 57.6916 62.9038 57.5092 62.9038 57.3072C62.9038 57.1024 62.8561 56.9201 62.7607 56.7601C62.6682 56.5974 62.5419 56.4698 62.382 56.3772C62.2249 56.2818 62.0509 56.2341 61.8602 56.2341H60.686V58.3761Z" fill="#2E2E2E"/>
<defs>
<filter id="filter0_d_851_4128" x="19.7787" y="18.5156" width="69.0168" height="70.7001" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4.20833"/>
<feGaussianBlur stdDeviation="1.2625"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.8125 0 0 0 0 0.568073 0 0 0 0 0.0792188 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_851_4128"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_851_4128" result="shape"/>
</filter>
<linearGradient id="paint0_linear_851_4128" x1="54.2871" y1="0" x2="89.8475" y2="99.5271" gradientUnits="userSpaceOnUse">
<stop stop-color="#E2A11A"/>
<stop offset="1" stop-color="#D82108"/>
</linearGradient>
<linearGradient id="paint1_linear_851_4128" x1="104.156" y1="45.8711" x2="107.268" y2="54.5797" gradientUnits="userSpaceOnUse">
<stop stop-color="#E2A11A"/>
<stop offset="1" stop-color="#D82108"/>
</linearGradient>
<linearGradient id="paint2_linear_851_4128" x1="4.41875" y1="45.8711" x2="7.53029" y2="54.5797" gradientUnits="userSpaceOnUse">
<stop stop-color="#E2A11A"/>
<stop offset="1" stop-color="#D82108"/>
</linearGradient>
</defs>
</svg>
