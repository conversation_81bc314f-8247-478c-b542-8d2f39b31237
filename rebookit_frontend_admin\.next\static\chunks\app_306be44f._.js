(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "axiosErrorHandler": (()=>axiosErrorHandler)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
// import history from "./history";
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/utils/utils.js [app-client] (ecmascript)");
;
;
const axiosErrorHandler = (error, action, checkUnauthorized = true)=>{
    console.log("error", error);
    const requestStatus = error?.request?.status;
    const responseStatus = error?.response?.status;
    const dataStatus = error?.data?.statusCode;
    // Only log out on true 401 Unauthorized from response
    if (responseStatus === 401) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$utils$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeToken"])();
        if ("object" !== 'undefined' && window.location) {
            window.location.href = "/login";
        }
        return;
    }
    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {
        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er.messages)) || error?.data?.error?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.error || error?.response?.data?.error || error?.data?.error);
    }
    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {
        console.log("error log is", error);
        if (Array.isArray(error?.response?.data?.message) || Array?.isArray(error?.data?.message)) error?.response?.data?.message?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er)) || error?.data?.message?.map((er)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(er));
        else __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message || error?.response?.data?.data || error?.data?.message);
    }
    if (checkUnauthorized && (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)) {
        if (localStorage.getItem("token")) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(error?.response?.data?.message);
        }
    }
    if (action === "uploadImage") {
        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {
            if (localStorage.getItem("token")) {
                const message = error?.response?.data?.message;
                message && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].error(message);
            } else history.push("/");
        }
    }
    if (error?.response) return error.response;
    else if (error?.request) return error.request;
    else return error?.message;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/service/axios.js [app-client] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, k: __turbopack_refresh__, m: module, e: exports } = __turbopack_context__;
{
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
const { default: axios } = __turbopack_context__.r("[project]/node_modules/axios/dist/browser/axios.cjs [app-client] (ecmascript)");
const { getToken } = __turbopack_context__.r("[project]/app/utils/utils.js [app-client] (ecmascript)");
const BASE_URL = ("TURBOPACK compile-time value", "http://localhost:4002");
const instance = axios.create({
    baseURL: BASE_URL + "/api",
    // Lets keep a check as default is 0 millisecond i.e. never
    // Note: timeout is only for server response not network i.e. server reachability
    timeout: 100000,
    // Lets keep a check as default bytes- 2k
    maxContentLength: 1000,
    // Lets keep a check as default 5 seems high
    maxRedirects: 2
});
instance.interceptors.request.use((config)=>{
    // const token = localStorage.getItem("auth");
    const token = getToken();
    console.log("token", token);
    if (token) {
        config.headers.Authorization = `Bearer ${token}`;
    }
    // Rate limiting: only fire a request every 2 sec from lodash.debounce
    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });
    return Promise.resolve(config);
}, function(error) {
    const response = handleLogError(error); // log them
    return Promise.reject(error);
});
module.exports = instance;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/service/adManagement.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createAdPricingRule": (()=>createAdPricingRule),
    "createIndividualPricingRuleInResource": (()=>createIndividualPricingRuleInResource),
    "createOverridePricingRuleInResource": (()=>createOverridePricingRuleInResource),
    "deleteIndividualRule": (()=>deleteIndividualRule),
    "deleteOverrightRuleDate": (()=>deleteOverrightRuleDate),
    "deleteResource": (()=>deleteResource),
    "delete_AddRule": (()=>delete_AddRule),
    "fetchAdPricingRules": (()=>fetchAdPricingRules),
    "getAdPricingRuleById": (()=>getAdPricingRuleById),
    "updateBasePrice": (()=>updateBasePrice),
    "updateIndividualRule": (()=>updateIndividualRule)
});
const { axiosErrorHandler } = __turbopack_context__.r("[project]/app/utils/axiosError.handler.js [app-client] (ecmascript)");
const instance = __turbopack_context__.r("[project]/app/service/axios.js [app-client] (ecmascript)");
let uri = {
    //create
    createAdPricingRule: "/ad-management",
    createIndividualPricingRuleInResource: "/ad-management/priceRule",
    createOverridePricingRuleInResource: "/ad-management/overrideRule",
    //get
    fetchAdPricingRules: "/ad-management",
    getAdPricingRule_byId: "/ad-management",
    // update
    updateBasePrice: "/ad-management/basePrice",
    updateIndividualRule: "/ad-management/priceRule",
    // delete
    deleteResource: "/ad-management",
    delete_AddRule: "/ad-management",
    deleteIndividualRule: "/ad-management/priceRule",
    deleteOverrightRuleDate: "/ad-management/overrideRule"
};
const createAdPricingRule = async (data)=>{
    let response = await instance.post(uri.createAdPricingRule, data).catch(axiosErrorHandler);
    return response;
};
const createIndividualPricingRuleInResource = async (data)=>{
    let response = await instance.post(uri.createIndividualPricingRuleInResource, data).catch(axiosErrorHandler);
    return response;
};
const createOverridePricingRuleInResource = async (data)=>{
    let response = await instance.post(uri.createOverridePricingRuleInResource, data).catch(axiosErrorHandler);
    return response;
};
const fetchAdPricingRules = async ()=>{
    let response = await instance.get(uri.fetchAdPricingRules).catch(axiosErrorHandler);
    return response;
};
const getAdPricingRuleById = async (id)=>{
    let response = await instance.get(`${uri.getAdPricingRule_byId}/${id}`).catch(axiosErrorHandler);
    return response;
};
const updateBasePrice = async (data)=>{
    let response = await instance.put(`${uri.updateBasePrice}`, data).catch(axiosErrorHandler);
    return response;
};
const updateIndividualRule = async (data)=>{
    let response = await instance.put(`${uri.updateIndividualRule}`, data).catch(axiosErrorHandler);
    return response;
};
const deleteResource = async (id)=>{
    let response = await instance.delete(`${uri.deleteResource}/${id}`).catch(axiosErrorHandler);
    return response;
};
const delete_AddRule = async (id, data)=>{
    let response = await instance.delete(`${uri.delete_AddRule}/${id}`, data).catch(axiosErrorHandler);
    return response;
};
const deleteIndividualRule = async (data)=>{
    let response = await instance.delete(`${uri.deleteIndividualRule}`, {
        data
    }).catch(axiosErrorHandler);
    return response;
};
const deleteOverrightRuleDate = async (data)=>{
    let response = await instance.delete(`${uri.deleteOverrightRuleDate}`, {
        data
    }).catch(axiosErrorHandler);
    return response;
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ManageAdPricingPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-icons/md/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-toastify/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$adManagement$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/app/service/adManagement.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
// --- Helpers for Calendar ---
const weekDays = [
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday",
    "Sunday"
];
const defaultCurrency = "JMD";
const defaultPrice = "30000";
// Dummy dataset simulating API/database response
const dummyCalendarDB = {
    "2025-07": [
        // Each entry: { day: 1, price: "100", currency: "JMD" }
        {
            day: 5,
            price: "200",
            currency: "USD"
        },
        {
            day: 10,
            price: "150",
            currency: "JMD"
        },
        {
            day: 20,
            price: "300",
            currency: "USD"
        }
    ],
    "2025-08": [
        {
            day: 1,
            price: "120",
            currency: "JMD"
        },
        {
            day: 15,
            price: "250",
            currency: "USD"
        }
    ]
};
// Helper to get number of days in a month
function getDaysInMonth(year, month) {
    return new Date(year, month + 1, 0).getDate();
}
// Helper to get the first day index (0=Monday, 6=Sunday)
function getFirstDayIndex(year, month) {
    // JS: 0=Sunday, 6=Saturday; we want 0=Monday
    let jsDay = new Date(year, month, 1).getDay();
    return jsDay === 0 ? 6 : jsDay - 1;
}
// Helper to generate a matrix for the calendar (weeks x 7)
function getCalendarMatrix(year, month) {
    const daysInMonth = getDaysInMonth(year, month);
    const firstDayIdx = getFirstDayIndex(year, month);
    const matrix = [];
    let day = 1 - firstDayIdx;
    for(let row = 0; row < 6; row++){
        const week = [];
        for(let col = 0; col < 7; col++){
            week.push(day > 0 && day <= daysInMonth ? day : "");
            day++;
        }
        matrix.push(week);
    }
    return matrix;
}
// Helper to get week range for a given day (returns [startDay, endDay])
function getWeekRange(year, month, day) {
    const date = new Date(year, month, day);
    const jsDay = date.getDay(); // 0=Sunday
    const mondayOffset = jsDay === 0 ? -6 : 1 - jsDay;
    const start = new Date(year, month, day + mondayOffset);
    const end = new Date(start);
    end.setDate(start.getDate() + 6);
    return [
        Math.max(1, start.getDate()),
        Math.min(getDaysInMonth(year, month), end.getDate())
    ];
}
// Enums for ad page and position types
const AdPageTypeEnum = Object.freeze({
    HOMEPAGE: "home page",
    SELLPAGE: "sell page",
    COMMUNITYPAGE: "community page",
    LISTINGPAGE: "listing page"
});
const AdPositionTypeEnum = Object.freeze({
    TOP: "top",
    MIDDLE: "middle",
    BOTTOM: "bottom",
    NOT_FIXED: "No fixed position"
});
const adPageOptions = Object.values(AdPageTypeEnum);
const adPositionOptions = Object.values(AdPositionTypeEnum);
// Ad types array for dynamic rendering
const adTypes = [
    {
        key: "Banner",
        label: "Banner",
        icon: "/icons/ad-member-banner.svg"
    },
    {
        key: "Grid",
        label: "Grid",
        icon: "/icons/ad-member-grid.svg"
    }
];
// Helper for duplicate check
function findDuplicateRule(rules, ruleForm, editingRuleIdx) {
    for(let i = 0; i < rules.length; i++){
        if (editingRuleIdx !== null && i === editingRuleIdx) continue;
        const r = rules[i];
        if (r.name === ruleForm.name) return 'name';
        // if (r.priority === ruleForm.priority) return 'priority';
        if (r.color === ruleForm.color) return 'color';
    // if (r.startDate === ruleForm.startDate && r.endDate === ruleForm.endDate) return 'date';
    }
    return null;
}
// Helper to get rule for a given date (returns the rule with highest priority if multiple match)
// Priority logic: higher number = higher priority (e.g., 100 is higher than 10)
function getRuleForDate(rules, year, month, day) {
    // Convert to yyyy-mm-dd
    const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    // Filter rules where date is in range and enabled
    const matching = rules.filter((rule)=>{
        if (!rule.enabled) return false;
        if (!rule.startDate || !rule.endDate) return false;
        return rule.startDate <= dateStr && dateStr <= rule.endDate;
    });
    // If multiple, pick the one with highest priority (largest number)
    if (matching.length === 0) return null;
    return matching.reduce((a, b)=>{
        if (a.priority == null) return b;
        if (b.priority == null) return a;
        return Number(a.priority) > Number(b.priority) ? a : b;
    });
}
// Helper to format date as 'YYYY-MM-DDTHH:mm:ss.SSS+00:00'
function formatDateToApi(dateStr) {
    if (!dateStr) return '';
    const d = new Date(dateStr);
    // Get the offset in minutes and convert to "+00:00" format
    const tzOffset = -d.getTimezoneOffset();
    const sign = tzOffset >= 0 ? '+' : '-';
    const pad = (n)=>String(Math.floor(Math.abs(n))).padStart(2, '0');
    const hours = pad(tzOffset / 60);
    const minutes = pad(tzOffset % 60);
    return d.getFullYear() + '-' + pad(d.getMonth() + 1) + '-' + pad(d.getDate()) + 'T' + pad(d.getHours()) + ':' + pad(d.getMinutes()) + ':' + pad(d.getSeconds()) + '.' + String(d.getMilliseconds()).padStart(3, '0') + sign + hours + ':' + minutes;
}
function formatDateTime(dateStr) {
    if (!dateStr) return '';
    const d = new Date(dateStr);
    return d.toLocaleString('en-JM', {
        dateStyle: 'medium',
        timeStyle: 'short'
    });
}
function ManageAdPricingPage() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const [isSaving, setIsSaving] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // UI state
    const [showCalendar, setShowCalendar] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Ad type state
    const [adType, setAdType] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    // Form state
    const [page, setPage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [placement, setPlacement] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    // Base price state
    const [baseCurrency, setBaseCurrency] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("JMD");
    const [basePrice, setBasePrice] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    // Calendar month/year state
    const today = new Date();
    const [calendarYear, setCalendarYear] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(today.getFullYear());
    const [calendarMonth, setCalendarMonth] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(today.getMonth()); // 0-indexed
    const [currency, setCurrency] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultCurrency);
    const [price, setPrice] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultPrice);
    // Calendar price data: prefill every day of the current year with base price
    const [calendarPrices, setCalendarPrices] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        "ManageAdPricingPage.useState": ()=>{
            const days = Array(getDaysInMonth(calendarYear, calendarMonth)).fill(null);
            for(let i = 0; i < days.length; i++){
                days[i] = {
                    price: basePrice,
                    currency: baseCurrency
                };
            }
            return days;
        }
    }["ManageAdPricingPage.useState"]);
    // Modal state for price input
    const [showSetPrice, setShowSetPrice] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedCell, setSelectedCell] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        day: null
    });
    const [modalPrice, setModalPrice] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [modalCurrency, setModalCurrency] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(defaultCurrency);
    // Track if calendar data is dirty
    const [calendarDirty, setCalendarDirty] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Fetch already created plans to disable existing combos and show actions
    const [createdPlans, setCreatedPlans] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ManageAdPricingPage.useEffect": ()=>{
            ({
                "ManageAdPricingPage.useEffect": async ()=>{
                    try {
                        const res = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$adManagement$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchAdPricingRules"])();
                        const resources = res?.data?.resources || res?.data || [];
                        if (Array.isArray(resources)) {
                            setCreatedPlans(resources.map({
                                "ManageAdPricingPage.useEffect": (r)=>({
                                        page: String(r.page || "").toLowerCase(),
                                        type: String(r.type || "").toLowerCase(),
                                        position: String(r.position || "").toLowerCase(),
                                        _id: r._id
                                    })
                            }["ManageAdPricingPage.useEffect"]));
                        }
                    } catch (e) {}
                }
            })["ManageAdPricingPage.useEffect"]();
        }
    }["ManageAdPricingPage.useEffect"], []);
    const isPlanCreated = (pageName, typeName, positionName = "")=>{
        const p = String(pageName || "").toLowerCase();
        const t = String(typeName || "").toLowerCase();
        const pos = String(positionName || "").toLowerCase();
        return createdPlans.some((cp)=>cp.page === p && cp.type === t && (pos ? cp.position === pos : true) && (!pos ? !cp.position || cp.position === "" : true));
    };
    // Add state for per-date price overrides
    const [dateOverrides, setDateOverrides] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({}); // { 'YYYY-MM-DD': { price, currency } }
    // Next button enable logic
    const requiresPlacement = page === AdPageTypeEnum.HOMEPAGE || page === AdPageTypeEnum.SELLPAGE;
    const isNextEnabled = !!(adType && page && basePrice && (requiresPlacement ? placement : true));
    // Step indicator logic
    const currentStep = showCalendar ? 2 : 1;
    // State for pricing rule modal
    const [showRuleModal, setShowRuleModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [ruleForm, setRuleForm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        name: "",
        enabled: true,
        startDate: "",
        endDate: "",
        price: basePrice,
        currency: baseCurrency,
        priority: null,
        color: ""
    });
    const [rules, setRules] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    // Add state to track which rule is being edited
    const [editingRuleIdx, setEditingRuleIdx] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Dummy state to force calendar re-render
    const [calendarRefresh, setCalendarRefresh] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    console.log(ruleForm, "ruleform", rules);
    // --- Handlers ---
    // Restrict calendar navigation to current year only
    const handlePrevMonth = ()=>{
        setCalendarMonth((prev)=>{
            if (prev === 0) {
                return 0; // Don't go to previous year
            }
            return prev - 1;
        });
        setTimeout(loadMonthData, 0);
    };
    const handleNextMonth = ()=>{
        setCalendarMonth((prev)=>{
            if (prev === 11) {
                return 11; // Don't go to next year
            }
            return prev + 1;
        });
        setTimeout(loadMonthData, 0);
    };
    // Load month data from dummy DB
    function loadMonthData() {
        const key = `${calendarYear}-${String(calendarMonth + 1).padStart(2, "0")}`;
        const monthData = dummyCalendarDB[key] || [];
        const days = Array(getDaysInMonth(calendarYear, calendarMonth)).fill(null);
        monthData.forEach(({ day, price, currency })=>{
            days[day - 1] = {
                price,
                currency
            };
        });
        setCalendarPrices(days);
        setCalendarDirty(false);
    }
    // Open set price modal, prefill if price exists or override exists
    const handleCellClick = (day)=>{
        setSelectedCell({
            day
        });
        const dateStr = `${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        if (dateOverrides[dateStr]) {
            setModalPrice(dateOverrides[dateStr].price || "");
            setModalCurrency(dateOverrides[dateStr].currency || defaultCurrency);
        } else {
            const cell = calendarPrices[day - 1];
            setModalPrice("");
            setModalCurrency(cell?.currency || defaultCurrency);
        }
        setShowSetPrice(true);
    };
    // Confirm price change for a cell (override logic)
    const handleConfirm = ()=>{
        if (selectedCell.day !== null) {
            const dateStr = `${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(selectedCell.day).padStart(2, '0')}`;
            setDateOverrides((prev)=>({
                    ...prev,
                    [dateStr]: {
                        price: modalPrice,
                        currency: modalCurrency
                    }
                }));
            setCalendarDirty(true);
        }
        setShowSetPrice(false);
        setSelectedCell({
            day: null
        });
        setModalPrice("");
        setModalCurrency(defaultCurrency);
    };
    // Remove override for a date
    const handleRemoveOverride = (day)=>{
        const dateStr = `${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
        setDateOverrides((prev)=>{
            const copy = {
                ...prev
            };
            delete copy[dateStr];
            return copy;
        });
        setCalendarDirty(true);
    };
    // Save calendar changes (simulate API)
    const handleSaveCalendar = ()=>{
        // Here you would send calendarPrices to your API/database
        setCalendarDirty(false);
    // Show a toast
    // toast.success("Calendar prices saved successfully!");
    };
    // Update calendar prices when base price changes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ManageAdPricingPage.useEffect": ()=>{
            if (showCalendar) {
                const days = Array(getDaysInMonth(calendarYear, calendarMonth)).fill(null);
                for(let i = 0; i < days.length; i++){
                    days[i] = {
                        price: basePrice,
                        currency: baseCurrency
                    };
                }
                setCalendarPrices(days);
            }
        }
    }["ManageAdPricingPage.useEffect"], [
        basePrice,
        baseCurrency,
        calendarYear,
        calendarMonth,
        showCalendar
    ]);
    // Stepper click handlers
    const handleStepClick = (step)=>{
        if (step === 1) {
            setShowCalendar(false);
        } else if (step === 2 && isNextEnabled) {
            setShowCalendar(true);
            loadMonthData();
        }
    };
    // --- API Save Handler ---
    const handleApiSave = async ()=>{
        setIsSaving(true);
        // Build pricingRules array
        const pricingRules = rules.map((rule)=>{
            const ruleObj = {
                name: rule.name,
                color: rule.color,
                startDate: rule.startDate ? formatDateToApi(rule.startDate) : '',
                endDate: rule.endDate ? formatDateToApi(rule.endDate) : '',
                price: rule.price ? Number(rule.price) : null,
                isActive: !!rule.enabled
            };
            if (rule.priority !== null && rule.priority !== '' && rule.priority !== undefined) {
                ruleObj.priority = Number(rule.priority);
            }
            return ruleObj;
        });
        // Build overrideRules array
        const overrideRules = Object.entries(dateOverrides).map(([date, val])=>({
                date: date ? formatDateToApi(date) : '',
                price: val.price ? Number(val.price) : null
            }));
        // Build payload
        const payload = {
            type: adType ? adType.toLowerCase() : '',
            page,
            position: placement,
            basePrice: basePrice ? String(basePrice) : '',
            pricingRules: pricingRules.length ? pricingRules : [],
            overrideRules: overrideRules.length ? overrideRules : []
        };
        try {
            const res = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$adManagement$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createAdPricingRule"])(payload);
            if (res && (res.status === 200 || res.status === 201)) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].success('Rules saved successfully!');
                setTimeout(()=>{
                    router.push('/ad-management/current-ad-plan');
                }, 1000);
            } else {
            // Error toast is already handled in the service, do not show again
            }
        } catch (error) {
        // Error toast is already handled in the service, do not show again
        }
        setIsSaving(false);
    };
    // Save button enablement: allow if there are pricing rules or overrides, and not saving
    const canSave = (rules.length > 0 || Object.keys(dateOverrides).length > 0) && !isSaving;
    // --- UI ---
    if (!showCalendar) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "p-6 border border-gray-200 shadow-xs rounded-lg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-8 flex items-center gap-4",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "button",
                            className: `w-8 h-8 flex items-center justify-center rounded-full font-bold text-base transition-all duration-200 ${!showCalendar ? "bg-[#0161AB] text-white" : "bg-[#F5F8FE] text-[#0161AB] border border-[#0161AB]"}`,
                            style: {
                                outline: "none"
                            },
                            onClick: ()=>handleStepClick(1),
                            tabIndex: 0,
                            children: isNextEnabled ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                width: "20",
                                height: "20",
                                viewBox: "0 0 20 20",
                                fill: "none",
                                xmlns: "http://www.w3.org/2000/svg",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                        cx: "10",
                                        cy: "10",
                                        r: "10",
                                        fill: "#4D7906"
                                    }, void 0, false, {
                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                        lineNumber: 455,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M6 10.5L9 13.5L14 8.5",
                                        stroke: "white",
                                        strokeWidth: "2",
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round"
                                    }, void 0, false, {
                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                        lineNumber: 456,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                lineNumber: 448,
                                columnNumber: 15
                            }, this) : 1
                        }, void 0, false, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 436,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: `font-[poppins] text-lg ${!showCalendar ? "text-[#0161AB] font-semibold" : "text-gray-400"}`,
                            children: "Step 1: Ad Details"
                        }, void 0, false, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 468,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "button",
                            className: `w-8 h-8 flex items-center justify-center rounded-full font-bold text-base transition-all duration-200 ${showCalendar && isNextEnabled ? "bg-[#0161AB] text-white" : "bg-[#F5F8FE] text-[#0161AB] border border-[#0161AB]"} ${isNextEnabled ? "cursor-pointer" : "cursor-not-allowed"}`,
                            style: {
                                outline: "none"
                            },
                            onClick: ()=>isNextEnabled && handleStepClick(2),
                            tabIndex: isNextEnabled ? 0 : -1,
                            disabled: !isNextEnabled,
                            children: "2"
                        }, void 0, false, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 476,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: `font-[poppins] text-lg ${showCalendar ? "text-[#0161AB] font-semibold" : "text-gray-400"}`,
                            children: "Step 2: Calendar Management"
                        }, void 0, false, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 490,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                    lineNumber: 435,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            className: "block font-[poppins] mb-2 text-[#211F54]",
                            children: [
                                "Select Ad Placement Combo",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-[#E1020C]",
                                    children: "*"
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 502,
                                    columnNumber: 38
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 501,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-5",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "font-semibold text-[#211F54] mb-2",
                                    children: "Home page"
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 506,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",
                                    children: [
                                        {
                                            type: "Banner",
                                            position: AdPositionTypeEnum.TOP,
                                            page: AdPageTypeEnum.HOMEPAGE
                                        },
                                        {
                                            type: "Grid",
                                            position: AdPositionTypeEnum.MIDDLE,
                                            page: AdPageTypeEnum.HOMEPAGE
                                        },
                                        {
                                            type: "Banner",
                                            position: AdPositionTypeEnum.BOTTOM,
                                            page: AdPageTypeEnum.HOMEPAGE
                                        }
                                    ].map((c)=>{
                                        const alreadyCreated = isPlanCreated(c.page, c.type, c.position);
                                        const plan = createdPlans.find((cp)=>cp.page === String(c.page).toLowerCase() && cp.type === String(c.type).toLowerCase() && cp.position === String(c.position).toLowerCase());
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: `flex-1 border rounded-xl p-6 flex flex-col items-start gap-2 transition-all border-[#0161AB] bg-[#F5F8FE]`,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex gap-2 items-center flex-wrap",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "px-2 py-0.5 rounded-full bg-purple-50 text-purple-700 text-xs",
                                                            children: [
                                                                "Type: ",
                                                                c.type
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 523,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "px-2 py-0.5 rounded-full bg-gray-100 text-gray-700 text-xs",
                                                            children: [
                                                                "Position: ",
                                                                c.position
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 524,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 522,
                                                    columnNumber: 21
                                                }, this),
                                                alreadyCreated && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "px-2 py-0.5 rounded-full bg-green-50 text-green-700 text-xs",
                                                    children: "Plan already created"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 527,
                                                    columnNumber: 23
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "mt-3 flex gap-2 w-full",
                                                    children: alreadyCreated ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "px-3 py-1 rounded-md text-xs border border-gray-300",
                                                                onClick: ()=>{
                                                                    if (plan?._id) alert(`View plan id: ${plan._id}`);
                                                                },
                                                                children: "View"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                lineNumber: 532,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "px-3 py-1 rounded-md text-xs border border-gray-300",
                                                                onClick: ()=>{
                                                                    if (plan?._id) alert(`Edit plan id: ${plan._id}`);
                                                                },
                                                                children: "Edit"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                lineNumber: 533,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "px-3 py-1 rounded-md text-xs border border-red-300 text-red-600",
                                                                onClick: ()=>{
                                                                    if (plan?._id) alert(`Delete plan id: ${plan._id}`);
                                                                },
                                                                children: "Delete"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                lineNumber: 534,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        className: "px-3 py-1 rounded-md text-xs bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white",
                                                        onClick: ()=>{
                                                            setPage(c.page);
                                                            setAdType(c.type);
                                                            setPlacement(c.position);
                                                            setShowCalendar(true);
                                                            setTimeout(loadMonthData, 0);
                                                        },
                                                        children: "Create Plan"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                        lineNumber: 537,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 529,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, `${c.page}-${c.type}-${c.position}`, true, {
                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                            lineNumber: 518,
                                            columnNumber: 19
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 507,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 505,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-5",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "font-semibold text-[#211F54] mb-2",
                                    children: "Sell page"
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 548,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",
                                    children: [
                                        {
                                            type: "Banner",
                                            position: AdPositionTypeEnum.BOTTOM,
                                            page: AdPageTypeEnum.SELLPAGE
                                        }
                                    ].map((c)=>{
                                        const selected = page === c.page && adType === c.type && placement === c.position;
                                        const alreadyCreated = isPlanCreated(c.page, c.type, c.position);
                                        const plan = createdPlans.find((cp)=>cp.page === String(c.page).toLowerCase() && cp.type === String(c.type).toLowerCase() && cp.position === String(c.position).toLowerCase());
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            disabled: alreadyCreated,
                                            className: `flex-1 border rounded-xl p-6 flex flex-col items-start gap-2 transition-all ${selected ? "border-[#0161AB] bg-[#F5F8FE]" : "border-gray-200 bg-white"} ${alreadyCreated ? "opacity-60 cursor-not-allowed" : ""}`,
                                            onClick: ()=>{
                                                if (alreadyCreated) return;
                                                setPage(c.page);
                                                setAdType(c.type);
                                                setPlacement(c.position);
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex gap-2 items-center flex-wrap ",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "px-2 py-0.5 rounded-full bg-purple-50 text-purple-700 text-xs",
                                                            children: [
                                                                "Type: ",
                                                                c.type
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 572,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "px-2 py-0.5 rounded-full bg-gray-100 text-gray-700 text-xs",
                                                            children: [
                                                                "Position: ",
                                                                c.position
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 573,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 571,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "mt-3 flex gap-2 w-full",
                                                    children: alreadyCreated ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "px-3 py-1 rounded-md text-xs border border-gray-300",
                                                                onClick: (e)=>{
                                                                    e.stopPropagation();
                                                                    if (plan?._id) router.push(`/ad-management/current-ad-plan/view/${plan._id}`);
                                                                },
                                                                children: "View"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                lineNumber: 578,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "px-3 py-1 rounded-md text-xs border border-gray-300",
                                                                onClick: (e)=>{
                                                                    e.stopPropagation();
                                                                    if (plan?._id) router.push(`/ad-management/current-ad-plan/edit/${plan._id}`);
                                                                },
                                                                children: "Edit"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                lineNumber: 579,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "px-3 py-1 rounded-md text-xs border border-red-300 text-red-600",
                                                                onClick: async (e)=>{
                                                                    e.stopPropagation();
                                                                    if (!plan?._id) return;
                                                                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$adManagement$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteResource"])(plan._id);
                                                                    const res = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$adManagement$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchAdPricingRules"])();
                                                                    const resources = res?.data?.resources || res?.data || [];
                                                                    setCreatedPlans(resources.map((r)=>({
                                                                            page: String(r.page || "").toLowerCase(),
                                                                            type: String(r.type || "").toLowerCase(),
                                                                            position: String(r.position || "").toLowerCase(),
                                                                            _id: r._id
                                                                        })));
                                                                },
                                                                children: "Delete"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                lineNumber: 580,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        className: "px-3 py-1 rounded-md text-xs bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white",
                                                        onClick: (e)=>{
                                                            e.stopPropagation();
                                                            setPage(c.page);
                                                            setAdType(c.type);
                                                            setPlacement(c.position);
                                                            setShowCalendar(true);
                                                            setTimeout(loadMonthData, 0);
                                                        },
                                                        children: "Create Plan"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                        lineNumber: 583,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 575,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, `${c.page}-${c.type}-${c.position}`, true, {
                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                            lineNumber: 557,
                                            columnNumber: 19
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 549,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 547,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-5",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "font-semibold text-[#211F54] mb-2",
                                    children: "Community page"
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 594,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",
                                    children: [
                                        {
                                            type: "Banner",
                                            position: AdPositionTypeEnum.NOT_FIXED,
                                            page: AdPageTypeEnum.COMMUNITYPAGE
                                        },
                                        {
                                            type: "Grid",
                                            position: AdPositionTypeEnum.NOT_FIXED,
                                            page: AdPageTypeEnum.COMMUNITYPAGE
                                        }
                                    ].map((c)=>{
                                        const selected = page === c.page && adType === c.type && placement === c.position;
                                        const alreadyCreated = isPlanCreated(c.page, c.type, c.position);
                                        const plan = createdPlans.find((cp)=>cp.page === String(c.page).toLowerCase() && cp.type === String(c.type).toLowerCase() && cp.position === String(c.position).toLowerCase());
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            disabled: alreadyCreated,
                                            className: `flex-1 border rounded-xl p-6 flex flex-col items-start gap-2 transition-all ${selected ? "border-[#0161AB] bg-[#F5F8FE]" : "border-gray-200 bg-white"} ${alreadyCreated ? "opacity-60 cursor-not-allowed" : ""}`,
                                            onClick: ()=>{
                                                if (alreadyCreated) return;
                                                setPage(c.page);
                                                setAdType(c.type);
                                                setPlacement(c.position);
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex gap-2 items-center flex-wrap",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "px-2 py-0.5 rounded-full bg-purple-50 text-purple-700 text-xs",
                                                            children: [
                                                                "Type: ",
                                                                c.type
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 619,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "px-2 py-0.5 rounded-full bg-gray-100 text-gray-700 text-xs",
                                                            children: [
                                                                "Position: ",
                                                                c.position
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 620,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 618,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "mt-3 flex gap-2 w-full",
                                                    children: alreadyCreated ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "px-3 py-1 rounded-md text-xs border border-gray-300",
                                                                onClick: (e)=>{
                                                                    e.stopPropagation();
                                                                    if (plan?._id) router.push(`/ad-management/current-ad-plan/view/${plan._id}`);
                                                                },
                                                                children: "View"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                lineNumber: 625,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "px-3 py-1 rounded-md text-xs border border-gray-300",
                                                                onClick: (e)=>{
                                                                    e.stopPropagation();
                                                                    if (plan?._id) router.push(`/ad-management/current-ad-plan/edit/${plan._id}`);
                                                                },
                                                                children: "Edit"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                lineNumber: 626,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "px-3 py-1 rounded-md text-xs border border-red-300 text-red-600",
                                                                onClick: async (e)=>{
                                                                    e.stopPropagation();
                                                                    if (!plan?._id) return;
                                                                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$adManagement$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteResource"])(plan._id);
                                                                    const res = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$adManagement$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchAdPricingRules"])();
                                                                    const resources = res?.data?.resources || res?.data || [];
                                                                    setCreatedPlans(resources.map((r)=>({
                                                                            page: String(r.page || "").toLowerCase(),
                                                                            type: String(r.type || "").toLowerCase(),
                                                                            position: String(r.position || "").toLowerCase(),
                                                                            _id: r._id
                                                                        })));
                                                                },
                                                                children: "Delete"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                lineNumber: 627,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        className: "px-3 py-1 rounded-md text-xs bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white",
                                                        onClick: (e)=>{
                                                            e.stopPropagation();
                                                            setPage(c.page);
                                                            setAdType(c.type);
                                                            setPlacement(c.position);
                                                            setShowCalendar(true);
                                                            setTimeout(loadMonthData, 0);
                                                        },
                                                        children: "Create Plan"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                        lineNumber: 630,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 622,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, `${c.page}-${c.type}-any`, true, {
                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                            lineNumber: 604,
                                            columnNumber: 19
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 595,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 593,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-5",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "font-semibold text-[#211F54] mb-2",
                                    children: "Product page"
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 641,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",
                                    children: [
                                        {
                                            type: "Banner",
                                            position: AdPositionTypeEnum.NOT_FIXED,
                                            page: AdPageTypeEnum.LISTINGPAGE
                                        },
                                        {
                                            type: "Grid",
                                            position: AdPositionTypeEnum.NOT_FIXED,
                                            page: AdPageTypeEnum.LISTINGPAGE
                                        }
                                    ].map((c)=>{
                                        const selected = page === c.page && adType === c.type && placement === c.position;
                                        const alreadyCreated = isPlanCreated(c.page, c.type, c.position);
                                        const plan = createdPlans.find((cp)=>cp.page === String(c.page).toLowerCase() && cp.type === String(c.type).toLowerCase() && cp.position === String(c.position).toLowerCase());
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            disabled: alreadyCreated,
                                            className: `flex-1 border rounded-xl p-6 flex flex-col items-start gap-2 transition-all ${selected ? "border-[#0161AB] bg-[#F5F8FE]" : "border-gray-200 bg-white"} ${alreadyCreated ? "opacity-60 cursor-not-allowed" : ""}`,
                                            onClick: ()=>{
                                                if (alreadyCreated) return;
                                                setPage(c.page);
                                                setAdType(c.type);
                                                setPlacement(c.position);
                                            },
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex gap-2 items-center flex-wrap",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "px-2 py-0.5 rounded-full bg-purple-50 text-purple-700 text-xs",
                                                            children: [
                                                                "Type: ",
                                                                c.type
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 666,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "px-2 py-0.5 rounded-full bg-gray-100 text-gray-700 text-xs",
                                                            children: [
                                                                "Position: ",
                                                                c.position
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 667,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 665,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "mt-3 flex gap-2 w-full",
                                                    children: alreadyCreated ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "px-3 py-1 rounded-md text-xs border border-gray-300",
                                                                onClick: (e)=>{
                                                                    e.stopPropagation();
                                                                    if (plan?._id) router.push(`/ad-management/current-ad-plan/view/${plan._id}`);
                                                                },
                                                                children: "View"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                lineNumber: 672,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "px-3 py-1 rounded-md text-xs border border-gray-300",
                                                                onClick: (e)=>{
                                                                    e.stopPropagation();
                                                                    if (plan?._id) router.push(`/ad-management/current-ad-plan/edit/${plan._id}`);
                                                                },
                                                                children: "Edit"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                lineNumber: 673,
                                                                columnNumber: 27
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                                className: "px-3 py-1 rounded-md text-xs border border-red-300 text-red-600",
                                                                onClick: async (e)=>{
                                                                    e.stopPropagation();
                                                                    if (!plan?._id) return;
                                                                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$adManagement$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deleteResource"])(plan._id);
                                                                    const res = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$app$2f$service$2f$adManagement$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fetchAdPricingRules"])();
                                                                    const resources = res?.data?.resources || res?.data || [];
                                                                    setCreatedPlans(resources.map((r)=>({
                                                                            page: String(r.page || "").toLowerCase(),
                                                                            type: String(r.type || "").toLowerCase(),
                                                                            position: String(r.position || "").toLowerCase(),
                                                                            _id: r._id
                                                                        })));
                                                                },
                                                                children: "Delete"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                lineNumber: 674,
                                                                columnNumber: 27
                                                            }, this)
                                                        ]
                                                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                        className: "px-3 py-1 rounded-md text-xs bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white",
                                                        onClick: (e)=>{
                                                            e.stopPropagation();
                                                            setPage(c.page);
                                                            setAdType(c.type);
                                                            setPlacement(c.position);
                                                            setShowCalendar(true);
                                                            setTimeout(loadMonthData, 0);
                                                        },
                                                        children: "Create Plan"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                        lineNumber: 677,
                                                        columnNumber: 25
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 669,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, `${c.page}-${c.type}-any`, true, {
                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                            lineNumber: 651,
                                            columnNumber: 19
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 642,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 640,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                    lineNumber: 500,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            className: "block font-[poppins] mb-1 text-[#211F54]",
                            children: [
                                "Base Price",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-[#E1020C]",
                                    children: "*"
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 689,
                                    columnNumber: 23
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 688,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center w-full bg-white border border-gray-200 rounded-lg overflow-hidden",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                    className: "bg-transparent px-4 py-3 font-[poppins] focus:outline-none border-none text-[#211F54]",
                                    value: baseCurrency,
                                    disabled: true,
                                    style: {
                                        cursor: "not-allowed"
                                    },
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                        value: "JMD",
                                        children: "J$"
                                    }, void 0, false, {
                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                        lineNumber: 698,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 692,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "number",
                                    min: "0",
                                    maxLength: 10,
                                    className: "flex-1 px-4 py-3 ml-4 font-[poppins] bg-transparent focus:outline-none border-l border-gray-200 text-[#211F54]",
                                    value: basePrice,
                                    onChange: (e)=>{
                                        // Only allow numbers and empty string, and max 10 digits
                                        const val = e.target.value;
                                        if (/^\d{0,10}$/.test(val)) setBasePrice(val);
                                    },
                                    placeholder: "Enter amount"
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 700,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 691,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                    lineNumber: 687,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    className: `w-full py-4 font-semibold text-lg rounded-full ${isNextEnabled ? "cursor-pointer text-white border-radius-[66px] bg-gradient-to-r from-[#211F54] to-[#0161AB]" : "bg-gray-500 cursor-not-allowed"}`,
                    style: isNextEnabled ? {
                        borderRadius: "66px",
                        background: "linear-gradient(268deg, #211F54 11.09%, #0161AB 98.55%)"
                    } : {},
                    disabled: !isNextEnabled,
                    onClick: ()=>{
                        if (isNextEnabled) {
                            setShowCalendar(true);
                            loadMonthData();
                        }
                    },
                    children: "Next"
                }, void 0, false, {
                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                    lineNumber: 728,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
            lineNumber: 433,
            columnNumber: 7
        }, this);
    }
    // Calendar matrix for current month/year
    const calendar = getCalendarMatrix(calendarYear, calendarMonth);
    // Compute if all required fields are filled
    const isRuleFormValid = !!(ruleForm.name && ruleForm.startDate && ruleForm.endDate && ruleForm.color && ruleForm.price);
    // Get today's date in yyyy-mm-dd format
    const todayStr = new Date().toISOString().split('T')[0];
    // Find the most recent rule (excluding base price)
    // Filter out base price rules
    const nonBaseRules = rules.filter((rule)=>rule.name?.toLowerCase() !== 'base price');
    // Find the most recent rule by updatedAt (ensure updatedAt exists and is valid)
    let mostRecentRule = null;
    if (nonBaseRules.length > 0) {
        mostRecentRule = nonBaseRules.reduce((a, b)=>{
            // If either updatedAt is missing, treat as less recent
            const aTime = a.updatedAt ? new Date(a.updatedAt).getTime() : 0;
            const bTime = b.updatedAt ? new Date(b.updatedAt).getTime() : 0;
            return aTime > bTime ? a : b;
        });
    }
    // Use the rule's name as fallback if _id is missing (for frontend-only rules)
    const mostRecentRuleId = mostRecentRule?._id || mostRecentRule?.name || null;
    console.log(mostRecentRuleId, "mostRecentRuleId", nonBaseRules, mostRecentRule);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "p-6 border border-gray-200 shadow-xs rounded-lg",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: " flex items-center gap-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        className: `w-8 h-8 flex items-center justify-center rounded-full font-bold text-base transition-all duration-200 ${!showCalendar ? "bg-[#0161AB] text-white" : "bg-[#F5F8FE] text-[#0161AB] border border-[#0161AB]"}`,
                        style: {
                            outline: "none"
                        },
                        onClick: ()=>handleStepClick(1),
                        tabIndex: 0,
                        children: [
                            " ",
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                width: "20",
                                height: "20",
                                viewBox: "0 0 20 20",
                                fill: "none",
                                xmlns: "http://www.w3.org/2000/svg",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                        cx: "10",
                                        cy: "10",
                                        r: "10",
                                        fill: "#4D7906"
                                    }, void 0, false, {
                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                        lineNumber: 812,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        d: "M6 10.5L9 13.5L14 8.5",
                                        stroke: "white",
                                        strokeWidth: "2",
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round"
                                    }, void 0, false, {
                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                        lineNumber: 813,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                lineNumber: 805,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                        lineNumber: 793,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: `font-[poppins] text-lg ${!showCalendar ? "text-[#0161AB] font-semibold" : "text-gray-400"}`,
                        children: "Step 1: Ad Details"
                    }, void 0, false, {
                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                        lineNumber: 822,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        className: `w-8 h-8 flex items-center justify-center rounded-full font-bold text-base transition-all duration-200 ${showCalendar && isNextEnabled ? "bg-[#0161AB] text-white" : "bg-[#F5F8FE] text-[#0161AB] border border-[#0161AB]"} ${isNextEnabled ? "cursor-pointer" : "cursor-not-allowed"}`,
                        style: {
                            outline: "none"
                        },
                        onClick: ()=>isNextEnabled && handleStepClick(2),
                        tabIndex: isNextEnabled ? 0 : -1,
                        disabled: !isNextEnabled,
                        children: "2"
                    }, void 0, false, {
                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                        lineNumber: 830,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: `font-[poppins] text-lg ${showCalendar ? "text-[#0161AB] font-semibold" : "text-gray-400"}`,
                        children: "Step 2: Calendar Management"
                    }, void 0, false, {
                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                        lineNumber: 844,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                lineNumber: 792,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-end gap-8 mb-4 border-b border-gray-200 pb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2"
                    }, void 0, false, {
                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                        lineNumber: 854,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center gap-2 ",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "font-[poppins] font-medium bg-white px-4 py-2 rounded border border-gray-200 shadow-sm",
                                children: new Date(calendarYear, calendarMonth).toLocaleString("default", {
                                    month: "long",
                                    year: "numeric"
                                })
                            }, void 0, false, {
                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                lineNumber: 864,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "px-3 py-2 bg-white border border-gray-200 rounded-lg shadow-sm text-xl  text-[#211F54]",
                                onClick: handlePrevMonth,
                                children: "<"
                            }, void 0, false, {
                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                lineNumber: 870,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                className: "px-3 py-2 bg-white border border-gray-200 rounded-lg shadow-sm text-xl  text-[#211F54]",
                                onClick: handleNextMonth,
                                children: ">"
                            }, void 0, false, {
                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                lineNumber: 876,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                        lineNumber: 863,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        type: "button",
                        className: "inline-flex items-center justify-center px-4 py-4 rounded-full bg-gradient-to-tr from-[#211F54] to-[#0161AB] min-h-[34.71px] h-[47px] w-[175px] font-inter",
                        onClick: ()=>{
                            setShowRuleModal(true);
                            setEditingRuleIdx(null);
                        },
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                            className: "text-white text-[14.2px] font-semibold leading-[22.09px] text-center",
                            children: "Set Pricing Rule"
                        }, void 0, false, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 891,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                        lineNumber: 883,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                lineNumber: 853,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-xl font-bold mb-4 text-[#211F54]",
                        children: "Pricing Rules"
                    }, void 0, false, {
                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                        lineNumber: 898,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "overflow-x-auto",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                            className: "min-w-full bg-white rounded-lg shadow border border-gray-200",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                        className: "bg-[#F5F6FA] text-[#211F54]",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                className: "p-3 text-left font-semibold border-b border-gray-200",
                                                children: "Name"
                                            }, void 0, false, {
                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                lineNumber: 903,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                className: "p-3 text-left font-semibold border-b border-gray-200",
                                                children: "Price"
                                            }, void 0, false, {
                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                lineNumber: 904,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                className: "p-3 text-left font-semibold border-b border-gray-200",
                                                children: "Priority"
                                            }, void 0, false, {
                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                lineNumber: 905,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                className: "p-3 text-left font-semibold border-b border-gray-200",
                                                children: "Start Date"
                                            }, void 0, false, {
                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                lineNumber: 906,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                className: "p-3 text-left font-semibold border-b border-gray-200",
                                                children: "End Date"
                                            }, void 0, false, {
                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                lineNumber: 907,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                className: "p-3 text-left font-semibold border-b border-gray-200",
                                                children: "Color"
                                            }, void 0, false, {
                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                lineNumber: 908,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                className: "p-3 text-left font-semibold border-b border-gray-200",
                                                children: "Status"
                                            }, void 0, false, {
                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                lineNumber: 909,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                className: "p-3 text-left font-semibold border-b border-gray-200",
                                                children: "Last Updated"
                                            }, void 0, false, {
                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                lineNumber: 910,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                                className: "p-3 text-left font-semibold border-b border-gray-200",
                                                children: "Actions"
                                            }, void 0, false, {
                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                lineNumber: 911,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                        lineNumber: 902,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 901,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                                    children: nonBaseRules.length === 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                            colSpan: 9,
                                            className: "text-center p-4 text-gray-400",
                                            children: "No pricing rules"
                                        }, void 0, false, {
                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                            lineNumber: 916,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                        lineNumber: 916,
                                        columnNumber: 17
                                    }, this) : nonBaseRules.map((rule, idx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                            className: "hover:bg-[#F5F6FA]",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    className: "p-3",
                                                    children: rule.name
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 919,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    className: "p-3 border-b border-gray-200",
                                                    children: formatJMD(rule.price)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 920,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    className: "p-3",
                                                    children: rule.priority
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 921,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    className: "p-3",
                                                    children: rule.startDate
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 922,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    className: "p-3",
                                                    children: rule.endDate
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 923,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    className: "p-3",
                                                    children: rule.color && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "inline-flex items-center gap-2",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            style: {
                                                                background: rule.color,
                                                                width: 18,
                                                                height: 18,
                                                                borderRadius: '50%',
                                                                border: '1px solid #ccc',
                                                                display: 'inline-block'
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 927,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                        lineNumber: 926,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 924,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    className: "p-3",
                                                    children: rule?.enabled ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        style: {
                                                            color: 'green',
                                                            fontWeight: 600
                                                        },
                                                        children: "Active"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                        lineNumber: 933,
                                                        columnNumber: 23
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        style: {
                                                            color: 'red',
                                                            fontWeight: 600
                                                        },
                                                        children: "Inactive"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                        lineNumber: 935,
                                                        columnNumber: 23
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 931,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    className: "p-3",
                                                    children: [
                                                        formatDateTime(rule.updatedAt),
                                                        rule?.name === mostRecentRuleId && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "ml-2 px-2 py-1 bg-green-100 text-green-700 rounded text-xs font-semibold",
                                                            children: "Recent"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 941,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 938,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                    className: "p-3 flex gap-2",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            className: "px-2 py-1 bg-[#0161AB] text-white rounded text-xs",
                                                            onClick: ()=>{
                                                                setRuleForm({
                                                                    ...rule
                                                                });
                                                                setShowRuleModal(true);
                                                                setEditingRuleIdx(idx);
                                                            },
                                                            children: "Edit"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 945,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            className: "px-2 py-1 bg-red-500 text-white rounded text-xs",
                                                            onClick: ()=>{
                                                                setRules((rules)=>rules.filter((_, i)=>i !== idx));
                                                            },
                                                            children: "Delete"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 950,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 944,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, rule._id, true, {
                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                            lineNumber: 918,
                                            columnNumber: 17
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 914,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 900,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                        lineNumber: 899,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                lineNumber: 897,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-lg shadow p-0 overflow-x-auto border border-gray-200",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("table", {
                    className: "min-w-full text-sm font-[poppins]",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("thead", {
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                children: weekDays.map((day)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("th", {
                                        className: "p-3 text-center font-semibold text-[#211F54] bg-[#F5F6FA] border-b border-gray-200",
                                        children: day
                                    }, day, false, {
                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                        lineNumber: 966,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                lineNumber: 964,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 963,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tbody", {
                            children: calendar.map((week, rowIdx)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("tr", {
                                    children: week.map((day, colIdx)=>{
                                        if (!day) return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {}, colIdx, false, {
                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                            lineNumber: 979,
                                            columnNumber: 36
                                        }, this);
                                        let isDisabled = false;
                                        const dateStr = `${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                                        // Check for override
                                        const override = dateOverrides[dateStr];
                                        if (override) {
                                            // Override present: show override price/currency, bg-gray-200, no border, cross icon
                                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                                className: "p-0 text-center align-top ",
                                                style: {
                                                    minWidth: 120,
                                                    height: 90
                                                },
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex flex-col items-center justify-center h-full py-2 bg-gray-200 rounded-lg relative",
                                                    style: {
                                                        border: 'none',
                                                        position: 'relative'
                                                    },
                                                    onClick: ()=>!isDisabled && handleCellClick(day),
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                            className: "absolute top-1 right-1 text-xs text-gray-500 hover:text-red-500 bg-white rounded-full p-0.5 border border-gray-300 z-10",
                                                            style: {
                                                                lineHeight: 1,
                                                                fontSize: 14
                                                            },
                                                            title: "Remove override",
                                                            onClick: (e)=>{
                                                                e.stopPropagation();
                                                                handleRemoveOverride(day);
                                                            },
                                                            children: "×"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 998,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "font-normal text-xl text-center",
                                                            children: String(day).padStart(2, "0")
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 1009,
                                                            columnNumber: 27
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "flex items-center gap-2 mt-2",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-xs font-semibold",
                                                                style: {
                                                                    color: '#888'
                                                                },
                                                                children: (override.currency === "USD" ? "US$" : "J$") + " " + override.price
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                lineNumber: 1013,
                                                                columnNumber: 29
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 1012,
                                                            columnNumber: 27
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 992,
                                                    columnNumber: 25
                                                }, this)
                                            }, colIdx, false, {
                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                lineNumber: 987,
                                                columnNumber: 23
                                            }, this);
                                        }
                                        // Highlight logic (existing)
                                        const ruleForDay = getRuleForDateWithUpdatedAt(rules, calendarYear, calendarMonth, day);
                                        const highlightStyle = ruleForDay ? {
                                            border: `2px solid ${ruleForDay.color}`,
                                            color: ruleForDay.color,
                                            fontWeight: 600
                                        } : {};
                                        // Show rule price if present, else base price
                                        const priceToShow = ruleForDay ? ruleForDay.price : basePrice;
                                        const currencyToShow = ruleForDay ? ruleForDay.currency : baseCurrency;
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("td", {
                                            className: "p-0 text-center align-top ",
                                            style: {
                                                minWidth: 120,
                                                height: 90
                                            },
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex flex-col items-center justify-center h-full py-2 border border-gray-100 rounded-lg relative",
                                                style: ("TURBOPACK compile-time falsy", 0) ? ("TURBOPACK unreachable", undefined) : highlightStyle,
                                                onClick: ()=>!isDisabled && handleCellClick(day),
                                                children: [
                                                    ruleForDay && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "absolute truncate max-w-[100px] left-1 top-1 text-[10px] font-semibold px-1 rounded",
                                                        style: {
                                                            background: '#fff',
                                                            color: ruleForDay.color,
                                                            border: `1px solid ${ruleForDay.color}`,
                                                            zIndex: 2
                                                        },
                                                        title: ruleForDay.name,
                                                        children: ruleForDay.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                        lineNumber: 1052,
                                                        columnNumber: 27
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "font-normal text-xl text-center",
                                                        children: String(day).padStart(2, "0")
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                        lineNumber: 1060,
                                                        columnNumber: 25
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center gap-2 mt-2",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-xs font-semibold",
                                                            style: ruleForDay ? {
                                                                color: ruleForDay.color
                                                            } : {
                                                                color: '#888'
                                                            },
                                                            children: (currencyToShow === "USD" ? "US$" : "J$") + " " + priceToShow
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 1064,
                                                            columnNumber: 27
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                        lineNumber: 1063,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                lineNumber: 1041,
                                                columnNumber: 23
                                            }, this)
                                        }, colIdx, false, {
                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                            lineNumber: 1036,
                                            columnNumber: 21
                                        }, this);
                                    })
                                }, rowIdx, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 977,
                                    columnNumber: 15
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 975,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                    lineNumber: 962,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                lineNumber: 961,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                className: `w-full py-4 rounded-full mt-8 font-semibold text-lg ${canSave ? "bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white cursor-pointer" : "bg-gray-500 text-white cursor-not-allowed"}`,
                disabled: !canSave,
                onClick: handleApiSave,
                children: isSaving ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                    className: "flex items-center justify-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                            className: "animate-spin h-6 w-6 mr-2 text-white",
                            xmlns: "http://www.w3.org/2000/svg",
                            fill: "none",
                            viewBox: "0 0 24 24",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("circle", {
                                    className: "opacity-25",
                                    cx: "12",
                                    cy: "12",
                                    r: "10",
                                    stroke: "currentColor",
                                    strokeWidth: "4"
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 1092,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                    className: "opacity-75",
                                    fill: "currentColor",
                                    d: "M4 12a8 8 0 018-8v8z"
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 1093,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 1091,
                            columnNumber: 13
                        }, this),
                        "Saving..."
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                    lineNumber: 1090,
                    columnNumber: 11
                }, this) : 'Save'
            }, void 0, false, {
                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                lineNumber: 1080,
                columnNumber: 7
            }, this),
            showSetPrice && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 flex items-center justify-center z-50",
                style: {
                    background: "rgba(0, 0, 0, 0.40)"
                },
                onClick: ()=>setShowSetPrice(false),
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white rounded-2xl shadow-lg p-8 w-full max-w-sm relative border border-gray-100",
                    onClick: (e)=>e.stopPropagation(),
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-lg text-left pb-2 font-[poppins]",
                            children: [
                                selectedCell.day && dateOverrides[`${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(selectedCell.day).padStart(2, '0')}`] ? "Update" : "Set",
                                " ",
                                "Price",
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-[#E1020C]",
                                    children: "*"
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 1118,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 1113,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center w-full bg-white border border-gray-200 rounded-lg overflow-hidden mb-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                    className: "appearance-none bg-transparent px-4 py-3 font-[poppins] focus:outline-none border-none border-r border-gray-200 text-[#211F54]",
                                    value: modalCurrency,
                                    style: {
                                        cursor: 'not-allowed'
                                    },
                                    disabled: true,
                                    onChange: (e)=>setModalCurrency(e.target.value),
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                        value: "JMD",
                                        children: "JMD"
                                    }, void 0, false, {
                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                        lineNumber: 1128,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 1121,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "number",
                                    min: "1",
                                    max: "9999999999",
                                    className: "flex-1 px-4 py-3 ml-4 font-[poppins] bg-transparent focus:outline-none border-l border-gray-200 text-[#211F54]",
                                    value: modalPrice,
                                    onChange: (e)=>{
                                        const val = e.target.value;
                                        // Only allow numbers, no leading zero, max 10 digits, no zero
                                        if (/^([1-9][0-9]{0,9})?$/.test(val)) {
                                            setModalPrice(val);
                                        }
                                    },
                                    onKeyDown: (e)=>{
                                        if ([
                                            "e",
                                            "E",
                                            "+",
                                            "-"
                                        ].includes(e.key)) {
                                            e.preventDefault();
                                        }
                                    },
                                    placeholder: "Enter price"
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 1130,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 1120,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: `w-full py-3 rounded-full text-lg ${!modalPrice ? 'bg-gray-400 text-white cursor-not-allowed' : 'bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white cursor-pointer'}`,
                            onClick: handleConfirm,
                            disabled: !modalPrice,
                            children: selectedCell.day && dateOverrides[`${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(selectedCell.day).padStart(2, '0')}`] ? "Update" : "Set"
                        }, void 0, false, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 1151,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                    lineNumber: 1109,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                lineNumber: 1103,
                columnNumber: 9
            }, this),
            showRuleModal && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-2 fixed flex-col inset-0 flex items-center justify-center z-50 ",
                style: {
                    background: "rgba(0, 0, 0, 0.80)"
                },
                onClick: ()=>{
                    setShowRuleModal(false), setRuleForm({
                        name: "",
                        enabled: true,
                        startDate: "",
                        endDate: "",
                        price: null,
                        currency: "",
                        priority: null,
                        color: ""
                    });
                },
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    onClick: (e)=>e.stopPropagation(),
                    className: " b w-[643px] h-fit min-w-[200px] rounded-[16.87px] border border-[#F8F9FA] bg-white shadow-[0px_3.374px_16.87px_0px_rgba(238,238,238,0.50)] p-8 relative ",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            className: " absolute top-2 right-2 text-2xl",
                            onClick: ()=>setShowRuleModal(false),
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$icons$2f$md$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["MdClose"], {}, void 0, false, {
                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                lineNumber: 1197,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 1193,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: " flex flex-col items-start gap-[10px] flex-shrink-0  p-[20px] pt-[20px] pb-[20px] pl-[16px] pr-[16px] rounded-[16px] border border-[#EFF1F4] bg-white ",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center justify-between mb-4 w-full",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center justify-end gap-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-sm text-gray-500 mr-2",
                                                children: "Enabled"
                                            }, void 0, false, {
                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                lineNumber: 1210,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "button",
                                                "aria-pressed": ruleForm.enabled,
                                                onClick: ()=>setRuleForm((f)=>({
                                                            ...f,
                                                            enabled: !f.enabled
                                                        })),
                                                className: `
                      relative w-10 h-6 transition-colors duration-300
                      rounded-full focus:outline-none border-0
                      ${ruleForm.enabled ? "bg-gradient-to-r from-[#24194B] to-[#0B5B8C]" : "bg-gray-300"}
                    `,
                                                style: {
                                                    minWidth: "40px",
                                                    minHeight: "24px",
                                                    boxShadow: ruleForm.enabled ? "0 0 0 2px #0B5B8C22" : undefined,
                                                    padding: 0
                                                },
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: `
                        absolute top-0.5 left-0.5 transition-all duration-300
                        w-5 h-5 rounded-full bg-white
                        shadow
                        ${ruleForm.enabled ? "translate-x-4" : "translate-x-0"}
                      `,
                                                    style: {
                                                        boxShadow: "0 1px 4px 0 rgba(0,0,0,0.10)"
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 1235,
                                                    columnNumber: 21
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                lineNumber: 1211,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                        lineNumber: 1209,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 1208,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    className: "block font-[poppins]  text-[#211F54] text-sm",
                                    children: [
                                        "Rule Name",
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "text-[#E1020C]",
                                            children: "*"
                                        }, void 0, false, {
                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                            lineNumber: 1250,
                                            columnNumber: 26
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 1249,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    className: "w-full border text-sm border-gray-200 rounded-lg px-4 py-3 font-[poppins] bg-white mb-4",
                                    placeholder: "Enter Rule name",
                                    value: ruleForm.name,
                                    maxLength: 100,
                                    onChange: (e)=>setRuleForm((f)=>({
                                                ...f,
                                                name: e.target.value
                                            }))
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 1253,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mb-4 flex gap-4 w-full",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    className: "block font-[poppins] mb-1 text-[#211F54] text-sm",
                                                    children: [
                                                        "Start Date",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-[#E1020C]",
                                                            children: "*"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 1265,
                                                            columnNumber: 31
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 1264,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                    type: "date",
                                                    className: "w-full border text-sm border-gray-200 rounded-lg px-4 py-3 font-[poppins] bg-white",
                                                    value: ruleForm.startDate,
                                                    // min={todayStr}
                                                    onChange: (e)=>{
                                                        setRuleForm((f)=>({
                                                                ...f,
                                                                startDate: e.target.value,
                                                                endDate: ""
                                                            }));
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 1267,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                            lineNumber: 1263,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    className: "block font-[poppins] mb-1 text-[#211F54] text-sm",
                                                    children: [
                                                        "End Date",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-[#E1020C]",
                                                            children: "*"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 1279,
                                                            columnNumber: 29
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 1278,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                    type: "date",
                                                    className: "w-full border text-sm border-gray-200 rounded-lg px-4 py-3 font-[poppins] bg-white",
                                                    value: ruleForm.endDate,
                                                    min: ruleForm.startDate ? new Date(new Date(ruleForm.startDate).getTime() + 24 * 60 * 60 * 1000).toISOString().split('T')[0] : todayStr,
                                                    onChange: (e)=>{
                                                        const val = e.target.value;
                                                        if (ruleForm.startDate && val <= ruleForm.startDate) {
                                                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].warn('End date must be after start date');
                                                            return;
                                                        }
                                                        setRuleForm((f)=>({
                                                                ...f,
                                                                endDate: val
                                                            }));
                                                    }
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 1281,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                            lineNumber: 1277,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 1262,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mb-4 w-full",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            className: "block font-[poppins] mb-1 text-[#211F54] text-sm",
                                            children: [
                                                "Set price",
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-[#E1020C]",
                                                    children: "*"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 1299,
                                                    columnNumber: 28
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                            lineNumber: 1298,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex items-center w-full bg-white border border-gray-200 rounded-lg overflow-hidden",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                                    className: "bg-transparent px-4 py-3 font-[poppins] focus:outline-none border-none text-[#211F54]",
                                                    value: ruleForm.currency,
                                                    disabled: true,
                                                    style: {
                                                        cursor: "not-allowed"
                                                    },
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                        value: "JMD",
                                                        children: "J$"
                                                    }, void 0, false, {
                                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                        lineNumber: 1312,
                                                        columnNumber: 34
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 1302,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                    type: "number",
                                                    min: "0",
                                                    maxLength: 10,
                                                    className: "flex-1 px-4 text-sm py-3 ml-4 font-[poppins] bg-transparent focus:outline-none border-l border-gray-200 text-[#211F54]",
                                                    value: ruleForm.price,
                                                    onChange: (e)=>{
                                                        const val = e.target.value;
                                                        if (/^\d{0,10}$/.test(val)) {
                                                            setRuleForm((f)=>({
                                                                    ...f,
                                                                    price: val
                                                                }));
                                                        }
                                                    },
                                                    placeholder: "Enter price"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 1315,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                            lineNumber: 1301,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 1297,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mb-4 flex gap-4 items-center w-full ",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    className: "block font-[poppins] mb-1 text-[#211F54] text-sm",
                                                    children: "Set Priority"
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 1333,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center gap-2 w-full",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                                        className: "w-full border border-gray-200 rounded-lg px-2 py-2 font-[poppins] bg-white",
                                                        value: ruleForm.priority || '',
                                                        onChange: (e)=>{
                                                            setRuleForm((f)=>({
                                                                    ...f,
                                                                    priority: e.target.value
                                                                }));
                                                        },
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                className: "text-sm border-gray-200",
                                                                value: "",
                                                                children: "Select priority"
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                lineNumber: 1344,
                                                                columnNumber: 23
                                                            }, this),
                                                            [
                                                                ...Array(10)
                                                            ].map((_, i)=>{
                                                                const val = (i + 1) * 10;
                                                                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                                    value: val,
                                                                    children: val
                                                                }, val, false, {
                                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                    lineNumber: 1348,
                                                                    columnNumber: 27
                                                                }, this);
                                                            })
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                        lineNumber: 1337,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 1336,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                            lineNumber: 1332,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex-1",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                    className: "block font-[poppins] mb-1 text-[#211F54] text-sm",
                                                    children: [
                                                        "Choose Colour",
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-[#E1020C]",
                                                            children: "*"
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 1356,
                                                            columnNumber: 34
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 1355,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "flex items-center w-full",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                                type: "color",
                                                                value: ruleForm.color,
                                                                onChange: (e)=>setRuleForm((f)=>({
                                                                            ...f,
                                                                            color: e.target.value
                                                                        })),
                                                                className: "flex-1 cursor-pointer h-[48px] border border-gray-200 rounded-l-lg  font-[poppins] bg-white text-sm",
                                                                style: {
                                                                    backgroundColor: ruleForm.color,
                                                                    color: "#211F54",
                                                                    transition: "background 0.2s",
                                                                    fontWeight: 500
                                                                }
                                                            }, void 0, false, {
                                                                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                                lineNumber: 1360,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 1359,
                                                            columnNumber: 21
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                            type: "text",
                                                            value: ruleForm.color,
                                                            placeholder: "Select color",
                                                            onChange: (e)=>setRuleForm((f)=>({
                                                                        ...f,
                                                                        color: e.target.value
                                                                    })),
                                                            className: "flex-1 border border-gray-200 rounded-r-lg px-2 h-[48px] font-[poppins] bg-white text-sm placeholder:text-sm placeholder:border-gray-200",
                                                            style: {
                                                                borderLeft: "none"
                                                            }
                                                        }, void 0, false, {
                                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                            lineNumber: 1375,
                                                            columnNumber: 21
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                                    lineNumber: 1358,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                            lineNumber: 1354,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 1331,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 1200,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-end gap-4 mt-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    className: "px-6 py-2 rounded border border-gray-300 bg-white text-gray-700 font-semibold",
                                    onClick: ()=>{
                                        setShowRuleModal(false);
                                        setRuleForm({
                                            name: "",
                                            enabled: true,
                                            startDate: "",
                                            endDate: "",
                                            price: null,
                                            currency: "",
                                            priority: null,
                                            color: ""
                                        });
                                        setEditingRuleIdx(null);
                                    },
                                    children: "Cancel"
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 1405,
                                    columnNumber: 15
                                }, this),
                                editingRuleIdx !== null ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    className: `px-6 py-2 rounded font-semibold ${isRuleFormValid ? 'bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white' : 'bg-gray-400 text-white cursor-not-allowed'}`,
                                    disabled: !isRuleFormValid,
                                    onClick: ()=>{
                                        if (!isRuleFormValid) return;
                                        const duplicateKey = findDuplicateRule(rules, ruleForm, editingRuleIdx);
                                        if (duplicateKey) {
                                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].warn(`Rule already exists with ${duplicateKey}`);
                                            return;
                                        }
                                        setRules((rules)=>{
                                            const updated = rules.map((r, i)=>i === editingRuleIdx ? {
                                                    ...ruleForm,
                                                    priority: ruleForm.priority ? Number(ruleForm.priority) : null,
                                                    updatedAt: new Date().toISOString()
                                                } : r);
                                            setTimeout(()=>{
                                                setShowRuleModal(false);
                                                setRuleForm({
                                                    name: "",
                                                    enabled: true,
                                                    startDate: "",
                                                    endDate: "",
                                                    price: null,
                                                    currency: "",
                                                    priority: null,
                                                    color: ""
                                                });
                                                setEditingRuleIdx(null);
                                                setCalendarRefresh((v)=>v + 1);
                                            }, 0);
                                            return updated;
                                        });
                                    },
                                    children: "Update Rule"
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 1425,
                                    columnNumber: 17
                                }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    className: `px-6 py-2 rounded font-semibold ${isRuleFormValid ? 'bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white' : 'bg-gray-400 text-white cursor-not-allowed'}`,
                                    disabled: !isRuleFormValid,
                                    onClick: ()=>{
                                        if (!isRuleFormValid) return;
                                        const duplicateKey = findDuplicateRule(rules, ruleForm, null);
                                        if (duplicateKey) {
                                            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$toastify$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toast"].warn(`Rule already exists with ${duplicateKey}`);
                                            return;
                                        }
                                        setRules((rules)=>{
                                            const updated = [
                                                ...rules,
                                                {
                                                    ...ruleForm,
                                                    priority: ruleForm.priority ? Number(ruleForm.priority) : null,
                                                    updatedAt: new Date().toISOString()
                                                }
                                            ];
                                            setTimeout(()=>{
                                                setShowRuleModal(false);
                                                setRuleForm({
                                                    name: "",
                                                    enabled: true,
                                                    startDate: "",
                                                    endDate: "",
                                                    price: null,
                                                    currency: "",
                                                    priority: null,
                                                    color: ""
                                                });
                                                setCalendarRefresh((v)=>v + 1);
                                            }, 0);
                                            return updated;
                                        });
                                    },
                                    children: "Save Rule"
                                }, void 0, false, {
                                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                                    lineNumber: 1467,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                            lineNumber: 1404,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                    lineNumber: 1180,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
                lineNumber: 1165,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/app/ad-management/(tabs)/manage-ad-pricing/page.js",
        lineNumber: 790,
        columnNumber: 5
    }, this);
}
_s(ManageAdPricingPage, "wBFipfYHtwKGUEBkylXHMxDFxHo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = ManageAdPricingPage;
function formatJMD(price) {
    if (price === null || price === undefined) return 'N/A';
    return new Intl.NumberFormat('en-JM', {
        style: 'currency',
        currency: 'JMD'
    }).format(price);
}
// Custom getRuleForDate for calendar
function getRuleForDateWithUpdatedAt(rules, year, month, day) {
    const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    const matching = rules.filter((rule)=>{
        if (rule.isActive === false && rule.enabled === false) return false;
        if (!rule.startDate || !rule.endDate) return false;
        const start = rule.startDate;
        const end = rule.endDate;
        return start <= dateStr && dateStr <= end;
    });
    if (matching.length === 0) return null;
    matching.sort((a, b)=>{
        if (Number(b.priority) !== Number(a.priority)) {
            return Number(b.priority) - Number(a.priority);
        }
        return new Date(b.updatedAt) - new Date(a.updatedAt);
    });
    return matching[0];
}
var _c;
__turbopack_context__.k.register(_c, "ManageAdPricingPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=app_306be44f._.js.map