{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/utils/axiosError.handler.js"], "sourcesContent": ["import { toast } from \"react-toastify\";\r\n// import history from \"./history\";\r\nimport { removeToken } from \"../utils/utils\";\r\n\r\n\r\nexport const axiosErrorHandler = (error, action, checkUnauthorized = true) => {\r\n\r\n    console.log(\"error\",error)\r\n    const requestStatus = error?.request?.status;\r\n    const responseStatus = error?.response?.status;\r\n    const dataStatus = error?.data?.statusCode;\r\n\r\n    // Only log out on true 401 Unauthorized from response\r\n    if (responseStatus === 401) {\r\n        removeToken();\r\n        if (typeof window !== 'undefined' && window.location) {\r\n            window.location.href = \"/login\";\r\n        }\r\n        return;\r\n    }\r\n    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {\r\n        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map(er => toast.error(er.messages)) || error?.data?.error?.map(er => toast.error(er))\r\n        else\r\n            toast.error(\r\n                error?.response?.data?.error || error?.response?.data?.error || error?.data?.error,\r\n            );\r\n    }\r\n    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {\r\n        console.log(\"error log is\", error)\r\n        if (Array.isArray(error?.response?.data?.message) || Array?.isArray(error?.data?.message)) error?.response?.data?.message?.map(er => toast.error(er)) || error?.data?.message?.map(er => toast.error(er))\r\n        else\r\n            toast.error(\r\n                error?.response?.data?.message || error?.response?.data?.data || error?.data?.message,\r\n            );\r\n    }\r\n    if (\r\n        checkUnauthorized &&\r\n        (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)\r\n    ) {\r\n        if (localStorage.getItem(\"token\")) {\r\n            toast.error(error?.response?.data?.message);\r\n        }\r\n    }\r\n\r\n    if (action === \"uploadImage\") {\r\n        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {\r\n            if (localStorage.getItem(\"token\")) {\r\n                const message = error?.response?.data?.message;\r\n                message && toast.error(message);\r\n            } else history.push(\"/\");\r\n        }\r\n    }\r\n\r\n    if (error?.response) return error.response;\r\n    else if (error?.request) return error.request;\r\n    else return error?.message;\r\n};"], "names": [], "mappings": ";;;AAAA;AACA,mCAAmC;AACnC;;;AAGO,MAAM,oBAAoB,CAAC,OAAO,QAAQ,oBAAoB,IAAI;IAErE,QAAQ,GAAG,CAAC,SAAQ;IACpB,MAAM,gBAAgB,OAAO,SAAS;IACtC,MAAM,iBAAiB,OAAO,UAAU;IACxC,MAAM,aAAa,OAAO,MAAM;IAEhC,sDAAsD;IACtD,IAAI,mBAAmB,KAAK;QACxB,CAAA,GAAA,wHAAA,CAAA,cAAW,AAAD;QACV,IAAI,aAAkB,eAAe,OAAO,QAAQ,EAAE;YAClD,OAAO,QAAQ,CAAC,IAAI,GAAG;QAC3B;QACA;IACJ;IACA,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,KAAK;QACvE,IAAI,MAAM,OAAO,CAAC,OAAO,UAAU,MAAM,UAAU,OAAO,QAAQ,OAAO,MAAM,QAAQ,OAAO,UAAU,MAAM,OAAO,IAAI,CAAA,KAAM,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,GAAG,QAAQ,MAAM,OAAO,MAAM,OAAO,IAAI,CAAA,KAAM,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;aAElM,sJAAA,CAAA,QAAK,CAAC,KAAK,CACP,OAAO,UAAU,MAAM,SAAS,OAAO,UAAU,MAAM,SAAS,OAAO,MAAM;IAEzF;IACA,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,OAAO,kBAAkB,KAAK;QAChG,QAAQ,GAAG,CAAC,gBAAgB;QAC5B,IAAI,MAAM,OAAO,CAAC,OAAO,UAAU,MAAM,YAAY,OAAO,QAAQ,OAAO,MAAM,UAAU,OAAO,UAAU,MAAM,SAAS,IAAI,CAAA,KAAM,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,QAAQ,OAAO,MAAM,SAAS,IAAI,CAAA,KAAM,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;aAEjM,sJAAA,CAAA,QAAK,CAAC,KAAK,CACP,OAAO,UAAU,MAAM,WAAW,OAAO,UAAU,MAAM,QAAQ,OAAO,MAAM;IAE1F;IACA,IACI,qBACA,CAAC,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,GAAG,GACxE;QACE,IAAI,aAAa,OAAO,CAAC,UAAU;YAC/B,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,UAAU,MAAM;QACvC;IACJ;IAEA,IAAI,WAAW,eAAe;QAC1B,IAAI,eAAe,OAAO,mBAAmB,OAAO,kBAAkB,KAAK;YACvE,IAAI,aAAa,OAAO,CAAC,UAAU;gBAC/B,MAAM,UAAU,OAAO,UAAU,MAAM;gBACvC,WAAW,sJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YAC3B,OAAO,QAAQ,IAAI,CAAC;QACxB;IACJ;IAEA,IAAI,OAAO,UAAU,OAAO,MAAM,QAAQ;SACrC,IAAI,OAAO,SAAS,OAAO,MAAM,OAAO;SACxC,OAAO,OAAO;AACvB", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/service/axios.js"], "sourcesContent": ["const { default: axios } = require(\"axios\");\r\nconst { getToken } = require(\"../utils/utils\");\r\n\r\nconst BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;\r\n\r\nconst instance = axios.create({\r\n  baseURL: BASE_URL+\"/api\" ,\r\n\r\n  // Lets keep a check as default is 0 millisecond i.e. never\r\n  // Note: timeout is only for server response not network i.e. server reachability\r\n  timeout: 100000,\r\n\r\n  // Lets keep a check as default bytes- 2k\r\n  maxContentLength: 1000,\r\n\r\n  // Lets keep a check as default 5 seems high\r\n  maxRedirects: 2,\r\n});\r\n\r\ninstance.interceptors.request.use(\r\n  (config) => {\r\n    // const token = localStorage.getItem(\"auth\");\r\n    const token = getToken();\r\n    console.log(\"token\", token)\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n\r\n    // Rate limiting: only fire a request every 2 sec from lodash.debounce\r\n    //return new Promise((resolve) => { debounce( () => resolve(config),2000); });\r\n    return Promise.resolve(config);\r\n  },\r\n  function (error) {\r\n    const response = handleLogError(error); // log them\r\n\r\n    return Promise.reject(error);\r\n  }\r\n  // multiple options as to when and how to apply these interceptors\r\n  // , { synchronous: true, runWhen: onGetCall }\r\n);\r\n\r\n\r\nmodule.exports = instance;"], "names": [], "mappings": "AAGiB;AAHjB,MAAM,EAAE,SAAS,KAAK,EAAE;AACxB,MAAM,EAAE,QAAQ,EAAE;AAElB,MAAM;AAEN,MAAM,WAAW,MAAM,MAAM,CAAC;IAC5B,SAAS,WAAS;IAElB,2DAA2D;IAC3D,iFAAiF;IACjF,SAAS;IAET,yCAAyC;IACzC,kBAAkB;IAElB,4CAA4C;IAC5C,cAAc;AAChB;AAEA,SAAS,YAAY,CAAC,OAAO,CAAC,GAAG,CAC/B,CAAC;IACC,8CAA8C;IAC9C,MAAM,QAAQ;IACd,QAAQ,GAAG,CAAC,SAAS;IACrB,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IAEA,sEAAsE;IACtE,8EAA8E;IAC9E,OAAO,QAAQ,OAAO,CAAC;AACzB,GACA,SAAU,KAAK;IACb,MAAM,WAAW,eAAe,QAAQ,WAAW;IAEnD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAMF,OAAO,OAAO,GAAG", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/service/adManagement.js"], "sourcesContent": ["const { axiosErrorHandler } = require(\"../utils/axiosError.handler\");\r\nconst instance = require(\"./axios\");\r\n\r\nlet uri = {\r\n    //create\r\n  createAdPricingRule: \"/ad-management\",\r\n  createIndividualPricingRuleInResource : \"/ad-management/priceRule\",\r\n  createOverridePricingRuleInResource : \"/ad-management/overrideRule\",\r\n\r\n  //get\r\n  fetchAdPricingRules: \"/ad-management\", // fetch all the resource\r\n  getAdPricingRule_byId: \"/ad-management\",  // fetch single resource\r\n\r\n  // update\r\n  updateBasePrice: \"/ad-management/basePrice\",  // update only base price of any resource\r\n  updateIndividualRule: \"/ad-management/priceRule\",  // update any single rule of resource\r\n\r\n  // delete\r\n  deleteResource: \"/ad-management\",\r\n  delete_AddRule: \"/ad-management\",\r\n  deleteIndividualRule: \"/ad-management/priceRule\",\r\n  deleteOverrightRuleDate: \"/ad-management/overrideRule\",\r\n};\r\n\r\n// Create a new ad\r\nexport const createAdPricingRule = async (data) => {\r\n  let response = await instance\r\n    .post(uri.createAdPricingRule, data)\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n\r\nexport const createIndividualPricingRuleInResource = async (data) => {\r\n    let response = await instance\r\n      .post(uri.createIndividualPricingRuleInResource, data)\r\n      .catch(axiosErrorHandler);\r\n    return response;\r\n  };\r\n\r\n  \r\n\r\n  export const createOverridePricingRuleInResource = async (data) => {\r\n    let response = await instance\r\n      .post(uri.createOverridePricingRuleInResource, data)\r\n      .catch(axiosErrorHandler);\r\n    return response;\r\n  };\r\n\r\n\r\n// Get all ads (optionally with filters in data)\r\nexport const fetchAdPricingRules = async () => {\r\n  let response = await instance\r\n    .get(uri.fetchAdPricingRules)\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n\r\n// Get ad by ID\r\nexport const getAdPricingRuleById = async (id) => {\r\n  let response = await instance\r\n    .get(`${uri.getAdPricingRule_byId}/${id}`)\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n\r\nexport const updateBasePrice = async (data) => {\r\n  let response = await instance\r\n    .put(`${uri.updateBasePrice}`, data)\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n\r\n// Update ad by ID\r\nexport const updateIndividualRule = async (data) => {\r\n  let response = await instance\r\n    .put(`${uri.updateIndividualRule}`, data)\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n\r\nexport const deleteResource = async (id) => {\r\n  let response = await instance\r\n    .delete(`${uri.deleteResource}/${id}`)\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n\r\nexport const delete_AddRule = async (id, data) => {\r\n  let response = await instance\r\n    .delete(`${uri.delete_AddRule}/${id}`, data)\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n\r\nexport const deleteIndividualRule = async (data) => {\r\n  let response = await instance\r\n    .delete(`${uri.deleteIndividualRule}`, { data })\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n\r\nexport const deleteOverrightRuleDate = async (data) => {\r\n  let response = await instance\r\n    .delete(`${uri.deleteOverrightRuleDate}`, { data })\r\n    .catch(axiosErrorHandler);\r\n  return response;\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,MAAM,EAAE,iBAAiB,EAAE;AAC3B,MAAM;AAEN,IAAI,MAAM;IACN,QAAQ;IACV,qBAAqB;IACrB,uCAAwC;IACxC,qCAAsC;IAEtC,KAAK;IACL,qBAAqB;IACrB,uBAAuB;IAEvB,SAAS;IACT,iBAAiB;IACjB,sBAAsB;IAEtB,SAAS;IACT,gBAAgB;IAChB,gBAAgB;IAChB,sBAAsB;IACtB,yBAAyB;AAC3B;AAGO,MAAM,sBAAsB,OAAO;IACxC,IAAI,WAAW,MAAM,SAClB,IAAI,CAAC,IAAI,mBAAmB,EAAE,MAC9B,KAAK,CAAC;IACT,OAAO;AACT;AAEO,MAAM,wCAAwC,OAAO;IACxD,IAAI,WAAW,MAAM,SAClB,IAAI,CAAC,IAAI,qCAAqC,EAAE,MAChD,KAAK,CAAC;IACT,OAAO;AACT;AAIO,MAAM,sCAAsC,OAAO;IACxD,IAAI,WAAW,MAAM,SAClB,IAAI,CAAC,IAAI,mCAAmC,EAAE,MAC9C,KAAK,CAAC;IACT,OAAO;AACT;AAIK,MAAM,sBAAsB;IACjC,IAAI,WAAW,MAAM,SAClB,GAAG,CAAC,IAAI,mBAAmB,EAC3B,KAAK,CAAC;IACT,OAAO;AACT;AAGO,MAAM,uBAAuB,OAAO;IACzC,IAAI,WAAW,MAAM,SAClB,GAAG,CAAC,GAAG,IAAI,qBAAqB,CAAC,CAAC,EAAE,IAAI,EACxC,KAAK,CAAC;IACT,OAAO;AACT;AAEO,MAAM,kBAAkB,OAAO;IACpC,IAAI,WAAW,MAAM,SAClB,GAAG,CAAC,GAAG,IAAI,eAAe,EAAE,EAAE,MAC9B,KAAK,CAAC;IACT,OAAO;AACT;AAGO,MAAM,uBAAuB,OAAO;IACzC,IAAI,WAAW,MAAM,SAClB,GAAG,CAAC,GAAG,IAAI,oBAAoB,EAAE,EAAE,MACnC,KAAK,CAAC;IACT,OAAO;AACT;AAEO,MAAM,iBAAiB,OAAO;IACnC,IAAI,WAAW,MAAM,SAClB,MAAM,CAAC,GAAG,IAAI,cAAc,CAAC,CAAC,EAAE,IAAI,EACpC,KAAK,CAAC;IACT,OAAO;AACT;AAEO,MAAM,iBAAiB,OAAO,IAAI;IACvC,IAAI,WAAW,MAAM,SAClB,MAAM,CAAC,GAAG,IAAI,cAAc,CAAC,CAAC,EAAE,IAAI,EAAE,MACtC,KAAK,CAAC;IACT,OAAO;AACT;AAEO,MAAM,uBAAuB,OAAO;IACzC,IAAI,WAAW,MAAM,SAClB,MAAM,CAAC,GAAG,IAAI,oBAAoB,EAAE,EAAE;QAAE;IAAK,GAC7C,KAAK,CAAC;IACT,OAAO;AACT;AAEO,MAAM,0BAA0B,OAAO;IAC5C,IAAI,WAAW,MAAM,SAClB,MAAM,CAAC,GAAG,IAAI,uBAAuB,EAAE,EAAE;QAAE;IAAK,GAChD,KAAK,CAAC;IACT,OAAO;AACT", "debugId": null}}, {"offset": {"line": 189, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/OneDrive/Desktop/Akshay/LOOP_PROJECTS/REEBOKIT/rebookit_frontend_admin/app/ad-management/%28tabs%29/manage-ad-pricing/page.js"], "sourcesContent": ["\"use client\";\nimport { useState, useEffect } from \"react\";\nimport { CiEdit } from \"react-icons/ci\";\nimport { MdClose } from \"react-icons/md\";\nimport { toast } from 'react-toastify';\nimport { createAdPricingRule, fetchAdPricingRules, deleteResource } from '../../../service/adManagement';\nimport { useRouter } from 'next/navigation';\n\n// --- Helpers for Calendar ---\nconst weekDays = [\n  \"Monday\",\n  \"Tuesday\",\n  \"Wednesday\",\n  \"Thursday\",\n  \"Friday\",\n  \"Saturday\",\n  \"Sunday\",\n];\nconst defaultCurrency = \"JMD\";\nconst defaultPrice = \"30000\";\n\n// Dummy dataset simulating API/database response\nconst dummyCalendarDB = {\n  \"2025-07\": [\n    // Each entry: { day: 1, price: \"100\", currency: \"JMD\" }\n    { day: 5, price: \"200\", currency: \"USD\" },\n    { day: 10, price: \"150\", currency: \"JMD\" },\n    { day: 20, price: \"300\", currency: \"USD\" },\n  ],\n  \"2025-08\": [\n    { day: 1, price: \"120\", currency: \"JMD\" },\n    { day: 15, price: \"250\", currency: \"USD\" },\n  ],\n  // ...add more months as needed\n};\n\n// Helper to get number of days in a month\nfunction getDaysInMonth(year, month) {\n  return new Date(year, month + 1, 0).getDate();\n}\n\n// Helper to get the first day index (0=Monday, 6=Sunday)\nfunction getFirstDayIndex(year, month) {\n  // JS: 0=Sunday, 6=Saturday; we want 0=Monday\n  let jsDay = new Date(year, month, 1).getDay();\n  return jsDay === 0 ? 6 : jsDay - 1;\n}\n\n// Helper to generate a matrix for the calendar (weeks x 7)\nfunction getCalendarMatrix(year, month) {\n  const daysInMonth = getDaysInMonth(year, month);\n  const firstDayIdx = getFirstDayIndex(year, month);\n  const matrix = [];\n  let day = 1 - firstDayIdx;\n  for (let row = 0; row < 6; row++) {\n    const week = [];\n    for (let col = 0; col < 7; col++) {\n      week.push(day > 0 && day <= daysInMonth ? day : \"\");\n      day++;\n    }\n    matrix.push(week);\n  }\n  return matrix;\n}\n\n// Helper to get week range for a given day (returns [startDay, endDay])\nfunction getWeekRange(year, month, day) {\n  const date = new Date(year, month, day);\n  const jsDay = date.getDay(); // 0=Sunday\n  const mondayOffset = jsDay === 0 ? -6 : 1 - jsDay;\n  const start = new Date(year, month, day + mondayOffset);\n  const end = new Date(start);\n  end.setDate(start.getDate() + 6);\n  return [\n    Math.max(1, start.getDate()),\n    Math.min(getDaysInMonth(year, month), end.getDate()),\n  ];\n}\n\n// Enums for ad page and position types\nconst AdPageTypeEnum = Object.freeze({\n  HOMEPAGE: \"home page\",\n  SELLPAGE: \"sell page\",\n  COMMUNITYPAGE: \"community page\",\n  LISTINGPAGE: \"listing page\",\n});\nconst AdPositionTypeEnum = Object.freeze({\n  TOP: \"top\",\n  MIDDLE: \"middle\",\n  BOTTOM: \"bottom\",\n  NOT_FIXED: \"No fixed position\",\n});\nconst adPageOptions = Object.values(AdPageTypeEnum);\nconst adPositionOptions = Object.values(AdPositionTypeEnum);\n\n// Ad types array for dynamic rendering\nconst adTypes = [\n  {\n    key: \"Banner\",\n    label: \"Banner\",\n    icon: \"/icons/ad-member-banner.svg\",\n  },\n  {\n    key: \"Grid\",\n    label: \"Grid\",\n    icon: \"/icons/ad-member-grid.svg\",\n  },\n  // Add more ad types here as needed\n];\n\n// Helper for duplicate check\nfunction findDuplicateRule(rules, ruleForm, editingRuleIdx) {\n  for (let i = 0; i < rules.length; i++) {\n    if (editingRuleIdx !== null && i === editingRuleIdx) continue;\n    const r = rules[i];\n    if (r.name === ruleForm.name) return 'name';\n    // if (r.priority === ruleForm.priority) return 'priority';\n    if (r.color === ruleForm.color) return 'color';\n    // if (r.startDate === ruleForm.startDate && r.endDate === ruleForm.endDate) return 'date';\n  }\n  return null;\n}\n\n// Helper to get rule for a given date (returns the rule with highest priority if multiple match)\n// Priority logic: higher number = higher priority (e.g., 100 is higher than 10)\nfunction getRuleForDate(rules, year, month, day) {\n  // Convert to yyyy-mm-dd\n  const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;\n  // Filter rules where date is in range and enabled\n  const matching = rules.filter(rule => {\n    if (!rule.enabled) return false;\n    if (!rule.startDate || !rule.endDate) return false;\n    return rule.startDate <= dateStr && dateStr <= rule.endDate;\n  });\n  // If multiple, pick the one with highest priority (largest number)\n  if (matching.length === 0) return null;\n  return matching.reduce((a, b) => {\n    if (a.priority == null) return b;\n    if (b.priority == null) return a;\n    return Number(a.priority) > Number(b.priority) ? a : b;\n  });\n}\n\n// Helper to format date as 'YYYY-MM-DDTHH:mm:ss.SSS+00:00'\nfunction formatDateToApi(dateStr) {\n  if (!dateStr) return '';\n  const d = new Date(dateStr);\n  // Get the offset in minutes and convert to \"+00:00\" format\n  const tzOffset = -d.getTimezoneOffset();\n  const sign = tzOffset >= 0 ? '+' : '-';\n  const pad = n => String(Math.floor(Math.abs(n))).padStart(2, '0');\n  const hours = pad(tzOffset / 60);\n  const minutes = pad(tzOffset % 60);\n  return d.getFullYear() + '-' +\n    pad(d.getMonth() + 1) + '-' +\n    pad(d.getDate()) + 'T' +\n    pad(d.getHours()) + ':' +\n    pad(d.getMinutes()) + ':' +\n    pad(d.getSeconds()) + '.' +\n    String(d.getMilliseconds()).padStart(3, '0') +\n    sign + hours + ':' + minutes;\n}\n\nfunction formatDateTime(dateStr) {\n  if (!dateStr) return '';\n  const d = new Date(dateStr);\n  return d.toLocaleString('en-JM', { dateStyle: 'medium', timeStyle: 'short' });\n}\n\n// --- Main Page Component ---\nexport default function ManageAdPricingPage() {\n  const router = useRouter();\n  const [isSaving, setIsSaving] = useState(false);\n  // UI state\n  const [showCalendar, setShowCalendar] = useState(false);\n\n  // Ad type state\n  const [adType, setAdType] = useState(\"\");\n\n  // Form state\n  const [page, setPage] = useState(\"\");\n  const [placement, setPlacement] = useState(\"\");\n  // Base price state\n  const [baseCurrency, setBaseCurrency] = useState(\"JMD\");\n  const [basePrice, setBasePrice] = useState(\"\");\n\n  // Calendar month/year state\n  const today = new Date();\n  const [calendarYear, setCalendarYear] = useState(today.getFullYear());\n  const [calendarMonth, setCalendarMonth] = useState(today.getMonth()); // 0-indexed\n  const [currency, setCurrency] = useState(defaultCurrency);\n  const [price, setPrice] = useState(defaultPrice);\n\n  // Calendar price data: prefill every day of the current year with base price\n  const [calendarPrices, setCalendarPrices] = useState(() => {\n    const days = Array(getDaysInMonth(calendarYear, calendarMonth)).fill(null);\n    for (let i = 0; i < days.length; i++) {\n      days[i] = { price: basePrice, currency: baseCurrency };\n    }\n    return days;\n  });\n\n  // Modal state for price input\n  const [showSetPrice, setShowSetPrice] = useState(false);\n  const [selectedCell, setSelectedCell] = useState({ day: null });\n  const [modalPrice, setModalPrice] = useState(\"\");\n  const [modalCurrency, setModalCurrency] = useState(defaultCurrency);\n\n  // Track if calendar data is dirty\n  const [calendarDirty, setCalendarDirty] = useState(false);\n\n  // Fetch already created plans to disable existing combos and show actions\n  const [createdPlans, setCreatedPlans] = useState([]);\n  useEffect(() => {\n    (async () => {\n      try {\n        const res = await fetchAdPricingRules();\n        const resources = res?.data?.resources || res?.data || [];\n        if (Array.isArray(resources)) {\n          setCreatedPlans(\n            resources.map((r) => ({\n              page: String(r.page || \"\").toLowerCase(),\n              type: String(r.type || \"\").toLowerCase(),\n              position: String(r.position || \"\").toLowerCase(),\n              _id: r._id,\n            }))\n          );\n        }\n      } catch (e) {}\n    })();\n  }, []);\n\n  const isPlanCreated = (pageName, typeName, positionName = \"\") => {\n    const p = String(pageName || \"\").toLowerCase();\n    const t = String(typeName || \"\").toLowerCase();\n    const pos = String(positionName || \"\").toLowerCase();\n    return createdPlans.some(\n      (cp) => cp.page === p && cp.type === t && (pos ? cp.position === pos : true) && (!pos ? !cp.position || cp.position === \"\" : true)\n    );\n  };\n\n  // Add state for per-date price overrides\n  const [dateOverrides, setDateOverrides] = useState({}); // { 'YYYY-MM-DD': { price, currency } }\n\n  // Next button enable logic\n  const requiresPlacement = page === AdPageTypeEnum.HOMEPAGE || page === AdPageTypeEnum.SELLPAGE;\n  const isNextEnabled = !!(adType && page && basePrice && (requiresPlacement ? placement : true));\n\n  // Step indicator logic\n  const currentStep = showCalendar ? 2 : 1;\n\n  // State for pricing rule modal\n  const [showRuleModal, setShowRuleModal] = useState(false);\n  const [ruleForm, setRuleForm] = useState({\n    name: \"\",\n    enabled: true,\n    startDate: \"\",\n    endDate: \"\",\n    price: basePrice,\n    currency: baseCurrency,\n    priority: null,\n    color: \"\",\n  });\n  const [rules, setRules] = useState([]);\n  // Add state to track which rule is being edited\n  const [editingRuleIdx, setEditingRuleIdx] = useState(null);\n\n  // Dummy state to force calendar re-render\n  const [calendarRefresh, setCalendarRefresh] = useState(0);\n\n  console.log(ruleForm,\"ruleform\", rules)\n  // --- Handlers ---\n\n  // Restrict calendar navigation to current year only\n  const handlePrevMonth = () => {\n    setCalendarMonth((prev) => {\n      if (prev === 0) {\n        return 0; // Don't go to previous year\n      }\n      return prev - 1;\n    });\n    setTimeout(loadMonthData, 0);\n  };\n  const handleNextMonth = () => {\n    setCalendarMonth((prev) => {\n      if (prev === 11) {\n        return 11; // Don't go to next year\n      }\n      return prev + 1;\n    });\n    setTimeout(loadMonthData, 0);\n  };\n\n  // Load month data from dummy DB\n  function loadMonthData() {\n    const key = `${calendarYear}-${String(calendarMonth + 1).padStart(2, \"0\")}`;\n    const monthData = dummyCalendarDB[key] || [];\n    const days = Array(getDaysInMonth(calendarYear, calendarMonth)).fill(null);\n    monthData.forEach(({ day, price, currency }) => {\n      days[day - 1] = { price, currency };\n    });\n    setCalendarPrices(days);\n    setCalendarDirty(false);\n  }\n\n  // Open set price modal, prefill if price exists or override exists\n  const handleCellClick = (day) => {\n    setSelectedCell({ day });\n    const dateStr = `${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;\n    if (dateOverrides[dateStr]) {\n      setModalPrice(dateOverrides[dateStr].price || \"\");\n      setModalCurrency(dateOverrides[dateStr].currency || defaultCurrency);\n    } else {\n      const cell = calendarPrices[day - 1];\n      setModalPrice( \"\");\n      setModalCurrency(cell?.currency || defaultCurrency);\n    }\n    setShowSetPrice(true);\n  };\n\n  // Confirm price change for a cell (override logic)\n  const handleConfirm = () => {\n    if (selectedCell.day !== null) {\n      const dateStr = `${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(selectedCell.day).padStart(2, '0')}`;\n      setDateOverrides((prev) => ({\n        ...prev,\n        [dateStr]: { price: modalPrice, currency: modalCurrency },\n      }));\n      setCalendarDirty(true);\n    }\n    setShowSetPrice(false);\n    setSelectedCell({ day: null });\n    setModalPrice(\"\");\n    setModalCurrency(defaultCurrency);\n  };\n\n  // Remove override for a date\n  const handleRemoveOverride = (day) => {\n    const dateStr = `${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;\n    setDateOverrides((prev) => {\n      const copy = { ...prev };\n      delete copy[dateStr];\n      return copy;\n    });\n    setCalendarDirty(true);\n  };\n\n  // Save calendar changes (simulate API)\n  const handleSaveCalendar = () => {\n    // Here you would send calendarPrices to your API/database\n    setCalendarDirty(false);\n    // Show a toast\n    // toast.success(\"Calendar prices saved successfully!\");\n  };\n\n  // Update calendar prices when base price changes\n  useEffect(() => {\n    if (showCalendar) {\n      const days = Array(getDaysInMonth(calendarYear, calendarMonth)).fill(\n        null\n      );\n      for (let i = 0; i < days.length; i++) {\n        days[i] = { price: basePrice, currency: baseCurrency };\n      }\n      setCalendarPrices(days);\n    }\n  }, [basePrice, baseCurrency, calendarYear, calendarMonth, showCalendar]);\n\n  // Stepper click handlers\n  const handleStepClick = (step) => {\n    if (step === 1) {\n      setShowCalendar(false);\n    } else if (step === 2 && isNextEnabled) {\n      setShowCalendar(true);\n      loadMonthData();\n    }\n  };\n\n  // --- API Save Handler ---\n  const handleApiSave = async () => {\n    setIsSaving(true);\n    // Build pricingRules array\n    const pricingRules = rules.map(rule => {\n      const ruleObj = {\n        name: rule.name,\n        color: rule.color,\n        startDate: rule.startDate ? formatDateToApi(rule.startDate) : '',\n        endDate: rule.endDate ? formatDateToApi(rule.endDate) : '',\n        price: rule.price ? Number(rule.price) : null,\n        isActive: !!rule.enabled,\n      };\n      if (rule.priority !== null && rule.priority !== '' && rule.priority !== undefined) {\n        ruleObj.priority = Number(rule.priority);\n      }\n      return ruleObj;\n    });\n    // Build overrideRules array\n    const overrideRules = Object.entries(dateOverrides).map(([date, val]) => ({\n      date: date ? formatDateToApi(date) : '',\n      price: val.price ? Number(val.price) : null,\n    }));\n    // Build payload\n    const payload = {\n      type: adType ? adType.toLowerCase() : '',\n      page,\n      position: placement,\n      basePrice: basePrice ? String(basePrice) : '',\n      pricingRules: pricingRules.length ? pricingRules : [],\n      overrideRules: overrideRules.length ? overrideRules : [],\n    };\n    try {\n      const res = await createAdPricingRule(payload);\n      if (res && (res.status === 200 || res.status === 201)) {\n        toast.success('Rules saved successfully!');\n        setTimeout(() => {\n          router.push('/ad-management/current-ad-plan');\n        }, 1000);\n      } else {\n        // Error toast is already handled in the service, do not show again\n      }\n    } catch (error) {\n      // Error toast is already handled in the service, do not show again\n    }\n    setIsSaving(false);\n  };\n\n  // Save button enablement: allow if there are pricing rules or overrides, and not saving\n  const canSave = (rules.length > 0 || Object.keys(dateOverrides).length > 0) && !isSaving;\n\n  // --- UI ---\n  if (!showCalendar) {\n    return (\n      <div className=\"p-6 border border-gray-200 shadow-xs rounded-lg\">\n        {/* Stepper */}\n        <div className=\"mb-8 flex items-center gap-4\">\n          <button\n            type=\"button\"\n            className={`w-8 h-8 flex items-center justify-center rounded-full font-bold text-base transition-all duration-200 ${\n              !showCalendar\n                ? \"bg-[#0161AB] text-white\"\n                : \"bg-[#F5F8FE] text-[#0161AB] border border-[#0161AB]\"\n            }`}\n            style={{ outline: \"none\" }}\n            onClick={() => handleStepClick(1)}\n            tabIndex={0}\n          >\n            {isNextEnabled ? (\n              <svg\n                width=\"20\"\n                height=\"20\"\n                viewBox=\"0 0 20 20\"\n                fill=\"none\"\n                xmlns=\"http://www.w3.org/2000/svg\"\n              >\n                <circle cx=\"10\" cy=\"10\" r=\"10\" fill=\"#4D7906\" />\n                <path\n                  d=\"M6 10.5L9 13.5L14 8.5\"\n                  stroke=\"white\"\n                  strokeWidth=\"2\"\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                />\n              </svg>\n            ) : (\n              1\n            )}\n          </button>\n          <span\n            className={`font-[poppins] text-lg ${\n              !showCalendar ? \"text-[#0161AB] font-semibold\" : \"text-gray-400\"\n            }`}\n          >\n            Step 1: Ad Details\n          </span>\n          {/* <div className=\"flex-1 h-0.5 bg-gray-200 mx-2\" /> */}\n          <button\n            type=\"button\"\n            className={`w-8 h-8 flex items-center justify-center rounded-full font-bold text-base transition-all duration-200 ${\n              showCalendar && isNextEnabled\n                ? \"bg-[#0161AB] text-white\"\n                : \"bg-[#F5F8FE] text-[#0161AB] border border-[#0161AB]\"\n            } ${isNextEnabled ? \"cursor-pointer\" : \"cursor-not-allowed\"}`}\n            style={{ outline: \"none\" }}\n            onClick={() => isNextEnabled && handleStepClick(2)}\n            tabIndex={isNextEnabled ? 0 : -1}\n            disabled={!isNextEnabled}\n          >\n            2\n          </button>\n          <span\n            className={`font-[poppins] text-lg ${\n              showCalendar ? \"text-[#0161AB] font-semibold\" : \"text-gray-400\"\n            }`}\n          >\n            Step 2: Calendar Management\n          </span>\n        </div>\n\n        {/* Combo selection cards */}\n        <div className=\"mb-8\">\n          <label className=\"block font-[poppins] mb-2 text-[#211F54]\">\n            Select Ad Placement Combo<span className=\"text-[#E1020C]\">*</span>\n          </label>\n          {/* Home page section */}\n          <div className=\"mb-5\">\n            <div className=\"font-semibold text-[#211F54] mb-2\">Home page</div>\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {[\n                { type: \"Banner\", position: AdPositionTypeEnum.TOP, page: AdPageTypeEnum.HOMEPAGE },\n                { type: \"Grid\", position: AdPositionTypeEnum.MIDDLE, page: AdPageTypeEnum.HOMEPAGE },\n                { type: \"Banner\", position: AdPositionTypeEnum.BOTTOM, page: AdPageTypeEnum.HOMEPAGE },\n              ].map((c) => {\n                const alreadyCreated = isPlanCreated(c.page, c.type, c.position);\n                const plan = createdPlans.find(\n                  (cp) => cp.page === String(c.page).toLowerCase() && cp.type === String(c.type).toLowerCase() && cp.position === String(c.position).toLowerCase()\n                );\n                return (\n                  <div\n                    key={`${c.page}-${c.type}-${c.position}`}\n                    className={`flex-1 border rounded-xl p-6 flex flex-col items-start gap-2 transition-all border-[#0161AB] bg-[#F5F8FE]`}\n                  >\n                    <div className=\"flex gap-2 items-center flex-wrap\">\n                      <span className=\"px-2 py-0.5 rounded-full bg-purple-50 text-purple-700 text-xs\">Type: {c.type}</span>\n                      <span className=\"px-2 py-0.5 rounded-full bg-gray-100 text-gray-700 text-xs\">Position: {c.position}</span>\n                    </div>\n                    {alreadyCreated && (\n                      <span className=\"px-2 py-0.5 rounded-full bg-green-50 text-green-700 text-xs\">Plan already created</span>\n                    )}\n                    <div className=\"mt-3 flex gap-2 w-full\">\n                      {alreadyCreated ? (\n                        <>\n                          <button className=\"px-3 py-1 rounded-md text-xs border border-gray-300\" onClick={() => { if(plan?._id) alert(`View plan id: ${plan._id}`); }}>View</button>\n                          <button className=\"px-3 py-1 rounded-md text-xs border border-gray-300\" onClick={() => { if(plan?._id) alert(`Edit plan id: ${plan._id}`); }}>Edit</button>\n                          <button className=\"px-3 py-1 rounded-md text-xs border border-red-300 text-red-600\" onClick={() => { if(plan?._id) alert(`Delete plan id: ${plan._id}`); }}>Delete</button>\n                        </>\n                      ) : (\n                        <button className=\"px-3 py-1 rounded-md text-xs bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white\" onClick={() => { setPage(c.page); setAdType(c.type); setPlacement(c.position); setShowCalendar(true); setTimeout(loadMonthData, 0); }}>Create Plan</button>\n                      )}\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Sell page section */}\n          <div className=\"mb-5\">\n            <div className=\"font-semibold text-[#211F54] mb-2\">Sell page</div>\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {[\n                { type: \"Banner\", position: AdPositionTypeEnum.BOTTOM, page: AdPageTypeEnum.SELLPAGE },\n              ].map((c) => {\n                const selected = page === c.page && adType === c.type && placement === c.position;\n                const alreadyCreated = isPlanCreated(c.page, c.type, c.position);\n                const plan = createdPlans.find((cp) => cp.page === String(c.page).toLowerCase() && cp.type === String(c.type).toLowerCase() && cp.position === String(c.position).toLowerCase());\n                return (\n                  <button\n                    key={`${c.page}-${c.type}-${c.position}`}\n                    type=\"button\"\n                    disabled={alreadyCreated}\n                    className={`flex-1 border rounded-xl p-6 flex flex-col items-start gap-2 transition-all ${\n                      selected ? \"border-[#0161AB] bg-[#F5F8FE]\" : \"border-gray-200 bg-white\"\n                    } ${alreadyCreated ? \"opacity-60 cursor-not-allowed\" : \"\"}`}\n                    onClick={() => {\n                      if (alreadyCreated) return;\n                      setPage(c.page);\n                      setAdType(c.type);\n                      setPlacement(c.position);\n                    }}\n                  >\n                    <div className=\"flex gap-2 items-center flex-wrap \">\n                      <span className=\"px-2 py-0.5 rounded-full bg-purple-50 text-purple-700 text-xs\">Type: {c.type}</span>\n                      <span className=\"px-2 py-0.5 rounded-full bg-gray-100 text-gray-700 text-xs\">Position: {c.position}</span>\n                    </div>\n                    <div className=\"mt-3 flex gap-2 w-full\">\n                      {alreadyCreated ? (\n                        <>\n                          <button className=\"px-3 py-1 rounded-md text-xs border border-gray-300\" onClick={(e) => { e.stopPropagation(); if(plan?._id) router.push(`/ad-management/current-ad-plan/view/${plan._id}`); }}>View</button>\n                          <button className=\"px-3 py-1 rounded-md text-xs border border-gray-300\" onClick={(e) => { e.stopPropagation(); if(plan?._id) router.push(`/ad-management/current-ad-plan/edit/${plan._id}`); }}>Edit</button>\n                          <button className=\"px-3 py-1 rounded-md text-xs border border-red-300 text-red-600\" onClick={async (e) => { e.stopPropagation(); if(!plan?._id) return; await deleteResource(plan._id); const res = await fetchAdPricingRules(); const resources = res?.data?.resources || res?.data || []; setCreatedPlans(resources.map((r) => ({ page: String(r.page||\"\").toLowerCase(), type: String(r.type||\"\").toLowerCase(), position: String(r.position||\"\").toLowerCase(), _id: r._id }))); }}>Delete</button>\n                        </>\n                      ) : (\n                        <button className=\"px-3 py-1 rounded-md text-xs bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white\" onClick={(e) => { e.stopPropagation(); setPage(c.page); setAdType(c.type); setPlacement(c.position); setShowCalendar(true); setTimeout(loadMonthData, 0); }}>Create Plan</button>\n                      )}\n                    </div>\n                  </button>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Community page section (no fixed placement) */}\n          <div className=\"mb-5\">\n            <div className=\"font-semibold text-[#211F54] mb-2\">Community page</div>\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {[\n                { type: \"Banner\", position: AdPositionTypeEnum.NOT_FIXED, page: AdPageTypeEnum.COMMUNITYPAGE },\n                { type: \"Grid\", position: AdPositionTypeEnum.NOT_FIXED, page: AdPageTypeEnum.COMMUNITYPAGE },\n              ].map((c) => {\n                const selected = page === c.page && adType === c.type && placement === c.position;\n                const alreadyCreated = isPlanCreated(c.page, c.type, c.position);\n                const plan = createdPlans.find((cp) => cp.page === String(c.page).toLowerCase() && cp.type === String(c.type).toLowerCase() && cp.position === String(c.position).toLowerCase());\n                return (\n                  <button\n                    key={`${c.page}-${c.type}-any`}\n                    type=\"button\"\n                    disabled={alreadyCreated}\n                    className={`flex-1 border rounded-xl p-6 flex flex-col items-start gap-2 transition-all ${\n                      selected ? \"border-[#0161AB] bg-[#F5F8FE]\" : \"border-gray-200 bg-white\"\n                    } ${alreadyCreated ? \"opacity-60 cursor-not-allowed\" : \"\"}`}\n                    onClick={() => {\n                      if (alreadyCreated) return;\n                      setPage(c.page);\n                      setAdType(c.type);\n                      setPlacement(c.position);\n                    }}\n                  >\n                    <div className=\"flex gap-2 items-center flex-wrap\">\n                      <span className=\"px-2 py-0.5 rounded-full bg-purple-50 text-purple-700 text-xs\">Type: {c.type}</span>\n                      <span className=\"px-2 py-0.5 rounded-full bg-gray-100 text-gray-700 text-xs\">Position: {c.position}</span>\n                    </div>\n                    <div className=\"mt-3 flex gap-2 w-full\">\n                      {alreadyCreated ? (\n                        <>\n                          <button className=\"px-3 py-1 rounded-md text-xs border border-gray-300\" onClick={(e) => { e.stopPropagation(); if(plan?._id) router.push(`/ad-management/current-ad-plan/view/${plan._id}`); }}>View</button>\n                          <button className=\"px-3 py-1 rounded-md text-xs border border-gray-300\" onClick={(e) => { e.stopPropagation(); if(plan?._id) router.push(`/ad-management/current-ad-plan/edit/${plan._id}`); }}>Edit</button>\n                          <button className=\"px-3 py-1 rounded-md text-xs border border-red-300 text-red-600\" onClick={async (e) => { e.stopPropagation(); if(!plan?._id) return; await deleteResource(plan._id); const res = await fetchAdPricingRules(); const resources = res?.data?.resources || res?.data || []; setCreatedPlans(resources.map((r) => ({ page: String(r.page||\"\").toLowerCase(), type: String(r.type||\"\").toLowerCase(), position: String(r.position||\"\").toLowerCase(), _id: r._id }))); }}>Delete</button>\n                        </>\n                      ) : (\n                        <button className=\"px-3 py-1 rounded-md text-xs bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white\" onClick={(e) => { e.stopPropagation(); setPage(c.page); setAdType(c.type); setPlacement(c.position); setShowCalendar(true); setTimeout(loadMonthData, 0); }}>Create Plan</button>\n                      )}\n                    </div>\n                  </button>\n                );\n              })}\n            </div>\n          </div>\n\n          {/* Product page (listing page) section (no fixed placement) */}\n          <div className=\"mb-5\">\n            <div className=\"font-semibold text-[#211F54] mb-2\">Product page</div>\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4\">\n              {[\n                { type: \"Banner\", position: AdPositionTypeEnum.NOT_FIXED, page: AdPageTypeEnum.LISTINGPAGE },\n                { type: \"Grid\", position: AdPositionTypeEnum.NOT_FIXED, page: AdPageTypeEnum.LISTINGPAGE },\n              ].map((c) => {\n                const selected = page === c.page && adType === c.type && placement === c.position;\n                const alreadyCreated = isPlanCreated(c.page, c.type, c.position);\n                const plan = createdPlans.find((cp) => cp.page === String(c.page).toLowerCase() && cp.type === String(c.type).toLowerCase() && cp.position === String(c.position).toLowerCase());\n                return (\n                  <button\n                    key={`${c.page}-${c.type}-any`}\n                    type=\"button\"\n                    disabled={alreadyCreated}\n                    className={`flex-1 border rounded-xl p-6 flex flex-col items-start gap-2 transition-all ${\n                      selected ? \"border-[#0161AB] bg-[#F5F8FE]\" : \"border-gray-200 bg-white\"\n                    } ${alreadyCreated ? \"opacity-60 cursor-not-allowed\" : \"\"}`}\n                    onClick={() => {\n                      if (alreadyCreated) return;\n                      setPage(c.page);\n                      setAdType(c.type);\n                      setPlacement(c.position);\n                    }}\n                  >\n                    <div className=\"flex gap-2 items-center flex-wrap\">\n                      <span className=\"px-2 py-0.5 rounded-full bg-purple-50 text-purple-700 text-xs\">Type: {c.type}</span>\n                      <span className=\"px-2 py-0.5 rounded-full bg-gray-100 text-gray-700 text-xs\">Position: {c.position}</span>\n                    </div>\n                    <div className=\"mt-3 flex gap-2 w-full\">\n                      {alreadyCreated ? (\n                        <>\n                          <button className=\"px-3 py-1 rounded-md text-xs border border-gray-300\" onClick={(e) => { e.stopPropagation(); if(plan?._id) router.push(`/ad-management/current-ad-plan/view/${plan._id}`); }}>View</button>\n                          <button className=\"px-3 py-1 rounded-md text-xs border border-gray-300\" onClick={(e) => { e.stopPropagation(); if(plan?._id) router.push(`/ad-management/current-ad-plan/edit/${plan._id}`); }}>Edit</button>\n                          <button className=\"px-3 py-1 rounded-md text-xs border border-red-300 text-red-600\" onClick={async (e) => { e.stopPropagation(); if(!plan?._id) return; await deleteResource(plan._id); const res = await fetchAdPricingRules(); const resources = res?.data?.resources || res?.data || []; setCreatedPlans(resources.map((r) => ({ page: String(r.page||\"\").toLowerCase(), type: String(r.type||\"\").toLowerCase(), position: String(r.position||\"\").toLowerCase(), _id: r._id }))); }}>Delete</button>\n                        </>\n                      ) : (\n                        <button className=\"px-3 py-1 rounded-md text-xs bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white\" onClick={(e) => { e.stopPropagation(); setPage(c.page); setAdType(c.type); setPlacement(c.position); setShowCalendar(true); setTimeout(loadMonthData, 0); }}>Create Plan</button>\n                      )}\n                    </div>\n                  </button>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n        {/* Base Price */}\n        <div className=\"mb-8\">\n          <label className=\"block font-[poppins] mb-1 text-[#211F54]\">\n            Base Price<span className=\"text-[#E1020C]\">*</span>\n          </label>\n          <div className=\"flex items-center w-full bg-white border border-gray-200 rounded-lg overflow-hidden\">\n            <select\n              className=\"bg-transparent px-4 py-3 font-[poppins] focus:outline-none border-none text-[#211F54]\"\n              value={baseCurrency}\n              disabled\n              style={{ cursor: \"not-allowed\" }}\n            >\n              <option value=\"JMD\">J$</option>\n            </select>\n            <input\n              type=\"number\"\n              min=\"0\"\n              maxLength={10}\n              className=\"flex-1 px-4 py-3 ml-4 font-[poppins] bg-transparent focus:outline-none border-l border-gray-200 text-[#211F54]\"\n              value={basePrice}\n              onChange={(e) => {\n                // Only allow numbers and empty string, and max 10 digits\n                const val = e.target.value;\n                if (/^\\d{0,10}$/.test(val)) setBasePrice(val);\n              }}\n              placeholder=\"Enter amount\"\n            />\n          </div>\n        </div>\n        {/* Manage Calendar Button (commented out) */}\n        {/**\n        <button\n          className=\"flex items-center gap-2 px-8 py-3 rounded-lg bg-gradient-to-r from-[#E1020C] to-[#4D7906] text-white font-semibold text-lg mb-8\"\n          onClick={() => {\n            setShowCalendar(true);\n            loadMonthData();\n          }}\n        >\n          <span className=\"text-xl\">🗓️</span> Manage Calendar\n        </button>\n        */}\n        {/* Save Button */}\n        <button\n          className={`w-full py-4 font-semibold text-lg rounded-full ${\n            isNextEnabled\n              ? \"cursor-pointer text-white border-radius-[66px] bg-gradient-to-r from-[#211F54] to-[#0161AB]\"\n              : \"bg-gray-500 cursor-not-allowed\"\n          }`}\n          style={\n            isNextEnabled\n              ? {\n                  borderRadius: \"66px\",\n                  background:\n                    \"linear-gradient(268deg, #211F54 11.09%, #0161AB 98.55%)\",\n                }\n              : {}\n          }\n          disabled={!isNextEnabled}\n          onClick={() => {\n            if (isNextEnabled) {\n              setShowCalendar(true);\n              loadMonthData();\n            }\n          }}\n        >\n          Next\n        </button>\n      </div>\n    );\n  }\n\n  // Calendar matrix for current month/year\n  const calendar = getCalendarMatrix(calendarYear, calendarMonth);\n\n  // Compute if all required fields are filled\n  const isRuleFormValid = !!(\n    ruleForm.name &&\n    ruleForm.startDate &&\n    ruleForm.endDate &&\n    ruleForm.color &&\n    ruleForm.price\n  );\n\n  // Get today's date in yyyy-mm-dd format\n  const todayStr = new Date().toISOString().split('T')[0];\n\n  // Find the most recent rule (excluding base price)\n  // Filter out base price rules\n  const nonBaseRules = rules.filter(rule => rule.name?.toLowerCase() !== 'base price');\n  // Find the most recent rule by updatedAt (ensure updatedAt exists and is valid)\n  let mostRecentRule = null;\n  if (nonBaseRules.length > 0) {\n    mostRecentRule = nonBaseRules.reduce((a, b) => {\n      // If either updatedAt is missing, treat as less recent\n      const aTime = a.updatedAt ? new Date(a.updatedAt).getTime() : 0;\n      const bTime = b.updatedAt ? new Date(b.updatedAt).getTime() : 0;\n      return aTime > bTime ? a : b;\n    });\n  }\n  // Use the rule's name as fallback if _id is missing (for frontend-only rules)\n  const mostRecentRuleId = mostRecentRule?._id || mostRecentRule?.name || null;\n  console.log(mostRecentRuleId, \"mostRecentRuleId\", nonBaseRules, mostRecentRule);\n\n  return (\n    <div className=\"p-6 border border-gray-200 shadow-xs rounded-lg\">\n      {/* Stepper */}\n      <div className=\" flex items-center gap-4\">\n        <button\n          type=\"button\"\n          className={`w-8 h-8 flex items-center justify-center rounded-full font-bold text-base transition-all duration-200 ${\n            !showCalendar\n              ? \"bg-[#0161AB] text-white\"\n              : \"bg-[#F5F8FE] text-[#0161AB] border border-[#0161AB]\"\n          }`}\n          style={{ outline: \"none\" }}\n          onClick={() => handleStepClick(1)}\n          tabIndex={0}\n        >\n          {\" \"}\n          <svg\n            width=\"20\"\n            height=\"20\"\n            viewBox=\"0 0 20 20\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <circle cx=\"10\" cy=\"10\" r=\"10\" fill=\"#4D7906\" />\n            <path\n              d=\"M6 10.5L9 13.5L14 8.5\"\n              stroke=\"white\"\n              strokeWidth=\"2\"\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n            />\n          </svg>\n        </button>\n        <span\n          className={`font-[poppins] text-lg ${\n            !showCalendar ? \"text-[#0161AB] font-semibold\" : \"text-gray-400\"\n          }`}\n        >\n          Step 1: Ad Details\n        </span>\n        {/* <div className=\"w-18 h-0.5 bg-gray-200 mx-2\" /> */}\n        <button\n          type=\"button\"\n          className={`w-8 h-8 flex items-center justify-center rounded-full font-bold text-base transition-all duration-200 ${\n            showCalendar && isNextEnabled\n              ? \"bg-[#0161AB] text-white\"\n              : \"bg-[#F5F8FE] text-[#0161AB] border border-[#0161AB]\"\n          } ${isNextEnabled ? \"cursor-pointer\" : \"cursor-not-allowed\"}`}\n          style={{ outline: \"none\" }}\n          onClick={() => isNextEnabled && handleStepClick(2)}\n          tabIndex={isNextEnabled ? 0 : -1}\n          disabled={!isNextEnabled}\n        >\n          2\n        </button>\n        <span\n          className={`font-[poppins] text-lg ${\n            showCalendar ? \"text-[#0161AB] font-semibold\" : \"text-gray-400\"\n          }`}\n        >\n          Step 2: Calendar Management\n        </span>\n      </div>\n      {/* Header */}\n      <div className=\"flex items-center justify-end gap-8 mb-4 border-b border-gray-200 pb-6\">\n        <div className=\"flex items-center gap-2\">\n          {/* <button\n            className=\"text-2xl font-bold text-[#211F54] bg-transparent border-none p-0 mr-2\"\n            onClick={() => setShowCalendar(false)}\n          >\n            {\"<\"}\n          </button> */}\n          {/* <span className=\"text-xl font-bold font-[poppins]\">Budget Calendar</span> */}\n        </div>\n        <div className=\"flex items-center gap-2 \">\n          <span className=\"font-[poppins] font-medium bg-white px-4 py-2 rounded border border-gray-200 shadow-sm\">\n            {new Date(calendarYear, calendarMonth).toLocaleString(\"default\", {\n              month: \"long\",\n              year: \"numeric\",\n            })}\n          </span>\n          <button\n            className=\"px-3 py-2 bg-white border border-gray-200 rounded-lg shadow-sm text-xl  text-[#211F54]\"\n            onClick={handlePrevMonth}\n          >\n            {\"<\"}\n          </button>\n          <button\n            className=\"px-3 py-2 bg-white border border-gray-200 rounded-lg shadow-sm text-xl  text-[#211F54]\"\n            onClick={handleNextMonth}\n          >\n            {\">\"}\n          </button>\n        </div>\n        <button\n          type=\"button\"\n          className=\"inline-flex items-center justify-center px-4 py-4 rounded-full bg-gradient-to-tr from-[#211F54] to-[#0161AB] min-h-[34.71px] h-[47px] w-[175px] font-inter\"\n          onClick={() => {\n            setShowRuleModal(true);\n            setEditingRuleIdx(null);\n          }}\n        >\n          <span className=\"text-white text-[14.2px] font-semibold leading-[22.09px] text-center\">\n            Set Pricing Rule\n          </span>\n        </button>\n      </div>\n      {/* Pricing Rules Table */}\n      <div className=\"mb-8\">\n        <h3 className=\"text-xl font-bold mb-4 text-[#211F54]\">Pricing Rules</h3>\n        <div className=\"overflow-x-auto\">\n          <table className=\"min-w-full bg-white rounded-lg shadow border border-gray-200\">\n            <thead>\n              <tr className=\"bg-[#F5F6FA] text-[#211F54]\">\n                <th className=\"p-3 text-left font-semibold border-b border-gray-200\">Name</th>\n                <th className=\"p-3 text-left font-semibold border-b border-gray-200\">Price</th>\n                <th className=\"p-3 text-left font-semibold border-b border-gray-200\">Priority</th>\n                <th className=\"p-3 text-left font-semibold border-b border-gray-200\">Start Date</th>\n                <th className=\"p-3 text-left font-semibold border-b border-gray-200\">End Date</th>\n                <th className=\"p-3 text-left font-semibold border-b border-gray-200\">Color</th>\n                <th className=\"p-3 text-left font-semibold border-b border-gray-200\">Status</th>\n                <th className=\"p-3 text-left font-semibold border-b border-gray-200\">Last Updated</th>\n                <th className=\"p-3 text-left font-semibold border-b border-gray-200\">Actions</th>\n              </tr>\n            </thead>\n            <tbody>\n              {nonBaseRules.length === 0 ? (\n                <tr><td colSpan={9} className=\"text-center p-4 text-gray-400\">No pricing rules</td></tr>\n              ) : nonBaseRules.map((rule, idx) => (\n                <tr key={rule._id} className=\"hover:bg-[#F5F6FA]\">\n                  <td className=\"p-3\">{rule.name}</td>\n                  <td className=\"p-3 border-b border-gray-200\">{formatJMD(rule.price)}</td>\n                  <td className=\"p-3\">{rule.priority}</td>\n                  <td className=\"p-3\">{rule.startDate}</td>\n                  <td className=\"p-3\">{rule.endDate}</td>\n                  <td className=\"p-3\">\n                    {rule.color && (\n                      <span className=\"inline-flex items-center gap-2\">\n                        <span style={{ background: rule.color, width: 18, height: 18, borderRadius: '50%', border: '1px solid #ccc', display: 'inline-block' }} />\n                      </span>\n                    )}\n                  </td>\n                  <td className=\"p-3\">\n                    {rule?.enabled ? (\n                      <span style={{ color: 'green', fontWeight: 600 }}>Active</span>\n                    ) : (\n                      <span style={{ color: 'red', fontWeight: 600 }}>Inactive</span>\n                    )}\n                  </td>\n                  <td className=\"p-3\">\n                    {formatDateTime(rule.updatedAt)}\n                    {rule?.name === mostRecentRuleId && (\n                      <span className=\"ml-2 px-2 py-1 bg-green-100 text-green-700 rounded text-xs font-semibold\">Recent</span>\n                    )}\n                  </td>\n                  <td className=\"p-3 flex gap-2\">\n                    <button className=\"px-2 py-1 bg-[#0161AB] text-white rounded text-xs\" onClick={() => {\n                      setRuleForm({ ...rule });\n                      setShowRuleModal(true);\n                      setEditingRuleIdx(idx);\n                    }}>Edit</button>\n                    <button className=\"px-2 py-1 bg-red-500 text-white rounded text-xs\" onClick={() => {\n                      setRules(rules => rules.filter((_, i) => i !== idx));\n                    }}>Delete</button>\n                  </td>\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      </div>\n      {/* Calendar */}\n      <div className=\"bg-white rounded-lg shadow p-0 overflow-x-auto border border-gray-200\">\n        <table className=\"min-w-full text-sm font-[poppins]\">\n          <thead>\n            <tr>\n              {weekDays.map((day) => (\n                <th\n                  key={day}\n                  className=\"p-3 text-center font-semibold text-[#211F54] bg-[#F5F6FA] border-b border-gray-200\"\n                >\n                  {day}\n                </th>\n              ))}\n            </tr>\n          </thead>\n          <tbody>\n            {calendar.map((week, rowIdx) => (\n              <tr key={rowIdx}>\n                {week.map((day, colIdx) => {\n                  if (!day) return <td key={colIdx} />;\n                  let isDisabled = false;\n                  const dateStr = `${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;\n                  // Check for override\n                  const override = dateOverrides[dateStr];\n                  if (override) {\n                    // Override present: show override price/currency, bg-gray-200, no border, cross icon\n                    return (\n                      <td\n                        key={colIdx}\n                        className=\"p-0 text-center align-top \"\n                        style={{ minWidth: 120, height: 90 }}\n                      >\n                        <div\n                          className=\"flex flex-col items-center justify-center h-full py-2 bg-gray-200 rounded-lg relative\"\n                          style={{ border: 'none', position: 'relative' }}\n                          onClick={() => !isDisabled && handleCellClick(day)}\n                        >\n                          {/* Remove override cross icon */}\n                          <button\n                            className=\"absolute top-1 right-1 text-xs text-gray-500 hover:text-red-500 bg-white rounded-full p-0.5 border border-gray-300 z-10\"\n                            style={{ lineHeight: 1, fontSize: 14 }}\n                            title=\"Remove override\"\n                            onClick={e => {\n                              e.stopPropagation();\n                              handleRemoveOverride(day);\n                            }}\n                          >\n                            ×\n                          </button>\n                          <span className=\"font-normal text-xl text-center\">\n                            {String(day).padStart(2, \"0\")}\n                          </span>\n                          <div className=\"flex items-center gap-2 mt-2\">\n                            <span className=\"text-xs font-semibold\" style={{ color: '#888' }}>\n                              {(override.currency === \"USD\" ? \"US$\" : \"J$\") +\n                                \" \" +\n                                override.price}\n                            </span>\n                          </div>\n                        </div>\n                      </td>\n                    );\n                  }\n                  // Highlight logic (existing)\n                  const ruleForDay = getRuleForDateWithUpdatedAt(rules, calendarYear, calendarMonth, day);\n                  const highlightStyle = ruleForDay\n                    ? {\n                        border: `2px solid ${ruleForDay.color}`,\n                        color: ruleForDay.color,\n                        fontWeight: 600,\n                      }\n                    : {};\n                  // Show rule price if present, else base price\n                  const priceToShow = ruleForDay ? ruleForDay.price : basePrice;\n                  const currencyToShow = ruleForDay ? ruleForDay.currency : baseCurrency;\n                  return (\n                    <td\n                      key={colIdx}\n                      className=\"p-0 text-center align-top \"\n                      style={{ minWidth: 120, height: 90 }}\n                    >\n                      <div\n                        className=\"flex flex-col items-center justify-center h-full py-2 border border-gray-100 rounded-lg relative\"\n                        style={\n                          isDisabled\n                            ? { opacity: 0.5, pointerEvents: \"none\", ...highlightStyle }\n                            : highlightStyle\n                        }\n                        onClick={() => !isDisabled && handleCellClick(day)}\n                      >\n                        {/* Rule name label in top-left if rule exists */}\n                        {ruleForDay && (\n                          <span\n                            className=\"absolute truncate max-w-[100px] left-1 top-1 text-[10px] font-semibold px-1 rounded\"\n                            style={{ background: '#fff', color: ruleForDay.color, border: `1px solid ${ruleForDay.color}`, zIndex: 2 }}\n                            title={ruleForDay.name}\n                          >\n                            {ruleForDay.name}\n                          </span>\n                        )}\n                        <span className=\"font-normal text-xl text-center\">\n                          {String(day).padStart(2, \"0\")}\n                        </span>\n                        <div className=\"flex items-center gap-2 mt-2\">\n                          <span className=\"text-xs font-semibold\" style={ruleForDay ? { color: ruleForDay.color } : { color: '#888' }}>\n                            {(currencyToShow === \"USD\" ? \"US$\" : \"J$\") +\n                              \" \" +\n                              priceToShow}\n                          </span>\n                        </div>\n                      </div>\n                    </td>\n                  );\n                })}\n              </tr>\n            ))}\n          </tbody>\n        </table>\n      </div>\n      {/* Save Button */}\n      <button\n        className={`w-full py-4 rounded-full mt-8 font-semibold text-lg ${\n          canSave\n            ? \"bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white cursor-pointer\"\n            : \"bg-gray-500 text-white cursor-not-allowed\"\n        }`}\n        disabled={!canSave}\n        onClick={handleApiSave}\n      >\n        {isSaving ? (\n          <span className=\"flex items-center justify-center\">\n            <svg className=\"animate-spin h-6 w-6 mr-2 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8v8z\"></path>\n            </svg>\n            Saving...\n          </span>\n        ) : (\n          'Save'\n        )}\n      </button>\n      {/* Set Price Modal */}\n      {showSetPrice && (\n        <div\n          className=\"fixed inset-0 flex items-center justify-center z-50\"\n          style={{ background: \"rgba(0, 0, 0, 0.40)\" }}\n\n          onClick={() => setShowSetPrice(false)}\n        >\n          <div\n            className=\"bg-white rounded-2xl shadow-lg p-8 w-full max-w-sm relative border border-gray-100\"\n            onClick={(e) => e.stopPropagation()}\n          >\n            <h3 className=\"text-lg text-left pb-2 font-[poppins]\">\n              {selectedCell.day && (dateOverrides[`${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(selectedCell.day).padStart(2, '0')}`])\n                ? \"Update\"\n                : \"Set\"}{\" \"}\n              Price\n              <span className=\"text-[#E1020C]\">*</span>\n            </h3>\n            <div className=\"flex items-center w-full bg-white border border-gray-200 rounded-lg overflow-hidden mb-6\">\n              <select\n                className=\"appearance-none bg-transparent px-4 py-3 font-[poppins] focus:outline-none border-none border-r border-gray-200 text-[#211F54]\"\n                value={modalCurrency}\n                style={{ cursor: 'not-allowed' }}\n                disabled\n                onChange={(e) => setModalCurrency(e.target.value)}\n              >\n                <option value=\"JMD\">JMD</option>\n              </select>\n              <input\n                type=\"number\"\n                min=\"1\"\n                max=\"9999999999\"\n                className=\"flex-1 px-4 py-3 ml-4 font-[poppins] bg-transparent focus:outline-none border-l border-gray-200 text-[#211F54]\"\n                value={modalPrice}\n                onChange={(e) => {\n                  const val = e.target.value;\n                  // Only allow numbers, no leading zero, max 10 digits, no zero\n                  if (/^([1-9][0-9]{0,9})?$/.test(val)) {\n                    setModalPrice(val);\n                  }\n                }}\n                onKeyDown={e => {\n                  if ([\"e\", \"E\", \"+\", \"-\"].includes(e.key)) {\n                    e.preventDefault();\n                  }\n                }}\n                placeholder=\"Enter price\"\n              />\n            </div>\n            <button\n              className={`w-full py-3 rounded-full text-lg ${!modalPrice ? 'bg-gray-400 text-white cursor-not-allowed' : 'bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white cursor-pointer'}`}\n              onClick={handleConfirm}\n              disabled={!modalPrice}\n            >\n              {selectedCell.day && (dateOverrides[`${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(selectedCell.day).padStart(2, '0')}`])\n                ? \"Update\"\n                : \"Set\"}\n            </button>\n          </div>\n        </div>\n      )}\n      {/* Pricing Rule Modal */}\n      {showRuleModal && (\n        <div\n          className=\"border-2 fixed flex-col inset-0 flex items-center justify-center z-50 \"\n          style={{ background: \"rgba(0, 0, 0, 0.80)\" }}\n          onClick={() => {setShowRuleModal(false),\n          setRuleForm({\n                      name: \"\",\n                      enabled: true,\n                      startDate: \"\",\n                      endDate: \"\",\n                      price: null,\n                      currency: \"\",\n                      priority: null,\n                      color: \"\",\n                    });}}\n        >\n          <div\n            onClick={(e) => e.stopPropagation()}\n            className=\"\n              b\n              w-[643px] h-fit min-w-[200px]\n              rounded-[16.87px]\n              border border-[#F8F9FA]\n              bg-white\n              shadow-[0px_3.374px_16.87px_0px_rgba(238,238,238,0.50)]\n              p-8\n              relative\n            \"\n          >\n            <button\n              className=\" absolute top-2 right-2 text-2xl\"\n              onClick={() => setShowRuleModal(false)}\n            >\n              <MdClose />\n            </button>\n\n            <div\n              className=\"\n                flex flex-col items-start gap-[10px] flex-shrink-0\n                \n                p-[20px] pt-[20px] pb-[20px] pl-[16px] pr-[16px]\n                rounded-[16px] border border-[#EFF1F4] bg-white\n              \"\n            >\n              <div className=\"flex items-center justify-between mb-4 w-full\">\n                <div className=\"flex items-center justify-end gap-2\">\n                  <span className=\"text-sm text-gray-500 mr-2\">Enabled</span>\n                  <button\n                    type=\"button\"\n                    aria-pressed={ruleForm.enabled}\n                    onClick={() =>\n                      setRuleForm((f) => ({ ...f, enabled: !f.enabled }))\n                    }\n                    className={`\n                      relative w-10 h-6 transition-colors duration-300\n                      rounded-full focus:outline-none border-0\n                      ${\n                        ruleForm.enabled\n                          ? \"bg-gradient-to-r from-[#24194B] to-[#0B5B8C]\"\n                          : \"bg-gray-300\"\n                      }\n                    `}\n                    style={{\n                      minWidth: \"40px\",\n                      minHeight: \"24px\",\n                      boxShadow: ruleForm.enabled\n                        ? \"0 0 0 2px #0B5B8C22\"\n                        : undefined,\n                      padding: 0,\n                    }}\n                  >\n                    <span\n                      className={`\n                        absolute top-0.5 left-0.5 transition-all duration-300\n                        w-5 h-5 rounded-full bg-white\n                        shadow\n                        ${ruleForm.enabled ? \"translate-x-4\" : \"translate-x-0\"}\n                      `}\n                      style={{\n                        boxShadow: \"0 1px 4px 0 rgba(0,0,0,0.10)\",\n                      }}\n                    />\n                  </button>\n                </div>\n              </div>\n              <label className=\"block font-[poppins]  text-[#211F54] text-sm\">\n                Rule Name<span className=\"text-[#E1020C]\">*</span>\n              </label>\n\n              <input\n                className=\"w-full border text-sm border-gray-200 rounded-lg px-4 py-3 font-[poppins] bg-white mb-4\"\n                placeholder=\"Enter Rule name\"\n                value={ruleForm.name}\n                maxLength={100}\n                onChange={(e) =>\n                  setRuleForm((f) => ({ ...f, name: e.target.value }))\n                }\n              />\n              <div className=\"mb-4 flex gap-4 w-full\">\n                <div className=\"flex-1\">\n                  <label className=\"block font-[poppins] mb-1 text-[#211F54] text-sm\">\n                    Start Date<span className=\"text-[#E1020C]\">*</span>\n                  </label>\n                  <input\n                    type=\"date\"\n                    className=\"w-full border text-sm border-gray-200 rounded-lg px-4 py-3 font-[poppins] bg-white\"\n                    value={ruleForm.startDate}\n                    // min={todayStr}\n                    onChange={(e) => {\n                      setRuleForm((f) => ({ ...f, startDate: e.target.value, endDate: \"\" }));\n                    }}\n                  />\n                </div>\n                <div className=\"flex-1\">\n                  <label className=\"block font-[poppins] mb-1 text-[#211F54] text-sm\">\n                    End Date<span className=\"text-[#E1020C]\">*</span>\n                  </label>\n                  <input\n                    type=\"date\"\n                    className=\"w-full border text-sm border-gray-200 rounded-lg px-4 py-3 font-[poppins] bg-white\"\n                    value={ruleForm.endDate}\n                    min={ruleForm.startDate ? new Date(new Date(ruleForm.startDate).getTime() + 24*60*60*1000).toISOString().split('T')[0] : todayStr}\n                    onChange={(e) => {\n                      const val = e.target.value;\n                      if (ruleForm.startDate && val <= ruleForm.startDate) {\n                        toast.warn('End date must be after start date');\n                        return;\n                      }\n                      setRuleForm((f) => ({ ...f, endDate: val }));\n                    }}\n                  />\n                </div>\n              </div>\n              <div className=\"mb-4 w-full\">\n                <label className=\"block font-[poppins] mb-1 text-[#211F54] text-sm\">\n                  Set price<span className=\"text-[#E1020C]\">*</span>\n                </label>\n                <div className=\"flex items-center w-full bg-white border border-gray-200 rounded-lg overflow-hidden\">\n                  <select\n                    className=\"bg-transparent px-4 py-3 font-[poppins] focus:outline-none border-none text-[#211F54]\"\n                    value={ruleForm.currency}\n              disabled\n              style={{ cursor: \"not-allowed\" }}\n\n                    // onChange={(e) =>\n                    //   setRuleForm((f) => ({ ...f, currency: e.target.value }))\n                    // }\n                  >\n                                 <option value=\"JMD\">J$</option>\n\n                  </select>\n                  <input\n                    type=\"number\"\n                    min=\"0\"\n                    maxLength={10}\n                    className=\"flex-1 px-4 text-sm py-3 ml-4 font-[poppins] bg-transparent focus:outline-none border-l border-gray-200 text-[#211F54]\"\n                    value={ruleForm.price}\n                    onChange={(e) => {\n                      const val = e.target.value;\n                      if (/^\\d{0,10}$/.test(val)) {\n                        setRuleForm((f) => ({ ...f, price: val }));\n                      }\n                    }}\n                    placeholder=\"Enter price\"\n                  />\n                </div>\n              </div>\n              <div className=\"mb-4 flex gap-4 items-center w-full \">\n                <div className=\"flex-1\">\n                  <label className=\"block font-[poppins] mb-1 text-[#211F54] text-sm\">\n                    Set Priority\n                  </label>\n                  <div className=\"flex items-center gap-2 w-full\">\n                    <select\n                      className=\"w-full border border-gray-200 rounded-lg px-2 py-2 font-[poppins] bg-white\"\n                      value={ruleForm.priority || ''}\n                      onChange={e => {\n                        setRuleForm(f => ({ ...f, priority: e.target.value }));\n                      }}\n                    >\n                      <option className=\"text-sm border-gray-200\" value=\"\">Select priority</option>\n                      {[...Array(10)].map((_, i) => {\n                        const val = (i + 1) * 10;\n                        return (\n                          <option key={val} value={val}>{val}</option>\n                        );\n                      })}\n                    </select>\n                  </div>\n                </div>\n                <div className=\"flex-1\">\n                  <label className=\"block font-[poppins] mb-1 text-[#211F54] text-sm\">\n                    Choose Colour<span className=\"text-[#E1020C]\">*</span>\n                  </label>\n                  <div className=\"flex items-center w-full\">\n                    <div className=\"\">\n                      <input\n                        type=\"color\"\n                        value={ruleForm.color}\n                        onChange={(e) =>\n                          setRuleForm((f) => ({ ...f, color: e.target.value }))\n                        }\n                        className=\"flex-1 cursor-pointer h-[48px] border border-gray-200 rounded-l-lg  font-[poppins] bg-white text-sm\"\n                        style={{\n                          backgroundColor: ruleForm.color,\n                          color: \"#211F54\",\n                          transition: \"background 0.2s\",\n                          fontWeight: 500,\n                        }}\n                      />\n                    </div>\n                    <input\n                      type=\"text\"\n                      value={ruleForm.color}\n                      placeholder=\"Select color\"\n                      onChange={(e) =>\n                        setRuleForm((f) => ({ ...f, color: e.target.value }))\n                      }\n                      className=\"flex-1 border border-gray-200 rounded-r-lg px-2 h-[48px] font-[poppins] bg-white text-sm placeholder:text-sm placeholder:border-gray-200\"\n                      style={{ borderLeft: \"none\" }}\n                    />\n                  </div>\n                </div>\n              </div>\n              {/* <div className=\"mb-4 flex items-center gap-2\">\n                <input\n                  type=\"checkbox\"\n                  checked={ruleForm.copyToNextYear}\n                  onChange={(e) =>\n                    setRuleForm((f) => ({\n                      ...f,\n                      copyToNextYear: e.target.checked,\n                    }))\n                  }\n                />\n                <span className=\"font-[poppins] text-sm\">\n                  Copy to next year\n                </span>\n              </div> */}\n            </div>\n            <div className=\"flex justify-end gap-4 mt-6\">\n              <button\n                className=\"px-6 py-2 rounded border border-gray-300 bg-white text-gray-700 font-semibold\"\n                onClick={() => {\n                  setShowRuleModal(false);\n                  setRuleForm({\n                    name: \"\",\n                    enabled: true,\n                    startDate: \"\",\n                    endDate: \"\",\n                    price: null,\n                    currency: \"\",\n                    priority: null,\n                    color: \"\",\n                  });\n                  setEditingRuleIdx(null);\n                }}\n              >\n                Cancel\n              </button>\n              {editingRuleIdx !== null ? (\n                <button\n                  className={`px-6 py-2 rounded font-semibold ${isRuleFormValid ? 'bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white' : 'bg-gray-400 text-white cursor-not-allowed'}`}\n                  disabled={!isRuleFormValid}\n                  onClick={() => {\n                    if (!isRuleFormValid) return;\n                    const duplicateKey = findDuplicateRule(rules, ruleForm, editingRuleIdx);\n                    if (duplicateKey) {\n                      toast.warn(`Rule already exists with ${duplicateKey}`);\n                      return;\n                    }\n                    setRules(rules => {\n                      const updated = rules.map((r, i) => \n                        i === editingRuleIdx \n                          ? { \n                              ...ruleForm, \n                              priority: ruleForm.priority ? Number(ruleForm.priority) : null,\n                              updatedAt: new Date().toISOString()\n                            } \n                          : r\n                      );\n                      setTimeout(() => {\n                        setShowRuleModal(false);\n                        setRuleForm({\n                          name: \"\",\n                          enabled: true,\n                          startDate: \"\",\n                          endDate: \"\",\n                          price: null,\n                          currency: \"\",\n                          priority: null,\n                          color: \"\",\n                        });\n                        setEditingRuleIdx(null);\n                        setCalendarRefresh(v => v + 1);\n                      }, 0);\n                      return updated;\n                    });\n                  }}\n                >\n                  Update Rule\n                </button>\n              ) : (\n                <button\n                  className={`px-6 py-2 rounded font-semibold ${isRuleFormValid ? 'bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white' : 'bg-gray-400 text-white cursor-not-allowed'}`}\n                  disabled={!isRuleFormValid}\n                  onClick={() => {\n                    if (!isRuleFormValid) return;\n                    const duplicateKey = findDuplicateRule(rules, ruleForm, null);\n                    if (duplicateKey) {\n                      toast.warn(`Rule already exists with ${duplicateKey}`);\n                      return;\n                    }\n                    setRules((rules) => {\n                      const updated = [\n                        ...rules,\n                        { \n                          ...ruleForm, \n                          priority: ruleForm.priority ? Number(ruleForm.priority) : null,\n                          updatedAt: new Date().toISOString()\n                        }\n                      ];\n                      setTimeout(() => {\n                        setShowRuleModal(false);\n                        setRuleForm({\n                          name: \"\",\n                          enabled: true,\n                          startDate: \"\",\n                          endDate: \"\",\n                          price: null,\n                          currency: \"\",\n                          priority: null,\n                          color: \"\",\n                        });\n                        setCalendarRefresh(v => v + 1);\n                      }, 0);\n                      return updated;\n                    });\n                  }}\n                >\n                  Save Rule\n                </button>\n              )}\n            </div>\n          </div>\n        </div>\n      )}\n      {/* Rules List */}\n     \n    </div>\n  );\n}\n\nfunction formatJMD(price) {\n  if (price === null || price === undefined) return 'N/A';\n  return new Intl.NumberFormat('en-JM', { style: 'currency', currency: 'JMD' }).format(price);\n}\n\n// Custom getRuleForDate for calendar\nfunction getRuleForDateWithUpdatedAt(rules, year, month, day) {\n  const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;\n  const matching = rules.filter(rule => {\n    if (rule.isActive === false && rule.enabled === false) return false;\n    if (!rule.startDate || !rule.endDate) return false;\n    const start = rule.startDate;\n    const end = rule.endDate;\n    return start <= dateStr && dateStr <= end;\n  });\n  if (matching.length === 0) return null;\n  matching.sort((a, b) => {\n    if (Number(b.priority) !== Number(a.priority)) {\n      return Number(b.priority) - Number(a.priority);\n    }\n    return new Date(b.updatedAt) - new Date(a.updatedAt);\n  });\n  return matching[0];\n}\n"], "names": [], "mappings": ";;;;AACA;AAEA;AACA;AACA;AACA;;;AANA;;;;;;;AAQA,+BAA+B;AAC/B,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AACD,MAAM,kBAAkB;AACxB,MAAM,eAAe;AAErB,iDAAiD;AACjD,MAAM,kBAAkB;IACtB,WAAW;QACT,wDAAwD;QACxD;YAAE,KAAK;YAAG,OAAO;YAAO,UAAU;QAAM;QACxC;YAAE,KAAK;YAAI,OAAO;YAAO,UAAU;QAAM;QACzC;YAAE,KAAK;YAAI,OAAO;YAAO,UAAU;QAAM;KAC1C;IACD,WAAW;QACT;YAAE,KAAK;YAAG,OAAO;YAAO,UAAU;QAAM;QACxC;YAAE,KAAK;YAAI,OAAO;YAAO,UAAU;QAAM;KAC1C;AAEH;AAEA,0CAA0C;AAC1C,SAAS,eAAe,IAAI,EAAE,KAAK;IACjC,OAAO,IAAI,KAAK,MAAM,QAAQ,GAAG,GAAG,OAAO;AAC7C;AAEA,yDAAyD;AACzD,SAAS,iBAAiB,IAAI,EAAE,KAAK;IACnC,6CAA6C;IAC7C,IAAI,QAAQ,IAAI,KAAK,MAAM,OAAO,GAAG,MAAM;IAC3C,OAAO,UAAU,IAAI,IAAI,QAAQ;AACnC;AAEA,2DAA2D;AAC3D,SAAS,kBAAkB,IAAI,EAAE,KAAK;IACpC,MAAM,cAAc,eAAe,MAAM;IACzC,MAAM,cAAc,iBAAiB,MAAM;IAC3C,MAAM,SAAS,EAAE;IACjB,IAAI,MAAM,IAAI;IACd,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAO;QAChC,MAAM,OAAO,EAAE;QACf,IAAK,IAAI,MAAM,GAAG,MAAM,GAAG,MAAO;YAChC,KAAK,IAAI,CAAC,MAAM,KAAK,OAAO,cAAc,MAAM;YAChD;QACF;QACA,OAAO,IAAI,CAAC;IACd;IACA,OAAO;AACT;AAEA,wEAAwE;AACxE,SAAS,aAAa,IAAI,EAAE,KAAK,EAAE,GAAG;IACpC,MAAM,OAAO,IAAI,KAAK,MAAM,OAAO;IACnC,MAAM,QAAQ,KAAK,MAAM,IAAI,WAAW;IACxC,MAAM,eAAe,UAAU,IAAI,CAAC,IAAI,IAAI;IAC5C,MAAM,QAAQ,IAAI,KAAK,MAAM,OAAO,MAAM;IAC1C,MAAM,MAAM,IAAI,KAAK;IACrB,IAAI,OAAO,CAAC,MAAM,OAAO,KAAK;IAC9B,OAAO;QACL,KAAK,GAAG,CAAC,GAAG,MAAM,OAAO;QACzB,KAAK,GAAG,CAAC,eAAe,MAAM,QAAQ,IAAI,OAAO;KAClD;AACH;AAEA,uCAAuC;AACvC,MAAM,iBAAiB,OAAO,MAAM,CAAC;IACnC,UAAU;IACV,UAAU;IACV,eAAe;IACf,aAAa;AACf;AACA,MAAM,qBAAqB,OAAO,MAAM,CAAC;IACvC,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,WAAW;AACb;AACA,MAAM,gBAAgB,OAAO,MAAM,CAAC;AACpC,MAAM,oBAAoB,OAAO,MAAM,CAAC;AAExC,uCAAuC;AACvC,MAAM,UAAU;IACd;QACE,KAAK;QACL,OAAO;QACP,MAAM;IACR;IACA;QACE,KAAK;QACL,OAAO;QACP,MAAM;IACR;CAED;AAED,6BAA6B;AAC7B,SAAS,kBAAkB,KAAK,EAAE,QAAQ,EAAE,cAAc;IACxD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,IAAI,mBAAmB,QAAQ,MAAM,gBAAgB;QACrD,MAAM,IAAI,KAAK,CAAC,EAAE;QAClB,IAAI,EAAE,IAAI,KAAK,SAAS,IAAI,EAAE,OAAO;QACrC,2DAA2D;QAC3D,IAAI,EAAE,KAAK,KAAK,SAAS,KAAK,EAAE,OAAO;IACvC,2FAA2F;IAC7F;IACA,OAAO;AACT;AAEA,iGAAiG;AACjG,gFAAgF;AAChF,SAAS,eAAe,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;IAC7C,wBAAwB;IACxB,MAAM,UAAU,GAAG,KAAK,CAAC,EAAE,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK,QAAQ,CAAC,GAAG,MAAM;IAC/F,kDAAkD;IAClD,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA;QAC5B,IAAI,CAAC,KAAK,OAAO,EAAE,OAAO;QAC1B,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,OAAO,EAAE,OAAO;QAC7C,OAAO,KAAK,SAAS,IAAI,WAAW,WAAW,KAAK,OAAO;IAC7D;IACA,mEAAmE;IACnE,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;IAClC,OAAO,SAAS,MAAM,CAAC,CAAC,GAAG;QACzB,IAAI,EAAE,QAAQ,IAAI,MAAM,OAAO;QAC/B,IAAI,EAAE,QAAQ,IAAI,MAAM,OAAO;QAC/B,OAAO,OAAO,EAAE,QAAQ,IAAI,OAAO,EAAE,QAAQ,IAAI,IAAI;IACvD;AACF;AAEA,2DAA2D;AAC3D,SAAS,gBAAgB,OAAO;IAC9B,IAAI,CAAC,SAAS,OAAO;IACrB,MAAM,IAAI,IAAI,KAAK;IACnB,2DAA2D;IAC3D,MAAM,WAAW,CAAC,EAAE,iBAAiB;IACrC,MAAM,OAAO,YAAY,IAAI,MAAM;IACnC,MAAM,MAAM,CAAA,IAAK,OAAO,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,QAAQ,CAAC,GAAG;IAC7D,MAAM,QAAQ,IAAI,WAAW;IAC7B,MAAM,UAAU,IAAI,WAAW;IAC/B,OAAO,EAAE,WAAW,KAAK,MACvB,IAAI,EAAE,QAAQ,KAAK,KAAK,MACxB,IAAI,EAAE,OAAO,MAAM,MACnB,IAAI,EAAE,QAAQ,MAAM,MACpB,IAAI,EAAE,UAAU,MAAM,MACtB,IAAI,EAAE,UAAU,MAAM,MACtB,OAAO,EAAE,eAAe,IAAI,QAAQ,CAAC,GAAG,OACxC,OAAO,QAAQ,MAAM;AACzB;AAEA,SAAS,eAAe,OAAO;IAC7B,IAAI,CAAC,SAAS,OAAO;IACrB,MAAM,IAAI,IAAI,KAAK;IACnB,OAAO,EAAE,cAAc,CAAC,SAAS;QAAE,WAAW;QAAU,WAAW;IAAQ;AAC7E;AAGe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,WAAW;IACX,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,gBAAgB;IAChB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,aAAa;IACb,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,mBAAmB;IACnB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,4BAA4B;IAC5B,MAAM,QAAQ,IAAI;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,WAAW;IAClE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,QAAQ,KAAK,YAAY;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnC,6EAA6E;IAC7E,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;wCAAE;YACnD,MAAM,OAAO,MAAM,eAAe,cAAc,gBAAgB,IAAI,CAAC;YACrE,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,IAAI,CAAC,EAAE,GAAG;oBAAE,OAAO;oBAAW,UAAU;gBAAa;YACvD;YACA,OAAO;QACT;;IAEA,8BAA8B;IAC9B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;IAAK;IAC7D,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,kCAAkC;IAClC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,0EAA0E;IAC1E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR;iDAAC;oBACC,IAAI;wBACF,MAAM,MAAM,MAAM,CAAA,GAAA,iIAAA,CAAA,sBAAmB,AAAD;wBACpC,MAAM,YAAY,KAAK,MAAM,aAAa,KAAK,QAAQ,EAAE;wBACzD,IAAI,MAAM,OAAO,CAAC,YAAY;4BAC5B,gBACE,UAAU,GAAG;iEAAC,CAAC,IAAM,CAAC;wCACpB,MAAM,OAAO,EAAE,IAAI,IAAI,IAAI,WAAW;wCACtC,MAAM,OAAO,EAAE,IAAI,IAAI,IAAI,WAAW;wCACtC,UAAU,OAAO,EAAE,QAAQ,IAAI,IAAI,WAAW;wCAC9C,KAAK,EAAE,GAAG;oCACZ,CAAC;;wBAEL;oBACF,EAAE,OAAO,GAAG,CAAC;gBACf;aAAC;QACH;wCAAG,EAAE;IAEL,MAAM,gBAAgB,CAAC,UAAU,UAAU,eAAe,EAAE;QAC1D,MAAM,IAAI,OAAO,YAAY,IAAI,WAAW;QAC5C,MAAM,IAAI,OAAO,YAAY,IAAI,WAAW;QAC5C,MAAM,MAAM,OAAO,gBAAgB,IAAI,WAAW;QAClD,OAAO,aAAa,IAAI,CACtB,CAAC,KAAO,GAAG,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,QAAQ,KAAK,MAAM,IAAI,KAAK,CAAC,CAAC,MAAM,CAAC,GAAG,QAAQ,IAAI,GAAG,QAAQ,KAAK,KAAK,IAAI;IAErI;IAEA,yCAAyC;IACzC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,IAAI,wCAAwC;IAEhG,2BAA2B;IAC3B,MAAM,oBAAoB,SAAS,eAAe,QAAQ,IAAI,SAAS,eAAe,QAAQ;IAC9F,MAAM,gBAAgB,CAAC,CAAC,CAAC,UAAU,QAAQ,aAAa,CAAC,oBAAoB,YAAY,IAAI,CAAC;IAE9F,uBAAuB;IACvB,MAAM,cAAc,eAAe,IAAI;IAEvC,+BAA+B;IAC/B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,SAAS;QACT,WAAW;QACX,SAAS;QACT,OAAO;QACP,UAAU;QACV,UAAU;QACV,OAAO;IACT;IACA,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACrC,gDAAgD;IAChD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,0CAA0C;IAC1C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,QAAQ,GAAG,CAAC,UAAS,YAAY;IACjC,mBAAmB;IAEnB,oDAAoD;IACpD,MAAM,kBAAkB;QACtB,iBAAiB,CAAC;YAChB,IAAI,SAAS,GAAG;gBACd,OAAO,GAAG,4BAA4B;YACxC;YACA,OAAO,OAAO;QAChB;QACA,WAAW,eAAe;IAC5B;IACA,MAAM,kBAAkB;QACtB,iBAAiB,CAAC;YAChB,IAAI,SAAS,IAAI;gBACf,OAAO,IAAI,wBAAwB;YACrC;YACA,OAAO,OAAO;QAChB;QACA,WAAW,eAAe;IAC5B;IAEA,gCAAgC;IAChC,SAAS;QACP,MAAM,MAAM,GAAG,aAAa,CAAC,EAAE,OAAO,gBAAgB,GAAG,QAAQ,CAAC,GAAG,MAAM;QAC3E,MAAM,YAAY,eAAe,CAAC,IAAI,IAAI,EAAE;QAC5C,MAAM,OAAO,MAAM,eAAe,cAAc,gBAAgB,IAAI,CAAC;QACrE,UAAU,OAAO,CAAC,CAAC,EAAE,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE;YACzC,IAAI,CAAC,MAAM,EAAE,GAAG;gBAAE;gBAAO;YAAS;QACpC;QACA,kBAAkB;QAClB,iBAAiB;IACnB;IAEA,mEAAmE;IACnE,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;YAAE;QAAI;QACtB,MAAM,UAAU,GAAG,aAAa,CAAC,EAAE,OAAO,gBAAgB,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK,QAAQ,CAAC,GAAG,MAAM;QAC/G,IAAI,aAAa,CAAC,QAAQ,EAAE;YAC1B,cAAc,aAAa,CAAC,QAAQ,CAAC,KAAK,IAAI;YAC9C,iBAAiB,aAAa,CAAC,QAAQ,CAAC,QAAQ,IAAI;QACtD,OAAO;YACL,MAAM,OAAO,cAAc,CAAC,MAAM,EAAE;YACpC,cAAe;YACf,iBAAiB,MAAM,YAAY;QACrC;QACA,gBAAgB;IAClB;IAEA,mDAAmD;IACnD,MAAM,gBAAgB;QACpB,IAAI,aAAa,GAAG,KAAK,MAAM;YAC7B,MAAM,UAAU,GAAG,aAAa,CAAC,EAAE,OAAO,gBAAgB,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,aAAa,GAAG,EAAE,QAAQ,CAAC,GAAG,MAAM;YAC5H,iBAAiB,CAAC,OAAS,CAAC;oBAC1B,GAAG,IAAI;oBACP,CAAC,QAAQ,EAAE;wBAAE,OAAO;wBAAY,UAAU;oBAAc;gBAC1D,CAAC;YACD,iBAAiB;QACnB;QACA,gBAAgB;QAChB,gBAAgB;YAAE,KAAK;QAAK;QAC5B,cAAc;QACd,iBAAiB;IACnB;IAEA,6BAA6B;IAC7B,MAAM,uBAAuB,CAAC;QAC5B,MAAM,UAAU,GAAG,aAAa,CAAC,EAAE,OAAO,gBAAgB,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK,QAAQ,CAAC,GAAG,MAAM;QAC/G,iBAAiB,CAAC;YAChB,MAAM,OAAO;gBAAE,GAAG,IAAI;YAAC;YACvB,OAAO,IAAI,CAAC,QAAQ;YACpB,OAAO;QACT;QACA,iBAAiB;IACnB;IAEA,uCAAuC;IACvC,MAAM,qBAAqB;QACzB,0DAA0D;QAC1D,iBAAiB;IACjB,eAAe;IACf,wDAAwD;IAC1D;IAEA,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,IAAI,cAAc;gBAChB,MAAM,OAAO,MAAM,eAAe,cAAc,gBAAgB,IAAI,CAClE;gBAEF,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;oBACpC,IAAI,CAAC,EAAE,GAAG;wBAAE,OAAO;wBAAW,UAAU;oBAAa;gBACvD;gBACA,kBAAkB;YACpB;QACF;wCAAG;QAAC;QAAW;QAAc;QAAc;QAAe;KAAa;IAEvE,yBAAyB;IACzB,MAAM,kBAAkB,CAAC;QACvB,IAAI,SAAS,GAAG;YACd,gBAAgB;QAClB,OAAO,IAAI,SAAS,KAAK,eAAe;YACtC,gBAAgB;YAChB;QACF;IACF;IAEA,2BAA2B;IAC3B,MAAM,gBAAgB;QACpB,YAAY;QACZ,2BAA2B;QAC3B,MAAM,eAAe,MAAM,GAAG,CAAC,CAAA;YAC7B,MAAM,UAAU;gBACd,MAAM,KAAK,IAAI;gBACf,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,SAAS,GAAG,gBAAgB,KAAK,SAAS,IAAI;gBAC9D,SAAS,KAAK,OAAO,GAAG,gBAAgB,KAAK,OAAO,IAAI;gBACxD,OAAO,KAAK,KAAK,GAAG,OAAO,KAAK,KAAK,IAAI;gBACzC,UAAU,CAAC,CAAC,KAAK,OAAO;YAC1B;YACA,IAAI,KAAK,QAAQ,KAAK,QAAQ,KAAK,QAAQ,KAAK,MAAM,KAAK,QAAQ,KAAK,WAAW;gBACjF,QAAQ,QAAQ,GAAG,OAAO,KAAK,QAAQ;YACzC;YACA,OAAO;QACT;QACA,4BAA4B;QAC5B,MAAM,gBAAgB,OAAO,OAAO,CAAC,eAAe,GAAG,CAAC,CAAC,CAAC,MAAM,IAAI,GAAK,CAAC;gBACxE,MAAM,OAAO,gBAAgB,QAAQ;gBACrC,OAAO,IAAI,KAAK,GAAG,OAAO,IAAI,KAAK,IAAI;YACzC,CAAC;QACD,gBAAgB;QAChB,MAAM,UAAU;YACd,MAAM,SAAS,OAAO,WAAW,KAAK;YACtC;YACA,UAAU;YACV,WAAW,YAAY,OAAO,aAAa;YAC3C,cAAc,aAAa,MAAM,GAAG,eAAe,EAAE;YACrD,eAAe,cAAc,MAAM,GAAG,gBAAgB,EAAE;QAC1D;QACA,IAAI;YACF,MAAM,MAAM,MAAM,CAAA,GAAA,iIAAA,CAAA,sBAAmB,AAAD,EAAE;YACtC,IAAI,OAAO,CAAC,IAAI,MAAM,KAAK,OAAO,IAAI,MAAM,KAAK,GAAG,GAAG;gBACrD,sJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,WAAW;oBACT,OAAO,IAAI,CAAC;gBACd,GAAG;YACL,OAAO;YACL,mEAAmE;YACrE;QACF,EAAE,OAAO,OAAO;QACd,mEAAmE;QACrE;QACA,YAAY;IACd;IAEA,wFAAwF;IACxF,MAAM,UAAU,CAAC,MAAM,MAAM,GAAG,KAAK,OAAO,IAAI,CAAC,eAAe,MAAM,GAAG,CAAC,KAAK,CAAC;IAEhF,aAAa;IACb,IAAI,CAAC,cAAc;QACjB,qBACE,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,MAAK;4BACL,WAAW,CAAC,sGAAsG,EAChH,CAAC,eACG,4BACA,uDACJ;4BACF,OAAO;gCAAE,SAAS;4BAAO;4BACzB,SAAS,IAAM,gBAAgB;4BAC/B,UAAU;sCAET,8BACC,6LAAC;gCACC,OAAM;gCACN,QAAO;gCACP,SAAQ;gCACR,MAAK;gCACL,OAAM;;kDAEN,6LAAC;wCAAO,IAAG;wCAAK,IAAG;wCAAK,GAAE;wCAAK,MAAK;;;;;;kDACpC,6LAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;uCAInB;;;;;;sCAGJ,6LAAC;4BACC,WAAW,CAAC,uBAAuB,EACjC,CAAC,eAAe,iCAAiC,iBACjD;sCACH;;;;;;sCAID,6LAAC;4BACC,MAAK;4BACL,WAAW,CAAC,sGAAsG,EAChH,gBAAgB,gBACZ,4BACA,sDACL,CAAC,EAAE,gBAAgB,mBAAmB,sBAAsB;4BAC7D,OAAO;gCAAE,SAAS;4BAAO;4BACzB,SAAS,IAAM,iBAAiB,gBAAgB;4BAChD,UAAU,gBAAgB,IAAI,CAAC;4BAC/B,UAAU,CAAC;sCACZ;;;;;;sCAGD,6LAAC;4BACC,WAAW,CAAC,uBAAuB,EACjC,eAAe,iCAAiC,iBAChD;sCACH;;;;;;;;;;;;8BAMH,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,WAAU;;gCAA2C;8CACjC,6LAAC;oCAAK,WAAU;8CAAiB;;;;;;;;;;;;sCAG5D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAoC;;;;;;8CACnD,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,MAAM;4CAAU,UAAU,mBAAmB,GAAG;4CAAE,MAAM,eAAe,QAAQ;wCAAC;wCAClF;4CAAE,MAAM;4CAAQ,UAAU,mBAAmB,MAAM;4CAAE,MAAM,eAAe,QAAQ;wCAAC;wCACnF;4CAAE,MAAM;4CAAU,UAAU,mBAAmB,MAAM;4CAAE,MAAM,eAAe,QAAQ;wCAAC;qCACtF,CAAC,GAAG,CAAC,CAAC;wCACL,MAAM,iBAAiB,cAAc,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,QAAQ;wCAC/D,MAAM,OAAO,aAAa,IAAI,CAC5B,CAAC,KAAO,GAAG,IAAI,KAAK,OAAO,EAAE,IAAI,EAAE,WAAW,MAAM,GAAG,IAAI,KAAK,OAAO,EAAE,IAAI,EAAE,WAAW,MAAM,GAAG,QAAQ,KAAK,OAAO,EAAE,QAAQ,EAAE,WAAW;wCAEhJ,qBACE,6LAAC;4CAEC,WAAW,CAAC,yGAAyG,CAAC;;8DAEtH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAAgE;gEAAO,EAAE,IAAI;;;;;;;sEAC7F,6LAAC;4DAAK,WAAU;;gEAA6D;gEAAW,EAAE,QAAQ;;;;;;;;;;;;;gDAEnG,gCACC,6LAAC;oDAAK,WAAU;8DAA8D;;;;;;8DAEhF,6LAAC;oDAAI,WAAU;8DACZ,+BACC;;0EACE,6LAAC;gEAAO,WAAU;gEAAsD,SAAS;oEAAQ,IAAG,MAAM,KAAK,MAAM,CAAC,cAAc,EAAE,KAAK,GAAG,EAAE;gEAAG;0EAAG;;;;;;0EAC9I,6LAAC;gEAAO,WAAU;gEAAsD,SAAS;oEAAQ,IAAG,MAAM,KAAK,MAAM,CAAC,cAAc,EAAE,KAAK,GAAG,EAAE;gEAAG;0EAAG;;;;;;0EAC9I,6LAAC;gEAAO,WAAU;gEAAkE,SAAS;oEAAQ,IAAG,MAAM,KAAK,MAAM,CAAC,gBAAgB,EAAE,KAAK,GAAG,EAAE;gEAAG;0EAAG;;;;;;;qFAG9J,6LAAC;wDAAO,WAAU;wDAAuF,SAAS;4DAAQ,QAAQ,EAAE,IAAI;4DAAG,UAAU,EAAE,IAAI;4DAAG,aAAa,EAAE,QAAQ;4DAAG,gBAAgB;4DAAO,WAAW,eAAe;wDAAI;kEAAG;;;;;;;;;;;;2CAlB/O,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE;;;;;oCAuB9C;;;;;;;;;;;;sCAKJ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAoC;;;;;;8CACnD,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,MAAM;4CAAU,UAAU,mBAAmB,MAAM;4CAAE,MAAM,eAAe,QAAQ;wCAAC;qCACtF,CAAC,GAAG,CAAC,CAAC;wCACL,MAAM,WAAW,SAAS,EAAE,IAAI,IAAI,WAAW,EAAE,IAAI,IAAI,cAAc,EAAE,QAAQ;wCACjF,MAAM,iBAAiB,cAAc,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,QAAQ;wCAC/D,MAAM,OAAO,aAAa,IAAI,CAAC,CAAC,KAAO,GAAG,IAAI,KAAK,OAAO,EAAE,IAAI,EAAE,WAAW,MAAM,GAAG,IAAI,KAAK,OAAO,EAAE,IAAI,EAAE,WAAW,MAAM,GAAG,QAAQ,KAAK,OAAO,EAAE,QAAQ,EAAE,WAAW;wCAC7K,qBACE,6LAAC;4CAEC,MAAK;4CACL,UAAU;4CACV,WAAW,CAAC,4EAA4E,EACtF,WAAW,kCAAkC,2BAC9C,CAAC,EAAE,iBAAiB,kCAAkC,IAAI;4CAC3D,SAAS;gDACP,IAAI,gBAAgB;gDACpB,QAAQ,EAAE,IAAI;gDACd,UAAU,EAAE,IAAI;gDAChB,aAAa,EAAE,QAAQ;4CACzB;;8DAEA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAAgE;gEAAO,EAAE,IAAI;;;;;;;sEAC7F,6LAAC;4DAAK,WAAU;;gEAA6D;gEAAW,EAAE,QAAQ;;;;;;;;;;;;;8DAEpG,6LAAC;oDAAI,WAAU;8DACZ,+BACC;;0EACE,6LAAC;gEAAO,WAAU;gEAAsD,SAAS,CAAC;oEAAQ,EAAE,eAAe;oEAAI,IAAG,MAAM,KAAK,OAAO,IAAI,CAAC,CAAC,oCAAoC,EAAE,KAAK,GAAG,EAAE;gEAAG;0EAAG;;;;;;0EAChM,6LAAC;gEAAO,WAAU;gEAAsD,SAAS,CAAC;oEAAQ,EAAE,eAAe;oEAAI,IAAG,MAAM,KAAK,OAAO,IAAI,CAAC,CAAC,oCAAoC,EAAE,KAAK,GAAG,EAAE;gEAAG;0EAAG;;;;;;0EAChM,6LAAC;gEAAO,WAAU;gEAAkE,SAAS,OAAO;oEAAQ,EAAE,eAAe;oEAAI,IAAG,CAAC,MAAM,KAAK;oEAAQ,MAAM,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG;oEAAG,MAAM,MAAM,MAAM,CAAA,GAAA,iIAAA,CAAA,sBAAmB,AAAD;oEAAK,MAAM,YAAY,KAAK,MAAM,aAAa,KAAK,QAAQ,EAAE;oEAAE,gBAAgB,UAAU,GAAG,CAAC,CAAC,IAAM,CAAC;4EAAE,MAAM,OAAO,EAAE,IAAI,IAAE,IAAI,WAAW;4EAAI,MAAM,OAAO,EAAE,IAAI,IAAE,IAAI,WAAW;4EAAI,UAAU,OAAO,EAAE,QAAQ,IAAE,IAAI,WAAW;4EAAI,KAAK,EAAE,GAAG;wEAAC,CAAC;gEAAK;0EAAG;;;;;;;qFAG1d,6LAAC;wDAAO,WAAU;wDAAuF,SAAS,CAAC;4DAAQ,EAAE,eAAe;4DAAI,QAAQ,EAAE,IAAI;4DAAG,UAAU,EAAE,IAAI;4DAAG,aAAa,EAAE,QAAQ;4DAAG,gBAAgB;4DAAO,WAAW,eAAe;wDAAI;kEAAG;;;;;;;;;;;;2CAzBrQ,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE;;;;;oCA8B9C;;;;;;;;;;;;sCAKJ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAoC;;;;;;8CACnD,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,MAAM;4CAAU,UAAU,mBAAmB,SAAS;4CAAE,MAAM,eAAe,aAAa;wCAAC;wCAC7F;4CAAE,MAAM;4CAAQ,UAAU,mBAAmB,SAAS;4CAAE,MAAM,eAAe,aAAa;wCAAC;qCAC5F,CAAC,GAAG,CAAC,CAAC;wCACL,MAAM,WAAW,SAAS,EAAE,IAAI,IAAI,WAAW,EAAE,IAAI,IAAI,cAAc,EAAE,QAAQ;wCACjF,MAAM,iBAAiB,cAAc,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,QAAQ;wCAC/D,MAAM,OAAO,aAAa,IAAI,CAAC,CAAC,KAAO,GAAG,IAAI,KAAK,OAAO,EAAE,IAAI,EAAE,WAAW,MAAM,GAAG,IAAI,KAAK,OAAO,EAAE,IAAI,EAAE,WAAW,MAAM,GAAG,QAAQ,KAAK,OAAO,EAAE,QAAQ,EAAE,WAAW;wCAC7K,qBACE,6LAAC;4CAEC,MAAK;4CACL,UAAU;4CACV,WAAW,CAAC,4EAA4E,EACtF,WAAW,kCAAkC,2BAC9C,CAAC,EAAE,iBAAiB,kCAAkC,IAAI;4CAC3D,SAAS;gDACP,IAAI,gBAAgB;gDACpB,QAAQ,EAAE,IAAI;gDACd,UAAU,EAAE,IAAI;gDAChB,aAAa,EAAE,QAAQ;4CACzB;;8DAEA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAAgE;gEAAO,EAAE,IAAI;;;;;;;sEAC7F,6LAAC;4DAAK,WAAU;;gEAA6D;gEAAW,EAAE,QAAQ;;;;;;;;;;;;;8DAEpG,6LAAC;oDAAI,WAAU;8DACZ,+BACC;;0EACE,6LAAC;gEAAO,WAAU;gEAAsD,SAAS,CAAC;oEAAQ,EAAE,eAAe;oEAAI,IAAG,MAAM,KAAK,OAAO,IAAI,CAAC,CAAC,oCAAoC,EAAE,KAAK,GAAG,EAAE;gEAAG;0EAAG;;;;;;0EAChM,6LAAC;gEAAO,WAAU;gEAAsD,SAAS,CAAC;oEAAQ,EAAE,eAAe;oEAAI,IAAG,MAAM,KAAK,OAAO,IAAI,CAAC,CAAC,oCAAoC,EAAE,KAAK,GAAG,EAAE;gEAAG;0EAAG;;;;;;0EAChM,6LAAC;gEAAO,WAAU;gEAAkE,SAAS,OAAO;oEAAQ,EAAE,eAAe;oEAAI,IAAG,CAAC,MAAM,KAAK;oEAAQ,MAAM,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG;oEAAG,MAAM,MAAM,MAAM,CAAA,GAAA,iIAAA,CAAA,sBAAmB,AAAD;oEAAK,MAAM,YAAY,KAAK,MAAM,aAAa,KAAK,QAAQ,EAAE;oEAAE,gBAAgB,UAAU,GAAG,CAAC,CAAC,IAAM,CAAC;4EAAE,MAAM,OAAO,EAAE,IAAI,IAAE,IAAI,WAAW;4EAAI,MAAM,OAAO,EAAE,IAAI,IAAE,IAAI,WAAW;4EAAI,UAAU,OAAO,EAAE,QAAQ,IAAE,IAAI,WAAW;4EAAI,KAAK,EAAE,GAAG;wEAAC,CAAC;gEAAK;0EAAG;;;;;;;qFAG1d,6LAAC;wDAAO,WAAU;wDAAuF,SAAS,CAAC;4DAAQ,EAAE,eAAe;4DAAI,QAAQ,EAAE,IAAI;4DAAG,UAAU,EAAE,IAAI;4DAAG,aAAa,EAAE,QAAQ;4DAAG,gBAAgB;4DAAO,WAAW,eAAe;wDAAI;kEAAG;;;;;;;;;;;;2CAzBrQ,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC;;;;;oCA8BpC;;;;;;;;;;;;sCAKJ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAoC;;;;;;8CACnD,6LAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,MAAM;4CAAU,UAAU,mBAAmB,SAAS;4CAAE,MAAM,eAAe,WAAW;wCAAC;wCAC3F;4CAAE,MAAM;4CAAQ,UAAU,mBAAmB,SAAS;4CAAE,MAAM,eAAe,WAAW;wCAAC;qCAC1F,CAAC,GAAG,CAAC,CAAC;wCACL,MAAM,WAAW,SAAS,EAAE,IAAI,IAAI,WAAW,EAAE,IAAI,IAAI,cAAc,EAAE,QAAQ;wCACjF,MAAM,iBAAiB,cAAc,EAAE,IAAI,EAAE,EAAE,IAAI,EAAE,EAAE,QAAQ;wCAC/D,MAAM,OAAO,aAAa,IAAI,CAAC,CAAC,KAAO,GAAG,IAAI,KAAK,OAAO,EAAE,IAAI,EAAE,WAAW,MAAM,GAAG,IAAI,KAAK,OAAO,EAAE,IAAI,EAAE,WAAW,MAAM,GAAG,QAAQ,KAAK,OAAO,EAAE,QAAQ,EAAE,WAAW;wCAC7K,qBACE,6LAAC;4CAEC,MAAK;4CACL,UAAU;4CACV,WAAW,CAAC,4EAA4E,EACtF,WAAW,kCAAkC,2BAC9C,CAAC,EAAE,iBAAiB,kCAAkC,IAAI;4CAC3D,SAAS;gDACP,IAAI,gBAAgB;gDACpB,QAAQ,EAAE,IAAI;gDACd,UAAU,EAAE,IAAI;gDAChB,aAAa,EAAE,QAAQ;4CACzB;;8DAEA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;;gEAAgE;gEAAO,EAAE,IAAI;;;;;;;sEAC7F,6LAAC;4DAAK,WAAU;;gEAA6D;gEAAW,EAAE,QAAQ;;;;;;;;;;;;;8DAEpG,6LAAC;oDAAI,WAAU;8DACZ,+BACC;;0EACE,6LAAC;gEAAO,WAAU;gEAAsD,SAAS,CAAC;oEAAQ,EAAE,eAAe;oEAAI,IAAG,MAAM,KAAK,OAAO,IAAI,CAAC,CAAC,oCAAoC,EAAE,KAAK,GAAG,EAAE;gEAAG;0EAAG;;;;;;0EAChM,6LAAC;gEAAO,WAAU;gEAAsD,SAAS,CAAC;oEAAQ,EAAE,eAAe;oEAAI,IAAG,MAAM,KAAK,OAAO,IAAI,CAAC,CAAC,oCAAoC,EAAE,KAAK,GAAG,EAAE;gEAAG;0EAAG;;;;;;0EAChM,6LAAC;gEAAO,WAAU;gEAAkE,SAAS,OAAO;oEAAQ,EAAE,eAAe;oEAAI,IAAG,CAAC,MAAM,KAAK;oEAAQ,MAAM,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,GAAG;oEAAG,MAAM,MAAM,MAAM,CAAA,GAAA,iIAAA,CAAA,sBAAmB,AAAD;oEAAK,MAAM,YAAY,KAAK,MAAM,aAAa,KAAK,QAAQ,EAAE;oEAAE,gBAAgB,UAAU,GAAG,CAAC,CAAC,IAAM,CAAC;4EAAE,MAAM,OAAO,EAAE,IAAI,IAAE,IAAI,WAAW;4EAAI,MAAM,OAAO,EAAE,IAAI,IAAE,IAAI,WAAW;4EAAI,UAAU,OAAO,EAAE,QAAQ,IAAE,IAAI,WAAW;4EAAI,KAAK,EAAE,GAAG;wEAAC,CAAC;gEAAK;0EAAG;;;;;;;qFAG1d,6LAAC;wDAAO,WAAU;wDAAuF,SAAS,CAAC;4DAAQ,EAAE,eAAe;4DAAI,QAAQ,EAAE,IAAI;4DAAG,UAAU,EAAE,IAAI;4DAAG,aAAa,EAAE,QAAQ;4DAAG,gBAAgB;4DAAO,WAAW,eAAe;wDAAI;kEAAG;;;;;;;;;;;;2CAzBrQ,GAAG,EAAE,IAAI,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC;;;;;oCA8BpC;;;;;;;;;;;;;;;;;;8BAKN,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAM,WAAU;;gCAA2C;8CAChD,6LAAC;oCAAK,WAAU;8CAAiB;;;;;;;;;;;;sCAE7C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,OAAO;wCAAE,QAAQ;oCAAc;8CAE/B,cAAA,6LAAC;wCAAO,OAAM;kDAAM;;;;;;;;;;;8CAEtB,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,WAAW;oCACX,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC;wCACT,yDAAyD;wCACzD,MAAM,MAAM,EAAE,MAAM,CAAC,KAAK;wCAC1B,IAAI,aAAa,IAAI,CAAC,MAAM,aAAa;oCAC3C;oCACA,aAAY;;;;;;;;;;;;;;;;;;8BAiBlB,6LAAC;oBACC,WAAW,CAAC,+CAA+C,EACzD,gBACI,gGACA,kCACJ;oBACF,OACE,gBACI;wBACE,cAAc;wBACd,YACE;oBACJ,IACA,CAAC;oBAEP,UAAU,CAAC;oBACX,SAAS;wBACP,IAAI,eAAe;4BACjB,gBAAgB;4BAChB;wBACF;oBACF;8BACD;;;;;;;;;;;;IAKP;IAEA,yCAAyC;IACzC,MAAM,WAAW,kBAAkB,cAAc;IAEjD,4CAA4C;IAC5C,MAAM,kBAAkB,CAAC,CAAC,CACxB,SAAS,IAAI,IACb,SAAS,SAAS,IAClB,SAAS,OAAO,IAChB,SAAS,KAAK,IACd,SAAS,KAAK,AAChB;IAEA,wCAAwC;IACxC,MAAM,WAAW,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IAEvD,mDAAmD;IACnD,8BAA8B;IAC9B,MAAM,eAAe,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,IAAI,EAAE,kBAAkB;IACvE,gFAAgF;IAChF,IAAI,iBAAiB;IACrB,IAAI,aAAa,MAAM,GAAG,GAAG;QAC3B,iBAAiB,aAAa,MAAM,CAAC,CAAC,GAAG;YACvC,uDAAuD;YACvD,MAAM,QAAQ,EAAE,SAAS,GAAG,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK;YAC9D,MAAM,QAAQ,EAAE,SAAS,GAAG,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK;YAC9D,OAAO,QAAQ,QAAQ,IAAI;QAC7B;IACF;IACA,8EAA8E;IAC9E,MAAM,mBAAmB,gBAAgB,OAAO,gBAAgB,QAAQ;IACxE,QAAQ,GAAG,CAAC,kBAAkB,oBAAoB,cAAc;IAEhE,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,MAAK;wBACL,WAAW,CAAC,sGAAsG,EAChH,CAAC,eACG,4BACA,uDACJ;wBACF,OAAO;4BAAE,SAAS;wBAAO;wBACzB,SAAS,IAAM,gBAAgB;wBAC/B,UAAU;;4BAET;0CACD,6LAAC;gCACC,OAAM;gCACN,QAAO;gCACP,SAAQ;gCACR,MAAK;gCACL,OAAM;;kDAEN,6LAAC;wCAAO,IAAG;wCAAK,IAAG;wCAAK,GAAE;wCAAK,MAAK;;;;;;kDACpC,6LAAC;wCACC,GAAE;wCACF,QAAO;wCACP,aAAY;wCACZ,eAAc;wCACd,gBAAe;;;;;;;;;;;;;;;;;;kCAIrB,6LAAC;wBACC,WAAW,CAAC,uBAAuB,EACjC,CAAC,eAAe,iCAAiC,iBACjD;kCACH;;;;;;kCAID,6LAAC;wBACC,MAAK;wBACL,WAAW,CAAC,sGAAsG,EAChH,gBAAgB,gBACZ,4BACA,sDACL,CAAC,EAAE,gBAAgB,mBAAmB,sBAAsB;wBAC7D,OAAO;4BAAE,SAAS;wBAAO;wBACzB,SAAS,IAAM,iBAAiB,gBAAgB;wBAChD,UAAU,gBAAgB,IAAI,CAAC;wBAC/B,UAAU,CAAC;kCACZ;;;;;;kCAGD,6LAAC;wBACC,WAAW,CAAC,uBAAuB,EACjC,eAAe,iCAAiC,iBAChD;kCACH;;;;;;;;;;;;0BAKH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCASf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAK,WAAU;0CACb,IAAI,KAAK,cAAc,eAAe,cAAc,CAAC,WAAW;oCAC/D,OAAO;oCACP,MAAM;gCACR;;;;;;0CAEF,6LAAC;gCACC,WAAU;gCACV,SAAS;0CAER;;;;;;0CAEH,6LAAC;gCACC,WAAU;gCACV,SAAS;0CAER;;;;;;;;;;;;kCAGL,6LAAC;wBACC,MAAK;wBACL,WAAU;wBACV,SAAS;4BACP,iBAAiB;4BACjB,kBAAkB;wBACpB;kCAEA,cAAA,6LAAC;4BAAK,WAAU;sCAAuE;;;;;;;;;;;;;;;;;0BAM3F,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;;8CACf,6LAAC;8CACC,cAAA,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;0DACrE,6LAAC;gDAAG,WAAU;0DAAuD;;;;;;;;;;;;;;;;;8CAGzE,6LAAC;8CACE,aAAa,MAAM,KAAK,kBACvB,6LAAC;kDAAG,cAAA,6LAAC;4CAAG,SAAS;4CAAG,WAAU;sDAAgC;;;;;;;;;;+CAC5D,aAAa,GAAG,CAAC,CAAC,MAAM,oBAC1B,6LAAC;4CAAkB,WAAU;;8DAC3B,6LAAC;oDAAG,WAAU;8DAAO,KAAK,IAAI;;;;;;8DAC9B,6LAAC;oDAAG,WAAU;8DAAgC,UAAU,KAAK,KAAK;;;;;;8DAClE,6LAAC;oDAAG,WAAU;8DAAO,KAAK,QAAQ;;;;;;8DAClC,6LAAC;oDAAG,WAAU;8DAAO,KAAK,SAAS;;;;;;8DACnC,6LAAC;oDAAG,WAAU;8DAAO,KAAK,OAAO;;;;;;8DACjC,6LAAC;oDAAG,WAAU;8DACX,KAAK,KAAK,kBACT,6LAAC;wDAAK,WAAU;kEACd,cAAA,6LAAC;4DAAK,OAAO;gEAAE,YAAY,KAAK,KAAK;gEAAE,OAAO;gEAAI,QAAQ;gEAAI,cAAc;gEAAO,QAAQ;gEAAkB,SAAS;4DAAe;;;;;;;;;;;;;;;;8DAI3I,6LAAC;oDAAG,WAAU;8DACX,MAAM,wBACL,6LAAC;wDAAK,OAAO;4DAAE,OAAO;4DAAS,YAAY;wDAAI;kEAAG;;;;;6EAElD,6LAAC;wDAAK,OAAO;4DAAE,OAAO;4DAAO,YAAY;wDAAI;kEAAG;;;;;;;;;;;8DAGpD,6LAAC;oDAAG,WAAU;;wDACX,eAAe,KAAK,SAAS;wDAC7B,MAAM,SAAS,kCACd,6LAAC;4DAAK,WAAU;sEAA2E;;;;;;;;;;;;8DAG/F,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAO,WAAU;4DAAoD,SAAS;gEAC7E,YAAY;oEAAE,GAAG,IAAI;gEAAC;gEACtB,iBAAiB;gEACjB,kBAAkB;4DACpB;sEAAG;;;;;;sEACH,6LAAC;4DAAO,WAAU;4DAAkD,SAAS;gEAC3E,SAAS,CAAA,QAAS,MAAM,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;4DACjD;sEAAG;;;;;;;;;;;;;2CAlCE,KAAK,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;0BA2C3B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAM,WAAU;;sCACf,6LAAC;sCACC,cAAA,6LAAC;0CACE,SAAS,GAAG,CAAC,CAAC,oBACb,6LAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;;;;;;;;;;;sCAQb,6LAAC;sCACE,SAAS,GAAG,CAAC,CAAC,MAAM,uBACnB,6LAAC;8CACE,KAAK,GAAG,CAAC,CAAC,KAAK;wCACd,IAAI,CAAC,KAAK,qBAAO,6LAAC,UAAQ;;;;;wCAC1B,IAAI,aAAa;wCACjB,MAAM,UAAU,GAAG,aAAa,CAAC,EAAE,OAAO,gBAAgB,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK,QAAQ,CAAC,GAAG,MAAM;wCAC/G,qBAAqB;wCACrB,MAAM,WAAW,aAAa,CAAC,QAAQ;wCACvC,IAAI,UAAU;4CACZ,qFAAqF;4CACrF,qBACE,6LAAC;gDAEC,WAAU;gDACV,OAAO;oDAAE,UAAU;oDAAK,QAAQ;gDAAG;0DAEnC,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,QAAQ;wDAAQ,UAAU;oDAAW;oDAC9C,SAAS,IAAM,CAAC,cAAc,gBAAgB;;sEAG9C,6LAAC;4DACC,WAAU;4DACV,OAAO;gEAAE,YAAY;gEAAG,UAAU;4DAAG;4DACrC,OAAM;4DACN,SAAS,CAAA;gEACP,EAAE,eAAe;gEACjB,qBAAqB;4DACvB;sEACD;;;;;;sEAGD,6LAAC;4DAAK,WAAU;sEACb,OAAO,KAAK,QAAQ,CAAC,GAAG;;;;;;sEAE3B,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAK,WAAU;gEAAwB,OAAO;oEAAE,OAAO;gEAAO;0EAC5D,CAAC,SAAS,QAAQ,KAAK,QAAQ,QAAQ,IAAI,IAC1C,MACA,SAAS,KAAK;;;;;;;;;;;;;;;;;+CA5BjB;;;;;wCAkCX;wCACA,6BAA6B;wCAC7B,MAAM,aAAa,4BAA4B,OAAO,cAAc,eAAe;wCACnF,MAAM,iBAAiB,aACnB;4CACE,QAAQ,CAAC,UAAU,EAAE,WAAW,KAAK,EAAE;4CACvC,OAAO,WAAW,KAAK;4CACvB,YAAY;wCACd,IACA,CAAC;wCACL,8CAA8C;wCAC9C,MAAM,cAAc,aAAa,WAAW,KAAK,GAAG;wCACpD,MAAM,iBAAiB,aAAa,WAAW,QAAQ,GAAG;wCAC1D,qBACE,6LAAC;4CAEC,WAAU;4CACV,OAAO;gDAAE,UAAU;gDAAK,QAAQ;4CAAG;sDAEnC,cAAA,6LAAC;gDACC,WAAU;gDACV,OACE,6EAEI;gDAEN,SAAS,IAAM,CAAC,cAAc,gBAAgB;;oDAG7C,4BACC,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,YAAY;4DAAQ,OAAO,WAAW,KAAK;4DAAE,QAAQ,CAAC,UAAU,EAAE,WAAW,KAAK,EAAE;4DAAE,QAAQ;wDAAE;wDACzG,OAAO,WAAW,IAAI;kEAErB,WAAW,IAAI;;;;;;kEAGpB,6LAAC;wDAAK,WAAU;kEACb,OAAO,KAAK,QAAQ,CAAC,GAAG;;;;;;kEAE3B,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;4DAAwB,OAAO,aAAa;gEAAE,OAAO,WAAW,KAAK;4DAAC,IAAI;gEAAE,OAAO;4DAAO;sEACvG,CAAC,mBAAmB,QAAQ,QAAQ,IAAI,IACvC,MACA;;;;;;;;;;;;;;;;;2CA9BH;;;;;oCAoCX;mCAhGO;;;;;;;;;;;;;;;;;;;;;0BAuGjB,6LAAC;gBACC,WAAW,CAAC,oDAAoD,EAC9D,UACI,2EACA,6CACJ;gBACF,UAAU,CAAC;gBACX,SAAS;0BAER,yBACC,6LAAC;oBAAK,WAAU;;sCACd,6LAAC;4BAAI,WAAU;4BAAuC,OAAM;4BAA6B,MAAK;4BAAO,SAAQ;;8CAC3G,6LAAC;oCAAO,WAAU;oCAAa,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAK,QAAO;oCAAe,aAAY;;;;;;8CACxF,6LAAC;oCAAK,WAAU;oCAAa,MAAK;oCAAe,GAAE;;;;;;;;;;;;wBAC/C;;;;;;2BAIR;;;;;;YAIH,8BACC,6LAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,YAAY;gBAAsB;gBAE3C,SAAS,IAAM,gBAAgB;0BAE/B,cAAA,6LAAC;oBACC,WAAU;oBACV,SAAS,CAAC,IAAM,EAAE,eAAe;;sCAEjC,6LAAC;4BAAG,WAAU;;gCACX,aAAa,GAAG,IAAK,aAAa,CAAC,GAAG,aAAa,CAAC,EAAE,OAAO,gBAAgB,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,aAAa,GAAG,EAAE,QAAQ,CAAC,GAAG,MAAM,CAAC,GAC7I,WACA;gCAAO;gCAAI;8CAEf,6LAAC;oCAAK,WAAU;8CAAiB;;;;;;;;;;;;sCAEnC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAU;oCACV,OAAO;oCACP,OAAO;wCAAE,QAAQ;oCAAc;oCAC/B,QAAQ;oCACR,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;8CAEhD,cAAA,6LAAC;wCAAO,OAAM;kDAAM;;;;;;;;;;;8CAEtB,6LAAC;oCACC,MAAK;oCACL,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,UAAU,CAAC;wCACT,MAAM,MAAM,EAAE,MAAM,CAAC,KAAK;wCAC1B,8DAA8D;wCAC9D,IAAI,uBAAuB,IAAI,CAAC,MAAM;4CACpC,cAAc;wCAChB;oCACF;oCACA,WAAW,CAAA;wCACT,IAAI;4CAAC;4CAAK;4CAAK;4CAAK;yCAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,GAAG;4CACxC,EAAE,cAAc;wCAClB;oCACF;oCACA,aAAY;;;;;;;;;;;;sCAGhB,6LAAC;4BACC,WAAW,CAAC,iCAAiC,EAAE,CAAC,aAAa,8CAA8C,0EAA0E;4BACrL,SAAS;4BACT,UAAU,CAAC;sCAEV,aAAa,GAAG,IAAK,aAAa,CAAC,GAAG,aAAa,CAAC,EAAE,OAAO,gBAAgB,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,aAAa,GAAG,EAAE,QAAQ,CAAC,GAAG,MAAM,CAAC,GAC7I,WACA;;;;;;;;;;;;;;;;;YAMX,+BACC,6LAAC;gBACC,WAAU;gBACV,OAAO;oBAAE,YAAY;gBAAsB;gBAC3C,SAAS;oBAAO,iBAAiB,QACjC,YAAY;wBACA,MAAM;wBACN,SAAS;wBACT,WAAW;wBACX,SAAS;wBACT,OAAO;wBACP,UAAU;wBACV,UAAU;wBACV,OAAO;oBACT;gBAAG;0BAEb,cAAA,6LAAC;oBACC,SAAS,CAAC,IAAM,EAAE,eAAe;oBACjC,WAAU;;sCAWV,6LAAC;4BACC,WAAU;4BACV,SAAS,IAAM,iBAAiB;sCAEhC,cAAA,6LAAC,iJAAA,CAAA,UAAO;;;;;;;;;;sCAGV,6LAAC;4BACC,WAAU;;8CAOV,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA6B;;;;;;0DAC7C,6LAAC;gDACC,MAAK;gDACL,gBAAc,SAAS,OAAO;gDAC9B,SAAS,IACP,YAAY,CAAC,IAAM,CAAC;4DAAE,GAAG,CAAC;4DAAE,SAAS,CAAC,EAAE,OAAO;wDAAC,CAAC;gDAEnD,WAAW,CAAC;;;sBAGV,EACE,SAAS,OAAO,GACZ,iDACA,cACL;oBACH,CAAC;gDACD,OAAO;oDACL,UAAU;oDACV,WAAW;oDACX,WAAW,SAAS,OAAO,GACvB,wBACA;oDACJ,SAAS;gDACX;0DAEA,cAAA,6LAAC;oDACC,WAAW,CAAC;;;;wBAIV,EAAE,SAAS,OAAO,GAAG,kBAAkB,gBAAgB;sBACzD,CAAC;oDACD,OAAO;wDACL,WAAW;oDACb;;;;;;;;;;;;;;;;;;;;;;8CAKR,6LAAC;oCAAM,WAAU;;wCAA+C;sDACrD,6LAAC;4CAAK,WAAU;sDAAiB;;;;;;;;;;;;8CAG5C,6LAAC;oCACC,WAAU;oCACV,aAAY;oCACZ,OAAO,SAAS,IAAI;oCACpB,WAAW;oCACX,UAAU,CAAC,IACT,YAAY,CAAC,IAAM,CAAC;gDAAE,GAAG,CAAC;gDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4CAAC,CAAC;;;;;;8CAGtD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;wDAAmD;sEACxD,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DAE7C,6LAAC;oDACC,MAAK;oDACL,WAAU;oDACV,OAAO,SAAS,SAAS;oDACzB,iBAAiB;oDACjB,UAAU,CAAC;wDACT,YAAY,CAAC,IAAM,CAAC;gEAAE,GAAG,CAAC;gEAAE,WAAW,EAAE,MAAM,CAAC,KAAK;gEAAE,SAAS;4DAAG,CAAC;oDACtE;;;;;;;;;;;;sDAGJ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;wDAAmD;sEAC1D,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DAE3C,6LAAC;oDACC,MAAK;oDACL,WAAU;oDACV,OAAO,SAAS,OAAO;oDACvB,KAAK,SAAS,SAAS,GAAG,IAAI,KAAK,IAAI,KAAK,SAAS,SAAS,EAAE,OAAO,KAAK,KAAG,KAAG,KAAG,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;oDACzH,UAAU,CAAC;wDACT,MAAM,MAAM,EAAE,MAAM,CAAC,KAAK;wDAC1B,IAAI,SAAS,SAAS,IAAI,OAAO,SAAS,SAAS,EAAE;4DACnD,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC;4DACX;wDACF;wDACA,YAAY,CAAC,IAAM,CAAC;gEAAE,GAAG,CAAC;gEAAE,SAAS;4DAAI,CAAC;oDAC5C;;;;;;;;;;;;;;;;;;8CAIN,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAM,WAAU;;gDAAmD;8DACzD,6LAAC;oDAAK,WAAU;8DAAiB;;;;;;;;;;;;sDAE5C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,WAAU;oDACV,OAAO,SAAS,QAAQ;oDAC9B,QAAQ;oDACR,OAAO;wDAAE,QAAQ;oDAAc;8DAMZ,cAAA,6LAAC;wDAAO,OAAM;kEAAM;;;;;;;;;;;8DAGnC,6LAAC;oDACC,MAAK;oDACL,KAAI;oDACJ,WAAW;oDACX,WAAU;oDACV,OAAO,SAAS,KAAK;oDACrB,UAAU,CAAC;wDACT,MAAM,MAAM,EAAE,MAAM,CAAC,KAAK;wDAC1B,IAAI,aAAa,IAAI,CAAC,MAAM;4DAC1B,YAAY,CAAC,IAAM,CAAC;oEAAE,GAAG,CAAC;oEAAE,OAAO;gEAAI,CAAC;wDAC1C;oDACF;oDACA,aAAY;;;;;;;;;;;;;;;;;;8CAIlB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;8DAAmD;;;;;;8DAGpE,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAU;wDACV,OAAO,SAAS,QAAQ,IAAI;wDAC5B,UAAU,CAAA;4DACR,YAAY,CAAA,IAAK,CAAC;oEAAE,GAAG,CAAC;oEAAE,UAAU,EAAE,MAAM,CAAC,KAAK;gEAAC,CAAC;wDACtD;;0EAEA,6LAAC;gEAAO,WAAU;gEAA0B,OAAM;0EAAG;;;;;;4DACpD;mEAAI,MAAM;6DAAI,CAAC,GAAG,CAAC,CAAC,GAAG;gEACtB,MAAM,MAAM,CAAC,IAAI,CAAC,IAAI;gEACtB,qBACE,6LAAC;oEAAiB,OAAO;8EAAM;mEAAlB;;;;;4DAEjB;;;;;;;;;;;;;;;;;;sDAIN,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;wDAAmD;sEACrD,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;;;;;;;8DAEhD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEACC,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU,CAAC,IACT,YAAY,CAAC,IAAM,CAAC;4EAAE,GAAG,CAAC;4EAAE,OAAO,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;gEAErD,WAAU;gEACV,OAAO;oEACL,iBAAiB,SAAS,KAAK;oEAC/B,OAAO;oEACP,YAAY;oEACZ,YAAY;gEACd;;;;;;;;;;;sEAGJ,6LAAC;4DACC,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,aAAY;4DACZ,UAAU,CAAC,IACT,YAAY,CAAC,IAAM,CAAC;wEAAE,GAAG,CAAC;wEAAE,OAAO,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DAErD,WAAU;4DACV,OAAO;gEAAE,YAAY;4DAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAqBtC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,WAAU;oCACV,SAAS;wCACP,iBAAiB;wCACjB,YAAY;4CACV,MAAM;4CACN,SAAS;4CACT,WAAW;4CACX,SAAS;4CACT,OAAO;4CACP,UAAU;4CACV,UAAU;4CACV,OAAO;wCACT;wCACA,kBAAkB;oCACpB;8CACD;;;;;;gCAGA,mBAAmB,qBAClB,6LAAC;oCACC,WAAW,CAAC,gCAAgC,EAAE,kBAAkB,4DAA4D,6CAA6C;oCACzK,UAAU,CAAC;oCACX,SAAS;wCACP,IAAI,CAAC,iBAAiB;wCACtB,MAAM,eAAe,kBAAkB,OAAO,UAAU;wCACxD,IAAI,cAAc;4CAChB,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,yBAAyB,EAAE,cAAc;4CACrD;wCACF;wCACA,SAAS,CAAA;4CACP,MAAM,UAAU,MAAM,GAAG,CAAC,CAAC,GAAG,IAC5B,MAAM,iBACF;oDACE,GAAG,QAAQ;oDACX,UAAU,SAAS,QAAQ,GAAG,OAAO,SAAS,QAAQ,IAAI;oDAC1D,WAAW,IAAI,OAAO,WAAW;gDACnC,IACA;4CAEN,WAAW;gDACT,iBAAiB;gDACjB,YAAY;oDACV,MAAM;oDACN,SAAS;oDACT,WAAW;oDACX,SAAS;oDACT,OAAO;oDACP,UAAU;oDACV,UAAU;oDACV,OAAO;gDACT;gDACA,kBAAkB;gDAClB,mBAAmB,CAAA,IAAK,IAAI;4CAC9B,GAAG;4CACH,OAAO;wCACT;oCACF;8CACD;;;;;yDAID,6LAAC;oCACC,WAAW,CAAC,gCAAgC,EAAE,kBAAkB,4DAA4D,6CAA6C;oCACzK,UAAU,CAAC;oCACX,SAAS;wCACP,IAAI,CAAC,iBAAiB;wCACtB,MAAM,eAAe,kBAAkB,OAAO,UAAU;wCACxD,IAAI,cAAc;4CAChB,sJAAA,CAAA,QAAK,CAAC,IAAI,CAAC,CAAC,yBAAyB,EAAE,cAAc;4CACrD;wCACF;wCACA,SAAS,CAAC;4CACR,MAAM,UAAU;mDACX;gDACH;oDACE,GAAG,QAAQ;oDACX,UAAU,SAAS,QAAQ,GAAG,OAAO,SAAS,QAAQ,IAAI;oDAC1D,WAAW,IAAI,OAAO,WAAW;gDACnC;6CACD;4CACD,WAAW;gDACT,iBAAiB;gDACjB,YAAY;oDACV,MAAM;oDACN,SAAS;oDACT,WAAW;oDACX,SAAS;oDACT,OAAO;oDACP,UAAU;oDACV,UAAU;oDACV,OAAO;gDACT;gDACA,mBAAmB,CAAA,IAAK,IAAI;4CAC9B,GAAG;4CACH,OAAO;wCACT;oCACF;8CACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYjB;GAh0CwB;;QACP,qIAAA,CAAA,YAAS;;;KADF;AAk0CxB,SAAS,UAAU,KAAK;IACtB,IAAI,UAAU,QAAQ,UAAU,WAAW,OAAO;IAClD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QAAE,OAAO;QAAY,UAAU;IAAM,GAAG,MAAM,CAAC;AACvF;AAEA,qCAAqC;AACrC,SAAS,4BAA4B,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG;IAC1D,MAAM,UAAU,GAAG,KAAK,CAAC,EAAE,OAAO,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK,QAAQ,CAAC,GAAG,MAAM;IAC/F,MAAM,WAAW,MAAM,MAAM,CAAC,CAAA;QAC5B,IAAI,KAAK,QAAQ,KAAK,SAAS,KAAK,OAAO,KAAK,OAAO,OAAO;QAC9D,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,OAAO,EAAE,OAAO;QAC7C,MAAM,QAAQ,KAAK,SAAS;QAC5B,MAAM,MAAM,KAAK,OAAO;QACxB,OAAO,SAAS,WAAW,WAAW;IACxC;IACA,IAAI,SAAS,MAAM,KAAK,GAAG,OAAO;IAClC,SAAS,IAAI,CAAC,CAAC,GAAG;QAChB,IAAI,OAAO,EAAE,QAAQ,MAAM,OAAO,EAAE,QAAQ,GAAG;YAC7C,OAAO,OAAO,EAAE,QAAQ,IAAI,OAAO,EAAE,QAAQ;QAC/C;QACA,OAAO,IAAI,KAAK,EAAE,SAAS,IAAI,IAAI,KAAK,EAAE,SAAS;IACrD;IACA,OAAO,QAAQ,CAAC,EAAE;AACpB", "debugId": null}}]}