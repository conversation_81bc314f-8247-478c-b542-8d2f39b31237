'use client'

import { useEffect, useState } from 'react';
import {
  getAdPricingRuleById,
  updateBasePrice,
  updateIndividualRule,
  deleteIndividualRule,
  deleteOverrightRuleDate,
  createOverridePricingRuleInResource
} from '../../../../../service/adManagement';
import { useParams } from 'next/navigation';
import RuleModal from '../../../../../components/common/RuleModal';
import CalendarRuleView from '../../../../../components/common/CalendarRuleView';
import { toast } from 'react-toastify';

function formatDate(dateStr) {
  if (!dateStr) return '';
  const d = new Date(dateStr);
  return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0');
}

function formatDateTime(dateStr) {
  if (!dateStr) return '';
  const d = new Date(dateStr);
  return d.toLocaleString('en-JM', { dateStyle: 'medium', timeStyle: 'short' });
}

const formatJMD = (value) => {
  if (value === undefined || value === null || value === '') return '';
  return 'J$' + Number(value).toLocaleString('en-JM');
};

export default function EditAdPlanPage() {
  const params = useParams();
  const [ad, setAd] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [basePrice, setBasePrice] = useState('');
  const [savingBase, setSavingBase] = useState(false);
  const [calendarMonth, setCalendarMonth] = useState(new Date().getMonth());
  const [calendarYear, setCalendarYear] = useState(new Date().getFullYear());
  const [dateOverrides, setDateOverrides] = useState({}); // { 'YYYY-MM-DD': { price, currency } }
  const [ruleModalVisible, setRuleModalVisible] = useState(false);
  const [ruleModalInitial, setRuleModalInitial] = useState(null);
  const [ruleModalIsEdit, setRuleModalIsEdit] = useState(false);
  const [ruleModalIdx, setRuleModalIdx] = useState(null);
  const [rules, setRules] = useState([]);
  const [originalBasePrice, setOriginalBasePrice] = useState('');
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState(null); // { type: 'rule'|'override', id: string, label: string }
  const [deleting, setDeleting] = useState(false);
  const [showOverrideModal, setShowOverrideModal] = useState(false);
  const [selectedOverrideDate, setSelectedOverrideDate] = useState('');
  const [overrideAmount, setOverrideAmount] = useState('');
  const [savingOverride, setSavingOverride] = useState(false);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [showDeactivateModal, setShowDeactivateModal] = useState(false);
  const [showCopyModal, setShowCopyModal] = useState(false);
  const currentYear = new Date().getFullYear();
  const years = Array.from({ length: 1 + (currentYear + 5 - 2020) }, (_, i) => 2020 + i);


  async function fetchData() {
    setLoading(true);
    setError(null);
    const res = await getAdPricingRuleById(params['plan.id']);
    if (res && res.data && res.data.resource) {
      setAd(res.data.resource);
      // Find the "base price" rule in pricingRules and set its price as basePrice
      let basePriceValue = '';
      if (Array.isArray(res.data.resource.pricingRules)) {
        const baseRule = res.data.resource.pricingRules.find(
          rule => rule.name && rule.name.toLowerCase() === "base price"
        );
        if (baseRule && baseRule.price !== undefined && baseRule.price !== null) {
          basePriceValue = baseRule.price;
        }
      }
      setBasePrice(basePriceValue);
      setOriginalBasePrice(basePriceValue);
      if (res.data.resource.pricingRules) setRules(res.data.resource.pricingRules);
      setDateOverrides({}); // Clear old overrides before setting new
      if (res.data.resource.overrideRules) {
        res.data.resource.overrideRules.forEach(override => {
          const date = new Date(override.date);
          setDateOverrides(prev => ({
            ...prev,
            [`${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`]: override
          }));
        });
      }
    } else if (res && res.data && res.data.message) {
      setError(res.data.message);
    } else {
      setError('Something went wrong');
    }
    setLoading(false);
  }

  useEffect(() => {
    if (!params['plan.id']) return;
    fetchData();
  }, [params]);

  async function handleBasePriceSave() {
    setSavingBase(true);
    try {
      const res = await updateBasePrice({
        price: basePrice,
        resourceId: params['plan.id'],
        baseId: rules?.find(
          rule => rule.name && rule.name.toLowerCase() === "base price"
        )?._id
      });
      if (res && res.data && (res.data.success || res.data.status === 'success' || res.status === 200)) {
        toast.success('Base price updated successfully!');
        fetchData();
      } else {
        toast.error(res?.data?.message || 'Failed to update base price');
      }
    } catch (err) {
      toast.error(err?.message || 'Failed to update base price');
    } finally {
      setSavingBase(false);
    }
  }

  async function handleDeleteRule(ruleId) {
    setDeleteTarget({ type: 'rule', id: ruleId, label: rules.find(r => r._id === ruleId)?.name });
    setShowDeleteModal(true);
  }

  async function handleDeleteOverride(overrideId) {
    setDeleteTarget({ type: 'override', id: overrideId, label: Object.values(dateOverrides).find(o => o._id === overrideId)?.date });
    setShowDeleteModal(true);
  }

  // Unified delete handler
  const handleDeleteConfirm = async () => {
    if (!deleteTarget) return;
    setDeleting(true);
    try {
      if (deleteTarget.type === 'rule') {
        const res = await deleteIndividualRule({
          resourceId: params['plan.id'],
          ruleId: deleteTarget.id
        });
        if (res && (res.status === 200 || res.status === 204)) {
          toast.success('Rule deleted successfully!');
        } else {
          toast.error(res?.data?.message || 'Failed to delete rule');
          setDeleting(false);
          return; // Don't close modal
        }
      } else if (deleteTarget.type === 'override') {
        const res = await deleteOverrightRuleDate({
          resourceId: params['plan.id'],
          overrideId: deleteTarget.id
        });
        if (res && (res.status === 200 || res.status === 204)) {
          toast.success('Override rule deleted successfully!');
        } else {
          toast.error(res?.data?.message || 'Failed to delete override rule');
          setDeleting(false);
          return; // Don't close modal
        }
      }
      fetchData();
      setShowDeleteModal(false);
      setDeleteTarget(null);
    } catch (err) {
      toast.error(err?.message || 'Failed to delete');
      setDeleting(false);
      // Don't close modal
    } finally {
      setDeleting(false);
    }
  };

  // Calendar navigation handlers
  const handlePrevMonth = () => {
    setCalendarMonth((prev) => (prev === 0 ? 0 : prev - 1));
  };
  const handleNextMonth = () => {
    setCalendarMonth((prev) => (prev === 11 ? 11 : prev + 1));
  };

  // Calendar cell click (for override, can be extended)
  const handleCellClick = (day) => {
    // Open modal to set override price for this day
    const dateStr = `${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    setSelectedOverrideDate(dateStr);
    setOverrideAmount('');
    setShowOverrideModal(true);
  };
  const handleRemoveOverride = (day) => {
    // Optionally remove override for this day
  };

  // Save override handler
  const handleSaveOverride = async () => {
    if (!overrideAmount || !selectedOverrideDate) return;
    setSavingOverride(true);
    try {
      const res = await createOverridePricingRuleInResource({
        resourceId: params['plan.id'],
        overridePrices: [
          {
            date: selectedOverrideDate,
            price: Number(overrideAmount)
          }
        ]
      });
      if (res && (res.status === 200 || res.status === 201)) {
        toast.success(`Override set for ${selectedOverrideDate} at J$${Number(overrideAmount).toLocaleString('en-JM')}`);
        setShowOverrideModal(false);
        setSelectedOverrideDate('');
        setOverrideAmount('');
        fetchData();
      } else {
        toast.error(res?.data?.message || 'Failed to set override');
      }
    } catch (err) {
      toast.error(err?.message || 'Failed to set override');
    } finally {
      setSavingOverride(false);
    }
  };

  // Rule modal state for add/edit
  useEffect(() => {
    if (ad && ad.pricingRules) setRules(ad.pricingRules);
  }, [ad]);

  // Open edit modal
  function handleEditRule(rule, idx) {
    setRuleModalInitial(rule);
    setRuleModalIsEdit(true);
    setRuleModalIdx(idx);
    setRuleModalVisible(true);
  }
  // Open add modal
  function handleAddRule() {
    setRuleModalInitial(null);
    setRuleModalIsEdit(false);
    setRuleModalIdx(null);
    setRuleModalVisible(true);
  }
  // Save rule (edit or add)
  async function handleRuleModalSave(updatedRule) {
    if (ruleModalIsEdit && ruleModalIdx !== null) {
      await updateIndividualRule(updatedRule?._id, updatedRule);
    }
    setRuleModalVisible(false);
    setRuleModalInitial(null);
    setRuleModalIsEdit(false);
    setRuleModalIdx(null);
    fetchData();
  }

  // In EditAdPlanPage, define a callback to refresh data after rule save
  const handleRuleSaved = () => {
    fetchData();
  };

  // Find the most recent rule (excluding base price)
  const nonBaseRules = rules.filter(rule => rule.name?.toLowerCase() !== 'base price');
  const mostRecentRule = nonBaseRules.reduce((a, b) => new Date(a.updatedAt) > new Date(b.updatedAt) ? a : b, nonBaseRules[0]);
  const mostRecentRuleId = mostRecentRule?._id;

  // Custom getRuleForDate for calendar
  function getRuleForDateWithUpdatedAt(rules, year, month, day) {
    const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    const matching = rules.filter(rule => {
      if (rule.isActive === false) return false;
      if (!rule.startDate || !rule.endDate) return false;
      const start = formatDate(rule.startDate);
      const end = formatDate(rule.endDate);
      return start <= dateStr && dateStr <= end;
    });
    if (matching.length === 0) return null;
    matching.sort((a, b) => {
      if (Number(b.priority) !== Number(a.priority)) {
        return Number(b.priority) - Number(a.priority);
      }
      return new Date(b.updatedAt) - new Date(a.updatedAt);
    });
    return matching[0];
  }

  if (loading) return <div className="flex items-center justify-center min-h-[60vh]">Loading...</div>;
  if (error) return <div className="flex items-center justify-center min-h-[60vh] text-red-500">{error}</div>;
  if (!ad) return null;

  // Resource details for header
  const resourceType = ad.type ? ad.type.charAt(0).toUpperCase() + ad.type.slice(1) : '';
  const resourcePage = ad.page ? ad.page.charAt(0).toUpperCase() + ad.page.slice(1) : '';
  const resourcePosition = ad.position ? ad.position.charAt(0).toUpperCase() + ad.position.slice(1) : '';
  const resourceStatus = ad.isActive ? 'Active' : 'Inactive';

  return (
    <div className="p-6 border border-gray-200 shadow-xs rounded-lg bg-[#F9FAFB]">
      {/* Resource Info Header */}
      <div className="flex flex-wrap items-center gap-4 mb-6 justify-between">
        <div className="flex flex-wrap items-center gap-4">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="9"
          height="15"
          viewBox="0 0 9 15"
          fill="none"
          style={{ cursor: "pointer" }}
          onClick={() => window.location.href = '/ad-management/current-ad-plan'}
        >
          <path
            d="M7.99939 13.9609L1.99995 7.5L7.99939 1.03906"
            stroke="black"
            strokeWidth="1.84598"
            strokeLinecap="round"
          />
        </svg>
          <h2 className="text-2xl font-bold text-[#211F54] mr-4">{resourcePage}</h2>
          <span className={`px-3 py-1 rounded-full text-sm font-semibold ${ad.type === 'grid' ? 'bg-blue-100 text-blue-700' : 'bg-purple-100 text-purple-700'}`}>Type: {resourceType}</span>
          <span className="px-3 py-1 rounded-full text-sm font-semibold bg-gray-100 text-gray-700">Position: {resourcePosition}</span>
          <span className={`px-3 py-1 rounded-full text-sm font-semibold ${ad.isActive ? 'bg-green-100 text-green-700' : 'bg-gray-200 text-gray-500'}`}>{resourceStatus}</span>
        </div>
        <div className="flex items-center gap-2">
          <label className="font-medium text-[#211F54] mr-2">Year:</label>
          <select
            className="border border-gray-300 rounded px-3 py-1 text-sm"
            value={selectedYear}
            onChange={e => setSelectedYear(Number(e.target.value))}
          >
            {years.map(y => (
              <option key={y} value={y}>{y}</option>
            ))}
          </select>
        </div>
      </div>
      {/* Action Checkboxes */}
      <div className="flex gap-8 mb-6">
        <label className="flex items-center gap-2 cursor-pointer">
          <input
            type="checkbox"
            checked={showDeactivateModal}
            onChange={e => setShowDeactivateModal(e.target.checked)}
            className="accent-[#E1020C]"
          />
          <span className="text-[#211F54] font-medium">Deactivate this resource</span>
        </label>
        <label className="flex items-center gap-2 cursor-pointer">
          <input
            type="checkbox"
            checked={showCopyModal}
            onChange={e => setShowCopyModal(e.target.checked)}
            className="accent-[#0161AB]"
          />
          <span className="text-[#211F54] font-medium">Copy this to next year</span>
        </label>
      </div>
      {/* Deactivate Confirmation Modal */}
      {showDeactivateModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center" style={{ background: 'rgba(0,0,0,0.7)' }} onClick={() => setShowDeactivateModal(false)}>
          <div className="bg-white rounded-2xl shadow-lg max-w-sm w-full p-6" onClick={e => e.stopPropagation()}>
            <h3 className="text-xl font-semibold mb-4 text-[#211F54]">Deactivate Resource</h3>
            <p className="mb-6 text-gray-700">Are you sure you want to deactivate this resource? This action can be undone later.</p>
            <div className="flex justify-end gap-3">
              <button className="px-4 py-2 rounded bg-gray-200 text-gray-700 font-semibold hover:bg-gray-300" onClick={() => setShowDeactivateModal(false)}>Cancel</button>
              <button className="px-4 py-2 rounded bg-[#E1020C] text-white font-semibold hover:bg-[#b30009]">Confirm & Deactivate</button>
            </div>
          </div>
        </div>
      )}
      {/* Copy to Next Year Confirmation Modal */}
      {showCopyModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center" style={{ background: 'rgba(0,0,0,0.7)' }} onClick={() => setShowCopyModal(false)}>
          <div className="bg-white rounded-2xl shadow-lg max-w-sm w-full p-6" onClick={e => e.stopPropagation()}>
            <h3 className="text-xl font-semibold mb-4 text-[#211F54]">Copy to Next Year</h3>
            <p className="mb-6 text-gray-700">Are you sure you want to copy this resource and its rules to next year?</p>
            <div className="flex justify-end gap-3">
              <button className="px-4 py-2 rounded bg-gray-200 text-gray-700 font-semibold hover:bg-gray-300" onClick={() => setShowCopyModal(false)}>Cancel</button>
              <button className="px-4 py-2 rounded bg-[#0161AB] text-white font-semibold hover:bg-[#024e8c]">Confirm & Copy</button>
            </div>
          </div>
        </div>
      )}
      {/* Base Price Edit */}
      <div className="mb-8 p-6 rounded-lg bg-white shadow flex flex-wrap gap-8 border border-gray-100 items-center">
        <div className="min-w-[180px]">
          <label className="block font-[poppins]  text-[#211F54] text-sm mb-2">
                Base Price<span className="text-[#E1020C]"></span>
              </label>
          <input
          type="number"
          className="w-full border text-sm border-gray-200 rounded-lg px-4 py-3 font-[poppins] bg-white mb-4"
          value={basePrice}
          onChange={e => {
            // Only allow up to 10 digits, no negative, no +, no e
            let val = e.target.value.replace(/[^0-9]/g, '');
            if (val.length > 10) val = val.slice(0, 10);
            setBasePrice(val);
          }}
          onKeyDown={e => {
            // Prevent entering '-', '+', 'e', '.'
            if (["-", "+", "e", "."].includes(e.key)) {
              e.preventDefault();
            }
          }}
          onPaste={e => {
            // Prevent pasting non-numeric or too long
            const paste = e.clipboardData.getData('text');
            if (!/^\d{1,10}$/.test(paste)) {
              e.preventDefault();
            }
          }}
          maxLength={10}
        />
        </div>
        <button
          onClick={handleBasePriceSave}
          disabled={
            !basePrice ||
            basePrice === originalBasePrice ||
            savingBase
          }
          className={`px-4 py-2 rounded shadow font-semibold ${
            (!basePrice || basePrice === originalBasePrice || savingBase)
              ? 'bg-gray-400 text-white cursor-not-allowed'
              : 'bg-[#0161AB] text-white cursor-pointer'
          }`}
        >
          {savingBase ? (
            <span className="flex items-center gap-2">
              <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
              </svg>
              Saving...
            </span>
          ) : (
            'Save'
          )}
        </button>
      </div>

      {/* Pricing Rules Table */}
      <div className="mb-8">
        <h3 className="text-xl font-bold mb-4 text-[#211F54]">Pricing Rules</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white rounded-lg shadow border border-gray-200">
            <thead>
              <tr className="bg-[#F5F6FA] text-[#211F54]">
                <th className="p-3 text-left font-semibold border-b border-gray-200">Name</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Price</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Priority</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Start Date</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">End Date</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Color</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Status</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Last Updated</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Actions</th>
              </tr>
            </thead>
            <tbody>
              {nonBaseRules.length === 0 ? (
                <tr><td colSpan={9} className="text-center p-4 text-gray-400">No pricing rules</td></tr>
              ) : nonBaseRules.map((rule, idx) => (
                <tr key={rule._id} className="hover:bg-[#F5F6FA]">
                  <td className="p-3">{rule.name}</td>
                  <td className="p-3 border-b border-gray-200">{formatJMD(rule.price)}</td>
                  <td className="p-3">{rule.priority}</td>
                  <td className="p-3">{formatDate(rule.startDate)}</td>
                  <td className="p-3">{formatDate(rule.endDate)}</td>
                  <td className="p-3">
                    {rule.color && (
                      <span className="inline-flex items-center gap-2">
                        <span style={{ background: rule.color, width: 18, height: 18, borderRadius: '50%', border: '1px solid #ccc', display: 'inline-block' }} />
                      </span>
                    )}
                  </td>
                  <td className="p-3">
                    {rule.isActive ? (
                      <span style={{ color: 'green', fontWeight: 600 }}>Active</span>
                    ) : (
                      <span style={{ color: 'red', fontWeight: 600 }}>Inactive</span>
                    )}
                  </td>
                  <td className="p-3">
                    {formatDateTime(rule.updatedAt)}
                    {rule._id === mostRecentRuleId && (
                      <span className="ml-2 px-2 py-1 bg-green-100 text-green-700 rounded text-xs font-semibold">Recent</span>
                    )}
                  </td>
                  <td className="p-3 flex gap-2">
                    <button className="px-2 py-1 bg-[#0161AB] text-white rounded text-xs" onClick={() => handleEditRule(rule, idx)}>Edit</button>
                    <button className="px-2 py-1 bg-red-500 text-white rounded text-xs" onClick={() => handleDeleteRule(rule._id)}>Delete</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      <button
        type="button"
        className="inline-flex  items-center justify-center px-4 py-2 rounded bg-gradient-to-tr from-[#211F54] to-[#0161AB] min-h-[34.71px] h-[47px] w-[175px] font-inter mb-4"
        onClick={handleAddRule}
      >
        <span className="text-white text-[14.2px] font-semibold leading-[22.09px] text-center">
          Add Pricing Rule
        </span>
      </button>
      {/* Override Rules Table */}
      <div className="mb-8">
        <h3 className="text-xl font-bold mb-4 text-[#211F54]">Override Rules</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white rounded-lg shadow border border-gray-200">
            <thead>
              <tr className="bg-[#F5F6FA] text-[#211F54]">
                <th className="p-3 text-left font-semibold border-b border-gray-200">Date</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Price</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Actions</th>
              </tr>
            </thead>
            <tbody>
              {Object.entries(dateOverrides).length === 0 ? (
                <tr><td colSpan={3} className="text-center p-4 text-gray-400">No override rules</td></tr>
              ) : Object.entries(dateOverrides).map(([date, override]) => (
                <tr key={date} className="hover:bg-[#F5F6FA]">
                  <td className="p-3">{date}</td>
                  <td className="p-3">
                    {formatJMD(override.price)}
                  </td>
                  <td className="p-3">
                    <button className="px-2 py-1 bg-red-500 text-white rounded text-xs" onClick={() => handleDeleteOverride(override._id)}>Delete</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

     

      {/* Calendar View */}
      <div className="mb-8">
        <div className="flex items-center gap-2 mb-2">
          <span className="font-[poppins] font-medium bg-white px-4 py-2 rounded border border-gray-200 shadow-sm">
            {new Date(calendarYear, calendarMonth).toLocaleString('default', { month: 'long', year: 'numeric' })}
          </span>
          <button className="px-3 py-2 bg-white border border-gray-200 rounded-lg shadow-sm text-xl  text-[#211F54]" onClick={handlePrevMonth}>{'<'}</button>
          <button className="px-3 py-2 bg-white border border-gray-200 rounded-lg shadow-sm text-xl  text-[#211F54]" onClick={handleNextMonth}>{'>'}</button>
        </div>
        <CalendarRuleView
          rules={rules.filter(rule => rule.name?.toLowerCase() !== 'base price')}
          basePrice={basePrice}
          baseCurrency={'JMD'}
          dateOverrides={dateOverrides}
          onCellClick={handleCellClick}
          onRemoveOverride={handleRemoveOverride}
          calendarMonth={calendarMonth}
          calendarYear={calendarYear}
          setCalendarMonth={setCalendarMonth}
          setCalendarYear={setCalendarYear}
          getRuleForDate={getRuleForDateWithUpdatedAt}
        />
      </div>

      {/* Edit Rule Modal */}
      <RuleModal
        visible={ruleModalVisible}
        onClose={() => setRuleModalVisible(false)}
        onSave={handleRuleModalSave}
        initialRule={ruleModalInitial}
        isEdit={ruleModalIsEdit}
        rules={rules}
        resourceId={params['plan.id']}
        editingRuleIdx={ruleModalIdx}
        ruleId={ruleModalInitial?._id}
        onRuleSaved={handleRuleSaved}
      />
      {showDeleteModal && (
        <div 
          style={{ background: "rgba(0, 0, 0, 0.80)" }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-opacity-40"
          onClick={() => {
            setShowDeleteModal(false);
            setDeleteTarget(null);
          }}
        >
          <div
            className="bg-white rounded-lg shadow-lg p-8 w-full max-w-md"
            onClick={e => e.stopPropagation()}
          >
            <h3 className="text-xl font-bold mb-4 text-[#211F54]">Delete Confirmation</h3>
            <p className="mb-6 text-gray-700">
              Are you sure you want to delete {deleteTarget?.type === 'rule' ? 'the rule' : 'the override rule'}
               {/* <b>{deleteTarget?.label}</b>? */}
                This action cannot be undone.
            </p>
            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 rounded bg-gray-200 text-gray-700 font-semibold hover:bg-gray-300"
                onClick={() => {
                  setShowDeleteModal(false);
                  setDeleteTarget(null);
                }}
                disabled={deleting}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 rounded bg-[#E1020C] text-white font-semibold hover:bg-[#b30009] flex items-center justify-center gap-2"
                onClick={handleDeleteConfirm}
                disabled={deleting}
              >
                {deleting ? (
                  <span className="flex items-center gap-2">
                    <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                    </svg>
                    Deleting...
                  </span>
                ) : (
                  'Confirm & Delete'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
      {showOverrideModal && (
        <div
          className="fixed inset-0 flex items-center justify-center z-50"
          style={{ background: 'rgba(0, 0, 0, 0.40)' }}
          onClick={() => {
            setShowOverrideModal(false);
            setSelectedOverrideDate('');
            setOverrideAmount('');
          }}
        >
          <div
            className="bg-white rounded-2xl shadow-lg p-8 w-full max-w-sm relative border border-gray-100"
            onClick={e => e.stopPropagation()}
          >
            <h3 className="text-lg text-left pb-2 font-[poppins]">
              {selectedOverrideDate ? 'Set' : 'Set'} Price
              <span className="text-[#E1020C]">*</span>
            </h3>
            <p className="text-gray-600 mb-4">Enter an amount to override the rule for this date ({selectedOverrideDate}).</p>
            <div className="flex items-center w-full bg-white border border-gray-200 rounded-lg overflow-hidden mb-6">
              <select
                className="appearance-none bg-transparent px-4 py-3 font-[poppins] focus:outline-none border-none border-r border-gray-200 text-[#211F54]"
                value="JMD"
                disabled
                style={{ cursor: 'not-allowed' }}
              >
                <option value="JMD">J$</option>
              </select>
              <input
                type="number"
                min="1"
                maxLength={10}
                className="flex-1 px-4 py-3 ml-4 font-[poppins] bg-transparent focus:outline-none border-l border-gray-200 text-[#211F54]"
                value={overrideAmount}
                onChange={e => {
                  let val = e.target.value.replace(/[^0-9]/g, '');
                  // Prevent leading zeros
                  if (val.length > 1) {
                    val = val.replace(/^0+/, '');
                  }
                  // Prevent only "0" as value
                  if (val === "0") {
                    setOverrideAmount('');
                  } else if (val.length <= 10) {
                    setOverrideAmount(val);
                  }
                }}
                onKeyDown={e => {
                  if (["e", "E", "+", "-", "."].includes(e.key)) e.preventDefault();
                }}
                placeholder="Enter price"
              />
            </div>
            <button
              onClick={handleSaveOverride}
              className={`w-full py-3 rounded-full text-lg ${(!overrideAmount || savingOverride)
                ? 'bg-gray-400 text-white cursor-not-allowed'
                : 'bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white cursor-pointer'}`}
              disabled={!overrideAmount || savingOverride}
            >
              {savingOverride ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin h-5 w-5 text-white mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
                  </svg>
                  Saving...
                </span>
              ) : (
                'Save'
              )}
            </button>
          </div>
        </div>
      )}
    </div>
  );
} 