"use client";
import {  fetchAdPricingRules, deleteResource } from '../../../service/adManagement';
import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';

function CenteredLoader({ message }) {
  return (
    <div className="flex items-center justify-center min-h-[60vh] w-full">
      <div className="flex flex-col items-center">
        <svg className="animate-spin h-10 w-10 text-[#0161AB] mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
        </svg>
        <span className="text-lg text-[#211F54] font-semibold">{message || 'Loading...'}</span>
      </div>
    </div>
  );
}

export default function CurrentAdPlanPage() {
  const router = useRouter();

 const [pricingRulesList, setPricingRuleList] = useState([])
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [timedOut, setTimedOut] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    let timeoutId = setTimeout(() => {
      setTimedOut(true);
      setLoading(false);
    }, 10000); // 10 seconds
    (async () => {
      const res = await fetchAdPricingRules();
      clearTimeout(timeoutId);
      if (res && res.data && Array.isArray(res.data.resources)) {
        setPricingRuleList(
          res.data.resources.map(item => ({
            id: item._id,
            type: item.type,
            position: item.position,
            isActive: item.isActive,
            page: item.page,
          }))
        );
        setError(null);
      } else if (res && res.data && res.data.message) {
        setError(res.data.message);
        setPricingRuleList([]);
      } else {
        setError('Something went wrong');
        setPricingRuleList([]);
      }
      setLoading(false);
      setTimedOut(false);
      console.log('Fetched ads:', res);
    })();
    return () => clearTimeout(timeoutId);
  }, []);

  // Delete handler
  const handleDelete = async (id) => {
    setDeleting(true);
    try {
      const res = await deleteResource(id);
      if (res && res.data && (res.data.success || res.data.status === 'success' || res.status === 200)) {
        setPricingRuleList((prev) => prev.filter((plan) => plan.id !== id));
        toast.success('Ad plan deleted successfully!');
      } else {
        toast.error(res?.data?.message || 'Failed to delete ad plan');
      }
    } catch (e) {
      toast.error(e?.message || 'Failed to delete ad plan');
    } finally {
      setDeleting(false);
      setShowDeleteModal(false);
      setDeleteTarget(null);
    }
  };

  if (loading) return <CenteredLoader message="Loading..." />;
  if (timedOut) return <CenteredLoader message="Request timed out. Please try again." />;
  if (error) return <CenteredLoader message={error || 'Something went wrong'} />;

  return (
    <>
      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <div 
          style={{ background: "rgba(0, 0, 0, 0.80)" }}
          className="fixed inset-0 z-50 flex items-center justify-center bg-opacity-40"
          onClick={() => {
            setShowDeleteModal(false);
            setDeleteTarget(null);
          }}
        >
          <div
            className="bg-white rounded-lg shadow-lg p-8 w-full max-w-md"
            onClick={e => e.stopPropagation()}
          >
            <h3 className="text-xl font-bold mb-4 text-[#211F54]">Delete Ad Plan</h3>
            <p className="mb-6 text-gray-700">
              Are you sure you want to delete this ad plan? This action cannot be undone.
            </p>
            <div className="flex justify-end gap-3">
              <button
                className="px-4 py-2 rounded bg-gray-200 text-gray-700 font-semibold hover:bg-gray-300"
                onClick={() => {
                  setShowDeleteModal(false);
                  setDeleteTarget(null);
                }}
                disabled={deleting}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 rounded bg-[#E1020C] text-white font-semibold hover:bg-[#b30009]"
                onClick={() => handleDelete(deleteTarget)}
                disabled={deleting}
              >
                {deleting ? 'Deleting...' : 'Confirm & Delete'}
              </button>
            </div>
          </div>
        </div>
      )}
      <div className="p-6 bg-white rounded-lg shadow overflow-x-auto font-medium min-h-screen">
        <h2 className="text-2xl font-bold mb-6">Current Ad Plan</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {pricingRulesList?.map((plan) => (
            <div
              key={plan.id}
              className="bg-white rounded-2xl border border-gray-100 shadow-md hover:shadow-lg transition-shadow p-6 flex flex-col justify-between min-h-[220px] relative"
            >
              {/* Header */}
              <div className="flex items-center justify-between mb-2">
                <div>
                  <div className="text-xl font-bold text-[#211F54]">{plan.page}</div>
                </div>
                <div className="flex items-center gap-2">
                  <span
                    className={`inline-flex items-center gap-2`}
                    title={plan.isActive ? "Active" : "Inactive"}
                  >
                    <span
                      className={`w-3 h-3 rounded-full inline-block ${plan.isActive ? 'bg-green-500' : 'bg-gray-300'}`}
                    ></span>
                    <span className="text-base text-[#211F54] font-medium">
                      {/* {plan.isActive ? "Active" : "Inactive"} */}
                    </span>
                  </span>
                </div>
              </div>
              {/* Details */}
              <div className="mb-4 flex mt-2 gap-2">
                <span className="inline-block w-[150px] bg-gray-100 text-gray-700 text-sm px-2 py-1 rounded mr-2">
                  Position: {plan.position ? plan.position.charAt(0).toUpperCase() + plan.position.slice(1) : ''}
                </span>
                <span className={`px-2 py-1 rounded w-[150px] text-sm font-semibold ${plan.type === 'grid' ? 'bg-blue-100 text-blue-700' : 'bg-purple-100 text-purple-700'}`}>
                  Type: {plan.type ? plan.type.charAt(0).toUpperCase() + plan.type.slice(1) : ''}
                </span>
              </div>
              {/* Actions */}
              <div className="flex items-center gap-3 mt-auto">
                <button
                  className="flex-1 py-2 rounded-full border border-[#0161AB] text-[#0161AB] font-semibold text-base hover:bg-[#F3F8FC] transition"
                  onClick={() => router.push(`/ad-management/current-ad-plan/view/${plan.id}`)}
                >
                  View
                </button>
                <button
                  className="flex-1 py-2 rounded-full bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white font-semibold text-base shadow hover:from-[#024e8c] hover:to-[#1a1b3a] transition"
                  onClick={() => router.push(`/ad-management/current-ad-plan/edit/${plan.id}`)}
                >
                  Edit
                </button>
                <button
                  className="flex-1 py-2 rounded-full border border-[#E1020C] text-[#E1020C] font-semibold text-base hover:bg-[#FFF0F1] transition flex items-center justify-center gap-2"
                  title="Delete"
                  onClick={() => {
                    setDeleteTarget(plan.id);
                    setShowDeleteModal(true);
                  }}
                >
                  Delete
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </>
  );
}