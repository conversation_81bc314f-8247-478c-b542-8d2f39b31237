'use client'

import { useEffect, useState } from 'react';
import { getAdPricingRuleById } from '../../../../../service/adManagement';
import { useParams } from 'next/navigation';
import CalendarRuleView from '../../../../../components/common/CalendarRuleView';

function formatDate(dateStr) {
  if (!dateStr) return '';
  const d = new Date(dateStr);
  return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0');
}

function formatDateTime(dateStr) {
  if (!dateStr) return '';
  const d = new Date(dateStr);
  return d.toLocaleString('en-JM', { dateStyle: 'medium', timeStyle: 'short' });
}

const formatJMD = (value) => {
  if (value === undefined || value === null || value === '') return '';
  return 'J$' + Number(value).toLocaleString('en-JM');
};

export default function ViewAdPlanPage() {
  const params = useParams();
  const [ad, setAd] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [calendarMonth, setCalendarMonth] = useState(new Date().getMonth());
  const [calendarYear, setCalendarYear] = useState(new Date().getFullYear());
  const [dateOverrides, setDateOverrides] = useState({});
  const [rules, setRules] = useState([]);

  async function fetchData() {
    setLoading(true);
    setError(null);
    const res = await getAdPricingRuleById(params['plan.id']);
    if (res && res.data && res.data.resource) {
      setAd(res.data.resource);
      if (res.data.resource.pricingRules) setRules(res.data.resource.pricingRules);
      setDateOverrides({});
      if (res.data.resource.overrideRules) {
        res.data.resource.overrideRules.forEach(override => {
          const date = new Date(override.date);
          setDateOverrides(prev => ({
            ...prev,
            [`${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`]: override
          }));
        });
      }
    } else if (res && res.data && res.data.message) {
      setError(res.data.message);
    } else {
      setError('Something went wrong');
    }
    setLoading(false);
  }

  useEffect(() => {
    if (!params['plan.id']) return;
    fetchData();
  }, [params]);

  // Find the most recent rule (excluding base price)
  const nonBaseRules = rules.filter(rule => rule.name?.toLowerCase() !== 'base price');
  const mostRecentRule = nonBaseRules.reduce((a, b) => new Date(a.updatedAt) > new Date(b.updatedAt) ? a : b, nonBaseRules[0]);
  const mostRecentRuleId = mostRecentRule?._id;

  function getRuleForDateWithUpdatedAt(rules, year, month, day) {
    const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    const matching = rules.filter(rule => {
      if (rule.isActive === false) return false;
      if (!rule.startDate || !rule.endDate) return false;
      const start = formatDate(rule.startDate);
      const end = formatDate(rule.endDate);
      return start <= dateStr && dateStr <= end;
    });
    if (matching.length === 0) return null;
    matching.sort((a, b) => {
      if (Number(b.priority) !== Number(a.priority)) {
        return Number(b.priority) - Number(a.priority);
      }
      return new Date(b.updatedAt) - new Date(a.updatedAt);
    });
    return matching[0];
  }

  if (loading) return <div className="flex items-center justify-center min-h-[60vh]">Loading...</div>;
  if (error) return <div className="flex items-center justify-center min-h-[60vh] text-red-500">{error}</div>;
  if (!ad) return null;

  // Resource details for header
  const resourceType = ad.type ? ad.type.charAt(0).toUpperCase() + ad.type.slice(1) : '';
  const resourcePage = ad.page ? ad.page.charAt(0).toUpperCase() + ad.page.slice(1) : '';
  const resourcePosition = ad.position ? ad.position.charAt(0).toUpperCase() + ad.position.slice(1) : '';
  const resourceStatus = ad.isActive ? 'Active' : 'Inactive';
  const basePrice = rules.find(rule => rule.name?.toLowerCase() === 'base price')?.price;

  return (
    <div className="bg-[#F5F6FA] min-h-screen w-full">
      {/* Hero Header */}
      <div className="w-full bg-gradient-to-tr from-[#0161AB] to-[#211F54] py-10 px-6 flex flex-col md:flex-row items-center justify-between gap-2 shadow-lg">
        <div className="flex flex-col gap-2 w-full">
          <div className="flex items-center gap-4 mb-2">
            <button
              className="rounded-full bg-white/80 hover:bg-white border border-gray-200 p-2 shadow"
              onClick={() => window.location.href = '/ad-management/current-ad-plan'}
              aria-label="Back"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="9"
                height="15"
                viewBox="0 0 9 15"
                fill="none"
              >
                <path
                  d="M7.99939 13.9609L1.99995 7.5L7.99939 1.03906"
                  stroke="#211F54"
                  strokeWidth="1.85"
                  strokeLinecap="round"
                />
              </svg>
            </button>
            <span className="text-4xl font-extrabold text-white tracking-tight drop-shadow">{resourcePage}</span>
          </div>
          <div className="flex flex-wrap gap-4 mt-2">
            <span className={`px-4 py-2 rounded-full text-base font-semibold ${ad.type === 'grid' ? 'bg-blue-200 text-blue-900' : 'bg-purple-200 text-purple-900'}`}>Type: {resourceType}</span>
            <span className="px-4 py-2 rounded-full text-base font-semibold bg-gray-200 text-gray-900">Position: {resourcePosition}</span>
            <span className={`px-4 py-2 rounded-full text-base font-semibold ${ad.isActive ? 'bg-green-200 text-green-900' : 'bg-gray-300 text-gray-600'}`}>{resourceStatus}</span>
          </div>
        </div>
        {/* Base Price Stat Card */}
        <div className="flex-shrink-0 w-full md:w-auto flex justify-center">
          <div className="rounded-3xl shadow-2xl bg-white/30 backdrop-blur-md border border-white/40 px-16 py-10 flex flex-col items-center min-w-[260px] max-w-full">
            <span className="text-lg font-semibold text-white/90 mb-2 tracking-wide">Base Price</span>
            <span className="text-5xl font-extrabold text-white drop-shadow-lg flex items-center gap-2">
              <svg xmlns='http://www.w3.org/2000/svg' width='32' height='32' fill='none' viewBox='0 0 24 24'><path fill='#fff' d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17.93c-2.83.48-5.48-1.51-5.96-4.34-.09-.52.36-.99.89-.99.44 0 .81.32.89.75.34 1.81 2.01 3.08 3.83 2.74 1.81-.34 3.08-2.01 2.74-3.83-.34-1.81-2.01-3.08-3.83-2.74-.52.09-.99-.36-.99-.89 0-.44.32-.81.75-.89 2.83-.48 5.48 1.51 5.96 4.34.09.52-.36.99-.89.99-.44 0-.81-.32-.89-.75-.34-1.81-2.01-3.08-3.83-2.74-1.81.34-3.08 2.01-2.74 3.83.34 1.81 2.01 3.08 3.83 2.74.52-.09.99.36.99.89 0 .44-.32.81-.75.89z'/></svg>
              {basePrice ? formatJMD(basePrice) : '-'}
            </span>
          </div>
        </div>
      </div>
      {/* Pricing Rules Table */}
      <div className="w-full px-0 md:px-2 py-2">
        <div className="bg-white rounded-3xl shadow-2xl p-10 w-full">
          <h3 className="text-2xl font-extrabold mb-6 text-[#211F54] tracking-tight">Pricing Rules</h3>
          <div className="overflow-x-auto">
            <table className="w-full text-lg rounded-2xl overflow-hidden">
              <thead>
                <tr className="bg-[#F5F6FA] text-[#211F54]">
                  <th className=" text-sm p-4 text-left font-bold">Name</th>
                  <th className=" text-sm p-4 text-left font-bold">Price</th>
                  <th className=" text-sm p-4 text-left font-bold">Priority</th>
                  <th className=" text-sm p-4 text-left font-bold">Start Date</th>
                  <th className=" text-sm p-4 text-left font-bold">End Date</th>
                  <th className=" text-sm p-4 text-left font-bold">Color</th>
                  <th className=" text-sm p-4 text-left font-bold">Status</th>
                  <th className=" text-sm p-4 text-left font-bold">Last Updated</th>
                </tr>
              </thead>
              <tbody>
                {nonBaseRules.length === 0 ? (
                  <tr><td colSpan={8} className="text-center p-8 text-gray-400 text-xl">No pricing rules</td></tr>
                ) : nonBaseRules.map((rule, idx) => (
                  <tr key={rule._id} className={idx % 2 === 0 ? "bg-white" : "bg-[#F9FAFB] hover:bg-[#F5F6FA]"}>
                    <td className=" text-sm p-4 font-semibold text-[#211F54]">{rule.name}</td>
                    <td className=" text-sm p-4 font-semibold">{formatJMD(rule.price)}</td>
                    <td className=" text-sm p-4">{rule.priority}</td>
                    <td className=" text-sm p-4">{formatDate(rule.startDate)}</td>
                    <td className=" text-sm p-4">{formatDate(rule.endDate)}</td>
                    <td className=" text-sm p-4">
                      {rule.color && (
                        <span className="inline-flex items-center text-sm gap-2">
                          <span style={{ background: rule.color, width: 22, height: 22, borderRadius: '50%', border: '2px solid #e5e7eb', display: 'inline-block' }} />
                        </span>
                      )}
                    </td>
                    <td className="p-4">
                      {rule.isActive ? (
                        <span className="bg-green-100  text-green-700 px-3 py-1 rounded-full text-sm font-bold">Active</span>
                      ) : (
                        <span className="bg-gray-200 text-gray-500 px-3 py-1 rounded-full text-sm font-bold">Inactive</span>
                      )}
                    </td>
                    <td className="p-4">
                      <span className="font-mono text-sm text-gray-500">{formatDateTime(rule.updatedAt)}</span>
                      {rule._id === mostRecentRuleId && (
                        <span className="ml-3 px-3 py-1 bg-green-100 text-green-700 rounded text-sm font-bold">Recent</span>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      {/* Override Rules Table */}
      <div className="w-full px-0 md:px-2 py-2">
        <div className="bg-white rounded-3xl shadow-2xl p-10 w-full">
          <h3 className="text-2xl font-extrabold mb-6 text-[#211F54] tracking-tight">Override Rules</h3>
          <div className="overflow-x-auto">
            <table className="w-full text-lg rounded-2xl overflow-hidden">
              <thead>
                <tr className="bg-[#F5F6FA] text-[#211F54]">
                  <th className="text-base  p-4 text-left font-bold">Date</th>
                  <th className="text-base  p-4 text-left font-bold">Price</th>
                </tr>
              </thead>
              <tbody>
                {Object.entries(dateOverrides).length === 0 ? (
                  <tr><td colSpan={2} className="text-center p-8 text-gray-400 text-xl">No override rules</td></tr>
                ) : Object.entries(dateOverrides).map(([date, override], idx) => (
                  <tr key={date} className={idx % 2 === 0 ? "bg-white" : "bg-[#F9FAFB] hover:bg-[#F5F6FA]"}>
                    <td className="text-sm  p-4 text-[#211F54]">{date}</td>
                    <td className="text-sm  p-4">{formatJMD(override.price)}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      {/* Calendar View */}
      <div className="w-full  px-0 md:px-2 py-2">
        <div className="bg-white rounded-3xl shadow-2xl p-10 w-full">
          <div className="flex items-center gap-4 mb-6 sticky top-0 z-10 bg-white rounded-t-3xl">
            <span className="font-[poppins] w-[350px] font-bold text-2xl bg-white px-6 py-3 rounded shadow-sm border border-gray-200">
              {new Date(calendarYear, calendarMonth).toLocaleString('default', { month: 'long', year: 'numeric' })}
            </span>
            <button className="px-4 py-3 bg-white border border-gray-200 rounded-lg shadow-sm text-2xl text-[#211F54]" onClick={() => setCalendarMonth((prev) => (prev === 0 ? 0 : prev - 1))}>{'<'}</button>
            <button className="px-4 py-3 bg-white border border-gray-200 rounded-lg shadow-sm text-2xl text-[#211F54]" onClick={() => setCalendarMonth((prev) => (prev === 11 ? 11 : prev + 1))}>{'>'}</button>
          </div>
          <CalendarRuleView
            rules={rules.filter(rule => rule.name?.toLowerCase() !== 'base price')}
            basePrice={basePrice}
            baseCurrency={'JMD'}
            dateOverrides={dateOverrides}
            onCellClick={() => {}} // No-op for view mode
            calendarMonth={calendarMonth}
            calendarYear={calendarYear}
            setCalendarMonth={setCalendarMonth}
            setCalendarYear={setCalendarYear}
            getRuleForDate={getRuleForDateWithUpdatedAt}
          />
        </div>
      </div>
    </div>
  );
} 