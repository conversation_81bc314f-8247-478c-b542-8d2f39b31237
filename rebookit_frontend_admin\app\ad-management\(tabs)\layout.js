"use client";
import { usePathname, useRouter } from "next/navigation";

const tabs = [
  { name: "Listing", path: "/ad-management/listing" },
  { name: "Current Ad Plan", path: "/ad-management/current-ad-plan" },
  { name: "Manage Ad Pricing", path: "/ad-management/manage-ad-pricing" },
];

export default function AdManagementLayout({ children }) {
  const pathname = usePathname();
  const router = useRouter();

  return (
    <div className="p-4 bg-white rounded-2xl">
    <p className="font-[poppins] font-medium flex items-center gap-2">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="9"
        height="15"
        viewBox="0 0 9 15"
        fill="none"
        style={{ cursor: "pointer" }}
        onClick={() => router.push('/ad-management')}
      >
        <path
          d="M7.99939 13.9609L1.99995 7.5L7.99939 1.03906"
          stroke="black"
          strokeWidth="1.84598"
          strokeLinecap="round"
        />
      </svg>
      Ad Management
    </p>
      {/* TABS FOR AD MANAGEMENT */}
      <div className="flex gap-2 mb-6 mt-4 border border-gray-200 shadow-xs rounded-lg p-2">
        {tabs.map((tab) => (
          <button
            key={tab.name}
            className={`px-4 py-2 w-full rounded-lg font-normal font-[poppins] ${
              pathname.startsWith(tab.path)
                ? "bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white"
                : ""
            }`}
            onClick={() => router.push(tab.path)}
            type="button"
          >
            {tab.name}
          </button>
        ))}
      </div>
      {/* RENDER THE SELECTED TAB PAGE */}
      <div>{children}</div>
    </div>
  );
}