"use client";
import { useState } from "react";

// MOCK DATA - REPLACE THIS WITH API DATA LATER
const mockAds = [
  {
    addId: "9898763",
    name: "Campaign Name",
    image: "https://m.media-amazon.com/images/I/71S0qR3rhlL._SL1360_.jpg", // PLACEHOLDER IMAGE PATH
    businessType: "Book Seller",
    pagePlacement: "Banner, Home Page, Bottom",
    duration: "5 Days",
    status: "pending", // "approved", "rejected"
  },
  {
    addId: "9898764",
    name: "Campaign Name",
    image: "https://m.media-amazon.com/images/I/71S0qR3rhlL._SL1360_.jpg",
    businessType: "Real Estate",
    pagePlacement: "Banner, Home Page, Bottom",
    duration: "5 Days",
    status: "approved",
  },
  {
    addId: "9898765",
    name: "Campaign Name",
    image: "https://m.media-amazon.com/images/I/71S0qR3rhlL._SL1360_.jpg",
    businessType: "Book Seller",
    pagePlacement: "Banner, Home Page, Bottom",
    duration: "5 Days",
    status: "rejected",
  },
  {
    addId: "9898763",
    name: "Campaign Name",
    image: "https://m.media-amazon.com/images/I/71S0qR3rhlL._SL1360_.jpg", // PLACEHOLDER IMAGE PATH
    businessType: "Book Seller",
    pagePlacement: "Banner, Home Page, Bottom",
    duration: "5 Days",
    status: "pending", // "approved", "rejected"
  },
  {
    addId: "9898764",
    name: "Campaign Name",
    image: "https://m.media-amazon.com/images/I/71S0qR3rhlL._SL1360_.jpg",
    businessType: "Real Estate",
    pagePlacement: "Banner, Home Page, Bottom",
    duration: "5 Days",
    status: "approved",
  },
  {
    addId: "9898765",
    name: "Campaign Name",
    image: "https://m.media-amazon.com/images/I/71S0qR3rhlL._SL1360_.jpg",
    businessType: "Book Seller",
    pagePlacement: "Banner, Home Page, Bottom",
    duration: "5 Days",
    status: "rejected",
  },
  {
    addId: "9898763",
    name: "Campaign Name",
    image: "https://m.media-amazon.com/images/I/71S0qR3rhlL._SL1360_.jpg", // PLACEHOLDER IMAGE PATH
    businessType: "Book Seller",
    pagePlacement: "Banner, Home Page, Bottom",
    duration: "5 Days",
    status: "pending", // "approved", "rejected"
  },
  {
    addId: "9898764",
    name: "Campaign Name",
    image: "https://m.media-amazon.com/images/I/71S0qR3rhlL._SL1360_.jpg",
    businessType: "Real Estate",
    pagePlacement: "Banner, Home Page, Bottom",
    duration: "5 Days",
    status: "approved",
  },
  {
    addId: "9898765",
    name: "Campaign Name",
    image: "https://m.media-amazon.com/images/I/71S0qR3rhlL._SL1360_.jpg",
    businessType: "Book Seller",
    pagePlacement: "Banner, Home Page, Bottom",
    duration: "5 Days",
    status: "rejected",
  },
];

export default function AdListingPage() {
  // STATE FOR ADS - REPLACE WITH API DATA
  const [ads, setAds] = useState(mockAds);

  // STATE FOR DECLINE POPUP
  const [showDecline, setShowDecline] = useState(false);
  const [declineId, setDeclineId] = useState(null);
  const [declineReason, setDeclineReason] = useState("");

  // HANDLE APPROVE CLICK
  const handleApprove = (addId) => {
    // TODO: CALL API TO APPROVE
    setAds((prev) => prev.map((ad) => (ad.addId === addId ? { ...ad, status: "approved" } : ad)));
  };

  // HANDLE DECLINE CLICK (SHOW POPUP)
  const handleDecline = (addId) => {
    setDeclineId(addId);
    setShowDecline(true);
    setDeclineReason("");
  };

  // HANDLE DECLINE SUBMIT
  const handleDeclineSubmit = () => {
    // TODO: CALL API TO DECLINE WITH REASON
    setAds((prev) => prev.map((ad) => (ad.addId === declineId ? { ...ad, status: "rejected" } : ad)));
    setShowDecline(false);
    setDeclineId(null);
    setDeclineReason("");
  };

  // CLOSE POPUP
  const handleClosePopup = () => {
    setShowDecline(false);
    setDeclineId(null);
    setDeclineReason("");
  };

  return (
    <div>
      {/* <h2 className="text-xl font-bold mb-4">Ad Management - Listing</h2> */}
      <div className="bg-white rounded-lg shadow overflow-x-auto font-medium">
        <table className="min-w-full text-sm">
          <thead className="bg-[#FAFBFB] textalign-center">
            <tr className="font-[poppins]">
              <th className="pl-6 pr-2">
                <input type="checkbox" className="w-5 h-5" />
              </th>
              <th className="p-2 text-left font-medium">Name Book</th>
              <th className="p-2 text-center font-medium">Bussiness Type</th>
              <th className="p-2 text-center font-medium">Page & Placement</th>
              <th className="p-2 text-center font-medium">Ad Duration</th>
              <th className="p-2 text-center font-medium">Action</th>
              <th className="p-2 text-center font-medium">Action</th>
            </tr>
          </thead>
          <tbody>
            {ads.map((ad) => (
              <tr key={ad.id} className="hover:bg-gray-50 font-normal text-center justify-center items-center py-50 h-28">
                <td className="pl-6 pr-2">
                  <input type="checkbox" className="w-5 h-5" />
                </td>
                <td className="p-2 flex items-center gap-2 h-28">
                  <img src={ad.image} alt="Book" className="w-14 h-20 rounded object-cover" />
                  <div className="flex justify-center items-start flex-col gap-2 pl-2">
                    <div className="font-[poppins] font-medium">{ad.name}</div>
                    <div className="text-xs text-gray-500 font-[poppins] font-normal">Ad Id: {ad.addId}</div>
                  </div>
                </td>
                <td className="p-2 font-[poppins]">{ad.businessType}</td>
                <td className="p-2 font-semibold font-[poppins]">{ad.pagePlacement}</td>
                <td className="p-2 font-[poppins]">{ad.duration}</td>
                <td className="p-2 font-[poppins]">
                  {ad.status === "approved" && (
                    <span className="px-6 py-3 rounded-full border border-blue-400 text-blue-500">Approved</span>
                  )}
                  {ad.status === "rejected" && <span className="px-6 py-3  rounded-full border border-red-400 text-red-500">Rejected</span>}
                  {ad.status === "pending" && (
                    <div className="flex justify-center items-center gap-2">    
                      <button
                        className="px-6 py-3 rounded-full bg-gradient-to-r from-[#E1020C] to-[#4D7906] text-white"
                        onClick={() => handleDecline(ad.addId)}
                      >
                        Decline
                      </button>
                      <button
                        className="px-6 py-3 rounded-full bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white"
                        onClick={() => handleApprove(ad.addId)}
                      >
                        Approve
                      </button>
                    </div>
                  )}
                </td>
                <td className="p-2">
                  <button className="text-xl font-bold">⋮</button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* DECLINE POPUP */}
      {showDecline && (
        <div className="fixed inset-0 backdrop-blur-xs flex items-center justify-center z-50">
          <div className="bg-white rounded-3xl shadow-lg  w-full max-w-xl relative">
            <h3 className="text-xl w-full font-semibold text-center p-4 pt-6 underline rounded-3xl border-b border-gray-200 mb-4">Comment</h3>
            <button className="absolute bg-white rounded-full px-3 py-1 text-center -top-4 -right-4 text-2xl" onClick={handleClosePopup}>
              ×
            </button>
            <div className="p-6 pt-2">
              <label className="block mb-2 font-normal font-[poppins]">Reason for Decline</label>
              <textarea
                className="w-full border border-gray-200 rounded p-2 mb-4 min-h-[100px] font-[poppins]"
                placeholder="Write here"
                value={declineReason}
                onChange={(e) => setDeclineReason(e.target.value)}
              />
              <div className="flex justify-end">
                <button
                  className="px-12 py-2 rounded-full bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white"
                  onClick={handleDeclineSubmit}
                  disabled={!declineReason.trim()}
                >
                  Sent
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
