"use client";
import { useState, useEffect } from "react";
import { CiEdit } from "react-icons/ci";
import { MdClose } from "react-icons/md";
import { toast } from 'react-toastify';
import { createAdPricingRule, fetchAdPricingRules, deleteResource } from '../../../service/adManagement';
import { useRouter } from 'next/navigation';

// --- Helpers for Calendar ---
const weekDays = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];
const defaultCurrency = "JMD";
const defaultPrice = "30000";

// Dummy dataset simulating API/database response
const dummyCalendarDB = {
  "2025-07": [
    // Each entry: { day: 1, price: "100", currency: "JMD" }
    { day: 5, price: "200", currency: "USD" },
    { day: 10, price: "150", currency: "JMD" },
    { day: 20, price: "300", currency: "USD" },
  ],
  "2025-08": [
    { day: 1, price: "120", currency: "JMD" },
    { day: 15, price: "250", currency: "USD" },
  ],
  // ...add more months as needed
};

// Helper to get number of days in a month
function getDaysInMonth(year, month) {
  return new Date(year, month + 1, 0).getDate();
}

// Helper to get the first day index (0=Monday, 6=Sunday)
function getFirstDayIndex(year, month) {
  // JS: 0=Sunday, 6=Saturday; we want 0=Monday
  let jsDay = new Date(year, month, 1).getDay();
  return jsDay === 0 ? 6 : jsDay - 1;
}

// Helper to generate a matrix for the calendar (weeks x 7)
function getCalendarMatrix(year, month) {
  const daysInMonth = getDaysInMonth(year, month);
  const firstDayIdx = getFirstDayIndex(year, month);
  const matrix = [];
  let day = 1 - firstDayIdx;
  for (let row = 0; row < 6; row++) {
    const week = [];
    for (let col = 0; col < 7; col++) {
      week.push(day > 0 && day <= daysInMonth ? day : "");
      day++;
    }
    matrix.push(week);
  }
  return matrix;
}

// Helper to get week range for a given day (returns [startDay, endDay])
function getWeekRange(year, month, day) {
  const date = new Date(year, month, day);
  const jsDay = date.getDay(); // 0=Sunday
  const mondayOffset = jsDay === 0 ? -6 : 1 - jsDay;
  const start = new Date(year, month, day + mondayOffset);
  const end = new Date(start);
  end.setDate(start.getDate() + 6);
  return [
    Math.max(1, start.getDate()),
    Math.min(getDaysInMonth(year, month), end.getDate()),
  ];
}

// Enums for ad page and position types
const AdPageTypeEnum = Object.freeze({
  HOMEPAGE: "home page",
  SELLPAGE: "sell page",
  COMMUNITYPAGE: "community page",
  LISTINGPAGE: "listing page",
});
const AdPositionTypeEnum = Object.freeze({
  TOP: "top",
  MIDDLE: "middle",
  BOTTOM: "bottom",
  NOT_FIXED: "No fixed position",
});
const adPageOptions = Object.values(AdPageTypeEnum);
const adPositionOptions = Object.values(AdPositionTypeEnum);

// Ad types array for dynamic rendering
const adTypes = [
  {
    key: "Banner",
    label: "Banner",
    icon: "/icons/ad-member-banner.svg",
  },
  {
    key: "Grid",
    label: "Grid",
    icon: "/icons/ad-member-grid.svg",
  },
  // Add more ad types here as needed
];

// Helper for duplicate check
function findDuplicateRule(rules, ruleForm, editingRuleIdx) {
  for (let i = 0; i < rules.length; i++) {
    if (editingRuleIdx !== null && i === editingRuleIdx) continue;
    const r = rules[i];
    if (r.name === ruleForm.name) return 'name';
    // if (r.priority === ruleForm.priority) return 'priority';
    if (r.color === ruleForm.color) return 'color';
    // if (r.startDate === ruleForm.startDate && r.endDate === ruleForm.endDate) return 'date';
  }
  return null;
}

// Helper to get rule for a given date (returns the rule with highest priority if multiple match)
// Priority logic: higher number = higher priority (e.g., 100 is higher than 10)
function getRuleForDate(rules, year, month, day) {
  // Convert to yyyy-mm-dd
  const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  // Filter rules where date is in range and enabled
  const matching = rules.filter(rule => {
    if (!rule.enabled) return false;
    if (!rule.startDate || !rule.endDate) return false;
    return rule.startDate <= dateStr && dateStr <= rule.endDate;
  });
  // If multiple, pick the one with highest priority (largest number)
  if (matching.length === 0) return null;
  return matching.reduce((a, b) => {
    if (a.priority == null) return b;
    if (b.priority == null) return a;
    return Number(a.priority) > Number(b.priority) ? a : b;
  });
}

// Helper to format date as 'YYYY-MM-DDTHH:mm:ss.SSS+00:00'
function formatDateToApi(dateStr) {
  if (!dateStr) return '';
  const d = new Date(dateStr);
  // Get the offset in minutes and convert to "+00:00" format
  const tzOffset = -d.getTimezoneOffset();
  const sign = tzOffset >= 0 ? '+' : '-';
  const pad = n => String(Math.floor(Math.abs(n))).padStart(2, '0');
  const hours = pad(tzOffset / 60);
  const minutes = pad(tzOffset % 60);
  return d.getFullYear() + '-' +
    pad(d.getMonth() + 1) + '-' +
    pad(d.getDate()) + 'T' +
    pad(d.getHours()) + ':' +
    pad(d.getMinutes()) + ':' +
    pad(d.getSeconds()) + '.' +
    String(d.getMilliseconds()).padStart(3, '0') +
    sign + hours + ':' + minutes;
}

function formatDateTime(dateStr) {
  if (!dateStr) return '';
  const d = new Date(dateStr);
  return d.toLocaleString('en-JM', { dateStyle: 'medium', timeStyle: 'short' });
}

// --- Main Page Component ---
export default function ManageAdPricingPage() {
  const router = useRouter();
  const [isSaving, setIsSaving] = useState(false);
  // UI state
  const [showCalendar, setShowCalendar] = useState(false);

  // Ad type state
  const [adType, setAdType] = useState("");

  // Form state
  const [page, setPage] = useState("");
  const [placement, setPlacement] = useState("");
  // Base price state
  const [baseCurrency, setBaseCurrency] = useState("JMD");
  const [basePrice, setBasePrice] = useState("");

  // Calendar month/year state
  const today = new Date();
  const [calendarYear, setCalendarYear] = useState(today.getFullYear());
  const [calendarMonth, setCalendarMonth] = useState(today.getMonth()); // 0-indexed
  const [currency, setCurrency] = useState(defaultCurrency);
  const [price, setPrice] = useState(defaultPrice);

  // Calendar price data: prefill every day of the current year with base price
  const [calendarPrices, setCalendarPrices] = useState(() => {
    const days = Array(getDaysInMonth(calendarYear, calendarMonth)).fill(null);
    for (let i = 0; i < days.length; i++) {
      days[i] = { price: basePrice, currency: baseCurrency };
    }
    return days;
  });

  // Modal state for price input
  const [showSetPrice, setShowSetPrice] = useState(false);
  const [selectedCell, setSelectedCell] = useState({ day: null });
  const [modalPrice, setModalPrice] = useState("");
  const [modalCurrency, setModalCurrency] = useState(defaultCurrency);

  // Track if calendar data is dirty
  const [calendarDirty, setCalendarDirty] = useState(false);

  // Fetch already created plans to disable existing combos and show actions
  const [createdPlans, setCreatedPlans] = useState([]);
  useEffect(() => {
    (async () => {
      try {
        const res = await fetchAdPricingRules();
        const resources = res?.data?.resources || res?.data || [];
        if (Array.isArray(resources)) {
          setCreatedPlans(
            resources.map((r) => ({
              page: String(r.page || "").toLowerCase(),
              type: String(r.type || "").toLowerCase(),
              position: String(r.position || "").toLowerCase(),
              _id: r._id,
            }))
          );
        }
      } catch (e) {}
    })();
  }, []);

  const isPlanCreated = (pageName, typeName, positionName = "") => {
    const p = String(pageName || "").toLowerCase();
    const t = String(typeName || "").toLowerCase();
    const pos = String(positionName || "").toLowerCase();
    return createdPlans.some(
      (cp) => cp.page === p && cp.type === t && (pos ? cp.position === pos : true) && (!pos ? !cp.position || cp.position === "" : true)
    );
  };

  // Add state for per-date price overrides
  const [dateOverrides, setDateOverrides] = useState({}); // { 'YYYY-MM-DD': { price, currency } }

  // Next button enable logic
  const requiresPlacement = page === AdPageTypeEnum.HOMEPAGE || page === AdPageTypeEnum.SELLPAGE;
  const isNextEnabled = !!(adType && page && basePrice && (requiresPlacement ? placement : true));

  // Step indicator logic
  const currentStep = showCalendar ? 2 : 1;

  // State for pricing rule modal
  const [showRuleModal, setShowRuleModal] = useState(false);
  const [ruleForm, setRuleForm] = useState({
    name: "",
    enabled: true,
    startDate: "",
    endDate: "",
    price: basePrice,
    currency: baseCurrency,
    priority: null,
    color: "",
  });
  const [rules, setRules] = useState([]);
  // Add state to track which rule is being edited
  const [editingRuleIdx, setEditingRuleIdx] = useState(null);

  // Dummy state to force calendar re-render
  const [calendarRefresh, setCalendarRefresh] = useState(0);

  console.log(ruleForm,"ruleform", rules)
  // --- Handlers ---

  // Restrict calendar navigation to current year only
  const handlePrevMonth = () => {
    setCalendarMonth((prev) => {
      if (prev === 0) {
        return 0; // Don't go to previous year
      }
      return prev - 1;
    });
    setTimeout(loadMonthData, 0);
  };
  const handleNextMonth = () => {
    setCalendarMonth((prev) => {
      if (prev === 11) {
        return 11; // Don't go to next year
      }
      return prev + 1;
    });
    setTimeout(loadMonthData, 0);
  };

  // Load month data from dummy DB
  function loadMonthData() {
    const key = `${calendarYear}-${String(calendarMonth + 1).padStart(2, "0")}`;
    const monthData = dummyCalendarDB[key] || [];
    const days = Array(getDaysInMonth(calendarYear, calendarMonth)).fill(null);
    monthData.forEach(({ day, price, currency }) => {
      days[day - 1] = { price, currency };
    });
    setCalendarPrices(days);
    setCalendarDirty(false);
  }

  // Open set price modal, prefill if price exists or override exists
  const handleCellClick = (day) => {
    setSelectedCell({ day });
    const dateStr = `${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    if (dateOverrides[dateStr]) {
      setModalPrice(dateOverrides[dateStr].price || "");
      setModalCurrency(dateOverrides[dateStr].currency || defaultCurrency);
    } else {
      const cell = calendarPrices[day - 1];
      setModalPrice( "");
      setModalCurrency(cell?.currency || defaultCurrency);
    }
    setShowSetPrice(true);
  };

  // Confirm price change for a cell (override logic)
  const handleConfirm = () => {
    if (selectedCell.day !== null) {
      const dateStr = `${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(selectedCell.day).padStart(2, '0')}`;
      setDateOverrides((prev) => ({
        ...prev,
        [dateStr]: { price: modalPrice, currency: modalCurrency },
      }));
      setCalendarDirty(true);
    }
    setShowSetPrice(false);
    setSelectedCell({ day: null });
    setModalPrice("");
    setModalCurrency(defaultCurrency);
  };

  // Remove override for a date
  const handleRemoveOverride = (day) => {
    const dateStr = `${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
    setDateOverrides((prev) => {
      const copy = { ...prev };
      delete copy[dateStr];
      return copy;
    });
    setCalendarDirty(true);
  };

  // Save calendar changes (simulate API)
  const handleSaveCalendar = () => {
    // Here you would send calendarPrices to your API/database
    setCalendarDirty(false);
    // Show a toast
    // toast.success("Calendar prices saved successfully!");
  };

  // Update calendar prices when base price changes
  useEffect(() => {
    if (showCalendar) {
      const days = Array(getDaysInMonth(calendarYear, calendarMonth)).fill(
        null
      );
      for (let i = 0; i < days.length; i++) {
        days[i] = { price: basePrice, currency: baseCurrency };
      }
      setCalendarPrices(days);
    }
  }, [basePrice, baseCurrency, calendarYear, calendarMonth, showCalendar]);

  // Stepper click handlers
  const handleStepClick = (step) => {
    if (step === 1) {
      setShowCalendar(false);
    } else if (step === 2 && isNextEnabled) {
      setShowCalendar(true);
      loadMonthData();
    }
  };

  // --- API Save Handler ---
  const handleApiSave = async () => {
    setIsSaving(true);
    // Build pricingRules array
    const pricingRules = rules.map(rule => {
      const ruleObj = {
        name: rule.name,
        color: rule.color,
        startDate: rule.startDate ? formatDateToApi(rule.startDate) : '',
        endDate: rule.endDate ? formatDateToApi(rule.endDate) : '',
        price: rule.price ? Number(rule.price) : null,
        isActive: !!rule.enabled,
      };
      if (rule.priority !== null && rule.priority !== '' && rule.priority !== undefined) {
        ruleObj.priority = Number(rule.priority);
      }
      return ruleObj;
    });
    // Build overrideRules array
    const overrideRules = Object.entries(dateOverrides).map(([date, val]) => ({
      date: date ? formatDateToApi(date) : '',
      price: val.price ? Number(val.price) : null,
    }));
    // Build payload
    const payload = {
      type: adType ? adType.toLowerCase() : '',
      page,
      position: placement,
      basePrice: basePrice ? String(basePrice) : '',
      pricingRules: pricingRules.length ? pricingRules : [],
      overrideRules: overrideRules.length ? overrideRules : [],
    };
    try {
      const res = await createAdPricingRule(payload);
      if (res && (res.status === 200 || res.status === 201)) {
        toast.success('Rules saved successfully!');
        setTimeout(() => {
          router.push('/ad-management/current-ad-plan');
        }, 1000);
      } else {
        // Error toast is already handled in the service, do not show again
      }
    } catch (error) {
      // Error toast is already handled in the service, do not show again
    }
    setIsSaving(false);
  };

  // Save button enablement: allow if there are pricing rules or overrides, and not saving
  const canSave = (rules.length > 0 || Object.keys(dateOverrides).length > 0) && !isSaving;

  // --- UI ---
  if (!showCalendar) {
    return (
      <div className="p-6 border border-gray-200 shadow-xs rounded-lg">
        {/* Stepper */}
        <div className="mb-8 flex items-center gap-4">
          <button
            type="button"
            className={`w-8 h-8 flex items-center justify-center rounded-full font-bold text-base transition-all duration-200 ${
              !showCalendar
                ? "bg-[#0161AB] text-white"
                : "bg-[#F5F8FE] text-[#0161AB] border border-[#0161AB]"
            }`}
            style={{ outline: "none" }}
            onClick={() => handleStepClick(1)}
            tabIndex={0}
          >
            {isNextEnabled ? (
              <svg
                width="20"
                height="20"
                viewBox="0 0 20 20"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <circle cx="10" cy="10" r="10" fill="#4D7906" />
                <path
                  d="M6 10.5L9 13.5L14 8.5"
                  stroke="white"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            ) : (
              1
            )}
          </button>
          <span
            className={`font-[poppins] text-lg ${
              !showCalendar ? "text-[#0161AB] font-semibold" : "text-gray-400"
            }`}
          >
            Step 1: Ad Details
          </span>
          {/* <div className="flex-1 h-0.5 bg-gray-200 mx-2" /> */}
          <button
            type="button"
            className={`w-8 h-8 flex items-center justify-center rounded-full font-bold text-base transition-all duration-200 ${
              showCalendar && isNextEnabled
                ? "bg-[#0161AB] text-white"
                : "bg-[#F5F8FE] text-[#0161AB] border border-[#0161AB]"
            } ${isNextEnabled ? "cursor-pointer" : "cursor-not-allowed"}`}
            style={{ outline: "none" }}
            onClick={() => isNextEnabled && handleStepClick(2)}
            tabIndex={isNextEnabled ? 0 : -1}
            disabled={!isNextEnabled}
          >
            2
          </button>
          <span
            className={`font-[poppins] text-lg ${
              showCalendar ? "text-[#0161AB] font-semibold" : "text-gray-400"
            }`}
          >
            Step 2: Calendar Management
          </span>
        </div>

        {/* Combo selection cards */}
        <div className="mb-8">
          <label className="block font-[poppins] mb-2 text-[#211F54]">
            Select Ad Placement Combo<span className="text-[#E1020C]">*</span>
          </label>
          {/* Home page section */}
          <div className="mb-5">
            <div className="font-semibold text-[#211F54] mb-2">Home page</div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                { type: "Banner", position: AdPositionTypeEnum.TOP, page: AdPageTypeEnum.HOMEPAGE },
                { type: "Grid", position: AdPositionTypeEnum.MIDDLE, page: AdPageTypeEnum.HOMEPAGE },
                { type: "Banner", position: AdPositionTypeEnum.BOTTOM, page: AdPageTypeEnum.HOMEPAGE },
              ].map((c) => {
                const alreadyCreated = isPlanCreated(c.page, c.type, c.position);
                const plan = createdPlans.find(
                  (cp) => cp.page === String(c.page).toLowerCase() && cp.type === String(c.type).toLowerCase() && cp.position === String(c.position).toLowerCase()
                );
                return (
                  <div
                    key={`${c.page}-${c.type}-${c.position}`}
                    className={`flex-1 border rounded-xl p-6 flex flex-col items-start gap-2 transition-all border-[#0161AB] bg-[#F5F8FE]`}
                  >
                    <div className="flex gap-2 items-center flex-wrap">
                      <span className="px-2 py-0.5 rounded-full bg-purple-50 text-purple-700 text-xs">Type: {c.type}</span>
                      <span className="px-2 py-0.5 rounded-full bg-gray-100 text-gray-700 text-xs">Position: {c.position}</span>
                    </div>
                    {alreadyCreated && (
                      <span className="px-2 py-0.5 rounded-full bg-green-50 text-green-700 text-xs">Plan already created</span>
                    )}
                    <div className="mt-3 flex gap-2 w-full">
                      {alreadyCreated ? (
                        <>
                          <button className="px-3 py-1 rounded-md text-xs border border-gray-300" onClick={() => { if(plan?._id) alert(`View plan id: ${plan._id}`); }}>View</button>
                          <button className="px-3 py-1 rounded-md text-xs border border-gray-300" onClick={() => { if(plan?._id) alert(`Edit plan id: ${plan._id}`); }}>Edit</button>
                          <button className="px-3 py-1 rounded-md text-xs border border-red-300 text-red-600" onClick={() => { if(plan?._id) alert(`Delete plan id: ${plan._id}`); }}>Delete</button>
                        </>
                      ) : (
                        <button className="px-3 py-1 rounded-md text-xs bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white" onClick={() => { setPage(c.page); setAdType(c.type); setPlacement(c.position); setShowCalendar(true); setTimeout(loadMonthData, 0); }}>Create Plan</button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Sell page section */}
          <div className="mb-5">
            <div className="font-semibold text-[#211F54] mb-2">Sell page</div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                { type: "Banner", position: AdPositionTypeEnum.BOTTOM, page: AdPageTypeEnum.SELLPAGE },
              ].map((c) => {
                const selected = page === c.page && adType === c.type && placement === c.position;
                const alreadyCreated = isPlanCreated(c.page, c.type, c.position);
                const plan = createdPlans.find((cp) => cp.page === String(c.page).toLowerCase() && cp.type === String(c.type).toLowerCase() && cp.position === String(c.position).toLowerCase());
                return (
                  <button
                    key={`${c.page}-${c.type}-${c.position}`}
                    type="button"
                    disabled={alreadyCreated}
                    className={`flex-1 border rounded-xl p-6 flex flex-col items-start gap-2 transition-all ${
                      selected ? "border-[#0161AB] bg-[#F5F8FE]" : "border-gray-200 bg-white"
                    } ${alreadyCreated ? "opacity-60 cursor-not-allowed" : ""}`}
                    onClick={() => {
                      if (alreadyCreated) return;
                      setPage(c.page);
                      setAdType(c.type);
                      setPlacement(c.position);
                    }}
                  >
                    <div className="flex gap-2 items-center flex-wrap border-[#0161AB] bg-[#F5F8FE]">
                      <span className="px-2 py-0.5 rounded-full bg-purple-50 text-purple-700 text-xs">Type: {c.type}</span>
                      <span className="px-2 py-0.5 rounded-full bg-gray-100 text-gray-700 text-xs">Position: {c.position}</span>
                    </div>
                    <div className="mt-3 flex gap-2 w-full">
                      {alreadyCreated ? (
                        <>
                          <button className="px-3 py-1 rounded-md text-xs border border-gray-300" onClick={(e) => { e.stopPropagation(); if(plan?._id) router.push(`/ad-management/current-ad-plan/view/${plan._id}`); }}>View</button>
                          <button className="px-3 py-1 rounded-md text-xs border border-gray-300" onClick={(e) => { e.stopPropagation(); if(plan?._id) router.push(`/ad-management/current-ad-plan/edit/${plan._id}`); }}>Edit</button>
                          <button className="px-3 py-1 rounded-md text-xs border border-red-300 text-red-600" onClick={async (e) => { e.stopPropagation(); if(!plan?._id) return; await deleteResource(plan._id); const res = await fetchAdPricingRules(); const resources = res?.data?.resources || res?.data || []; setCreatedPlans(resources.map((r) => ({ page: String(r.page||"").toLowerCase(), type: String(r.type||"").toLowerCase(), position: String(r.position||"").toLowerCase(), _id: r._id }))); }}>Delete</button>
                        </>
                      ) : (
                        <button className="px-3 py-1 rounded-md text-xs bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white" onClick={(e) => { e.stopPropagation(); setPage(c.page); setAdType(c.type); setPlacement(c.position); setShowCalendar(true); setTimeout(loadMonthData, 0); }}>Create Plan</button>
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Community page section (no fixed placement) */}
          <div className="mb-5">
            <div className="font-semibold text-[#211F54] mb-2">Community page</div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                { type: "Banner", position: AdPositionTypeEnum.NOT_FIXED, page: AdPageTypeEnum.COMMUNITYPAGE },
                { type: "Grid", position: AdPositionTypeEnum.NOT_FIXED, page: AdPageTypeEnum.COMMUNITYPAGE },
              ].map((c) => {
                const selected = page === c.page && adType === c.type && placement === c.position;
                const alreadyCreated = isPlanCreated(c.page, c.type, c.position);
                const plan = createdPlans.find((cp) => cp.page === String(c.page).toLowerCase() && cp.type === String(c.type).toLowerCase() && cp.position === String(c.position).toLowerCase());
                return (
                  <button
                    key={`${c.page}-${c.type}-any`}
                    type="button"
                    disabled={alreadyCreated}
                    className={`flex-1 border rounded-xl p-6 flex flex-col items-start gap-2 transition-all ${
                      selected ? "border-[#0161AB] bg-[#F5F8FE]" : "border-gray-200 bg-white"
                    } ${alreadyCreated ? "opacity-60 cursor-not-allowed" : ""}`}
                    onClick={() => {
                      if (alreadyCreated) return;
                      setPage(c.page);
                      setAdType(c.type);
                      setPlacement(c.position);
                    }}
                  >
                    <div className="flex gap-2 items-center flex-wrap">
                      <span className="px-2 py-0.5 rounded-full bg-purple-50 text-purple-700 text-xs">Type: {c.type}</span>
                      <span className="px-2 py-0.5 rounded-full bg-gray-100 text-gray-700 text-xs">Position: {c.position}</span>
                    </div>
                    <div className="mt-3 flex gap-2 w-full">
                      {alreadyCreated ? (
                        <>
                          <button className="px-3 py-1 rounded-md text-xs border border-gray-300" onClick={(e) => { e.stopPropagation(); if(plan?._id) router.push(`/ad-management/current-ad-plan/view/${plan._id}`); }}>View</button>
                          <button className="px-3 py-1 rounded-md text-xs border border-gray-300" onClick={(e) => { e.stopPropagation(); if(plan?._id) router.push(`/ad-management/current-ad-plan/edit/${plan._id}`); }}>Edit</button>
                          <button className="px-3 py-1 rounded-md text-xs border border-red-300 text-red-600" onClick={async (e) => { e.stopPropagation(); if(!plan?._id) return; await deleteResource(plan._id); const res = await fetchAdPricingRules(); const resources = res?.data?.resources || res?.data || []; setCreatedPlans(resources.map((r) => ({ page: String(r.page||"").toLowerCase(), type: String(r.type||"").toLowerCase(), position: String(r.position||"").toLowerCase(), _id: r._id }))); }}>Delete</button>
                        </>
                      ) : (
                        <button className="px-3 py-1 rounded-md text-xs bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white" onClick={(e) => { e.stopPropagation(); setPage(c.page); setAdType(c.type); setPlacement(c.position); setShowCalendar(true); setTimeout(loadMonthData, 0); }}>Create Plan</button>
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Product page (listing page) section (no fixed placement) */}
          <div className="mb-5">
            <div className="font-semibold text-[#211F54] mb-2">Product page</div>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {[
                { type: "Banner", position: AdPositionTypeEnum.NOT_FIXED, page: AdPageTypeEnum.LISTINGPAGE },
                { type: "Grid", position: AdPositionTypeEnum.NOT_FIXED, page: AdPageTypeEnum.LISTINGPAGE },
              ].map((c) => {
                const selected = page === c.page && adType === c.type && placement === c.position;
                const alreadyCreated = isPlanCreated(c.page, c.type, c.position);
                const plan = createdPlans.find((cp) => cp.page === String(c.page).toLowerCase() && cp.type === String(c.type).toLowerCase() && cp.position === String(c.position).toLowerCase());
                return (
                  <button
                    key={`${c.page}-${c.type}-any`}
                    type="button"
                    disabled={alreadyCreated}
                    className={`flex-1 border rounded-xl p-6 flex flex-col items-start gap-2 transition-all ${
                      selected ? "border-[#0161AB] bg-[#F5F8FE]" : "border-gray-200 bg-white"
                    } ${alreadyCreated ? "opacity-60 cursor-not-allowed" : ""}`}
                    onClick={() => {
                      if (alreadyCreated) return;
                      setPage(c.page);
                      setAdType(c.type);
                      setPlacement(c.position);
                    }}
                  >
                    <div className="flex gap-2 items-center flex-wrap">
                      <span className="px-2 py-0.5 rounded-full bg-purple-50 text-purple-700 text-xs">Type: {c.type}</span>
                      <span className="px-2 py-0.5 rounded-full bg-gray-100 text-gray-700 text-xs">Position: {c.position}</span>
                    </div>
                    <div className="mt-3 flex gap-2 w-full">
                      {alreadyCreated ? (
                        <>
                          <button className="px-3 py-1 rounded-md text-xs border border-gray-300" onClick={(e) => { e.stopPropagation(); if(plan?._id) router.push(`/ad-management/current-ad-plan/view/${plan._id}`); }}>View</button>
                          <button className="px-3 py-1 rounded-md text-xs border border-gray-300" onClick={(e) => { e.stopPropagation(); if(plan?._id) router.push(`/ad-management/current-ad-plan/edit/${plan._id}`); }}>Edit</button>
                          <button className="px-3 py-1 rounded-md text-xs border border-red-300 text-red-600" onClick={async (e) => { e.stopPropagation(); if(!plan?._id) return; await deleteResource(plan._id); const res = await fetchAdPricingRules(); const resources = res?.data?.resources || res?.data || []; setCreatedPlans(resources.map((r) => ({ page: String(r.page||"").toLowerCase(), type: String(r.type||"").toLowerCase(), position: String(r.position||"").toLowerCase(), _id: r._id }))); }}>Delete</button>
                        </>
                      ) : (
                        <button className="px-3 py-1 rounded-md text-xs bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white" onClick={(e) => { e.stopPropagation(); setPage(c.page); setAdType(c.type); setPlacement(c.position); setShowCalendar(true); setTimeout(loadMonthData, 0); }}>Create Plan</button>
                      )}
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        </div>
        {/* Base Price */}
        <div className="mb-8">
          <label className="block font-[poppins] mb-1 text-[#211F54]">
            Base Price<span className="text-[#E1020C]">*</span>
          </label>
          <div className="flex items-center w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
            <select
              className="bg-transparent px-4 py-3 font-[poppins] focus:outline-none border-none text-[#211F54]"
              value={baseCurrency}
              disabled
              style={{ cursor: "not-allowed" }}
            >
              <option value="JMD">J$</option>
            </select>
            <input
              type="number"
              min="0"
              maxLength={10}
              className="flex-1 px-4 py-3 ml-4 font-[poppins] bg-transparent focus:outline-none border-l border-gray-200 text-[#211F54]"
              value={basePrice}
              onChange={(e) => {
                // Only allow numbers and empty string, and max 10 digits
                const val = e.target.value;
                if (/^\d{0,10}$/.test(val)) setBasePrice(val);
              }}
              placeholder="Enter amount"
            />
          </div>
        </div>
        {/* Manage Calendar Button (commented out) */}
        {/**
        <button
          className="flex items-center gap-2 px-8 py-3 rounded-lg bg-gradient-to-r from-[#E1020C] to-[#4D7906] text-white font-semibold text-lg mb-8"
          onClick={() => {
            setShowCalendar(true);
            loadMonthData();
          }}
        >
          <span className="text-xl">🗓️</span> Manage Calendar
        </button>
        */}
        {/* Save Button */}
        <button
          className={`w-full py-4 font-semibold text-lg rounded-full ${
            isNextEnabled
              ? "cursor-pointer text-white border-radius-[66px] bg-gradient-to-r from-[#211F54] to-[#0161AB]"
              : "bg-gray-500 cursor-not-allowed"
          }`}
          style={
            isNextEnabled
              ? {
                  borderRadius: "66px",
                  background:
                    "linear-gradient(268deg, #211F54 11.09%, #0161AB 98.55%)",
                }
              : {}
          }
          disabled={!isNextEnabled}
          onClick={() => {
            if (isNextEnabled) {
              setShowCalendar(true);
              loadMonthData();
            }
          }}
        >
          Next
        </button>
      </div>
    );
  }

  // Calendar matrix for current month/year
  const calendar = getCalendarMatrix(calendarYear, calendarMonth);

  // Compute if all required fields are filled
  const isRuleFormValid = !!(
    ruleForm.name &&
    ruleForm.startDate &&
    ruleForm.endDate &&
    ruleForm.color &&
    ruleForm.price
  );

  // Get today's date in yyyy-mm-dd format
  const todayStr = new Date().toISOString().split('T')[0];

  // Find the most recent rule (excluding base price)
  // Filter out base price rules
  const nonBaseRules = rules.filter(rule => rule.name?.toLowerCase() !== 'base price');
  // Find the most recent rule by updatedAt (ensure updatedAt exists and is valid)
  let mostRecentRule = null;
  if (nonBaseRules.length > 0) {
    mostRecentRule = nonBaseRules.reduce((a, b) => {
      // If either updatedAt is missing, treat as less recent
      const aTime = a.updatedAt ? new Date(a.updatedAt).getTime() : 0;
      const bTime = b.updatedAt ? new Date(b.updatedAt).getTime() : 0;
      return aTime > bTime ? a : b;
    });
  }
  // Use the rule's name as fallback if _id is missing (for frontend-only rules)
  const mostRecentRuleId = mostRecentRule?._id || mostRecentRule?.name || null;
  console.log(mostRecentRuleId, "mostRecentRuleId", nonBaseRules, mostRecentRule);

  return (
    <div className="p-6 border border-gray-200 shadow-xs rounded-lg">
      {/* Stepper */}
      <div className=" flex items-center gap-4">
        <button
          type="button"
          className={`w-8 h-8 flex items-center justify-center rounded-full font-bold text-base transition-all duration-200 ${
            !showCalendar
              ? "bg-[#0161AB] text-white"
              : "bg-[#F5F8FE] text-[#0161AB] border border-[#0161AB]"
          }`}
          style={{ outline: "none" }}
          onClick={() => handleStepClick(1)}
          tabIndex={0}
        >
          {" "}
          <svg
            width="20"
            height="20"
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <circle cx="10" cy="10" r="10" fill="#4D7906" />
            <path
              d="M6 10.5L9 13.5L14 8.5"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </button>
        <span
          className={`font-[poppins] text-lg ${
            !showCalendar ? "text-[#0161AB] font-semibold" : "text-gray-400"
          }`}
        >
          Step 1: Ad Details
        </span>
        {/* <div className="w-18 h-0.5 bg-gray-200 mx-2" /> */}
        <button
          type="button"
          className={`w-8 h-8 flex items-center justify-center rounded-full font-bold text-base transition-all duration-200 ${
            showCalendar && isNextEnabled
              ? "bg-[#0161AB] text-white"
              : "bg-[#F5F8FE] text-[#0161AB] border border-[#0161AB]"
          } ${isNextEnabled ? "cursor-pointer" : "cursor-not-allowed"}`}
          style={{ outline: "none" }}
          onClick={() => isNextEnabled && handleStepClick(2)}
          tabIndex={isNextEnabled ? 0 : -1}
          disabled={!isNextEnabled}
        >
          2
        </button>
        <span
          className={`font-[poppins] text-lg ${
            showCalendar ? "text-[#0161AB] font-semibold" : "text-gray-400"
          }`}
        >
          Step 2: Calendar Management
        </span>
      </div>
      {/* Header */}
      <div className="flex items-center justify-end gap-8 mb-4 border-b border-gray-200 pb-6">
        <div className="flex items-center gap-2">
          {/* <button
            className="text-2xl font-bold text-[#211F54] bg-transparent border-none p-0 mr-2"
            onClick={() => setShowCalendar(false)}
          >
            {"<"}
          </button> */}
          {/* <span className="text-xl font-bold font-[poppins]">Budget Calendar</span> */}
        </div>
        <div className="flex items-center gap-2 ">
          <span className="font-[poppins] font-medium bg-white px-4 py-2 rounded border border-gray-200 shadow-sm">
            {new Date(calendarYear, calendarMonth).toLocaleString("default", {
              month: "long",
              year: "numeric",
            })}
          </span>
          <button
            className="px-3 py-2 bg-white border border-gray-200 rounded-lg shadow-sm text-xl  text-[#211F54]"
            onClick={handlePrevMonth}
          >
            {"<"}
          </button>
          <button
            className="px-3 py-2 bg-white border border-gray-200 rounded-lg shadow-sm text-xl  text-[#211F54]"
            onClick={handleNextMonth}
          >
            {">"}
          </button>
        </div>
        <button
          type="button"
          className="inline-flex items-center justify-center px-4 py-4 rounded-full bg-gradient-to-tr from-[#211F54] to-[#0161AB] min-h-[34.71px] h-[47px] w-[175px] font-inter"
          onClick={() => {
            setShowRuleModal(true);
            setEditingRuleIdx(null);
          }}
        >
          <span className="text-white text-[14.2px] font-semibold leading-[22.09px] text-center">
            Set Pricing Rule
          </span>
        </button>
      </div>
      {/* Pricing Rules Table */}
      <div className="mb-8">
        <h3 className="text-xl font-bold mb-4 text-[#211F54]">Pricing Rules</h3>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white rounded-lg shadow border border-gray-200">
            <thead>
              <tr className="bg-[#F5F6FA] text-[#211F54]">
                <th className="p-3 text-left font-semibold border-b border-gray-200">Name</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Price</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Priority</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Start Date</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">End Date</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Color</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Status</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Last Updated</th>
                <th className="p-3 text-left font-semibold border-b border-gray-200">Actions</th>
              </tr>
            </thead>
            <tbody>
              {nonBaseRules.length === 0 ? (
                <tr><td colSpan={9} className="text-center p-4 text-gray-400">No pricing rules</td></tr>
              ) : nonBaseRules.map((rule, idx) => (
                <tr key={rule._id} className="hover:bg-[#F5F6FA]">
                  <td className="p-3">{rule.name}</td>
                  <td className="p-3 border-b border-gray-200">{formatJMD(rule.price)}</td>
                  <td className="p-3">{rule.priority}</td>
                  <td className="p-3">{rule.startDate}</td>
                  <td className="p-3">{rule.endDate}</td>
                  <td className="p-3">
                    {rule.color && (
                      <span className="inline-flex items-center gap-2">
                        <span style={{ background: rule.color, width: 18, height: 18, borderRadius: '50%', border: '1px solid #ccc', display: 'inline-block' }} />
                      </span>
                    )}
                  </td>
                  <td className="p-3">
                    {rule?.enabled ? (
                      <span style={{ color: 'green', fontWeight: 600 }}>Active</span>
                    ) : (
                      <span style={{ color: 'red', fontWeight: 600 }}>Inactive</span>
                    )}
                  </td>
                  <td className="p-3">
                    {formatDateTime(rule.updatedAt)}
                    {rule?.name === mostRecentRuleId && (
                      <span className="ml-2 px-2 py-1 bg-green-100 text-green-700 rounded text-xs font-semibold">Recent</span>
                    )}
                  </td>
                  <td className="p-3 flex gap-2">
                    <button className="px-2 py-1 bg-[#0161AB] text-white rounded text-xs" onClick={() => {
                      setRuleForm({ ...rule });
                      setShowRuleModal(true);
                      setEditingRuleIdx(idx);
                    }}>Edit</button>
                    <button className="px-2 py-1 bg-red-500 text-white rounded text-xs" onClick={() => {
                      setRules(rules => rules.filter((_, i) => i !== idx));
                    }}>Delete</button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      {/* Calendar */}
      <div className="bg-white rounded-lg shadow p-0 overflow-x-auto border border-gray-200">
        <table className="min-w-full text-sm font-[poppins]">
          <thead>
            <tr>
              {weekDays.map((day) => (
                <th
                  key={day}
                  className="p-3 text-center font-semibold text-[#211F54] bg-[#F5F6FA] border-b border-gray-200"
                >
                  {day}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {calendar.map((week, rowIdx) => (
              <tr key={rowIdx}>
                {week.map((day, colIdx) => {
                  if (!day) return <td key={colIdx} />;
                  let isDisabled = false;
                  const dateStr = `${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                  // Check for override
                  const override = dateOverrides[dateStr];
                  if (override) {
                    // Override present: show override price/currency, bg-gray-200, no border, cross icon
                    return (
                      <td
                        key={colIdx}
                        className="p-0 text-center align-top "
                        style={{ minWidth: 120, height: 90 }}
                      >
                        <div
                          className="flex flex-col items-center justify-center h-full py-2 bg-gray-200 rounded-lg relative"
                          style={{ border: 'none', position: 'relative' }}
                          onClick={() => !isDisabled && handleCellClick(day)}
                        >
                          {/* Remove override cross icon */}
                          <button
                            className="absolute top-1 right-1 text-xs text-gray-500 hover:text-red-500 bg-white rounded-full p-0.5 border border-gray-300 z-10"
                            style={{ lineHeight: 1, fontSize: 14 }}
                            title="Remove override"
                            onClick={e => {
                              e.stopPropagation();
                              handleRemoveOverride(day);
                            }}
                          >
                            ×
                          </button>
                          <span className="font-normal text-xl text-center">
                            {String(day).padStart(2, "0")}
                          </span>
                          <div className="flex items-center gap-2 mt-2">
                            <span className="text-xs font-semibold" style={{ color: '#888' }}>
                              {(override.currency === "USD" ? "US$" : "J$") +
                                " " +
                                override.price}
                            </span>
                          </div>
                        </div>
                      </td>
                    );
                  }
                  // Highlight logic (existing)
                  const ruleForDay = getRuleForDateWithUpdatedAt(rules, calendarYear, calendarMonth, day);
                  const highlightStyle = ruleForDay
                    ? {
                        border: `2px solid ${ruleForDay.color}`,
                        color: ruleForDay.color,
                        fontWeight: 600,
                      }
                    : {};
                  // Show rule price if present, else base price
                  const priceToShow = ruleForDay ? ruleForDay.price : basePrice;
                  const currencyToShow = ruleForDay ? ruleForDay.currency : baseCurrency;
                  return (
                    <td
                      key={colIdx}
                      className="p-0 text-center align-top "
                      style={{ minWidth: 120, height: 90 }}
                    >
                      <div
                        className="flex flex-col items-center justify-center h-full py-2 border border-gray-100 rounded-lg relative"
                        style={
                          isDisabled
                            ? { opacity: 0.5, pointerEvents: "none", ...highlightStyle }
                            : highlightStyle
                        }
                        onClick={() => !isDisabled && handleCellClick(day)}
                      >
                        {/* Rule name label in top-left if rule exists */}
                        {ruleForDay && (
                          <span
                            className="absolute truncate max-w-[100px] left-1 top-1 text-[10px] font-semibold px-1 rounded"
                            style={{ background: '#fff', color: ruleForDay.color, border: `1px solid ${ruleForDay.color}`, zIndex: 2 }}
                            title={ruleForDay.name}
                          >
                            {ruleForDay.name}
                          </span>
                        )}
                        <span className="font-normal text-xl text-center">
                          {String(day).padStart(2, "0")}
                        </span>
                        <div className="flex items-center gap-2 mt-2">
                          <span className="text-xs font-semibold" style={ruleForDay ? { color: ruleForDay.color } : { color: '#888' }}>
                            {(currencyToShow === "USD" ? "US$" : "J$") +
                              " " +
                              priceToShow}
                          </span>
                        </div>
                      </div>
                    </td>
                  );
                })}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      {/* Save Button */}
      <button
        className={`w-full py-4 rounded-full mt-8 font-semibold text-lg ${
          canSave
            ? "bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white cursor-pointer"
            : "bg-gray-500 text-white cursor-not-allowed"
        }`}
        disabled={!canSave}
        onClick={handleApiSave}
      >
        {isSaving ? (
          <span className="flex items-center justify-center">
            <svg className="animate-spin h-6 w-6 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path>
            </svg>
            Saving...
          </span>
        ) : (
          'Save'
        )}
      </button>
      {/* Set Price Modal */}
      {showSetPrice && (
        <div
          className="fixed inset-0 flex items-center justify-center z-50"
          style={{ background: "rgba(0, 0, 0, 0.40)" }}

          onClick={() => setShowSetPrice(false)}
        >
          <div
            className="bg-white rounded-2xl shadow-lg p-8 w-full max-w-sm relative border border-gray-100"
            onClick={(e) => e.stopPropagation()}
          >
            <h3 className="text-lg text-left pb-2 font-[poppins]">
              {selectedCell.day && (dateOverrides[`${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(selectedCell.day).padStart(2, '0')}`])
                ? "Update"
                : "Set"}{" "}
              Price
              <span className="text-[#E1020C]">*</span>
            </h3>
            <div className="flex items-center w-full bg-white border border-gray-200 rounded-lg overflow-hidden mb-6">
              <select
                className="appearance-none bg-transparent px-4 py-3 font-[poppins] focus:outline-none border-none border-r border-gray-200 text-[#211F54]"
                value={modalCurrency}
                style={{ cursor: 'not-allowed' }}
                disabled
                onChange={(e) => setModalCurrency(e.target.value)}
              >
                <option value="JMD">JMD</option>
              </select>
              <input
                type="number"
                min="1"
                max="9999999999"
                className="flex-1 px-4 py-3 ml-4 font-[poppins] bg-transparent focus:outline-none border-l border-gray-200 text-[#211F54]"
                value={modalPrice}
                onChange={(e) => {
                  const val = e.target.value;
                  // Only allow numbers, no leading zero, max 10 digits, no zero
                  if (/^([1-9][0-9]{0,9})?$/.test(val)) {
                    setModalPrice(val);
                  }
                }}
                onKeyDown={e => {
                  if (["e", "E", "+", "-"].includes(e.key)) {
                    e.preventDefault();
                  }
                }}
                placeholder="Enter price"
              />
            </div>
            <button
              className={`w-full py-3 rounded-full text-lg ${!modalPrice ? 'bg-gray-400 text-white cursor-not-allowed' : 'bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white cursor-pointer'}`}
              onClick={handleConfirm}
              disabled={!modalPrice}
            >
              {selectedCell.day && (dateOverrides[`${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(selectedCell.day).padStart(2, '0')}`])
                ? "Update"
                : "Set"}
            </button>
          </div>
        </div>
      )}
      {/* Pricing Rule Modal */}
      {showRuleModal && (
        <div
          className="border-2 fixed flex-col inset-0 flex items-center justify-center z-50 "
          style={{ background: "rgba(0, 0, 0, 0.80)" }}
          onClick={() => {setShowRuleModal(false),
          setRuleForm({
                      name: "",
                      enabled: true,
                      startDate: "",
                      endDate: "",
                      price: null,
                      currency: "",
                      priority: null,
                      color: "",
                    });}}
        >
          <div
            onClick={(e) => e.stopPropagation()}
            className="
              b
              w-[643px] h-fit min-w-[200px]
              rounded-[16.87px]
              border border-[#F8F9FA]
              bg-white
              shadow-[0px_3.374px_16.87px_0px_rgba(238,238,238,0.50)]
              p-8
              relative
            "
          >
            <button
              className=" absolute top-2 right-2 text-2xl"
              onClick={() => setShowRuleModal(false)}
            >
              <MdClose />
            </button>

            <div
              className="
                flex flex-col items-start gap-[10px] flex-shrink-0
                
                p-[20px] pt-[20px] pb-[20px] pl-[16px] pr-[16px]
                rounded-[16px] border border-[#EFF1F4] bg-white
              "
            >
              <div className="flex items-center justify-between mb-4 w-full">
                <div className="flex items-center justify-end gap-2">
                  <span className="text-sm text-gray-500 mr-2">Enabled</span>
                  <button
                    type="button"
                    aria-pressed={ruleForm.enabled}
                    onClick={() =>
                      setRuleForm((f) => ({ ...f, enabled: !f.enabled }))
                    }
                    className={`
                      relative w-10 h-6 transition-colors duration-300
                      rounded-full focus:outline-none border-0
                      ${
                        ruleForm.enabled
                          ? "bg-gradient-to-r from-[#24194B] to-[#0B5B8C]"
                          : "bg-gray-300"
                      }
                    `}
                    style={{
                      minWidth: "40px",
                      minHeight: "24px",
                      boxShadow: ruleForm.enabled
                        ? "0 0 0 2px #0B5B8C22"
                        : undefined,
                      padding: 0,
                    }}
                  >
                    <span
                      className={`
                        absolute top-0.5 left-0.5 transition-all duration-300
                        w-5 h-5 rounded-full bg-white
                        shadow
                        ${ruleForm.enabled ? "translate-x-4" : "translate-x-0"}
                      `}
                      style={{
                        boxShadow: "0 1px 4px 0 rgba(0,0,0,0.10)",
                      }}
                    />
                  </button>
                </div>
              </div>
              <label className="block font-[poppins]  text-[#211F54] text-sm">
                Rule Name<span className="text-[#E1020C]">*</span>
              </label>

              <input
                className="w-full border text-sm border-gray-200 rounded-lg px-4 py-3 font-[poppins] bg-white mb-4"
                placeholder="Enter Rule name"
                value={ruleForm.name}
                maxLength={100}
                onChange={(e) =>
                  setRuleForm((f) => ({ ...f, name: e.target.value }))
                }
              />
              <div className="mb-4 flex gap-4 w-full">
                <div className="flex-1">
                  <label className="block font-[poppins] mb-1 text-[#211F54] text-sm">
                    Start Date<span className="text-[#E1020C]">*</span>
                  </label>
                  <input
                    type="date"
                    className="w-full border text-sm border-gray-200 rounded-lg px-4 py-3 font-[poppins] bg-white"
                    value={ruleForm.startDate}
                    // min={todayStr}
                    onChange={(e) => {
                      setRuleForm((f) => ({ ...f, startDate: e.target.value, endDate: "" }));
                    }}
                  />
                </div>
                <div className="flex-1">
                  <label className="block font-[poppins] mb-1 text-[#211F54] text-sm">
                    End Date<span className="text-[#E1020C]">*</span>
                  </label>
                  <input
                    type="date"
                    className="w-full border text-sm border-gray-200 rounded-lg px-4 py-3 font-[poppins] bg-white"
                    value={ruleForm.endDate}
                    min={ruleForm.startDate ? new Date(new Date(ruleForm.startDate).getTime() + 24*60*60*1000).toISOString().split('T')[0] : todayStr}
                    onChange={(e) => {
                      const val = e.target.value;
                      if (ruleForm.startDate && val <= ruleForm.startDate) {
                        toast.warn('End date must be after start date');
                        return;
                      }
                      setRuleForm((f) => ({ ...f, endDate: val }));
                    }}
                  />
                </div>
              </div>
              <div className="mb-4 w-full">
                <label className="block font-[poppins] mb-1 text-[#211F54] text-sm">
                  Set price<span className="text-[#E1020C]">*</span>
                </label>
                <div className="flex items-center w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
                  <select
                    className="bg-transparent px-4 py-3 font-[poppins] focus:outline-none border-none text-[#211F54]"
                    value={ruleForm.currency}
              disabled
              style={{ cursor: "not-allowed" }}

                    // onChange={(e) =>
                    //   setRuleForm((f) => ({ ...f, currency: e.target.value }))
                    // }
                  >
                                 <option value="JMD">J$</option>

                  </select>
                  <input
                    type="number"
                    min="0"
                    maxLength={10}
                    className="flex-1 px-4 text-sm py-3 ml-4 font-[poppins] bg-transparent focus:outline-none border-l border-gray-200 text-[#211F54]"
                    value={ruleForm.price}
                    onChange={(e) => {
                      const val = e.target.value;
                      if (/^\d{0,10}$/.test(val)) {
                        setRuleForm((f) => ({ ...f, price: val }));
                      }
                    }}
                    placeholder="Enter price"
                  />
                </div>
              </div>
              <div className="mb-4 flex gap-4 items-center w-full ">
                <div className="flex-1">
                  <label className="block font-[poppins] mb-1 text-[#211F54] text-sm">
                    Set Priority
                  </label>
                  <div className="flex items-center gap-2 w-full">
                    <select
                      className="w-full border border-gray-200 rounded-lg px-2 py-2 font-[poppins] bg-white"
                      value={ruleForm.priority || ''}
                      onChange={e => {
                        setRuleForm(f => ({ ...f, priority: e.target.value }));
                      }}
                    >
                      <option className="text-sm border-gray-200" value="">Select priority</option>
                      {[...Array(10)].map((_, i) => {
                        const val = (i + 1) * 10;
                        return (
                          <option key={val} value={val}>{val}</option>
                        );
                      })}
                    </select>
                  </div>
                </div>
                <div className="flex-1">
                  <label className="block font-[poppins] mb-1 text-[#211F54] text-sm">
                    Choose Colour<span className="text-[#E1020C]">*</span>
                  </label>
                  <div className="flex items-center w-full">
                    <div className="">
                      <input
                        type="color"
                        value={ruleForm.color}
                        onChange={(e) =>
                          setRuleForm((f) => ({ ...f, color: e.target.value }))
                        }
                        className="flex-1 cursor-pointer h-[48px] border border-gray-200 rounded-l-lg  font-[poppins] bg-white text-sm"
                        style={{
                          backgroundColor: ruleForm.color,
                          color: "#211F54",
                          transition: "background 0.2s",
                          fontWeight: 500,
                        }}
                      />
                    </div>
                    <input
                      type="text"
                      value={ruleForm.color}
                      placeholder="Select color"
                      onChange={(e) =>
                        setRuleForm((f) => ({ ...f, color: e.target.value }))
                      }
                      className="flex-1 border border-gray-200 rounded-r-lg px-2 h-[48px] font-[poppins] bg-white text-sm placeholder:text-sm placeholder:border-gray-200"
                      style={{ borderLeft: "none" }}
                    />
                  </div>
                </div>
              </div>
              {/* <div className="mb-4 flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={ruleForm.copyToNextYear}
                  onChange={(e) =>
                    setRuleForm((f) => ({
                      ...f,
                      copyToNextYear: e.target.checked,
                    }))
                  }
                />
                <span className="font-[poppins] text-sm">
                  Copy to next year
                </span>
              </div> */}
            </div>
            <div className="flex justify-end gap-4 mt-6">
              <button
                className="px-6 py-2 rounded border border-gray-300 bg-white text-gray-700 font-semibold"
                onClick={() => {
                  setShowRuleModal(false);
                  setRuleForm({
                    name: "",
                    enabled: true,
                    startDate: "",
                    endDate: "",
                    price: null,
                    currency: "",
                    priority: null,
                    color: "",
                  });
                  setEditingRuleIdx(null);
                }}
              >
                Cancel
              </button>
              {editingRuleIdx !== null ? (
                <button
                  className={`px-6 py-2 rounded font-semibold ${isRuleFormValid ? 'bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white' : 'bg-gray-400 text-white cursor-not-allowed'}`}
                  disabled={!isRuleFormValid}
                  onClick={() => {
                    if (!isRuleFormValid) return;
                    const duplicateKey = findDuplicateRule(rules, ruleForm, editingRuleIdx);
                    if (duplicateKey) {
                      toast.warn(`Rule already exists with ${duplicateKey}`);
                      return;
                    }
                    setRules(rules => {
                      const updated = rules.map((r, i) => 
                        i === editingRuleIdx 
                          ? { 
                              ...ruleForm, 
                              priority: ruleForm.priority ? Number(ruleForm.priority) : null,
                              updatedAt: new Date().toISOString()
                            } 
                          : r
                      );
                      setTimeout(() => {
                        setShowRuleModal(false);
                        setRuleForm({
                          name: "",
                          enabled: true,
                          startDate: "",
                          endDate: "",
                          price: null,
                          currency: "",
                          priority: null,
                          color: "",
                        });
                        setEditingRuleIdx(null);
                        setCalendarRefresh(v => v + 1);
                      }, 0);
                      return updated;
                    });
                  }}
                >
                  Update Rule
                </button>
              ) : (
                <button
                  className={`px-6 py-2 rounded font-semibold ${isRuleFormValid ? 'bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white' : 'bg-gray-400 text-white cursor-not-allowed'}`}
                  disabled={!isRuleFormValid}
                  onClick={() => {
                    if (!isRuleFormValid) return;
                    const duplicateKey = findDuplicateRule(rules, ruleForm, null);
                    if (duplicateKey) {
                      toast.warn(`Rule already exists with ${duplicateKey}`);
                      return;
                    }
                    setRules((rules) => {
                      const updated = [
                        ...rules,
                        { 
                          ...ruleForm, 
                          priority: ruleForm.priority ? Number(ruleForm.priority) : null,
                          updatedAt: new Date().toISOString()
                        }
                      ];
                      setTimeout(() => {
                        setShowRuleModal(false);
                        setRuleForm({
                          name: "",
                          enabled: true,
                          startDate: "",
                          endDate: "",
                          price: null,
                          currency: "",
                          priority: null,
                          color: "",
                        });
                        setCalendarRefresh(v => v + 1);
                      }, 0);
                      return updated;
                    });
                  }}
                >
                  Save Rule
                </button>
              )}
            </div>
          </div>
        </div>
      )}
      {/* Rules List */}
     
    </div>
  );
}

function formatJMD(price) {
  if (price === null || price === undefined) return 'N/A';
  return new Intl.NumberFormat('en-JM', { style: 'currency', currency: 'JMD' }).format(price);
}

// Custom getRuleForDate for calendar
function getRuleForDateWithUpdatedAt(rules, year, month, day) {
  const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  const matching = rules.filter(rule => {
    if (rule.isActive === false && rule.enabled === false) return false;
    if (!rule.startDate || !rule.endDate) return false;
    const start = rule.startDate;
    const end = rule.endDate;
    return start <= dateStr && dateStr <= end;
  });
  if (matching.length === 0) return null;
  matching.sort((a, b) => {
    if (Number(b.priority) !== Number(a.priority)) {
      return Number(b.priority) - Number(a.priority);
    }
    return new Date(b.updatedAt) - new Date(a.updatedAt);
  });
  return matching[0];
}
