"use client";

import React from "react";
import { useRouter } from "next/navigation";
import TotalAdTypeChart from "@/app/components/ad-management/TotalAdTypeChart";
import SalesChart from "@/app/components/ad-management/SalesChart";
import RequestByMonthChart from "@/app/components/ad-management/RequestByMonthChart";
import RevenueAdChart from "@/app/components/ad-management/RevenueAdChart";
import SalesIndicatorsChart from "@/app/components/ad-management/SalesIndicatorsChart";
import AnalyticsReportChart from "@/app/components/ad-management/AnalyticsReportChart";

const StatCard = ({ color, title, value, change, icon,font_color }) => (
  <div className={`flex-1 h-[148px] w-[258px] min-w-[200px] rounded-xl p-4 flex items-center gap-4 ${color}`}> 
    <div className="flex flex-col flex-1">
      <span className={`text-sm font-medium ${font_color}`}>{title}</span>
      <span className={`text-2xl ext-[#000] ${font_color} `}>{value}</span>
      <span className={`text-xs mt-1 ext-[#000] ${font_color}`}>{change}</span>
    </div>
    <div className="text-3xl">{icon}</div>
  </div>
);

export default function AdManagementPage() {
  const router = useRouter();
  // Mock data
  const stats = [
    {
      color: "bg-[#FDE333]",
      title: "Total Ads",
      value: 89,
      change: "▲ 7% vs Last Month",
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="41" height="40" viewBox="0 0 41 40" fill="none">
      <path d="M40.0664 20C40.0664 8.95431 31.2888 0 20.461 0C9.63328 0 0.855713 8.95431 0.855713 20C0.855713 31.0457 9.63328 40 20.461 40C31.2888 40 40.0664 31.0457 40.0664 20Z" fill="#EFD733"/>
      <path d="M25.9796 13.2221C25.9796 15.5553 23.5821 17.4442 20.6215 17.4442C17.6608 17.4442 15.2632 15.5553 15.2632 13.2221C15.2632 10.8899 17.6608 9 20.6215 9C23.5821 9 25.9796 10.8899 25.9796 13.2221ZM22.7686 24.4814C22.7738 22.1846 25.0909 20.3053 28.0023 20.2315C27.3769 19.9006 26.6984 19.6436 25.9796 19.468C25.7694 19.4155 25.5593 19.3759 25.3452 19.3374L25.1584 19.3095C24.9884 19.2849 24.8185 19.2667 24.6446 19.2496L24.4436 19.2324C24.2152 19.2324 23.9973 19.2035 23.7469 19.2035H17.496C17.2599 19.2035 17.0276 19.2035 16.7993 19.2324L16.5983 19.2496C16.4244 19.2496 16.2544 19.2849 16.0845 19.3095L15.9016 19.3374C15.6836 19.3759 15.4553 19.4155 15.2632 19.468C12.0795 20.2422 9.90503 22.5604 9.90503 25.186C9.90503 26.7408 11.5034 28 13.478 28H25.135C23.6573 27.2087 22.7686 25.8895 22.7686 24.4814Z" fill="white"/>
      <path d="M28.5095 21H28.5005C26.0012 21 23.9807 22.5695 23.9807 24.5031C23.9807 26.4377 26.0102 28.0042 28.5095 28C31.0088 28 33.0332 26.4305 33.0293 24.4969C33.0293 22.5664 31.005 21 28.5095 21ZM30.7893 24.8432H28.9973V26.2297C28.9973 26.4201 28.7958 26.5761 28.5493 26.5761C28.3029 26.5761 28.1013 26.4201 28.1013 26.2297V24.8432H26.3093C26.0641 24.8432 25.8613 24.6872 25.8613 24.4969C25.8613 24.3065 26.0641 24.1505 26.3093 24.1505H28.1013V22.764C28.1013 22.5737 28.3029 22.4177 28.5493 22.4177C28.7958 22.4177 28.9973 22.5737 28.9973 22.764V24.1505H30.7893C31.0358 24.1505 31.2373 24.3065 31.2373 24.4969C31.2373 24.6872 31.0358 24.8432 30.7893 24.8432Z" fill="white"/>
    </svg>,
      font_color: "text-[#000]",
    },
    {
      color: "bg-[#FF000A]",
      title: "New Ads",
      value: 89,
      change: "▼ 7% vs Last Month",
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="41" height="40" viewBox="0 0 41 40" fill="none">
      <path d="M40.0663 20C40.0663 8.95431 31.2886 0 20.4609 0C9.63316 0 0.855591 8.95431 0.855591 20C0.855591 31.0457 9.63316 40 20.4609 40C31.2886 40 40.0663 31.0457 40.0663 20Z" fill="#D20008"/>
      <path d="M25.9796 13.2221C25.9796 15.5553 23.5821 17.4442 20.6215 17.4442C17.6608 17.4442 15.2632 15.5553 15.2632 13.2221C15.2632 10.8899 17.6608 9 20.6215 9C23.5821 9 25.9796 10.8899 25.9796 13.2221ZM22.7686 24.4814C22.7738 22.1846 25.0909 20.3053 28.0023 20.2315C27.3769 19.9006 26.6984 19.6436 25.9796 19.468C25.7694 19.4155 25.5593 19.3759 25.3452 19.3374L25.1584 19.3095C24.9884 19.2849 24.8185 19.2667 24.6446 19.2496L24.4436 19.2324C24.2152 19.2324 23.9973 19.2035 23.7469 19.2035H17.496C17.2599 19.2035 17.0276 19.2035 16.7993 19.2324L16.5983 19.2496C16.4244 19.2496 16.2544 19.2849 16.0845 19.3095L15.9016 19.3374C15.6836 19.3759 15.4553 19.4155 15.2632 19.468C12.0795 20.2422 9.90503 22.5604 9.90503 25.186C9.90503 26.7408 11.5034 28 13.478 28H25.135C23.6573 27.2087 22.7686 25.8895 22.7686 24.4814Z" fill="white"/>
      <path d="M28.5095 21H28.5005C26.0011 21 23.9807 22.5695 23.9807 24.5031C23.9807 26.4377 26.0102 28.0042 28.5095 28C31.0088 28 33.0331 26.4305 33.0293 24.4969C33.0293 22.5664 31.0049 21 28.5095 21ZM30.7893 24.8432H28.9973V26.2297C28.9973 26.4201 28.7957 26.5761 28.5493 26.5761C28.3028 26.5761 28.1013 26.4201 28.1013 26.2297V24.8432H26.3093C26.0641 24.8432 25.8613 24.6872 25.8613 24.4969C25.8613 24.3065 26.0641 24.1505 26.3093 24.1505H28.1013V22.764C28.1013 22.5737 28.3028 22.4177 28.5493 22.4177C28.7957 22.4177 28.9973 22.5737 28.9973 22.764V24.1505H30.7893C31.0357 24.1505 31.2373 24.3065 31.2373 24.4969C31.2373 24.6872 31.0357 24.8432 30.7893 24.8432Z" fill="white"/>
    </svg>,
      font_color: "text-[#FFFFFF]",
    },
    {
      color: "bg-[#4D7906]",
      title: "Pending Ads",
      value: 89,
      change: "▲ 7% vs Last Month",
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="41" height="40" viewBox="0 0 41 40" fill="none">
      <path d="M40.0663 20C40.0663 8.95431 31.2886 0 20.4609 0C9.63316 0 0.855591 8.95431 0.855591 20C0.855591 31.0457 9.63316 40 20.4609 40C31.2886 40 40.0663 31.0457 40.0663 20Z" fill="#538601"/>
      <path d="M25.9796 13.2221C25.9796 15.5553 23.5821 17.4442 20.6215 17.4442C17.6608 17.4442 15.2632 15.5553 15.2632 13.2221C15.2632 10.8899 17.6608 9 20.6215 9C23.5821 9 25.9796 10.8899 25.9796 13.2221ZM22.7686 24.4814C22.7738 22.1846 25.0909 20.3053 28.0023 20.2315C27.3769 19.9006 26.6984 19.6436 25.9796 19.468C25.7694 19.4155 25.5593 19.3759 25.3452 19.3374L25.1584 19.3095C24.9884 19.2849 24.8185 19.2667 24.6446 19.2496L24.4436 19.2324C24.2152 19.2324 23.9973 19.2035 23.7469 19.2035H17.496C17.2599 19.2035 17.0276 19.2035 16.7993 19.2324L16.5983 19.2496C16.4244 19.2496 16.2544 19.2849 16.0845 19.3095L15.9016 19.3374C15.6836 19.3759 15.4553 19.4155 15.2632 19.468C12.0795 20.2422 9.90503 22.5604 9.90503 25.186C9.90503 26.7408 11.5034 28 13.478 28H25.135C23.6573 27.2087 22.7686 25.8895 22.7686 24.4814Z" fill="white"/>
      <path d="M28.5095 21H28.5005C26.0011 21 23.9807 22.5695 23.9807 24.5031C23.9807 26.4377 26.0102 28.0042 28.5095 28C31.0088 28 33.0331 26.4305 33.0293 24.4969C33.0293 22.5664 31.0049 21 28.5095 21ZM30.7893 24.8432H28.9973V26.2297C28.9973 26.4201 28.7957 26.5761 28.5493 26.5761C28.3028 26.5761 28.1013 26.4201 28.1013 26.2297V24.8432H26.3093C26.0641 24.8432 25.8613 24.6872 25.8613 24.4969C25.8613 24.3065 26.0641 24.1505 26.3093 24.1505H28.1013V22.764C28.1013 22.5737 28.3028 22.4177 28.5493 22.4177C28.7957 22.4177 28.9973 22.5737 28.9973 22.764V24.1505H30.7893C31.0357 24.1505 31.2373 24.3065 31.2373 24.4969C31.2373 24.6872 31.0357 24.8432 30.7893 24.8432Z" fill="white"/>
    </svg>,
      font_color: "text-[#FFFFFF]",
    },
    {
      color: "bg-[#211F54]",
      title: "Total Ad Sales",
      value: "J$ 108999",
      change: "▲ 7% vs Last Month",
      icon: <svg xmlns="http://www.w3.org/2000/svg" width="41" height="40" viewBox="0 0 41 40" fill="none">
      <path d="M40.0663 20C40.0663 8.95431 31.2887 0 20.461 0C9.63322 0 0.855652 8.95431 0.855652 20C0.855652 31.0457 9.63322 40 20.461 40C31.2887 40 40.0663 31.0457 40.0663 20Z" fill="#2F2B85"/>
      <path d="M25.9796 13.2221C25.9796 15.5553 23.5821 17.4442 20.6215 17.4442C17.6608 17.4442 15.2632 15.5553 15.2632 13.2221C15.2632 10.8899 17.6608 9 20.6215 9C23.5821 9 25.9796 10.8899 25.9796 13.2221ZM22.7686 24.4814C22.7738 22.1846 25.0909 20.3053 28.0023 20.2315C27.3769 19.9006 26.6984 19.6436 25.9796 19.468C25.7694 19.4155 25.5593 19.3759 25.3452 19.3374L25.1584 19.3095C24.9884 19.2849 24.8185 19.2667 24.6446 19.2496L24.4436 19.2324C24.2152 19.2324 23.9973 19.2035 23.7469 19.2035H17.496C17.2599 19.2035 17.0276 19.2035 16.7993 19.2324L16.5983 19.2496C16.4244 19.2496 16.2544 19.2849 16.0845 19.3095L15.9016 19.3374C15.6836 19.3759 15.4553 19.4155 15.2632 19.468C12.0795 20.2422 9.90503 22.5604 9.90503 25.186C9.90503 26.7408 11.5034 28 13.478 28H25.135C23.6573 27.2087 22.7686 25.8895 22.7686 24.4814Z" fill="white"/>
      <path d="M28.5095 21H28.5005C26.0011 21 23.9807 22.5695 23.9807 24.5031C23.9807 26.4377 26.0102 28.0042 28.5095 28C31.0088 28 33.0331 26.4305 33.0293 24.4969C33.0293 22.5664 31.0049 21 28.5095 21ZM30.7893 24.8432H28.9973V26.2297C28.9973 26.4201 28.7957 26.5761 28.5493 26.5761C28.3028 26.5761 28.1013 26.4201 28.1013 26.2297V24.8432H26.3093C26.0641 24.8432 25.8613 24.6872 25.8613 24.4969C25.8613 24.3065 26.0641 24.1505 26.3093 24.1505H28.1013V22.764C28.1013 22.5737 28.3028 22.4177 28.5493 22.4177C28.7957 22.4177 28.9973 22.5737 28.9973 22.764V24.1505H30.7893C31.0357 24.1505 31.2373 24.3065 31.2373 24.4969C31.2373 24.6872 31.0357 24.8432 30.7893 24.8432Z" fill="white"/>
    </svg>,
      font_color: "text-[#FFFFFF]",
    },
  ];

  return (
    <div className="bg-white rounded-lg p-3 ">
      {/* Header Section */}
      <div className="w-full max-w-6xl mx-auto mb-8  flex items-center justify-between px-2 py-2">
        <div className="text-black text-lg font-poppins font-medium leading-6">Ad Management</div>
        <div className="flex items-center gap-4">
          <button
            className="h-10 px-6 bg-white border border-gray-200 rounded-full text-black text-sm font-poppins font-normal leading-5 hover:bg-gray-100 transition"
            onClick={() => router.push('/ad-management/listing')}
          >
            See Ad Listing
          </button>
          <button
            className="h-10 px-7 bg-gradient-to-tr from-[#211F54] to-[#0161AB] rounded-full text-white text-sm font-poppins font-normal leading-5 shadow hover:opacity-90 transition"
            onClick={() => router.push('/ad-management/manage-ad-pricing')}
          >
            + create Ad
          </button>
        </div>
      </div>
      {/* Stat Cards */}
      <div className="flex justify-center align-middle gap-4 mb-6">
        {stats.map((stat, idx) => (
          <StatCard key={idx} {...stat} />
        ))}
      </div>

      {/* Main Dashboard Content */}
       <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-6">
        <div className="col-span-1 lg:col-span-1"><TotalAdTypeChart /></div>
        <div className="col-span-1 lg:col-span-1"><SalesChart /></div>
        <div className="col-span-1 lg:col-span-1"><RequestByMonthChart /></div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6  mb-6">
        <div className="col-span-1 lg:col-span-2"><RevenueAdChart /></div>
        <div className="col-span-1 lg:col-span-2"><SalesIndicatorsChart /></div>
      </div>

      <div className="grid grid-cols-1 gap-6">
        <AnalyticsReportChart />
      </div> 
    </div>
  );
} 