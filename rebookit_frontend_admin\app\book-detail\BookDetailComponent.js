"use client";

import React, {useEffect, useState} from "react";
import {getToken, userDataFromLocal} from "../utils/utils";
import {useSearchParams} from "next/navigation";
import {getBooksById} from "../service/booklisting";
import ItemRespectiveDetails from "./ItemRespectiveDetails";

const BookDetailComponent = () => {
  const query = useSearchParams();
  const [BookDetail, setBookDetail] = useState({});
  const [isLoading, setisLoading] = useState(false);
  const [mainImage, setMainImage] = useState("");

  const fetchBookDetails = async (id) => {
    try {
      setisLoading(true);
      if (!id) {
        return;
      }
      let response = await getBooksById(id);
      if (response?.status == 200) {
        setBookDetail(response?.data);
        // Set first image as main image if available
        if (response.data?.images?.length > 0) {
          setMainImage(response.data.images[0]);
        }
      }
      setisLoading(false);
    } catch (err) {
      console.log("fetchBookDetails err", err);
    }
  };

  useEffect(() => {
    fetchBookDetails(query.get("id"));
  }, [query.get("id")]);

  // All consoles
  // console.log("query", query.get("id"));
  console.log("BookDetails", BookDetail);

  return (
    // <div className="bg-white rounded-xl overflow-hidden">
    //   {/* Main Content */}
    //   <div className="p-6">
    //     <div className="flex flex-col lg:flex-row gap-8">
    //       {/* Image Gallery Section */}
    //       <div className="lg:w-1/2">
    //         <div className="bg-gray-100 rounded-lg p-4 flex justify-center items-center h-96">
    //           <img
    //             src={mainImage || ""}
    //             alt="Book Cover"
    //             className="h-full object-contain shadow-lg rounded"
    //           />
    //         </div>

    //         <div className="flex justify-start mt-6 space-x-4">
    //           {BookDetail?.images.map((img, index) => (
    //             <div
    //               key={index}
    //               className={`cursor-pointer rounded-lg overflow-hidden transition-all duration-200 hover:scale-105 ${
    //                 mainImage === img ? " scale-105" : "border-transparent"
    //               }`}
    //               onClick={() => setMainImage(img)}
    //             >
    //               <div className="bg-gray-200 border-2 border-dashed rounded-xl w-16 h-16" />
    //             </div>
    //           ))}
    //         </div>
    //       </div>

    //       {/* Book Details Section */}
    //       <div className="lg:w-1/2">
    //         <div className="flex flex-wrap gap-2 mb-4">
    //           {/* {categories.map((category, index) => (
    //           ))} */}
    //           <span className="bg-indigo-100 text-indigo-800 text-xs font-semibold px-2.5 py-0.5 rounded">
    //             {BookDetail?.categoryId?.name}
    //           </span>
    //         </div>

    //         <h1 className="text-3xl font-semibold text-gray-900 mb-2">
    //           {BookDetail?.title || "Book Title"}
    //         </h1>

    //         <div className="flex items-center gap-4 mb-6">
    //           {/* <div className="flex items-center bg-yellow-100 px-3 py-1 rounded-full">
    //             <span className="text-yellow-700 font-bold">★4.5</span>
    //           </div> */}
    //           {/* <div className="text-gray-700">
    //             <span className="font-bold">549</span> pages
    //           </div> */}
    //           <div className="text-xl global_linear_gradient px-3 py-2 rounded-full font-bold text-white">
    //             J$435
    //           </div>
    //         </div>

    //         <div className=" bg-gray-50 p-4 mb-6">
    //           <div className="">
    //             <div className="mb-2">
    //               <p className="text-sm text-gray-500">Seller</p>
    //               <p className="font-medium line-clamp-1">
    //                 {`${BookDetail?.createdBy?.firstName} ${BookDetail?.createdBy?.lastName}`}
    //                 <span className="bg-yellow-500 text-white text-xs px-1.5 py-0.5 rounded ml-1">
    //                   4.5
    //                 </span>
    //               </p>
    //             </div>
    //             <div className="mb-2">
    //               <p className="text-sm text-gray-500">Date Listed</p>
    //               <p className="font-medium line-clamp-1">
    //                 {BookDetail?.publishedAt}
    //               </p>
    //             </div>
    //             <div className="mb-2">
    //               <p className="text-sm text-gray-500">Location</p>
    //               <p className="font-medium line-clamp-1">
    //                 {BookDetail?.address?.formatted_address}
    //               </p>
    //             </div>
    //             <div className="mb-2">
    //               <p className="text-sm text-gray-500">Condition</p>
    //               <p className="font-medium line-clamp-2">
    //                 {BookDetail?.condition}
    //               </p>
    //             </div>
    //           </div>
    //         </div>

    //         <div className="mb-6">
    //           <h3 className="text-xl font-semibold mb-3">Description</h3>
    //           <p className="text-gray-700 leading-relaxed">
    //             Matt Ridley explores the process of innovation—how new ideas
    //             develop, spread, and impact society. He argues that innovation
    //             thrives in freedom and is driven by trial and error,
    //             collaboration, and incremental progress rather than sudden
    //             breakthroughs. The book examines real-world innovations from
    //             electricity to vaccines, explaining how innovators succeed and
    //             what stops progress.
    //           </p>
    //         </div>
    //       </div>
    //     </div>
    //   </div>
    // </div>

    <div className="bg-white rounded-xl overflow-hidden">
      {/* Main Content */}
      <div className="p-6">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Image Gallery Section */}
          <div className="lg:w-[35%]">
            {/* Main Image Display */}
            <div className="bg-gray-100 rounded-lg p-4 flex justify-center items-center h-108">
              {mainImage ? (
                <img
                  src={mainImage}
                  alt="Book Cover"
                  className="h-full w-full object-contain rounded"
                />
              ) : (
                <div className="bg-gray-200 border-2 border-dashed rounded-xl w-full h-full flex items-center justify-center">
                  <span className="text-gray-500">No Image Available</span>
                </div>
              )}
            </div>

            {/* Thumbnail Gallery */}
            <div className="mt-6">
              <div className="flex flex-wrap gap-3">
                {BookDetail?.images?.map((img, index) => (
                  <div
                    key={index}
                    className={`cursor-pointer rounded-lg overflow-hidden transition-all duration-200 ${
                      mainImage === img
                        ? "ring-2 ring-blue-500 ring-offset-2"
                        : "opacity-80 hover:opacity-100"
                    }`}
                    onClick={() => setMainImage(img)}
                  >
                    <div className="w-16 h-16 bg-gray-200 flex items-center justify-center">
                      <img
                        src={img}
                        alt={`Thumbnail ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Book Details Section */}
          <div className="lg:w-[65%]">
            {/* Category */}
            <div className="mb-4 flex flex-wrap gap-2">
              <span className="bg-indigo-100 text-indigo-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                {BookDetail?.categoryId?.name || "Uncategorized"}
              </span>
              <span className="bg-indigo-100 text-indigo-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                {BookDetail?.subCategoryId?.name || "Uncategorized"}
              </span>
              <span className="bg-indigo-100 text-indigo-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                {BookDetail?.subSubCategoryId?.name || "Uncategorized"}
              </span>
            </div>

            {/* Title */}
            <h1 className="text-4xl font-semibold text-gray-900 mb-2">
              {BookDetail?.title || "Untitled Book"}
            </h1>

            {/* Price */}
            <div className="flex items-center gap-4 mb-6">
              <div className="text-xl global_linear_gradient px-3 py-2 rounded-full font-bold text-white">
                J${BookDetail?.price || "N/A"}
              </div>
            </div>

            {/* Seller Info */}
            <div className="bg-gray-50 p-4 rounded-lg mb-6">
              <div className="">
                <div className="mb-2">
                  {/* <p className="text-sm text-gray-500">Seller</p> */}
                  <h3 className="font-semibold line-clamp-1 text-2xl flex items-center">
                    {BookDetail?.createdBy?.firstName
                      ? `${BookDetail.createdBy.firstName} ${
                          BookDetail.createdBy.lastName || ""
                        }`
                      : "Unknown Seller"}
                    <span className="bg-yellow-500 text-white text-xs px-1.5 py-0.5 rounded ml-1">
                      {BookDetail?.sellerRating || "4.5"}
                    </span>
                  </h3>
                </div>
                <div className="mb-2">
                  <p className="text-sm text-gray-500">Date Listed</p>
                  <p className="font-medium line-clamp-1">
                    {BookDetail?.publishedAt || BookDetail?.createdAt
                      ? new Date(
                          BookDetail?.publishedAt || BookDetail?.createdAt
                        ).toLocaleDateString()
                      : "N/A"}
                  </p>
                </div>
                <ItemRespectiveDetails bookState={BookDetail} />
                <div className="mb-2">
                  <p className="text-sm text-gray-500">Location</p>
                  <p className="font-medium line-clamp-2">
                    {BookDetail?.address?.formatted_address ||
                      "Location not specified"}
                  </p>
                </div>
                {/* <div className="mb-2">
                  <p className="text-sm text-gray-500">Condition</p>
                  <p className="font-medium capitalize line-clamp-1">
                    {BookDetail?.condition?.toLowerCase() || "Unknown"}
                  </p>
                </div> */}
              </div>
            </div>

            {/* Description */}
            <div className="mb-6">
              <h3 className="text-xl font-semibold mb-3">
                Description <div className="" />
              </h3>

              <p className="text-gray-700 leading-relaxed">
                {BookDetail?.description || "No description available."}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookDetailComponent;
