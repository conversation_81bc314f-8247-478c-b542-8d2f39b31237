import React from "react";
import {ItemKindEnum, itemToKind, labelItemToKind} from "../config/constant";
import moment from "moment";

export default function ItemRespectiveDetails({bookState}) {
  // Common styling classes for all items
  const containerClass = "mb-2";
  const labelClass = "text-sm text-gray-500";
  const valueClass = "font-medium line-clamp-1";

  // Helper component for consistent rendering
  const DetailItem = ({label, value}) => (
    <div className={containerClass}>
      <p className={labelClass}>{label}</p>
      <p className={valueClass}>{value}</p>
    </div>
  );

  if (bookState.__t == ItemKindEnum.BookItem) {
    return (
      <div>
        <DetailItem
          label="Author"
          value={bookState?.authors?.map((a) => a).join(", ")}
        />
        <DetailItem label="ISBN" value={bookState?.isbn_number} />
        <DetailItem label="Condition" value={bookState?.condition} />
      </div>
    );
  } else if (bookState.__t == ItemKindEnum.TutorItem) {
    return (
      <div>
        <DetailItem
          label={itemToKind.TutorItem.experience}
          value={bookState?.experience}
        />
        <DetailItem
          label={itemToKind.TutorItem.highestQualification}
          value={bookState?.highestQualification}
        />
        <DetailItem
          label={itemToKind.TutorItem.targetClasses}
          value={bookState?.targetClasses}
        />
      </div>
    );
  } else if (bookState.__t == ItemKindEnum.EventItem) {
    return (
      <div>
        <DetailItem
          label={labelItemToKind.EventItem.eventStartDate}
          value={moment(bookState?.eventStartDate).format("DD-MMM-YYYY HH:MM")}
        />
        <DetailItem
          label={labelItemToKind.EventItem.eventEndDate}
          value={moment(bookState?.eventEndDate).format("DD-MMM-YYYY HH:MM")}
        />
        <DetailItem
          label={labelItemToKind.EventItem.eventMode}
          value={bookState?.eventMode}
        />
        <DetailItem
          label={itemToKind.EventItem.website}
          value={
            <a
              href={bookState?.website}
              className="text-blue-600 hover:underline"
            >
              {bookState?.website}
            </a>
          }
        />
      </div>
    );
  } else if (bookState.__t == ItemKindEnum.SchoolItem) {
    return (
      <div>
        <DetailItem
          label={itemToKind.SchoolItem.classesOffered}
          value={bookState?.classesOffered}
        />
        <DetailItem
          label={itemToKind.SchoolItem.schoolType}
          value={bookState?.schoolType}
        />
        <DetailItem
          label={itemToKind.SchoolItem.website}
          value={
            <a
              href={bookState?.website}
              className="text-blue-600 hover:underline"
            >
              {bookState?.website}
            </a>
          }
        />
      </div>
    );
  } else if (bookState.__t == ItemKindEnum.ScholarshipAwardItem) {
    return (
      <div>
        <DetailItem
          label={itemToKind.ScholarshipAwardItem.eligibilityCriteria}
          value={bookState?.eligibilityCriteria}
        />
        <DetailItem
          label={itemToKind.ScholarshipAwardItem.scholarshipType}
          value={bookState?.scholarshipType}
        />
        <DetailItem
          label={itemToKind.ScholarshipAwardItem.website}
          value={
            <a
              href={bookState?.website}
              className="text-blue-600 hover:underline"
            >
              {bookState?.website}
            </a>
          }
        />
      </div>
    );
  } else if (bookState.__t == ItemKindEnum.ExtracurricularActivityItem) {
    return (
      <div>
        <DetailItem
          label={itemToKind.ExtracurricularActivityItem.activityType}
          value={bookState?.activityType}
        />
        <DetailItem
          label={itemToKind.ExtracurricularActivityItem.frequency}
          value={bookState?.frequency}
        />
        <DetailItem
          label={itemToKind.ExtracurricularActivityItem.targetStudents}
          value={bookState?.targetStudents}
        />
      </div>
    );
  }

  // Return null if no matching type
  return null;
}
