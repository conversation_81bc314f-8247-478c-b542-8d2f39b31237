"use client";

import moment from "moment";
import dynamic from "next/dynamic";
import {useEffect, useState} from "react";

import {USER_ROUTES} from "@/app/config/api";
import {debouncFunc, getToken} from "@/app/utils/utils";
import {toast} from "react-toastify";
import SubmitButton from "../components/common/SubmitButton";
import {deleteItem, getList} from "../service/booklisting";
import Popup from "../components/common/PopUp";
import Image from "next/image";
import MagnifierIcon from "@/public/icons/magnifier_icon.svg";
import Pagination from "../components/common/Pagination";

const TableComponent = dynamic(() =>
  import("@/app/components/common/TableComponent/TableComponent")
);

const BookListing = () => {
  const [openActionDropdown, setOpenActionDropdown] = useState(null);
  const [selectedAll, setSelectedAll] = useState(false);
  const [selectedData, setSelectedData] = useState([]);
  const [isLoading, setisLoading] = useState(false);
  const [submitisLoading, setsubmitisLoading] = useState(false);
  const [deleteBoolean, setdeleteBoolean] = useState(false);
  //Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [declineReason, setdeclineReason] = useState("");
  const [itemToDecline, setitemToDecline] = useState({});
  console.log("totalPages", totalPages);
  console.log("pageSize", pageSize);
  const [deleteObject, setdeleteObject] = useState({
    title: "",
    message: "",
    confirmText: "",
    cancelText: "",
    onConfirm: {},
    onCancel: () => {},
  });
  const [tableDataObj, setTableDataObj] = useState({
    headings: [
      {
        key: "Name Item",
        width: "25",
      },
      {
        key: "Seller",
        width: "15",
      },
      {
        key: "Price",
        width: "15",
      },
      {
        key: "Location",
        width: "25",
      },
      {
        key: "Listed Date",
        width: "15",
      },
      {
        key: "Action",
        width: "15",
        center: true,
      },
      {
        key: "",
        width: "5",
      },
    ],
    content: [
      // {
      //     name_book: {
      //         type: 'profileMix',
      //         image: "",
      //         name: "Can’t Hurt me" || "-",
      //         secondaryValue: "ISBN: 9898763" || '-',
      //         id: "2"
      //     },
      //     seller: {
      //         type: 'default',
      //         value: "Himanshu" || '-',
      //     },
      //     location: {
      //         type: 'withoutLineClamp',
      //         value: "123, Hope Road, kingston 6, St.Andrew, Jamaica" || '-',
      //     },
      //     listed_date: {
      //         type: 'default',
      //         value: `15-03-2025` || '-',
      //     },
      //     actions: {
      //         type: 'actionMultiButtons',
      //         buttons: [
      //             {
      //                 type: 'action_btns',
      //                 btn1: "Approved",
      //                 pendingBtn1: "Decline",
      //                 pendingBtn2: "Approve",
      //                 class1: "text-xs leading-normal py-1.5 px-6 text-white border border-[#211F54] rounded-full global_text_linear_gradient",
      //                 pendingClass1: "text-xs leading-normal py-1.5 px-2.5 text-white bg-[linear-gradient(90deg,#E1020C_0%,#4D7906_100%)] rounded-full",
      //                 pendingClass2: "text-xs leading-normal py-1.5 px-2.5 text-white rounded-full global_linear_gradient",
      //                 onClick1: () => console.log('Primary action clicked'),
      //                 onClick2: () => console.log('Secondary action clicked'),
      //             },
      //         ],
      //         value: "approved",
      //         class: '',
      //     },
      //     action: {
      //         type: 'dropdownElem',
      //         id: "2",
      //         dropdownHandlerFn: dropdownHandler,
      //         dropdownList: [
      //             {
      //                 label: "Option1"
      //             },
      //             {
      //                 label: "Option2"
      //             },
      //         ]
      //     },
      // },
    ],
  });

  console.log("book table", tableDataObj);
  console.log("isLoading", isLoading);
  console.log("declineReason", declineReason);
  const updateItemStatus = (item, status) => {
    try {
      let payload = {
        status: status,
      };
      if (status == "rejected") {
        payload.reason = declineReason;
      }
      console.log("item is ", item);

      let userToken = getToken();
      fetch(USER_ROUTES.UPDATE_LIST_ITEM + "/" + item?._id, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userToken}`,
        },
        body: JSON.stringify(payload),
      }).then(async (res) => {
        const response = await res.json();
        let bookData = response.data;
        if (!bookData?.error) {
          toast.success("Updated successfully!");
          getListing();
        } else {
          console.log("bookData err", bookData);
          toast.error(response?.message || "Internal Server Error");
        }
        setdeclineReason("");
        setitemToDecline({});
        document.getElementById("myModal").classList.add("hidden");
      });

      setOpenActionDropdown(null);
    } catch (error) {
      console.log("error", error);
    }
  };

  console.log(deleteObject, "deleteObject");

  const deleteItemSoft = async (item) => {
    try {
      setdeleteBoolean(true);
      setdeleteObject({
        title: "Remove Item",
        confirmText: "Remove",
        cancelText: "Cancel",
        message: "Are You sure want to remove it?",
        onConfirm: async () => {
          setisLoading(true);
          let response = await deleteItem(`/${item?._id}`);
          if (response.status == 200) {
            toast.success("Removed successfully!");
            getListing();
            setdeleteBoolean(false);
          }
          setisLoading(false);
        },
        onCancel: () => setdeleteBoolean(false),
      });
    } catch (error) {
      console.log("error", error);
    }
  };

  const copyFunction = () => {};

  const deleteItemPermanent = async (item) => {
    try {
      setdeleteBoolean(true);
      setdeleteObject({
        title: "Delete Item Permanently",
        confirmText: "Delete Permanently",
        cancelText: "Cancel",
        message: "Are You sure want to Delete it Permanently it?",
        onConfirm: async () => {
          setisLoading(true);
          let response = await deleteItem(`/${item?._id}` + "?hard=true");
          if (response.status == 200) {
            toast.success("Deleted successfully!");
            getListing();
            setdeleteBoolean(false);
          }
          setisLoading(false);
        },
        onCancel: () => setdeleteBoolean(false),
      });

      // let response = await deleteItem(`/${item?._id}` + "?hard=true")
      // if (response.status == 200) {
      //     toast.success("Deleted successfully!")
      //     getListing()
      // }
    } catch (error) {
      console.log("error", error);
    }
  };

  const getListing = async (search, page = currentPage) => {
    try {
      let userToken = getToken();
      setisLoading(true);
      let payload = {
        filters: {},
        sort: {createdAt: -1},
      };

      // Build query parameters
      const queryParams = new URLSearchParams({
        page: page,
        pageSize: pageSize,
        // sort: JSON.stringify({createdAt: -1}),
      });

      // Construct the URL with query parameters
      const url = `${USER_ROUTES.LIST_ITEM}?${queryParams.toString()}`;

      // Make GET request with query parameters
      //   const response = await fetch(url, {
      //     method: "GET",
      //     headers: {
      //       "Content-Type": "application/json",
      //       Authorization: `Bearer ${getToken()}`,
      //     },
      //   });

      if (search) {
        payload.filters = {keyword: search};
        payload.sort = {};
      }
      let query = "?";
      if (currentPage) {
        query = query + `&page=${currentPage}`;
      }
      if (pageSize) {
        query = query + `&pageSize=${pageSize}`;
      }
      let bookData = await getList(query, payload);
      bookData = bookData?.data;
      console.log("bookData", bookData);

      setTotalItems(bookData?.totalCount || 0);
      setTotalPages(bookData?.totalPages || 1);
      //   setCurrentPage(bookData?.page || 1);

      setTableDataObj((prev) => ({
        ...prev,
        content: bookData.data?.map((book) => {
          const approvedClass = "bg-green-200 font-semibold text-green-600";
          const rejectedClass = "bg-red-200 font-semibold text-red-600 ";
          const pendingClass = "global_text_linear_gradient";
          return {
            name_book: {
              type: "profileMix",
              image: book.images.length ? book.images[0] : "",
              name: book?.title,
              secondaryValue: book?.categoryDoc?.name,
              // secondaryValue: book?.price,
              id: book._id,
            },
            seller: {
              type: "default",
              value:
                `${book?.createdByDoc?.firstName} ${book?.createdByDoc?.lastName}` ||
                "-",
            },
            price: {
              type: "default",
              value: "J$" + book?.price || "-",
            },
            location: {
              type: "withoutLineClamp",
              value: book.address?.formatted_address || "-",
            },
            listed_date: {
              type: "default",
              value: moment(bookData?.createdAt).format("DD-MM-YYYY") || "-",
            },
            actions: {
              type: "actionMultiButtons",
              buttons: [
                {
                  type: "action_btns",
                  btn1: book?.status,
                  pendingBtn1: "Decline",
                  pendingBtn2: "Approve",
                  class1:
                    book?.status === "accepted"
                      ? `text-xs py-1.5 px-6  rounded-full ${approvedClass}`
                      : book?.status === "removed by admin"
                      ? `text-xs py-1.5 px-6  rounded-full ${rejectedClass}`
                      : `text-xs py-1.5 px-6  border border-[#211F54] rounded-full ${pendingClass}`,
                  pendingClass1:
                    "text-xs leading-normal py-1.5 px-2.5 text-white bg-[linear-gradient(90deg,#E1020C_0%,#4D7906_100%)] rounded-full",
                  pendingClass2:
                    "text-xs leading-normal py-1.5 px-2.5 text-white rounded-full global_linear_gradient",
                  onClick1:
                    book?.status === "pending"
                      ? () => openModelForDeclineReason(book, "rejected")
                      : undefined,
                  onClick2:
                    book?.status === "pending"
                      ? () => updateItemStatus(book, "accepted")
                      : undefined,
                },
              ],
              value: book.status,
              class: "",
            },
            action: {
              type: "dropdownElem",
              id: book._id,
              dropdownHandlerFn: dropdownHandler,
              dropdownList: [
                {
                  label: "Delete",
                  clickFn: () => deleteItemSoft(book),
                },
                {
                  label: "Delete Permanently",
                  clickFn: () => deleteItemPermanent(book),
                },
              ],
            },
          };
        }),
      }));

      setisLoading(false);
    } catch (error) {
      setisLoading(false);
      console.log("error", error);
    }
  };
  useEffect(() => {
    getListing();
  }, [currentPage, pageSize]);

  // Handle page size change
  const handlePageChange = (number) => {
    setCurrentPage(number);
  };
  const handlePageSizeChange = (e) => {
    const newValue = parseInt(e.target.value);
    setPageSize(newValue);
    setCurrentPage(1);
    getListing(undefined, 1);
  };

  console.log("pageSize", pageSize);
  //   // Handle items per page change
  //   const handleItemsPerPageChange = (e) => {
  //     const newValue = parseInt(e.target.value);
  //     setItemsPerPage(newValue);
  //     setCurrentPage(1);
  //     getListing(undefined, 1);
  //   };

  function dropdownHandler(id) {
    setOpenActionDropdown((prevId) => (prevId === id ? null : id));
  }

  function closeDropdown() {
    setOpenActionDropdown(null);
  }

  const selectAllHandler = () => {
    setSelectedAll(!selectedAll);
    if (selectedAll) {
      setSelectedData([]);
    } else {
      // Todo - apiData is used when api is added
      // setSelectedData(tableDataObj.content?.map((item) => item.id));
    }
  };

  // console.log("selectedData", typeof selectedData)

  const singleSelectHandler = (itemId) => {
    setSelectedData((prev) => {
      const currentSelection = Array.isArray(prev)
        ? prev
        : [prev].filter(Boolean);

      if (currentSelection.includes(itemId)) {
        return currentSelection.filter((id) => id !== itemId);
      } else {
        return [...currentSelection, itemId];
      }
    });
  };
  const openModelForDeclineReason = (item, status) => {
    if (status == "rejected") {
      setitemToDecline(item);
      document.getElementById("myModal").classList.remove("hidden");
    } else {
      // updateItemStatus(item, status)
    }
  };
  const InnerDiv = () => {
    return <div> Decline</div>;
  };
  const submitQuestion = () => {
    setsubmitisLoading(true);
    updateItemStatus(itemToDecline, "rejected");
    setTimeout(() => {
      setsubmitisLoading(false);
    }, 1000);
  };
  const searchHandle = (event) => {
    console.log(event.target.value, "debounce event");
    let payload = {
      searchTerm: event.target.value || "",
    };
    getListing(event.target.value || "");
  };
  let debounceHandle = debouncFunc(searchHandle, 1000);

  return (
    <div className="bg-white rounded-2xl p-5">
      <div className="flex  items-center justify-between mb-2">
        <p className="font-semibold text-[20px]">Item Listing</p>
        <div className="bg-gray-50 px-2 rounded-lg flex items-center">
          <div className="w-6 h-6  relative overflow-hidden">
            <Image
              src={MagnifierIcon}
              alt="Search Icon"
              fill
              className="object-cover"
              sizes="24px"
            />
          </div>
          <input
            placeholder="Search Items..."
            className="rounded-lg outline-none bg-gray-50 ps-2 p-2"
            onChange={debounceHandle}
          />
        </div>
      </div>
      <TableComponent
        tableDataObj={tableDataObj}
        loading={isLoading}
        selectAllHandler={selectAllHandler}
        selectedAll={selectedAll}
        selectedData={selectedData}
        singleSelectHandler={singleSelectHandler}
        openActionDropdown={openActionDropdown}
        closeDropdown={closeDropdown}
      />

      {/* +++++++++++++++ PAGINATION ++++++++++++++++++++++++++ */}
      <Pagination
        setPageSize={setPageSize}
        setCurrentPage={setCurrentPage}
        getListing={getListing}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalItems}
        pageSize={pageSize}
      />
      {/* +++++++++++++++ PAGINATION ++++++++++++++++++++++++++ */}
      <div
        id="myModal"
        className=" text-[black] fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden bg-[#EAEAEA]"
      >
        <div className="bg-[#EAEAEA]  rounded-lg w-full max-w-lg shadow-lg relative">
          <div
            className="flex bg-white w-[30px] cursor-pointer  h-[30px] justify-center absolute top-[-10px] right-[-10px] rounded-full  "
            onClick={() => {
              let docElement = document
                .getElementById("myModal")
                .classList.add("hidden");
              console.log("docElement", docElement);
              // setaddQuestionInput("")
              // setmodelForAnswer(false)
            }}
          >
            <button className="text-gray-500 hover:text-red-600 text-xl font-bold">
              &times;
            </button>
          </div>

          <div className="py-3 bg-white rounded-lg ">
            <h2 className="text-xl font-semibold mb-4  border-b w-fit mx-auto">
              {"Enter Reason"}
            </h2>
          </div>

          <div className="px-3 mt-2">
            <textarea
              className="rounded-lg outline-none w-full border border-gray-400 p-2"
              rows={4}
              onChange={(e) => setdeclineReason(e.target.value)}
            />
          </div>
          {/* <!-- Action Button --> */}
          <div class="my-2 flex justify-start mx-4">
            {/* <div className='flex gap-3.5 mt-3 items-center justify-center md:flex-col md:justify-center md:h-full md:w-fit md:items-start md:gap-2.5'>
                                        <button className='py-[7.5px] px-[30px] text-[13px] font-semibold leading-[18px] text-center text-white rounded-full global_linear_gradient md:order-2 md:text-base md:leading-[22px]'
                                            onClick={submitQuestion}
                                        >Submit</button>
            
                                    </div> */}
            <div className="max-w-[300px] ">
              <SubmitButton
                isLoading={submitisLoading}
                InnerDiv={InnerDiv}
                type={"button"}
                btnAction={submitQuestion}
              />
            </div>
          </div>
        </div>
      </div>

      {/* <div className="mb-[40px] h-[100px]"></div> */}
      <Popup
        isOpen={deleteBoolean}
        actionLoading={isLoading}
        {...deleteObject}
      />
    </div>
  );
};

export default BookListing;
