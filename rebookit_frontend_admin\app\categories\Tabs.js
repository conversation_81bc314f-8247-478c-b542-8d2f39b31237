"use client";

import { useDispatch, useSelector } from "react-redux";
import MembershipScss from "./categories.module.scss";
import {
  changeCategoryState,
  changeCategoryTab,
  changeMemberShipTab,
} from "../redux/slices/storeSlice";
import { useRouter } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { getCategories } from "../service/category";
import { useSearchParams } from "next/navigation";
import bookIcon from "@/public/landing/bookCategory.png";

export default function Tabs() {
  const searchParams = useSearchParams();
  const [isLoading, setisLoading] = useState(false)
  const dispatch = useDispatch();
  const router = useRouter();
  const [categories, setcategories] = useState([]);
  // const [isLoading, setisLoading] = useState(false);

  const storeData = useSelector((state) => state.storeData);

  // Add this to preserve category ID in URL
  const getTabUrl = (key) => {
    const categoryId = searchParams.get("categoryId");
    return categoryId
      ? `/categories/${key}?categoryId=${categoryId}`
      : `/categories/${key}`;
  };

  const routeMap = {
    // category: "Category",
    subcategory: "SubCategory",
    sub_subcategory: "SubSubCategory",
    sub_sub_subcategory: "SubSubSubCategory",
  };
  console.log("storeData", storeData);

  console.log("location", window.location.pathname.split("/"));
  let allnestPast = window.location.pathname.split("/");
  const tabs = Object.entries(routeMap);
  console.log("tabs", tabs);
  const activeIndex = storeData.categoryTab;
  console.log("activeIndex", activeIndex);
  let pathName = allnestPast[allnestPast.length];

  const getCategoriesFunc = useCallback(async () => {
    setisLoading(true)
    let response = await getCategories();
    console.log("response getCategories", response);
    if (response.status == 200) {
      setcategories(response.data.categories);
    }
    setisLoading(false)
  }, [])
  useEffect(() => {
    getCategoriesFunc();
  }, []);

  return (
    <>
      <div>
        {/* <div className={`w-[200px] h-[250px] rounded-md ${MembershipScss['skeleton-shimmer']}`}></div> */}
        <h1 className="text-xl font-semibold mb-5">Select Main Category</h1>
        <div className="flex mb-6  gap-5 min-h-[200px]">
          {!isLoading ? categories?.map((item, index) => {
            return (
              <div
                className={`cursor-pointer flex flex-col px-[35px] py-[15px] rounded-lg gap-4 w-[180px] min-h-[140px]  border ${storeData.categoriesState?.categoryId == item._id ? "border-gray-500" : "border-gray-200"}    items-center justify-start`}
                key={index}
                onClick={() => {
                  // router.push(`/categories/subcategory?categoryId=${item._id}`)
                  dispatch(changeCategoryState(item._id))
                }
                }
              >
                <div className="bg-gray-200 rounded-full w-[100px] p-5 h-[100px]">
                  <img
                    // src={item.image}
                    src={bookIcon.src}
                    alt="categories_image"
                    className="rounded-full w-full h-full object-cover"
                  />
                </div>
                <p className="w-[90%] text-center break-after-all">
                  {item.name}
                </p>
              </div>
            );
          }) :
            <div className="flex gap-3">
              <div className={`w-[200px] h-[200px] rounded-md ${MembershipScss['skeleton-shimmer']}`}></div>
              <div className={`w-[200px] h-[200px] rounded-md ${MembershipScss['skeleton-shimmer']}`}></div>

            </div>
          }
        </div>
      </div>
      <div
        className={`bg-white min-h-full h-full rounded-2xl p-5 ${MembershipScss.membershipContainer}`}
      >
        <div className="relative flex justify-around items-center py-2 px-3 rounded-[5px] border border-[#F3F3F3] gap-2 h-[64px] overflow-hidden">
          {/* Animated background for active tab */}
          <div
            className="absolute h-[36px] rounded-[8px] bg-gradient-to-r from-[#0161AB] to-[#211F54] transition-all duration-400 ease-in-out"
            style={{
              width: `calc(100% / ${tabs.length})`,
              left: `calc(${activeIndex} * 100% / ${tabs.length})`,
              top: "50%",
              transform: "translateY(-50%)",
              zIndex: 10,
            }}
          />

          {/* Tabs */}
          {tabs.map(([key, label], index) => (
            <span
              key={key}
              className={`py-1  px-1.5 text-sm leading-[29px] text-center w-1/1 cursor-pointer z-20 transition-colors duration-300 ${activeIndex === index
                ? "text-white font-semibold"
                : "text-[#444]"
                }`}
              onClick={() => {
                dispatch(changeCategoryTab(index));
                router.push(`/categories/${key}`);
              }}
            >
              {label}
            </span>
          ))}
        </div>
      </div>
    </>
  );
}
