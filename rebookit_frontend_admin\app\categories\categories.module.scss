.membershipContainer {

    .gradientAllRoundBorder {
        position: relative;
        background: linear-gradient(white, white) padding-box,
            linear-gradient(268deg, #211F54 11.09%, #0161AB 98.55%) border-box;
        border: 1px solid transparent;
    }
}


@keyframes shimmer {
  0% {
    background-position: -400px 0;
  }
  100% {
    background-position: 400px 0;
  }
}

.skeleton-shimmer {
  background: linear-gradient(
    to right,
    #e0e0e0 0%,
    #f8f8f8 50%,
    #e0e0e0 100%
  );
  background-size: 800px 100%;
  animation: shimmer 1.2s infinite;
}
