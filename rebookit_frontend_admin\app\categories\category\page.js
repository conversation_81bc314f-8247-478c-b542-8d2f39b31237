"use client";
import React, {useEffect, useRef, useState} from "react";
import Tabs from "../Tabs";
import {BsThreeDotsVertical} from "react-icons/bs";
import {RiFileUploadLine} from "react-icons/ri";
import SubmitButton from "@/app/components/common/SubmitButton";
import {addCategory, getCategories, uploadFile} from "@/app/service/category";
import moment from "moment";

export default function CategoryPage() {
  const [categories, setcategories] = useState([]);
  const [isLoading, setisLoading] = useState(false);
  const [submitisLoading, setsubmitisLoading] = useState(false);
  const [selectedImage, setselectedImage] = useState(null);
  const [nameOfCategory, setnameOfCategory] = useState("");
  const fileInputRef = useRef(null);
  const [selectedImageOnUpload, setselectedImageOnUpload] = useState(null);

  const handleButtonClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setselectedImageOnUpload(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setTimeout(() => {
          setselectedImage(reader.result); // base64 image string
        }, 500);
      };
      reader.readAsDataURL(file);
      console.log("Selected file:", file.name);
    }
  };

  const InnerDiv = () => {
    return <div>Submit</div>;
  };

  const submitQuestion = async () => {
    setsubmitisLoading(true);
    const formData = new FormData();
    formData.append("file", selectedImageOnUpload);
    let file = await uploadFile(formData);
    console.log("file", file);
    if (file.status == 200) {
      let payload = {
        name: nameOfCategory,
        image: file.data.url,
      };
      let category = await addCategory(payload);
      console.log("category", category);
      if (category.status == 200) {
      }
    }
    setsubmitisLoading(false);
  };
  const getCategoriesFunc = async () => {
    let response = await getCategories();
    console.log("response getCategories", response);
    if (response.status == 200) {
      setcategories(response.data.categories);
    }
  };
  useEffect(() => {
    getCategoriesFunc();
  }, []);

  return (
    <div>
      <Tabs />

      <div className="flex justify-end my-4">
        <button
          className="bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white px-5 py-2 rounded-full"
          onClick={() =>
            document.getElementById("myModal").classList.remove("hidden")
          }
        >
          Create New Category
        </button>
      </div>

      <table className=" w-full border border-[#EFF1F4] rounded-lg border-separate">
        <thead className=" border-b border-[#EFF1F4]">
          <th className="px-[10px] text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB] flex items-center">
            <span>Name </span>
          </th>
          <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB] ">
            Created On
          </th>
          <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]">
            No of Sub Category{" "}
          </th>
          <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]">
            {" "}
            Action
          </th>
        </thead>
        <tbody className=" w-full">
          {!isLoading ? (
            categories?.map((item, index) => {
              return (
                <tr className="py-[10px]  bg-white px-2">
                  <td className="border-b border-[#EFF1F4]  flex px-[10px] py-[10px] bg-white">
                    {" "}
                    <div className="flex items-center">
                      <img
                        className="ml-2 w-[40px] h-[40px] rounded-full"
                        src={
                          item.image ||
                          "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/952747fd-94f3-4f7f-ba93-eaf188f302aa.png"
                        }
                      />{" "}
                    </div>
                    <div className="flex items-center pl-2">
                      {" "}
                      <p className="font-medium text-[14px]"> {item.name}</p>
                    </div>{" "}
                  </td>
                  <td className="bg-white border-b border-[#EFF1F4] ">
                    {moment(item?.createdAt).format("DD-MM-YYYY")}
                  </td>
                  <td className="bg-white border-b border-[#EFF1F4] ">{3}</td>
                  <td className="bg-white border-b border-[#EFF1F4] ">
                    <BsThreeDotsVertical />
                  </td>
                </tr>
              );
            })
          ) : (
            <tr>
              <td
                colSpan={5}
                className="border-b border-[#EFF1F4]  px-[10px] py-[10px] bg-white text-[16px] text-center "
              >
                ...Loading
              </td>
            </tr>
          )}
        </tbody>
      </table>

      <div
        id="myModal"
        className=" text-[black] fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden bg-[#EAEAEA]"
      >
        <div className="bg-[#EAEAEA]  rounded-lg w-full max-w-lg shadow-lg relative">
          <div
            className="flex bg-white w-[30px] cursor-pointer  h-[30px] justify-center absolute top-[-10px] right-[-10px] rounded-full  "
            onClick={() => {
              let docElement = document
                .getElementById("myModal")
                .classList.add("hidden");
              console.log("docElement", docElement);
              // setaddQuestionInput("")
              // setmodelForAnswer(false)
            }}
          >
            <button className="text-gray-500 hover:text-red-600 text-xl font-bold">
              &times;
            </button>
          </div>

          <div className="py-3 bg-white rounded-lg ">
            <h2 className="text-xl font-semibold mb-4  border-b w-fit mx-auto">
              {"Create Category"}
            </h2>
          </div>

          <div className="px-4 mt-4">
            <label>Name</label>
            <input
              placeholder="Add Name"
              onChange={(e) => setnameOfCategory(e.target.value)}
              className="px-2 py-2 rounded-md w-full border-[1px] border-[gray] outline-none"
            />

            <input
              ref={fileInputRef}
              type="file"
              className="hidden"
              onChange={handleFileChange}
            />
            <div className="mt-2">
              <button
                onClick={handleButtonClick}
                className="py-3 px-2 flex items-center bg-[#f4f4f4] text-[#8C8C8C] w-full"
              >
                {" "}
                <RiFileUploadLine />
                Upload Image
              </button>
            </div>
            {selectedImage && (
              <div className="my-3 relative w-fit">
                <button
                  onClick={() => setselectedImage(null)}
                  className="absolute bg-[white] top-[-10px] rounded-full w-[20px] h-[20px] right-0 z-[100] text-gray-500 hover:text-red-600 text-xl font-bold"
                >
                  &times;
                </button>

                <img src={selectedImage} className="h-[100px]" alt="image" />
              </div>
            )}
          </div>
          {/* <!-- Action Button --> */}
          <div class="my-2 flex justify-start mx-4">
            {/* <div className='flex gap-3.5 mt-3 items-center justify-center md:flex-col md:justify-center md:h-full md:w-fit md:items-start md:gap-2.5'>
                            <button className='py-[7.5px] px-[30px] text-[13px] font-semibold leading-[18px] text-center text-white rounded-full global_linear_gradient md:order-2 md:text-base md:leading-[22px]'
                                onClick={submitQuestion}
                            >Submit</button>

                        </div> */}
            <div className="max-w-[300px] ">
              <SubmitButton
                isLoading={submitisLoading}
                InnerDiv={InnerDiv}
                type={"button"}
                btnAction={submitQuestion}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
