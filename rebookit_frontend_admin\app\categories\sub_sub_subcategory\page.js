"use client";
import React, { useEffect, useRef, useState } from "react";
import Tabs from "../Tabs";
import {
  addSubSubSubCategory,
  getCategories,
  getSubCategories,
  getSubSubCategories,
  getSubSubSubCategories,
  uploadFile,
} from "@/app/service/category";
import { RiFileUploadLine } from "react-icons/ri";
import SubmitButton from "@/app/components/common/SubmitButton";
import { useSelector } from "react-redux";
import { changeCategoryState } from "@/app/redux/slices/storeSlice";
import { toast } from "react-toastify";

const SubSubSubCategoryPage = () => {
  const storeData = useSelector(state => state.storeData)
  const [subSubSubCategories, setSubSubSubCategories] = useState([]);
  const [subSubCategories, setSubSubCategories] = useState([]);
  const [selectedCategory, setselectedCategory] = useState(storeData.categoriesState.categoryId || "")
  const [subCategories, setsubCategories] = useState([])
  const [categories, setcategories] = useState([])
  const [selectedSubCategory, setselectedSubCategory] = useState([])
  const [selectedSubSubCategory, setSelectedSubSubCategory] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [submitIsLoading, setSubmitIsLoading] = useState(false);
  const fileInputRef = useRef(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [enteredName, setEnteredName] = useState("");
  const [selectedImageOnUpload, setSelectedImageOnUpload] = useState(null);

  // const fetchSubSubCategories = async () => {
  //   try {
  //     const response = await getSubSubCategories();
  //     if (response.status === 200) {
  //       setSubSubCategories(response.data.subSubCategories || []);

  //       if (
  //         response.data.subSubCategories.length > 0 &&
  //         !selectedSubSubCategory
  //       ) {
  //         setSelectedSubSubCategory(response.data.subSubCategories[0]._id);
  //       }
  //     }
  //   } catch (error) {
  //     console.error("Error fetching sub-sub-categories:", error);
  //     toast.error("Failed to load sub-sub-categories");
  //   }
  // };

  const fetchSubCategories = async (id) => {

    let response = await getSubCategories(id)
    if (response.status == 200) {
      response.data.subCategories
      setsubCategories(response.data.subCategories)
    }

  }

  const fetchSubSubCategories = async (id) => {

    try {
      let response = await getSubSubCategories(id)
      if (response.status == 200) {
        setSubSubCategories(response.data.subSubCategories)
      }
    } catch (err) {
      console.log("err", err)
    }
  }
  useEffect(() => {
    if (selectedCategory.length) {
      fetchSubCategories(selectedCategory)
    }
  }, [selectedCategory])

  useEffect(() => {
    if (selectedSubCategory.length) {

      fetchSubSubCategories(selectedSubCategory)
    }
  }, [selectedSubCategory])
  console.log("category", categories)
  console.log("subCategory", subCategories)
  console.log("subSubCategory", subSubCategories)
  console.log("subSubSubCategory", subSubSubCategories)
  console.log("selectedCatrgory", selectedCategory)
  console.log("selectedSubCategory", selectedSubCategory)
  console.log("selectedSubCategory", selectedSubCategory)






  const fetchAllData = async (id) => {
    setIsLoading(true);

    try {
      // Fetch all categories
      const categoriesRes = await getSubSubSubCategories(id);
      if (categoriesRes.status !== 200) return;
      const categories = categoriesRes.data?.subSubSubCategories;
      setSubSubSubCategories(categories)
      // Process all levels
      // setSubSubSubCategories(allData.flat());
    } catch (error) {
      console.error("Error fetching data:", error);
    } finally {
      setIsLoading(false);
    }
  };

  console.log("subSubSubCategories", subSubSubCategories);

  const submitSubSubSubCategory = async () => {
    setSubmitIsLoading(true);
    try {
      // Upload image first if exists
      let imageUrl = "";
      if (selectedImageOnUpload) {
        const formData = new FormData();
        formData.append("file", selectedImageOnUpload);
        const fileResponse = await uploadFile(formData);
        imageUrl = fileResponse.data.url;
      }
      
      // Create payload
      const payload = {
        name: enteredName,
        image: imageUrl,
        subSubCategoryId: selectedSubSubCategory,
      };

      // Add sub-sub-sub-category
      const response = await addSubSubSubCategory(payload);

      if (response.status === 200) {
        toast.success("Sub-Sub-Sub-Category Added Successfully");
        // fetchAllData(); // Refresh data

        // Close modal and reset form
        document.getElementById("subSubSubModal").classList.add("hidden");
        setEnteredName("");
        setSelectedImage(null);
        setSelectedImageOnUpload(null);
        fetchAllData(response.data.subSubCategoryId)
      } else {
        toast.error("Failed to add sub-sub-sub-category");
      }
    } catch (error) {
      toast.error("Error adding sub-sub-sub-category");
      console.error("Error adding sub-sub-sub-category:", error);
    } finally {
      setSubmitIsLoading(false);
    }
  };

  const InnerDiv = () => {
    return <div className="px-4">Submit</div>;
  };

  const handleButtonClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedImageOnUpload(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setSelectedImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  useEffect(() => {
    // fetchSubSubCategories();
    // fetchAllData();
  }, []);


  const getCategoriesFunc = async () => {
    setIsLoading(true);
    let response = await getCategories();
    console.log("response getCategories", response);

    if (response.status == 200) {
      // setcategories(response.data.categories)
      let list = response.data?.categories;
      setcategories(list);
      // fetchSubCategory(list[0]._id)
      // setselectedSubCategory(list[0]._id)

      if (list.length > 0) {

        // dispatch(changeCategoryState(list[0]._id))
        // getSubCategory(list[0]._id);
      }
    }
    // setIsLoading(false);
  };

  const fetchSubSubCategory = async (id) => {

    try {
      // setIsLoading(true)

      let response = await getSubSubCategories(id)
      if (response.status == 200) {
        let list = response.data.subSubCategories || []
        setSubSubCategories(list)
        if (list.length) {
          fetchAllData(list[0]._id)
          setSelectedSubSubCategory(list[0]._id)
        }
        // setSubSubSubCategories(response.data.subSubSubCategories)

      }
      setIsLoading(false)
    } catch (err) {
      setIsLoading(false)
    }
  }
  console.log(subSubCategories, "subSubCategories")
  const fetchSubCategory = async (id) => {
    try {
      setIsLoading(true)
      let response = await getSubCategories(id)
      if (response.status == 200) {

        // response.data.categories    
        let list = response.data?.subCategories || []
        setsubCategories(list)
        if (list.length > 0) {
          setselectedSubCategory(list[0]._id)
          // fetchSubSubCategory(list[0]._id)
        }
        setIsLoading(true)
      }
    } catch (err) {
      console.log("Error in fetchin")
    }
  }
  useEffect(() => {
    getCategoriesFunc()
  }, [storeData.categoriesState.categoryId])

  useEffect(() => {
    if (storeData.categoriesState.categoryId) {
      fetchSubCategory(storeData.categoriesState.categoryId)
    }
  }, [storeData.categoriesState.categoryId])

  useEffect(() => {
    if (selectedSubCategory.length) {

      fetchSubSubCategory(selectedSubCategory)
    }
  }, [selectedSubCategory])


  console.log("selectedCategory", selectedCategory)
  console.log("selectedSubSubCategory", selectedSubSubCategory)
  return (
    <div>
      <Tabs />
      <div className="flex justify-between my-4">
        <div className="">
          {!isLoading?<div className="min-h-[50px]">
            <select value={storeData.categoriesState.categoryId} className="ml-2 border rounded w-[200px] text-gray border-[gray] px-2 py-1" onChange={(e) => {
              dispatch(changeCategoryState(e.target.value))
              // setselectedCategory(e.target.value)
            }}>
              {categories?.map(item => {
                return <option value={item._id}>{item.name}</option>
              })}
            </select>
            <select className="ml-2 border rounded w-[200px] text-gray border-[gray] px-2 py-1" onChange={(e) => setselectedSubCategory(e.target.value)}>
              {subCategories?.map(item => {
                return <option value={item._id}>{item.name}</option>
              })}
            </select>
            {subSubCategories.length > 0 && <select className="ml-2 border rounded w-[200px] text-gray border-[gray] px-2 py-1" onChange={(e) => setSelectedSubSubCategory(e.target.value)}>
              {subSubCategories?.map(item => {
                return <option value={item._id}>{item.name}</option>
              })}
            </select>}
          </div>:<div>...Loading</div>}
        </div>
        <button
          onClick={() =>
            document.getElementById("subSubSubModal").classList.remove("hidden")
          }
          className="bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white px-5 py-2 rounded-full"
        >
          Create New Sub-Sub-Sub-Category
        </button>
      </div>

      <table className="w-full border border-[#EFF1F4] rounded-lg border-separate">
        <thead className="border-b border-[#EFF1F4]">
          <tr>
            <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]">
              Name
            </th>

            <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]">
              Created On
            </th>
            {/* <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]">
              Action
            </th> */}
          </tr>
        </thead>
        <tbody className="w-full">
          {isLoading ? (
            <tr>
              <td colSpan={6} className="text-center py-4">
                Loading...
              </td>
            </tr>
          ) : subSubSubCategories?.length === 0 ? (
            <tr>
              <td colSpan={6} className="text-center py-4">
                No data found
              </td>
            </tr>
          ) : (
            subSubSubCategories.map((item) => (
              <tr key={item._id} className="py-[10px] bg-white px-2">
                <td className="border-b border-[#EFF1F4] px-[10px] py-[10px] bg-white flex items-center">
                   <div>
                      <img
                        className="ml-2 w-[40px] h-[40px] rounded-full"
                        src={
                          item.image ||
                          "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/952747fd-94f3-4f7f-ba93-eaf188f302aa.png"
                        }
                      />{" "}
                    </div>
                   <div className="ml-2"> 
                  {item?.name}
                  </div>
                </td>
                {/* <td className="bg-white border-b border-[#EFF1F4]">
                  {item.categoryName}
                </td>
                <td className="bg-white border-b border-[#EFF1F4]">
                  {item.subCategoryName}
                </td>
                <td className="bg-white border-b border-[#EFF1F4]">
                  {item.subSubCategoryName}
                </td> */}
                <td className="bg-white border-b border-[#EFF1F4]">
                  {new Date(item.createdAt).toLocaleDateString()}
                </td>
                <td className="bg-white border-b border-[#EFF1F4]">
                  {/* Action buttons or icons here */}
                </td>
              </tr>
            ))

          )}
        </tbody>
      </table>

      {/* Modal for creating new sub-sub-sub-category */}
      <div
        id="subSubSubModal"
        className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden"
      >
        <div className="bg-white rounded-lg w-full max-w-lg shadow-lg relative p-6">
          <div
            className="absolute top-4 right-4 cursor-pointer"
            onClick={() => {
              document.getElementById("subSubSubModal").classList.add("hidden");
              setEnteredName("");
              setSelectedImage(null);
              setSelectedImageOnUpload(null);
            }}
          >
            <button className="text-gray-500 hover:text-red-600 text-2xl">
              &times;
            </button>
          </div>

          <h2 className="text-xl font-semibold mb-4 text-center border-b pb-2">
            Create Sub-Sub-Sub-Category
          </h2>

          <div className="space-y-4">
            <div>
              <label className="block mb-1">Name</label>
              <input
                placeholder="Enter name"
                type="text"
                value={enteredName}
                onChange={(e) => setEnteredName(e.target.value)}
                className="w-full px-3 py-2 border rounded-md"
              />
            </div>

            <div>
              <label className="block mb-1">Category</label>
              <select
                value={storeData.categoriesState.categoryId || selectedCategory}
                onChange={(e) => setselectedCategory(e.target.value)}
                className="w-full px-3 py-2 border rounded-md"
              >
                {categories.map((item) => (
                  <option key={item._id} value={item._id}>
                    {item.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block mb-1">Sub-Category</label>
              <select
                value={selectedSubSubCategory || ""}
                onChange={(e) => setselectedSubCategory(e.target.value)}
                className="w-full px-3 py-2 border rounded-md"
              >
                {subCategories.map((item) => (
                  <option key={item._id} value={item._id}>
                    {item.name}
                  </option>
                ))}
              </select>
            </div>
            <div>
              <label className="block mb-1">Sub-Sub-Category</label>
              <select
                value={selectedSubSubCategory || ""}
                onChange={(e) => setSelectedSubSubCategory(e.target.value)}
                className="w-full px-3 py-2 border rounded-md"
              >
                {subSubCategories.map((item) => (
                  <option key={item._id} value={item._id}>
                    {item.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                onChange={handleFileChange}
              />
              <button
                onClick={handleButtonClick}
                className="w-full py-2 px-3 flex items-center justify-center bg-gray-100 text-gray-700 rounded-md"
              >
                <RiFileUploadLine className="mr-2" />
                Upload Image
              </button>
            </div>

            {selectedImage && (
              <div className="relative mt-2">
                <button
                  onClick={() => {
                    setSelectedImage(null);
                    setSelectedImageOnUpload(null);
                  }}
                  className="absolute -top-3 -right-3 bg-white rounded-full w-6 h-6 flex items-center justify-center shadow-md"
                >
                  &times;
                </button>
                <img
                  src={selectedImage}
                  className="max-h-40 mx-auto rounded-md"
                  alt="Preview"
                />
              </div>
            )}

            <div className="flex justify-center pt-4">
              <SubmitButton
                isLoading={submitIsLoading}
                InnerDiv={InnerDiv}
                type="button"
                btnAction={submitSubSubSubCategory}
                className="w-40"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SubSubSubCategoryPage;
