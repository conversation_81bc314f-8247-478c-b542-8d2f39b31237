"use client";
import React, { useEffect, useRef, useState } from "react";
import Tabs from "../Tabs";
import {
  addSubSubCategory,
  getCategories,
  getSubCategories,
  getSubSubCategories,
  uploadFile,
} from "@/app/service/category";
import moment from "moment";
import { BsThreeDotsVertical } from "react-icons/bs";
import { RiFileUploadLine } from "react-icons/ri";
import SubmitButton from "@/app/components/common/SubmitButton";
import { toast } from "react-toastify";
import { changeCategoryState } from "@/app/redux/slices/storeSlice";
import { useDispatch, useSelector } from "react-redux";

const sub_subcategory = () => {
  const storeData = useSelector(store => store.storeData)
  const [subsubcategories, setSubSubCategories] = useState([]);
  const [subCategories, setSubCategories] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [submitIsLoading, setSubmitIsLoading] = useState(false);
  const [selectedSubCategory, setSelectedSubCategory] = useState(null);
  const fileInputRef = useRef(null);
  const [selectedImage, setSelectedImage] = useState(null);
  const [enteredName, setEnteredName] = useState("");
  const [selectedImageOnUpload, setSelectedImageOnUpload] = useState(null);
  const [categories, setcategories] = useState([])
  const [selectedCategory, setselectedCategory] = useState(storeData.categoriesState.categoryId || "")
  const dispatch = useDispatch()
  // const [selectedSubSubCategory, setsetselectedSubSubCategory] = useState("")


  console.log(subCategories, "dropsub cat");

  const submitSubSubCategory = async () => {
    setSubmitIsLoading(true);
    try {
      // Upload image first if exists
      let imageUrl = "";
      if (selectedImageOnUpload) {
        const formData = new FormData();
        formData.append("file", selectedImageOnUpload);
        const fileResponse = await uploadFile(formData);
        imageUrl = fileResponse.data.url;
      }
      

      // Create payload
      const payload = {
        name: enteredName,
        image: imageUrl,
        subCategoryId: selectedSubCategory,
      };

      // Add sub-sub-category
      const response = await addSubSubCategory(payload);

      if (response.status == 200) {
        toast.success("Sub-Sub-Category Added Successfully");
        fetchSubSubCategory(response.data.subCategoryId);
        document.getElementById("subSubModal").classList.add("hidden");

        // Reset form
        setEnteredName("");
        setSelectedImage(null);
        setSelectedImageOnUpload(null);
      }
    } catch (error) {
      toast.error("Error adding sub-sub-category");
      console.error("Error adding sub-sub-category:", error);
    } finally {
      setSubmitIsLoading(false);
    }
  };

  const InnerDiv = () => {
    return <div className="px-4">Submit</div>;
  };

  const handleButtonClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedImageOnUpload(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setSelectedImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };


  console.log("selectedSubCategory", selectedSubCategory)
  const getSubSubCategoriesFunc = async () => {
    // setIsLoading(true);
    // try {
    //   const categoriesRes = await getCategories();
    //   if (categoriesRes.status !== 200) {
    //     throw new Error("Failed to fetch categories");
    //   }

    //   const categories = categoriesRes.data.categories;
    //   let allSubSubCategories = [];
    //   let allSubCategories = []; // NEW: To collect all sub-categories

    //   // Fetch sub-categories for each category
    //   const subCategoriesPromises = categories.map((category) =>
    //     getSubCategories(category._id)
    //   );

    //   const subCategoriesResponses = await Promise.all(subCategoriesPromises);

    //   // Process each category and its sub-categories
    //   for (let i = 0; i < categories.length; i++) {
    //     const category = categories[i];
    //     const subCategoriesRes = subCategoriesResponses[i];

    //     if (subCategoriesRes.status === 200) {
    //       const subCategories = subCategoriesRes.data.subCategories;

    //       // NEW: Add these sub-categories to our collection
    //       allSubCategories = [...allSubCategories, ...subCategories];

    //       // Fetch sub-sub-categories for each sub-category
    //       const subSubPromises = subCategories.map((subCategory) =>
    //         getSubSubCategories(subCategory._id)
    //       );

    //       const subSubResponses = await Promise.all(subSubPromises);

    //       // Process each sub-category and its sub-sub-categories
    //       for (let j = 0; j < subCategories.length; j++) {
    //         const subCategory = subCategories[j];
    //         const subSubRes = subSubResponses[j];

    //         if (subSubRes.status === 200) {
    //           const subSubCategories = subSubRes.data.subSubCategories || [];

    //           const enrichedSubSub = subSubCategories.map((item) => ({
    //             ...item,
    //             categoryName: category.name,
    //             subCategoryName: subCategory.name,
    //             categoryId: category._id,
    //             subCategoryId: subCategory._id,
    //           }));

    //           allSubSubCategories = [...allSubSubCategories, ...enrichedSubSub];
    //         }
    //       }
    //     }
    //   }

    //   // NEW: Set the sub-categories state
    //   setSubCategories(allSubCategories);
    //   // setSubSubCategories(allSubSubCategories);

    //   // NEW: Set the default selected sub-category
    //   if (allSubCategories.length > 0 && !selectedSubCategory) {
    //     setSelectedSubCategory(allSubCategories[0]._id);
    //   }
    // } catch (error) {
    //   console.error("Error fetching data:", error);
    // } finally {
    //   setIsLoading(false);
    // }
  };

  console.log(subsubcategories, "subsubcategories");

  useEffect(() => {
    getSubSubCategoriesFunc();
  }, []);
  const getCategoriesFunc = async () => {
    setIsLoading(true);
    let response = await getCategories();
    console.log("response getCategories", response);

    if (response.status == 200) {
      // setcategories(response.data.categories)
      let list = response.data?.categories;
      setcategories(list);
      // fetchSubCategory(list[0]._id)
      // setSelectedSubCategory(list[0]._id)

      if (list.length > 0) {

        // dispatch(changeCategoryState(list[0]._id))
        // getSubCategory(list[0]._id);
      }
    }
    // setIsLoading(false);
  };

  const fetchSubSubCategory = async (id) => {
    try {
      // setIsLoading(true)
      let response = await getSubSubCategories(id)
      if (response.status == 200) {
        setSubSubCategories(response.data.subSubCategories)
      }
      // setIsLoading(false)
    } catch (err) {
      // setIsLoading(false)
    }
  }
  const fetchSubCategory = async (id) => {

    try {
      // setIsLoading(true)
      let response = await getSubCategories(id)
      if (response.status == 200) {

        // response.data.categories    
        let list = response.data?.subCategories || []
        setSubCategories(list)
        if (list.length > 0) {
          setSelectedSubCategory(list[0]._id)
          fetchSubSubCategory(list[0]._id)
        }
        setIsLoading(false)
      }
    } catch (err) {
      console.log("Error in fetchin")
    }
  }
  useEffect(() => {
    getCategoriesFunc()
  }, [])

  useEffect(() => {
    if (storeData.categoriesState.categoryId) {
      fetchSubCategory(storeData.categoriesState.categoryId)
    }
  }, [storeData.categoriesState.categoryId])

  useEffect(() => {
    if (selectedSubCategory) {

      fetchSubSubCategory(selectedSubCategory)
    }
  }, [selectedSubCategory])




  console.log("sub sub", subsubcategories);
  return (
    <div>
      <Tabs />

      <div className="flex justify-between my-4">
        <div className="min-h-[50px]">
          {!isLoading?<div className="">
            <select value={storeData.categoriesState.categoryId} className="border rounded w-[200px] text-gray border-[gray] px-2 py-1" onChange={(e) => {
              dispatch(changeCategoryState(e.target.value))
              // setselectedCategory(e.target.value)
            }}
            >
              {categories?.map(item => {
                return <option value={item._id}>{item.name}</option>
              })}
            </select>
            <select className="ml-2 border rounded w-[200px] text-gray border-[gray] px-2 py-1" onChange={(e) => setSelectedSubCategory(e.target.value)}>
              {subCategories?.map(item => {
                return <option value={item._id}>{item.name}</option>
              })}
            </select>
          </div>:<div>...Loading</div>}
        </div>
        <button
          onClick={() =>
            document.getElementById("subSubModal").classList.remove("hidden")
          }
          className="bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white px-5 py-2 rounded-full"
        >
          Create New Sub-Category
        </button>
      </div>
      <table className=" w-full border border-[#EFF1F4] rounded-lg border-separate">
        <thead className=" border-b border-[#EFF1F4]">
          {/* <th className="px-[10px] text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB] flex items-center"><input type="checkbox" className="mr-2" /><span> Members List </span></th> */}
          <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB] ">
            Name
          </th>
          <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]">
            Created On
          </th>

          {/* <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]">
            {" "}
            Action
          </th> */}
        </thead>
        <tbody className=" w-full">
          {!isLoading ? (
            subsubcategories.length ? subsubcategories?.map((item, index) => {
              return (
                <tr className="py-[10px]  bg-white px-2">
                  <td className="border-b border-[#EFF1F4]  flex px-[10px] py-[10px] bg-white">
                    {" "}
                    <div>
                      <img
                        className="ml-2 w-[40px] h-[40px] rounded-full"
                        src={
                          item.image ||
                          "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/952747fd-94f3-4f7f-ba93-eaf188f302aa.png"
                        }
                      />{" "}
                    </div>
                    <div className="font-medium text-[14px] flex items-center ml-2">
                      {" "}
                      {item.name}
                    </div>{" "}
                  </td>
                  <td className="bg-white border-b border-[#EFF1F4] ">
                    {moment(item?.createdAt).format("DD-MM-YYYY")}
                  </td>
                  {/* <td className="bg-white border-b border-[#EFF1F4] ">{4}</td> */}
                  {/* <td className="bg-white border-b border-[#EFF1F4] ">
                    <BsThreeDotsVertical />
                  </td> */}
                </tr>
              );
            }) : <tr><td colSpan={5} className="text-center">No Data found</td></tr>
          ) : (
            <tr>
              <td
                colSpan={5}
                className="border-b border-[#EFF1F4]  px-[10px] py-[10px] bg-white text-[16px] text-center "
              >
                ...Loading
              </td>
            </tr>
          )}
        </tbody>
      </table>

      {/* Modal for creating new sub-sub-category */}
      <div
        id="subSubModal"
        className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden"
      >
        <div className="bg-white rounded-lg w-full max-w-lg shadow-lg relative p-6">
          <div
            className="absolute top-4 right-4 cursor-pointer"
            onClick={() => {
              document.getElementById("subSubModal").classList.add("hidden");
              setEnteredName("");
              setSelectedImage(null);
              setSelectedImageOnUpload(null);
            }}
          >
            <button className="text-gray-500 hover:text-red-600 text-2xl">
              &times;
            </button>
          </div>

          <h2 className="text-xl font-semibold mb-4 text-center border-b pb-2">
            Create Sub-Sub-Category
          </h2>

          <div className="space-y-4">
            <div>
              <label className="block mb-1">Name</label>
              <input
                placeholder="Enter name"
                type="text"
                value={enteredName}
                onChange={(e) => setEnteredName(e.target.value)}
                className="w-full px-3 py-2 border rounded-md"
              />
            </div>

            <div>
              <label className="block mb-1">Sub-Category</label>
              <select
                value={selectedSubCategory || ""}
                onChange={(e) => setSelectedSubCategory(e.target.value)}
                className="w-full px-3 py-2 border rounded-md"
              >
                {subCategories.map((item) => (
                  <option key={item._id} value={item._id}>
                    {item.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <input
                ref={fileInputRef}
                type="file"
                className="hidden"
                onChange={handleFileChange}
              />
              <button
                onClick={handleButtonClick}
                className="w-full py-2 px-3 flex items-center justify-center bg-gray-100 text-gray-700 rounded-md"
              >
                <RiFileUploadLine className="mr-2" />
                Upload Image
              </button>
            </div>

            {selectedImage && (
              <div className="relative mt-2">
                <button
                  onClick={() => {
                    setSelectedImage(null);
                    setSelectedImageOnUpload(null);
                  }}
                  className="absolute -top-3 -right-3 bg-white rounded-full w-6 h-6 flex items-center justify-center shadow-md"
                >
                  &times;
                </button>
                <img
                  src={selectedImage}
                  className="max-h-40 mx-auto rounded-md"
                  alt="Preview"
                />
              </div>
            )}

            <div className="flex justify-center pt-4">
              <SubmitButton
                isLoading={submitIsLoading}
                InnerDiv={InnerDiv}
                type="button"
                btnAction={submitSubSubCategory}
                className="w-40"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default sub_subcategory;
