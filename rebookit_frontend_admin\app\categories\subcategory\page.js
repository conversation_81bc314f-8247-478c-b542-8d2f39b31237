"use client";
import React, { useEffect, useRef, useState } from "react";
import Tabs from "../Tabs";
import { BsThreeDotsVertical } from "react-icons/bs";
import SubmitButton from "@/app/components/common/SubmitButton";
import { RiFileUploadLine } from "react-icons/ri";
import {
  addSubCategory,
  getCategories,
  getSubCategories,
  getSubSubCategories,
  uploadFile,
} from "@/app/service/category";
import moment from "moment";
import { toast } from "react-toastify";
import { useDispatch, useSelector } from "react-redux";
import { changeCategoryState } from "@/app/redux/slices/storeSlice";

export default function SubCategoryPage() {
  const [subcategories, setsubcategories] = useState([]);
  const [categories, setcategories] = useState([]);
  const [isLoading, setisLoading] = useState(false);
  const [submitisLoading, setsubmitisLoading] = useState(false);
  const [selectedCategory, setselectedCategory] = useState(null);
  const fileInputRef = useRef(null);
  const [selectedImage, setselectedImage] = useState(null);
  const [enterdName, setenterdName] = useState("");
  const [selectedImageOnUpload, setselectedImageOnUpload] = useState(null);
  const dispatch = useDispatch()
  const storeData = useSelector(state => state.storeData)
  console.log("storeData in in categoryTab", storeData)
  const submitQuestion = async () => {
    setsubmitisLoading(true);
    const formData = new FormData();
    formData.append("file", selectedImageOnUpload);
    let file = await uploadFile(formData);

    let payload = {
      name: enterdName,
      image: file.data.url,
      categoryId: storeData.categoriesState.categoryId || selectedCategory,
    };
    let response = await addSubCategory(payload);
    console.log("response", response);
    if (response.status == 200) {
      toast.success("Added Successfull");
      getCategories();
      document.getElementById("myModal").classList.add("hidden");
    }
    setsubmitisLoading(false);
  };

  const InnerDiv = () => {
    return <div className="px-4">Submit</div>;
  };

  const handleButtonClick = () => {
    fileInputRef.current.click();
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setselectedImageOnUpload(file);
      const reader = new FileReader();
      reader.onloadend = () => {
        setTimeout(() => {
          setselectedImage(reader.result); // base64 image string
        }, 500);
      };
      reader.readAsDataURL(file);
      console.log("Selected file:", file.name);
    }
  };
  const getCategoriesFunc = async () => {
    setisLoading(true);
    let response = await getCategories();
    console.log("response getCategories", response);

    if (response.status == 200) {
      // setcategories(response.data.categories)
      let list = response.data?.categories;
      setcategories(list);
      // list.map((item) => {
      //   getSubCategory(item._id);
      // });
      let subCatDataPool = [];
      // let allPromise=new Promise.all()
      if (list.length > 0) {

        dispatch(changeCategoryState(list[0]._id))

        // getSubCategory(list[0]._id);
      }
      // let allPromise = list.map((item) => {
      //   return getSubCategory(item._id);
      // });
      // const allresolved = await Promise.all(allPromise);
      // console.log("allresolved", allresolved);
      // let openArray = [];
      // allresolved.map((item) => {
      //   openArray.push(...item);
      // });
      // console.log("openArray", openArray);

      // setsubcategories(openArray);
    }
    setisLoading(false);
  };
  console.log("categories", categories);
  console.log(subcategories, "subcategories");

  const getSubCategory = async (id) => {

    let response = await getSubCategories(id);
    if (response.status == 200) {
      console.log(response.data?.subCategories, "rsub res");
      setsubcategories(response.data?.subCategories)
      return response.data.subCategories;
    }
  };

  useEffect(() => {
    getCategoriesFunc();
  }, []);

  useEffect(() => {
    if (storeData.categoriesState.categoryId) {
      getSubCategory(storeData.categoriesState.categoryId)
    }
  }, [storeData.categoriesState.categoryId])


  console.log("selectedImage", selectedImage);
  console.log("selectedCategory", selectedCategory);
  console.log("enterdName", enterdName);

  return (
    <div>

      <Tabs />

      <div className="flex justify-between my-4">
        <div className="min-h-[50px]">
          {!isLoading?<select value={storeData.categoriesState.categoryId} className=" border rounded w-[200px] text-gray border-gray-500 px-2 py-1" onChange={(e) => {
            dispatch(changeCategoryState(e.target.value))
            setselectedCategory(e.target.value)
          }}>
            {categories.map(item => {
              return <option value={item._id}>{item.name}</option>
            })}
          </select>:<div>...Loading</div>}
        </div>
        <button
          onClick={() =>
            document.getElementById("myModal").classList.remove("hidden")
          }
          className="bg-gradient-to-r from-[#0161AB] to-[#211F54] text-white px-5 py-2 rounded-full"
        >
          Create New Sub-Category
        </button>
      </div>
      <table className="mt-3 w-full border border-[#EFF1F4] rounded-lg border-separate">
        <thead className=" border-b border-[#EFF1F4]">
          {/* <th className="px-[10px] text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB] flex items-center"><input type="checkbox" className="mr-2" /><span> Members List </span></th> */}
          <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB] ">
            Name
          </th>

          <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]">
            Created On
          </th>

          {/* <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]">
            {" "}
            Action
          </th> */}
        </thead>
        <tbody className=" w-full">
          {!isLoading ? (
            subcategories?.map((item, index) => {
              return (
                <tr className="py-[10px]  bg-white px-2">
                  <td className="border-b border-[#EFF1F4]  flex px-[10px] py-[10px] bg-white">
                    {" "}
                    <div>
                      <img
                        className="ml-2 w-[40px] h-[40px] rounded-full"
                        src={
                          item.image ||
                          "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/952747fd-94f3-4f7f-ba93-eaf188f302aa.png"
                        }
                      />{" "}
                    </div>
                    <div className="font-medium text-[14px] flex items-center ml-2">
                      {" "}
                      {item.name}
                    </div>{" "}
                  </td>

                  <td className="bg-white border-b border-[#EFF1F4] ">
                    {moment(item?.createdAt).format("DD-MM-YYYY")}
                  </td>
                  {/* <td className="bg-white border-b border-[#EFF1F4] ">{4}</td> */}
                  {/* <td className="bg-white border-b border-[#EFF1F4] ">
                    <BsThreeDotsVertical />
                  </td> */}
                </tr>
              );
            })
          ) : (
            <tr>
              <td
                colSpan={5}
                className="border-b border-[#EFF1F4]  px-[10px] py-[10px] bg-white text-[16px] text-center "
              >
                ...Loading
              </td>
            </tr>
          )}
        </tbody>
      </table>

      <div
        id="myModal"
        className=" text-[black] fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden bg-[#EAEAEA]"
      >
        <div className="bg-[#EAEAEA]  rounded-lg w-full max-w-lg shadow-lg relative ">
          <div
            className="flex bg-white w-[30px] cursor-pointer  h-[30px] justify-center absolute top-[-10px] right-[-10px] rounded-full  "
            onClick={() => {
              let docElement = document
                .getElementById("myModal")
                .classList.add("hidden");
              console.log("docElement", docElement);
              // setaddQuestionInput("")
              // setmodelForAnswer(false)
            }}
          >
            <button className="text-gray-500 hover:text-red-600 text-xl font-bold">
              &times;
            </button>
          </div>

          <div className="py-3 bg-white rounded-lg ">
            <h2 className="text-xl font-semibold mb-4  border-b w-fit mx-auto">
              {"Create SubCategory"}
            </h2>
          </div>

          <div className="px-4 mt-4">
            <label>Name</label>
            <input
              placeholder="Add Name"
              type="text"
              value={enterdName}
              onChange={(e) => setenterdName(e.target.value)}
              className="px-2 py-2 rounded-md w-full border-[1px] border-[#dddddd] outline-none"
            />
            <div className="my-2">
              <select
                className="mt-3 px-2 py-2 rounded-md w-full border-[1px] border-[#dddddd] outline-none"
                onChange={(e) => setselectedCategory(e.target.value)}
              >
                {categories.map((item) => {
                  return <option value={item._id}>{item.name}</option>;
                })}
              </select>
            </div>
            <input
              ref={fileInputRef}
              type="file"
              className="hidden"
              onChange={handleFileChange}
            />
            <div className="mt-2">
              <button
                onClick={handleButtonClick}
                className="py-3 px-2 flex items-center bg-[#f4f4f4] text-[#8C8C8C] w-full"
              >
                {" "}
                <RiFileUploadLine />
                Upload Image
              </button>
            </div>

            {selectedImage && (
              <div className="my-3 relative w-fit">
                <button
                  onClick={() => setselectedImage(null)}
                  className="absolute bg-[white] top-[-10px] rounded-full w-[20px] h-[20px] right-0 z-[100] text-gray-500 hover:text-red-600 text-xl font-bold"
                >
                  &times;
                </button>
                <img src={selectedImage} className="h-[100px]" alt="image" />
              </div>
            )}
          </div>
          {/* <!-- Action Button --> */}
          <div class="my-2 flex justify-start mx-4">
            {/* <div className='flex gap-3.5 mt-3 items-center justify-center md:flex-col md:justify-center md:h-full md:w-fit md:items-start md:gap-2.5'>
                            <button className='py-[7.5px] px-[30px] text-[13px] font-semibold leading-[18px] text-center text-white rounded-full global_linear_gradient md:order-2 md:text-base md:leading-[22px]'
                                onClick={submitQuestion}
                            >Submit</button>

                        </div> */}
            <div className="max-w-[300px] mb-3">
              <SubmitButton
                isLoading={submitisLoading}
                InnerDiv={InnerDiv}
                type={"button"}
                btnAction={submitQuestion}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
