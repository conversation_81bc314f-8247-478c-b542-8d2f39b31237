import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxi<PERSON>,
  Tooltip,
  ResponsiveContainer,
  CartesianGrid,
  Legend,
} from "recharts";
import styles from './rechartsNoOutline.module.css';

const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul"];
const years = [2025, 2024, 2023];
const allData = {
  2025: [
    { name: "<PERSON>", total: 500, new: 300 },
    { name: "Feb", total: 600, new: 400 },
    { name: "<PERSON>", total: 500, new: 350 },
    { name: "Apr", total: 700, new: 500 },
    { name: "May", total: 400, new: 250 },
    { name: "<PERSON>", total: 600, new: 350 },
    { name: "<PERSON>", total: 800, new: 700 },
  ],
  2024: [
    { name: "Jan", total: 400, new: 200 },
    { name: "Feb", total: 500, new: 300 },
    { name: "<PERSON>", total: 450, new: 250 },
    { name: "Apr", total: 600, new: 400 },
    { name: "May", total: 350, new: 200 },
    { name: "<PERSON>", total: 500, new: 300 },
    { name: "<PERSON>", total: 700, new: 600 },
  ],
  2023: [
    { name: "<PERSON>", total: 300, new: 100 },
    { name: "Feb", total: 400, new: 200 },
    { name: "Mar", total: 350, new: 150 },
    { name: "Apr", total: 500, new: 300 },
    { name: "May", total: 250, new: 100 },
    { name: "Jun", total: 400, new: 200 },
    { name: "Jul", total: 600, new: 500 },
  ],
};

const FILTERS = ["All", "Banner", "Grid", "Featured"];

export default function AnalyticsReportChart() {
  const [selectedYear, setSelectedYear] = useState(years[0]);
  const [filter, setFilter] = useState("All");
  const data = allData[selectedYear];

  return (
    <div className={styles.noOutlineRecharts}>
      <div className="bg-white rounded-xl shadow p-8 flex flex-col" style={{ maxWidth: "100%" }}>
        <div className="flex justify-between items-center mb-8">
          <span className="text-lg font-bold text-black">Analytics Report</span>
          <div className="flex gap-0 bg-gray-100 rounded-lg overflow-hidden">
            {FILTERS.map((f, idx) => (
              <button
                key={f}
                className={`px-5 py-1 text-sm transition-all focus:outline-none ${filter === f ? "bg-white font-bold text-black shadow" : "text-gray-500 font-normal"} ${idx === 0 ? "rounded-l-lg" : ""} ${idx === FILTERS.length - 1 ? "rounded-r-lg" : ""}`}
                onClick={() => setFilter(f)}
              >
                {f}
              </button>
            ))}
          </div>
          <select
            className="rounded-lg px-3 py-1 text-xs shadow-sm border-0 bg-gray-100 focus:ring-2 focus:ring-green-300 focus:outline-none transition-all duration-150 font-semibold text-black"
            value={selectedYear}
            onChange={e => setSelectedYear(Number(e.target.value))}
          >
            {years.map(y => (
              <option key={y} value={y}>{y}</option>
            ))}
          </select>
        </div>
        <div className="flex flex-row gap-4 items-center w-full">
          <div className="flex flex-col gap-4 min-w-[120px]">
            <div className="border rounded-lg px-4 py-3 flex flex-col items-start gap-1">
              <span className="flex items-center gap-2"><span className="w-2 h-2 bg-green-500 rounded-full inline-block"></span><span className="text-green-600 font-bold text-lg">5705</span></span>
              <span className="text-xs text-gray-500">Total Ads</span>
            </div>
            <div className="border rounded-lg px-4 py-3 flex flex-col items-start gap-1">
              <span className="flex items-center gap-2"><span className="w-2 h-2 bg-green-300 rounded-full inline-block"></span><span className="text-green-600 font-bold text-lg">5705</span></span>
              <span className="text-xs text-gray-500">New Ads</span>
            </div>
          </div>
          <div className="flex-1" style={{ minWidth: 0 }}>
            <ResponsiveContainer width="100%" height={220}>
              <BarChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                <CartesianGrid strokeDasharray="3 3" vertical={false} />
                <XAxis dataKey="name" tick={{ fontSize: 17 }} axisLine={false} tickLine={false} interval={0} />
                <YAxis yAxisId="left" tick={{ fontSize: 13 }} axisLine={false} tickLine={false} />
                <YAxis yAxisId="right" orientation="right" tick={{ fontSize: 13 }} axisLine={false} tickLine={false} />
                <Tooltip formatter={(value) => value} />
                <Bar yAxisId="left" dataKey="total" fill="#22c55e" barSize={32} radius={[6, 6, 0, 0]} />
                <Bar yAxisId="right" dataKey="new" fill="#bbf7d0" barSize={24} radius={[6, 6, 0, 0]} />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      </div>
    </div>
  );
} 