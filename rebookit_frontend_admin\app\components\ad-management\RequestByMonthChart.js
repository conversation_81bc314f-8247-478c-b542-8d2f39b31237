import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts";
import styles from './rechartsNoOutline.module.css';

const allData = [
  { name: "Oct 2023", Banner: 160, Grid: 40, year: 2023 },
  { name: "Nov 2023", Banner: 180, Grid: 40, year: 2023 },
  { name: "Dec 2023", Banner: 190, Grid: 60, year: 2023 },
  { name: "Jan 2024", Banner: 170, Grid: 50, year: 2024 },
  { name: "Feb 2024", Banner: 200, Grid: 30, year: 2024 },
  { name: "Mar 2024", Banner: 150, Grid: 70, year: 2024 },
  { name: "Apr 2024", Banner: 180, Grid: 60, year: 2024 },
  { name: "May 2024", Banner: 210, Grid: 40, year: 2024 },
  { name: "Jun 2024", Banner: 160, Grid: 80, year: 2024 },
  { name: "Jul 2024", Banner: 190, Grid: 60, year: 2024 },
];

const years = [2024, 2023];

export default function RequestByMonthChart() {
  const [showBanner, setShowBanner] = useState(true);
  const [showGrid, setShowGrid] = useState(true);
  const [selectedYear, setSelectedYear] = useState(years[0]);

  const data = allData.filter((d) => d.year === selectedYear);
  const chartWidth = Math.max(420, data.length * 90);

  return (
    <div className={`bg-white rounded-xl shadow p-6 flex flex-col ${styles.noOutlineRecharts}`} style={{ minHeight: "100%" }}>
      <div className="flex justify-between items-center mb-4">
        <span className="text-md font-semibold">Request received by month</span>
        <select
          className="rounded-lg px-3 py-1 text-xs shadow-sm border-0 bg-gray-100 focus:ring-2 focus:ring-purple-300 focus:outline-none transition-all duration-150"
          value={selectedYear}
          onChange={e => setSelectedYear(Number(e.target.value))}
        >
          {years.map(y => (
            <option key={y} value={y}>{y}</option>
          ))}
        </select>
      </div>
      <div style={{ width: "100%", overflowX: "auto", scrollbarWidth: "none", msOverflowStyle: "none" }} className="hide-scrollbar">
        <div  style={{ minWidth: 420, width: chartWidth }}>
          <ResponsiveContainer width="100%" height={220}>
            <BarChart
              data={data}
              margin={{ top: 10, right: 0, left: 0, bottom: 0 }}
              barCategoryGap="10%"
            >
              <CartesianGrid strokeDasharray="3 3" vertical={false} />
              <XAxis dataKey="name" tick={{ fontSize: 13 }} axisLine={false} tickLine={false} interval={0} />
              <YAxis tick={{ fontSize: 12 }} axisLine={false} tickLine={false} />
              <Tooltip contentStyle={{ fontSize: 12 }} />
              {showBanner && (
                <Bar dataKey="Banner" fill="#fb7e4b" radius={[6, 6, 0, 0]} barSize={32} />
              )}
              {showGrid && (
                <Bar dataKey="Grid" fill="#e5e7eb" radius={[6, 6, 0, 0]} barSize={32} />
              )}
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
      <style>{`
        .hide-scrollbar::-webkit-scrollbar { display: none; }
        .hide-scrollbar { scrollbar-width: none; -ms-overflow-style: none; }
      `}</style>
      <div className="flex gap-4 text-xs items-center mt-2">
        <button
          className={`flex items-center gap-1 px-3 py-1 rounded-lg border transition-all shadow-sm focus:outline-none ${showBanner ? "bg-orange-400 text-white font-semibold border-orange-400" : "bg-white text-orange-400 border-orange-200"}`}
          onClick={() => setShowBanner((v) => !v)}
        >
          <span className="w-3 h-3 bg-orange-400 inline-block rounded-sm"></span> Banner
        </button>
        <button
          className={`flex items-center gap-1 px-3 py-1 rounded-lg border transition-all shadow-sm focus:outline-none ${showGrid ? "bg-gray-400 text-white font-semibold border-gray-400" : "bg-white text-gray-400 border-gray-200"}`}
          onClick={() => setShowGrid((v) => !v)}
        >
          <span className="w-3 h-3 bg-gray-300 inline-block rounded-sm"></span> Grid
        </button>
      </div>
    </div>
  );
} 