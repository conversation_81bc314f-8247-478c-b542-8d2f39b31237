import React, { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  CartesianGrid,
  Cell,
} from "recharts";
import styles from './rechartsNoOutline.module.css';

const months = [
  "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
];

const allData = [
  { month: "Jan", All: 80000, Banner: 50000, Grid: 30000 },
  { month: "Feb", All: 60000, Banner: 40000, Grid: 20000 },
  { month: "Mar", All: 100000, Banner: 70000, Grid: 30000 },
  { month: "Apr", All: 75000, Banner: 50000, Grid: 25000 },
  { month: "May", All: 90000, Banner: 60000, Grid: 30000 },
  { month: "Jun", All: 110000, Banner: 70000, Grid: 40000 },
  { month: "Jul", All: 125000, Banner: 80000, Grid: 45000 },
  { month: "Aug", All: 105000, Banner: 70000, Grid: 35000 },
  { month: "Sep", All: 80000, Banner: 50000, Grid: 30000 },
  { month: "Oct", All: 60000, Banner: 40000, Grid: 20000 },
  { month: "Nov", All: 95000, Banner: 60000, Grid: 35000 },
  { month: "Dec", All: 100000, Banner: 65000, Grid: 35000 },
];

const FILTERS = ["All", "Banner", "Grid"];

const CustomTooltip = ({ active, payload }) => {
  if (active && payload && payload.length) {
    return (
      <div style={{ background: "#111", color: "#fff", borderRadius: 8, padding: "6px 14px", fontSize: 16, fontWeight: 600 }}>
        J$ {payload[0].value.toLocaleString()}
      </div>
    );
  }
  return null;
};

export default function RevenueAdChart() {
  const [filter, setFilter] = useState("All");

  const maxIdx = allData.findIndex((d) => d[filter] === Math.max(...allData.map((d) => d[filter])));

  return (
    <div className={`bg-white rounded-xl shadow p-6 flex flex-col ${styles.noOutlineRecharts}`} style={{ minHeight: 340, maxWidth: 660 }}>
      <div className="flex justify-between items-center mb-2">
        <span className="text-md font-semibold">Revenue Ad</span>
        <div className="flex gap-0 bg-gray-100 rounded-lg overflow-hidden">
          {FILTERS.map((f) => (
            <button
              key={f}
              className={`px-6 py-1 text-sm transition-all focus:outline-none ${filter === f ? "bg-white font-semibold shadow text-gray-900" : "text-gray-500"}`}
              style={{ borderRadius: 0 }}
              onClick={() => setFilter(f)}
            >
              {f}
            </button>
          ))}
        </div>
      </div>
      <div style={{ width: '100%', height: 240, display: 'flex', alignItems: 'center' }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={allData} margin={{ top: 30, right: 0, left: 0, bottom: 0 }} barCategoryGap="10%">
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis dataKey="month" tick={{ fontSize: 15 }} axisLine={false} tickLine={false} />
            <YAxis tickFormatter={v => v / 1000 + "k"} tick={{ fontSize: 13 }} axisLine={false} tickLine={false} />
            <Tooltip 
              content={<CustomTooltip />} 
              cursor={{ fill: "rgba(0,0,0,0.04)" }} 
              wrapperStyle={{ left: '50%', transform: 'translateX(-50%)' }}
            />
            <defs>
              <linearGradient id="barGradient" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="#0a2a7a" />
                <stop offset="100%" stopColor="#3b82f6" />
              </linearGradient>
            </defs>
            <Bar 
              dataKey={filter} 
              fill="#dbeafe" 
              radius={[6, 6, 0, 0]} 
              barSize={36} 
              isAnimationActive={false}
            >
              {allData.map((entry, idx) => (
                <Cell key={`cell-${idx}`} fill={idx === maxIdx ? "url(#barGradient)" : "#dbeafe"} />
              ))}
            </Bar>
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
} 