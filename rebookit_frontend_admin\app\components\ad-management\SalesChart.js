import React, { useState } from "react";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  CartesianGrid,
} from "recharts";
import styles from './rechartsNoOutline.module.css';

const years = [2025, 2024, 2023];
const months = [
  "Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"
];

// Sample data for demonstration
const yearlyData = [
  { name: "Jan", sales: 40 },
  { name: "Feb", sales: 60 },
  { name: "Mar", sales: 80 },
  { name: "Apr", sales: 100 },
  { name: "May", sales: 120 },
  { name: "Jun", sales: 140 },
  { name: "Jul", sales: 160 },
  { name: "Aug", sales: 120 },
  { name: "Sep", sales: 100 },
  { name: "Oct", sales: 80 },
  { name: "Nov", sales: 60 },
  { name: "Dec", sales: 50 },
];

const getMonthDays = (year, monthIdx) => {
  const days = new Date(year, monthIdx + 1, 0).getDate();
  return Array.from({ length: days }, (_, i) => i + 1);
};

const getMonthlyData = (year, monthIdx) => {
  const days = getMonthDays(year, monthIdx);
  // Generate random sales for each day
  return days.map((d) => ({ name: d.toString(), sales: Math.floor(30 + Math.random() * 100) }));
};

export default function SalesChart() {
  const [mode, setMode] = useState("year"); // 'year' or 'month'
  const [selectedYear, setSelectedYear] = useState(years[0]);
  const [selectedMonth, setSelectedMonth] = useState(0); // 0 = Jan

  const chartData =
    mode === "year"
      ? yearlyData
      : getMonthlyData(selectedYear, selectedMonth);

  return (
    <div className={`bg-white rounded-xl shadow p-6 flex flex-col ${styles.noOutlineRecharts}`} style={{ minHeight: 350, maxWidth: 420 }}>
      <div className="flex justify-between items-center mb-4">
        <span className="text-md font-semibold">Sales</span>
        <div className="flex gap-2 items-center">
          <select
            className="rounded-lg px-3 py-1 text-xs shadow-sm border-0 bg-gray-100 focus:ring-2 focus:ring-purple-300 focus:outline-none transition-all duration-150"
            value={selectedYear}
            onChange={(e) => setSelectedYear(Number(e.target.value))}
          >
            {years.map((y) => (
              <option key={y} value={y}>{y}</option>
            ))}
          </select>
          <select
            className="rounded-lg px-3 py-1 text-xs shadow-sm border-0 bg-gray-100 focus:ring-2 focus:ring-purple-300 focus:outline-none transition-all duration-150 disabled:bg-gray-200 disabled:text-gray-400"
            value={selectedMonth}
            onChange={(e) => setSelectedMonth(Number(e.target.value))}
            disabled={mode === "year"}
          >
            {months.map((m, idx) => (
              <option key={m} value={idx}>{m}</option>
            ))}
          </select>
        </div>
      </div>
      <div className="flex gap-2 mb-2">
        <button
          className={`px-5 py-1 rounded-l-lg border border-gray-200 text-sm transition-all duration-150 focus:outline-none ${mode === "month" ? "bg-purple-100 text-purple-700 font-semibold shadow" : "bg-white text-gray-500"}`}
          onClick={() => setMode("month")}
        >
          Month
        </button>
        <button
          className={`px-5 py-1 rounded-r-lg border border-gray-200 text-sm transition-all duration-150 focus:outline-none ${mode === "year" ? "bg-purple-100 text-purple-700 font-semibold shadow" : "bg-white text-gray-500"}`}
          onClick={() => setMode("year")}
        >
          Year
        </button>
      </div>
      <div className="flex items-center gap-4 mb-1">
        <span className="text-2xl font-bold">J$ 123456</span>
        <span className="text-green-600 text-sm flex items-center">▲32% from last period</span>
      </div>
      <div className="mb-2 text-xs text-gray-500">{mode === "year" ? `${selectedYear}` : `${months[selectedMonth]} ${selectedYear}`}</div>
      <div style={{ width: "100%", height: 160 }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={chartData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis 
              dataKey="name" 
              tick={mode === "year" ? { fontSize: 12 } : false}
              axisLine={false} 
              tickLine={false} 
              interval={0} 
            />
            <YAxis hide />
            <Tooltip 
              cursor={{ fill: "#f3e8ff" }} 
              contentStyle={{ fontSize: mode === "year" ? 12 : 12 }} 
            />
            <Bar 
              dataKey="sales" 
              radius={[6, 6, 0, 0]} 
              fill="#a21caf" 
              barSize={mode === "year" ? 16 : 24} 
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
} 