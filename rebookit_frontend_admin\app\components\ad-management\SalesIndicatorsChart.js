import React, { useState } from "react";
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  Tooltip,
  ResponsiveContainer,
  CartesianGrid,
  Area,
  Legend,
} from "recharts";
import styles from './rechartsNoOutline.module.css';

const FILTERS = ["Quarter", "Weeks", "Days"];
const months = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];  

const quarterData = [
  { name: "Jan", sum: 5000, last: 5200 },
  { name: "Feb", sum: 6000, last: 6100 },
  { name: "<PERSON>", sum: 4800, last: 5000 },
  { name: "Apr", sum: 3000, last: 3200 },
  { name: "May", sum: 3500, last: 3400 },
  { name: "Jun", sum: 7000, last: 8000 },
  { name: "Jul", sum: 9000, last: 12000 },
  { name: "Aug", sum: 6000, last: 9000 },
  { name: "Sep", sum: 4000, last: 3500 },
  { name: "Oct", sum: 4500, last: 4700 },
  { name: "Nov", sum: 5200, last: 5300 },
  { name: "Dec", sum: 6100, last: 6200 },
];

const weeksData = quarterData.map((d, i) => ({ ...d, sum: d.sum - 1000, last: d.last - 1200 }));
const daysData = quarterData.map((d, i) => ({ ...d, sum: d.sum - 2000, last: d.last - 2000 }));

const filterToData = {
  Quarter: quarterData,
  Weeks: weeksData,
  Days: daysData,
};

const CustomTooltip = ({ active, payload }) => {
  if (active && payload && payload.length) {
    return (
      <div style={{ background: "#111", color: "#fff", borderRadius: 8, padding: "6px 14px", fontSize: 16, fontWeight: 600 }}>
        J$ {payload[0].value.toLocaleString()}
      </div>
    );
  }
  return null;
};

export default function SalesIndicatorsChart() {
  const [filter, setFilter] = useState("Quarter");
  const data = filterToData[filter];

  return (
    <div className={`bg-white rounded-xl shadow p-6 flex flex-col ${styles.noOutlineRecharts}`} style={{ minHeight: 340, maxWidth: "100%" }}>
      <div className="flex justify-between items-center mb-2">
        <span className="text-md font-semibold">Sales Indicators</span>
        <div className="flex gap-0 bg-gray-100 rounded-lg overflow-hidden">
          {FILTERS.map((f) => (
            <button
              key={f}
              className={`px-6 py-1 text-sm transition-all focus:outline-none ${filter === f ? "bg-white font-semibold shadow text-gray-900" : "text-gray-500"}`}
              style={{ borderRadius: 0 }}
              onClick={() => setFilter(f)}
            >
              {f}
            </button>
          ))}
        </div>
      </div>
      <div style={{ width: "100%", height: 220 }}>
        <ResponsiveContainer width="100%" height="100%">
          <LineChart data={data} margin={{ top: 20, right: 10, left: 0, bottom: 0 }} style={{ marginLeft: 0 }}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis dataKey="name" tick={{ fontSize: 15 }} axisLine={false} tickLine={false} interval={0} />
            <YAxis tick={{ fontSize: 13 }} axisLine={false} tickLine={false} />
            <Tooltip content={<CustomTooltip />} cursor={{ fill: "rgba(0,0,0,0.04)" }} />
            <defs>
              <linearGradient id="sumArea" x1="0" y1="0" x2="0" y2="1">
                <stop offset="0%" stopColor="#fca5a5" stopOpacity={0.3} />
                <stop offset="100%" stopColor="#fca5a5" stopOpacity={0.1} />
              </linearGradient>
            </defs>
            <Area type="monotone" dataKey="sum" stroke="#ef4444" fill="url(#sumArea)" fillOpacity={1} strokeWidth={3} />
            <Line type="monotone" dataKey="sum" stroke="#1e293b" strokeWidth={3} dot={false} activeDot={{ r: 8, fill: '#1e293b' }} />
            <Line type="monotone" dataKey="last" stroke="#f59e42" strokeWidth={3} dot={false} activeDot={{ r: 8, fill: '#f59e42' }} />
          </LineChart>
        </ResponsiveContainer>
      </div>
      <div className="flex gap-4 text-xs items-center mt-2">
        <span className="flex items-center gap-1"><span className="w-3 h-3 bg-blue-900 inline-block rounded-sm"></span> Sum of sales</span>
        <span className="flex items-center gap-1"><span className="w-3 h-3 bg-orange-400 inline-block rounded-sm"></span> Sales last year</span>
      </div>
    </div>
  );
} 