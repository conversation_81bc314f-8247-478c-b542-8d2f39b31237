import React, { useState } from "react";
import ReactApexChart from "react-apexcharts";

export default function TotalAdTypeChart() {
  const [series] = useState([22, 22, 22, 22, 44]);
  const colors = ["#22c55e", "#3b82f6", "#ef4444", "#fde047", "#211F54"];
  const labels = ["Received Green", "Received Blue", "Received Red", "Received Yellow", "blue"];

  const options = {
    chart: {
      type: "donut",
      fontFamily: 'Poppins, sans-serif',
    },
    colors,
    labels,
    legend: { show: false },
    plotOptions: {
      pie: {
        donut: {
          size: '65%',
        },
      },
    },
    dataLabels: { enabled: false },
    stroke: { show: false },
    responsive: [
      {
        breakpoint: 640,
        options: { chart: { width: 200 } },
      },
    ],
  };

  return (
    <div className="bg-white rounded-xl shadow p-6 flex flex-col" style={{ minHeight: 350, maxWidth: 420 }}>

  
    <span className="text-md font-semibold mb-4"> Total Ad Type</span>
    <div className="flex items-center flex-col justify-center w-full">
      <div className="w-24 mb-4 flex items-center justify-center ">
        <ReactApexChart options={options} series={series} type="donut" width={190} height={190} />
      </div>
      <div className="flex flex-wrap gap-2 justify-center text-xs mt-2">
        <span className="text-green-600">■</span> Received 22%
        <span className="text-blue-600">■</span> Received 22%
        <span className="text-red-600">■</span> Received 22%
        <span className="text-yellow-600">■</span> Received 22%
        <span className="text-blue-900">■</span> Received 22%|
      </div>
      </div>
    </div>
  );
} 