'use client'

import React, { useEffect, useState } from "react";
import Slider from "react-slick";

import bannerCss from "./banner.module.scss"


import testImage from "@/public/test.jpeg"
import Image from "next/image";
import Link from "next/link";


function BannerAd() {

    const [adsImages, setadsImages] = useState([])

    useEffect(() => {
        setadsImages([testImage, testImage, testImage])
    }, [])


    const settings = {
        dots: false,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: true,             
        autoplaySpeed: 3000     
    };

    return (
        <div className={`${bannerCss.bannerComponent}`}>

            <div className="slider-container">
                <Slider {...settings}>
                    {adsImages.map((adsImage, index) => (
                        <div key={index} className={`${bannerCss.frame}`}>
                            <Link href={"/"}>
                            <Image
                                src={adsImage}
                                alt="ads image"
                                fill
                                style={{ objectFit: 'contain' }} // ⚠️ Not "cover"
                            />
                            </Link>
                        </div>
                    ))}

                </Slider>
            </div>

        </div>
    );
}

export default BannerAd;
