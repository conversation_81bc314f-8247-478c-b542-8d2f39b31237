import React from 'react';

const weekDays = [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday',
];

const formatJMD = (value) => {
  if (value === undefined || value === null || value === '') return '';
  return 'J$' + Number(value).toLocaleString('en-JM');
};

function getDaysInMonth(year, month) {
  return new Date(year, month + 1, 0).getDate();
}

function getFirstDayIndex(year, month) {
  // Monday as first day (0=Monday, 6=Sunday)
  const jsDay = new Date(year, month, 1).getDay();
  return jsDay === 0 ? 6 : jsDay - 1;
}

function getCalendarMatrix(year, month) {
  const daysInMonth = getDaysInMonth(year, month);
  const firstDayIdx = getFirstDayIndex(year, month);
  const matrix = [];
  let day = 1 - firstDayIdx;
  for (let row = 0; row < 6; row++) {
    const week = [];
    for (let col = 0; col < 7; col++) {
      week.push(day > 0 && day <= daysInMonth ? day : '');
      day++;
    }
    matrix.push(week);
  }
  return matrix;
}

function normalizeDate(date) {
  if (!date) return '';
  if (/^\d{4}-\d{2}-\d{2}$/.test(date)) return date;
  const d = new Date(date);
  return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0');
}

function getRuleForDateDefault(rules, year, month, day) {
  const dateStr = `${year}-${String(month + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  const matching = rules.filter(rule => {
    if (rule.isActive === false) return false;
    if (!rule.startDate || !rule.endDate) return false;
    const start = normalizeDate(rule.startDate);
    const end = normalizeDate(rule.endDate);
    return start <= dateStr && dateStr <= end;
  });
  if (matching.length === 0) return null;
  const selected = matching.reduce((a, b) => {
    if (a.priority == null) return b;
    if (b.priority == null) return a;
    return Number(a.priority) > Number(b.priority) ? a : b;
  });
  return selected;
}

function formatPrice(currency, price) {
  if (currency === 'USD') {
    // Format as US$ 1,234.56
    if (price === undefined || price === null || price === '') return '';
    return 'US$' + Number(price).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
  } else {
    // Default to JMD
    return formatJMD(price);
  }
}

export default function CalendarRuleView({
  rules = [],
  basePrice = '',
  baseCurrency = 'JMD',
  dateOverrides = {},
  onCellClick = () => {},
  onRemoveOverride = () => {},
  calendarMonth,
  calendarYear,
  setCalendarMonth,
  setCalendarYear,
  getRuleForDate,
}) {
  const calendar = getCalendarMatrix(calendarYear, calendarMonth);
  // Use custom getRuleForDate if provided, else default
  const ruleForDateFn = getRuleForDate || getRuleForDateDefault;

  return (
    <div className="bg-white rounded-lg shadow p-0 overflow-x-auto border border-gray-200">
      <table className="min-w-full text-sm font-[poppins]">
        <thead>
          <tr>
            {weekDays.map(day => (
              <th
                key={day}
                className="p-3 text-center font-semibold text-[#211F54] bg-[#F5F6FA] border-b border-gray-200"
              >
                {day}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {calendar.map((week, rowIdx) => (
            <tr key={rowIdx}>
              {week.map((day, colIdx) => {
                if (!day) return <td key={colIdx} />;
                let isDisabled = false;
                const dateStr = `${calendarYear}-${String(calendarMonth + 1).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
                const override = dateOverrides[dateStr];
                if (override) {
                  return (
                    <td
                      key={colIdx}
                      className="p-0 text-center align-top "
                      style={{ minWidth: 120, height: 90 }}
                    >
                      <div
                        className="flex flex-col items-center justify-center h-full py-2 bg-gray-200 rounded-lg relative border"
                        style={{ border: 'none', position: 'relative' }}
                        onClick={() => !isDisabled && onCellClick(day)}
                      >
                          <span
                          className="absolute truncate max-w-[100px] left-1 top-1 text-[10px] font-semibold px-1 rounded"
                          // style={{ background: '#fff', color: ruleForDay.color, border: `1px solid ${ruleForDay.color}`, zIndex: 2 }}
                          // title={ruleForDay.name}
                        >
                          override
                        </span>
                        {/* <button
                          className="absolute top-1 right-1 text-xs text-gray-500 hover:text-red-500 bg-white rounded-full p-0.5 border border-gray-300 z-10"
                          style={{ lineHeight: 1, fontSize: 14 }}
                          title="Remove override"
                          onClick={e => {
                            e.stopPropagation();
                            onRemoveOverride(day);
                          }}
                        >
                          ×
                        </button> */}
                        <span className="font-normal text-xl text-center">
                          {String(day).padStart(2, '0')}
                        </span>
                        <div className="flex items-center gap-2 mt-2">
                          <span className="text-xs font-semibold" style={{ color: '#888' }}>
                            {formatPrice(override.currency, override.price)}
                          </span>
                        </div>
                      </div>
                    </td>
                  );
                }
                const ruleForDay = ruleForDateFn(rules, calendarYear, calendarMonth, day);
                const highlightStyle = ruleForDay
                  ? {
                      border: `1px solid ${ruleForDay.color}`,
                      color: ruleForDay.color,
                      fontWeight: 600,
                    }
                  : {};
                const priceToShow = ruleForDay ? ruleForDay.price : basePrice;
                const currencyToShow = ruleForDay ? ruleForDay.currency : baseCurrency;
                return (
                  <td
                    key={colIdx}
                    className="p-0 text-center align-top "
                    style={{ minWidth: 120, height: 90 }}
                  >
                    <div
                      className="flex flex-col items-center justify-center h-full py-2 border border-gray-100 rounded-lg relative"
                      style={
                        isDisabled
                          ? { opacity: 0.5, pointerEvents: 'none', ...highlightStyle }
                          : highlightStyle
                      }
                      onClick={() => !isDisabled && onCellClick(day)}
                    >
                      {ruleForDay && (
                        <span
                          className="absolute truncate max-w-[100px] left-1 top-1 text-[10px] font-semibold px-1 rounded"
                          style={{ background: '#fff', color: ruleForDay.color, border: `1px solid ${ruleForDay.color}`, zIndex: 2 }}
                          title={ruleForDay.name}
                        >
                          {ruleForDay.name}
                        </span>
                      )}
                      <span className="font-normal text-xl text-center">
                        {String(day).padStart(2, '0')}
                      </span>
                      <div className="flex items-center gap-2 mt-2">
                        <span
                          className="text-xs font-semibold"
                          style={ruleForDay ? { color: ruleForDay.color } : { color: '#888' }}
                        >
                          {formatPrice(currencyToShow, priceToShow)}
                        </span>
                      </div>
                    </div>
                  </td>
                );
              })}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
} 