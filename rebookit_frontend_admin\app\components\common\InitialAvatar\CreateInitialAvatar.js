export function createInitialsAvatar(name, options = {}) {
  // Default options
  const {
    size = 100,
    bgColor = null, // Default to null to use random color
    textColor = "#ffffff", // Default to white text
    shape = "circle",
    randomColor = true, // New option to enable random colors
  } = options;

  // Generate a random dark/rich color that works well with white text
  const generateRandomColor = () => {
    const hue = Math.floor(Math.random() * 360);
    const saturation = 70 + Math.floor(Math.random() * 30); // 70-100%
    const lightness = 30 + Math.floor(Math.random() * 20); // 30-50%
    return `hsl(${hue}, ${saturation}%, ${lightness}%)`;
  };

  // Get initials from name
  const getInitials = (name) => {
    if (!name || typeof name !== "string") return "";

    const words = name
      .trim()
      .split(/\s+/)
      .filter((word) => word.length > 0);
    if (words.length === 0) return "";

    if (words.length === 1) {
      return words[0].charAt(0).toUpperCase();
    }
    return `${words[0].charAt(0)}${words[words.length - 1].charAt(
      0
    )}`.toUpperCase();
  };

  const initials = getInitials(name);
  const fontSize = size * 0.4;

  // Determine background color
  const finalBgColor =
    bgColor || (randomColor ? generateRandomColor() : "#cccccc");

  // Create SVG based on shape
  let svgContent;
  if (shape === "circle") {
    const radius = size / 2;
    svgContent = `
      <circle cx="${radius}" cy="${radius}" r="${radius}" fill="${finalBgColor}" />
      <text x="50%" y="50%" dy="0.35em" text-anchor="middle" 
            font-family="Arial" font-size="${fontSize}" 
            fill="${textColor}" font-weight="bold">
        ${initials}
      </text>
    `;
  } else {
    svgContent = `
      <rect width="100%" height="100%" fill="${finalBgColor}" />
      <text x="50%" y="50%" dy="0.35em" text-anchor="middle" 
            font-family="Arial" font-size="${fontSize}" 
            fill="${textColor}" font-weight="bold">
        ${initials}
      </text>
    `;
  }

  // Create full SVG
  const svg = `
    <svg xmlns="http://www.w3.org/2000/svg" 
         width="${size}" 
         height="${size}" 
         viewBox="0 0 ${size} ${size}">
      ${svgContent}
    </svg>
  `;

  // Convert to data URL
  return `data:image/svg+xml,${encodeURIComponent(svg)}`;
}
