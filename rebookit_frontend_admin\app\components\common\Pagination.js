import React from "react";

export default function Pagination({
  setPageSize,
  setCurrentPage,
  getListing,
  currentPage,
  totalPages,
  totalItems,
  pageSize,
}) {
  console.log("totalPages inpagination", totalPages);
  const handlePageChange = (number) => {
    setCurrentPage(number);
  };
  if(totalPages>1)
  return (
    <div className="flex flex-col md:flex-row items-center justify-end mt-4 gap-4">
      {/* <div className="flex items-center">
        <div>
          <span className="mr-2 text-sm">Items per page:</span>
          <select
            value={pageSize}
            onChange={(e) => {
              const newSize = parseInt(e.target.value);
              setPageSize(newSize);
              setCurrentPage(1);
              getListing(undefined, 1);
            }}
            className="border rounded px-2 py-1 text-sm"
          >
            <option value={5}>5</option>
            <option value={10}>10</option>
            <option value={20}>20</option>
            <option value={50}>50</option>
          </select>
        </div>
        <div className="text-sm ml-2">
          Showing {Math.min((currentPage - 1) * pageSize + 1, totalItems)}-
          {Math.min(currentPage * pageSize, totalItems)} of {totalItems} items
        </div>
      </div> */}

      <div className="flex items-center gap-2">
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={`px-3 py-1 rounded border text-sm ${currentPage === 1
              ? "bg-gray-100 cursor-not-allowed"
              : "hover:bg-gray-100"
            }`}
        >
          Previous
        </button>

        {Array.from({ length: totalPages }, (_, i) => {
          let pageNum;
          if (totalPages <= 5) {
            pageNum = i + 1;
          } else if (currentPage <= 3) {
            pageNum = i + 1;
          } else if (currentPage >= totalPages - 2) {
            pageNum = totalPages - 4 + i;
          } else {
            pageNum = currentPage - 2 + i;
          }

          return (
            <button
              key={pageNum}
              onClick={() => handlePageChange(pageNum)}
              className={`px-3 py-1 rounded border text-sm ${currentPage === pageNum
                  ? "bg-[linear-gradient(268deg,_#211f54_11.09%,_#0161ab_98.55%)] text-white"
                  : "hover:bg-gray-100"
                }`}
            >
              {pageNum}
            </button>
          );
        })}

        {totalPages > 5 && currentPage < totalPages - 2 && (
          <span className="px-2">...</span>
        )}

        {totalPages > 5 && currentPage < totalPages - 1 && (
          <button
            onClick={() => handlePageChange(totalPages)}
            className={`px-3 py-1 rounded border text-sm ${currentPage === totalPages
                ? "bg-blue-500 text-white"
                : "hover:bg-gray-100"
              }`}
          >
            {totalPages}
          </button>
        )}

        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={`px-3 py-1 rounded border text-sm ${currentPage === totalPages
              ? "bg-gray-100 cursor-not-allowed"
              : "hover:bg-gray-100"
            }`}
        >
          Next
        </button>
      </div>


    </div>
  );
  else return ""
}
