import React from "react";

export default function Popup({
  isOpen,
  title = "Are you sure?",
  message = "",
  confirmText = "Confirm",
  cancelText = "Cancel",
  onConfirm,
  onCancel,
  actionLoading=false
  
}) {

    console.log("actionLoading in popup ",actionLoading)
  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 z-50 flex items-center justify-center"
      style={{backgroundColor: "rgba(0,0,0,0.7)"}}
    >
      <div className="bg-white rounded-2xl shadow-lg max-w-sm w-full p-6">
        <h3 className="text-xl font-semibold mb-2 ">{title}</h3>
        {message && <p className="text-gray-600 mb-4">{message}</p>}
        <div className="flex justify-end space-x-3">
          <button
            onClick={onCancel}
            className="px-4 py-2 rounded-lg border border-gray-300 hover:bg-gray-100 transition"
          >
            {cancelText}
          </button>
          <button
            onClick={onConfirm}
            className="px-4 py-2 rounded-lg global_linear_gradient text-white "
          >{
            actionLoading?"...Loading":confirmText
          }
            
          </button>
        </div>
      </div>
    </div>
  );
}
