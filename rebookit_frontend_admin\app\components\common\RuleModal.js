import React, { useState, useEffect } from 'react';
import { MdClose } from 'react-icons/md';
import { toast } from 'react-toastify';
import { updateIndividualRule, createIndividualPricingRuleInResource } from '../../service/adManagement';

function findDuplicateRule(rules, ruleForm, editingRuleIdx) {
  for (let i = 0; i < rules.length; i++) {
    if (editingRuleIdx !== null && i === editingRuleIdx) continue;
    const r = rules[i];
    if (r.name === ruleForm.name) return 'name';
    if (r.color === ruleForm.color) return 'color';
  }
  return null;
}

function normalizeDateInput(date) {
  if (!date) return '';
  if (/^\d{4}-\d{2}-\d{2}$/.test(date)) return date;
  const d = new Date(date);
  return d.getFullYear() + '-' + String(d.getMonth() + 1).padStart(2, '0') + '-' + String(d.getDate()).padStart(2, '0');
}

export default function RuleModal({
  visible,
  onClose,
  onSave,
  initialRule,
  isEdit,
  rules = [],
  editingRuleIdx = null,
  setCalendarRefresh,
  resourceId,
  ruleId,
  onRuleSaved,
}) {


  const todayStr = new Date().toISOString().split('T')[0];
  const [ruleForm, setRuleForm] = useState({
    name: '',
    isActive: true,
    startDate: '',
    endDate: '',
    price: '',
    currency: 'JMD',
    priority: null,
    color: '',
  });
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (initialRule) {
      setRuleForm({
        ...initialRule,
        isActive: initialRule.isActive !== undefined ? initialRule.isActive : true,
      });
    } else {
      setRuleForm({
        name: '',
        isActive: true,
        startDate: '',
        endDate: '',
        price: '',
        currency: 'JMD',
        priority: null,
        color: '',
      });
    }
  }, [initialRule, visible]);

  const isRuleFormValid = !!(
    ruleForm.name &&
    ruleForm.startDate &&
    ruleForm.endDate &&
    ruleForm.color &&
    ruleForm.price
  );


  const handleSave = async () => {
    if (!isRuleFormValid) return;
    const duplicateKey = findDuplicateRule(rules, ruleForm, editingRuleIdx);
    if (!isEdit && duplicateKey) {
      toast.warn(`Rule already exists with ${duplicateKey}`);
      return;
    }
    const ruleData = {
      name: ruleForm.name,
      price: ruleForm.price,
      startDate: normalizeDateInput(ruleForm.startDate),
      endDate: normalizeDateInput(ruleForm.endDate),
      color: ruleForm.color,
      ...(ruleForm.priority !== null && ruleForm.priority !== undefined && ruleForm.priority !== ''
        ? { priority: Number(ruleForm.priority) }
        : { priority: '' }),
      isActive: ruleForm.isActive,
    };

    const payload = isEdit
      ? {
          resourceId,
          ruleId,
          pricingRules: ruleData,
        }
      : {
          resourceId,
          rules: ruleData,
        };

    setSaving(true);
    try {
      let res;
      if (isEdit) {
        res = await updateIndividualRule(payload);
      } else {
        res = await createIndividualPricingRuleInResource(payload);
      }
      if (res && (res.status === 200 || res.status === 201)) {
        toast.success(isEdit ? 'Rule updated successfully!' : 'Rule created successfully!');
        if (onSave) onSave(payload.rules);
        if (onRuleSaved) onRuleSaved();
        if (setCalendarRefresh) setCalendarRefresh(v => v + 1);
        setSaving(false);
        onClose();
      } else {
        setSaving(false);
        toast.error(res?.data?.message || (isEdit ? 'Failed to update rule' : 'Failed to create rule'));
      }
    } catch (err) {
      setSaving(false);
      toast.error(err?.response?.data?.message || err?.message || (isEdit ? 'Failed to update rule' : 'Failed to create rule'));
    }
  };

  if (!visible) return null;

  return (
    <div
      className="border-2 fixed flex-col inset-0 flex items-center justify-center z-50 "
      style={{ background: 'rgba(0, 0, 0, 0.40)' }}
      onClick={() => {
        onClose();
      }}
    >
      <div
        onClick={e => e.stopPropagation()}
        className="w-[643px] h-fit min-w-[200px] rounded-[16.87px] border border-[#F8F9FA] bg-white shadow-[0px_3.374px_16.87px_0px_rgba(238,238,238,0.50)] p-8 relative"
      >
        <button className="absolute top-2 right-2 text-2xl" onClick={onClose}>
          <MdClose />
        </button>
        <div className="flex flex-col items-start gap-[10px] flex-shrink-0 p-[20px] pt-[20px] pb-[20px] pl-[16px] pr-[16px] rounded-[16px] border border-[#EFF1F4] bg-white">
          <div className="flex items-center justify-between mb-4 w-full">
            <div className="flex items-center justify-end gap-2">
              <span className="text-sm text-gray-500 mr-2">Enabled</span>
              <button
                type="button"
                aria-pressed={ruleForm.isActive}
                onClick={() => setRuleForm(f => ({ ...f, isActive: !f.isActive }))}
                className={`relative w-10 h-6 transition-colors duration-300 rounded-full focus:outline-none border-0 ${ruleForm.isActive ? 'bg-gradient-to-r from-[#24194B] to-[#0B5B8C]' : 'bg-gray-300'}`}
                style={{
                  minWidth: '40px',
                  minHeight: '24px',
                  boxShadow: ruleForm.isActive ? '0 0 0 2px #0B5B8C22' : undefined,
                  padding: 0,
                }}
              >
                <span
                  className={`absolute top-0.5 left-0.5 transition-all duration-300 w-5 h-5 rounded-full bg-white shadow ${ruleForm.isActive ? 'translate-x-4' : 'translate-x-0'}`}
                  style={{ boxShadow: '0 1px 4px 0 rgba(0,0,0,0.10)' }}
                />
              </button>
            </div>
          </div>
          <label className="block font-[poppins]  text-[#211F54] text-sm">
            Rule Name<span className="text-[#E1020C]">*</span>
          </label>
          <input
            className="w-full border text-sm border-gray-200 rounded-lg px-4 py-3 font-[poppins] bg-white mb-4"
            placeholder="Enter Rule name"
            value={ruleForm.name}
            maxLength={100}
            onChange={e => setRuleForm(f => ({ ...f, name: e.target.value }))}
          />
          <div className="mb-4 flex gap-4 w-full">
            <div className="flex-1">
              <label className="block font-[poppins] mb-1 text-[#211F54] text-sm">
                Start Date<span className="text-[#E1020C]">*</span>
              </label>
              <input
                type="date"
                className="w-full border text-sm border-gray-200 rounded-lg px-4 py-3 font-[poppins] bg-white"
                value={normalizeDateInput(ruleForm.startDate)}
                onChange={e => setRuleForm(f => ({ ...f, startDate: e.target.value, endDate: '' }))}
              />
            </div>
            <div className="flex-1">
              <label className="block font-[poppins] mb-1 text-[#211F54] text-sm">
                End Date<span className="text-[#E1020C]">*</span>
              </label>
              <input
                type="date"
                className="w-full border text-sm border-gray-200 rounded-lg px-4 py-3 font-[poppins] bg-white"
                value={normalizeDateInput(ruleForm.endDate)}
                min={ruleForm.startDate ? new Date(new Date(ruleForm.startDate).getTime() + 24*60*60*1000).toISOString().split('T')[0] : todayStr}
                onChange={e => {
                  const val = e.target.value;
                  if (ruleForm.startDate && val <= ruleForm.startDate) {
                    toast.warn('End date must be after start date');
                    return;
                  }
                  setRuleForm(f => ({ ...f, endDate: val }));
                }}
              />
            </div>
          </div>
          <div className="mb-4 w-full">
            <label className="block font-[poppins] mb-1 text-[#211F54] text-sm">
              Set price<span className="text-[#E1020C]">*</span>
            </label>
            <div className="flex items-center w-full bg-white border border-gray-200 rounded-lg overflow-hidden">
              <select
                className="bg-transparent px-4 py-3 font-[poppins] focus:outline-none border-none text-[#211F54]"
                value={ruleForm.currency}
                disabled
                style={{ cursor: 'not-allowed' }}
              >
                <option value="JMD">J$</option>
              </select>
              <input
                type="number"
                min="1"
                maxLength={10}
                className="flex-1 px-4 text-sm py-3 ml-4 font-[poppins] bg-transparent focus:outline-none border-l border-gray-200 text-[#211F54]"
                value={ruleForm.price}
                onChange={e => {
                  let val = e.target.value.replace(/^0+/, ''); // Remove leading zeros
                  // Only allow empty or numbers 1-9999999999
                  if (val === '' || (/^\d{1,10}$/.test(val) && val !== '0')) {
                    setRuleForm(f => ({ ...f, price: val }));
                  }
                }}
                placeholder="Enter price"
              />
            </div>
          </div>
          <div className="mb-4 flex gap-4 items-center w-full ">
            <div className="flex-1">
              <label className="block font-[poppins] mb-1 text-[#211F54] text-sm">
                Set Priority
              </label>
              <div className="flex items-center gap-2 w-full">
                <select
                  className="w-full border border-gray-200 rounded-lg px-2 py-2 font-[poppins] bg-white"
                  value={ruleForm.priority || ''}
                  onChange={e => {
                    setRuleForm(f => ({ ...f, priority: e.target.value }));
                  }}
                >
                  <option className="text-sm border-gray-200" value="">Select priority</option>
                  {[...Array(10)].map((_, i) => {
                    const val = (i + 1) * 10;
                    return (
                      <option key={val} value={val}>{val}</option>
                    );
                  })}
                </select>
              </div>
            </div>
            <div className="flex-1">
              <label className="block font-[poppins] mb-1 text-[#211F54] text-sm">
                Choose Colour<span className="text-[#E1020C]">*</span>
              </label>
              <div className="flex items-center w-full">
                <div className="">
                  <input
                    type="color"
                    value={ruleForm.color}
                    onChange={e => setRuleForm(f => ({ ...f, color: e.target.value }))}
                    className="flex-1 cursor-pointer h-[48px] border border-gray-200 rounded-l-lg  font-[poppins] bg-white text-sm"
                    style={{
                      backgroundColor: ruleForm.color,
                      color: '#211F54',
                      transition: 'background 0.2s',
                      fontWeight: 500,
                    }}
                  />
                </div>
                <input
                  type="text"
                  value={ruleForm.color}
                  placeholder="Select color"
                  onChange={e => setRuleForm(f => ({ ...f, color: e.target.value }))}
                  className="flex-1 border border-gray-200 rounded-r-lg px-2 h-[48px] font-[poppins] bg-white text-sm placeholder:text-sm placeholder:border-gray-200"
                  style={{ borderLeft: 'none' }}
                />
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-end gap-4 mt-6">
          <button
            className="px-6 py-2 rounded border border-gray-300 bg-white text-gray-700 font-semibold"
            onClick={() => {
              onClose();
            }}
            disabled={saving}
          >
            Cancel
          </button>
          <button
            className={`px-6 py-2 rounded font-semibold ${isRuleFormValid ? 'bg-gradient-to-r from-[#211F54] to-[#0161AB] text-white' : 'bg-gray-400 text-white cursor-not-allowed'}`}
            disabled={!isRuleFormValid || saving}
            onClick={handleSave}
          >
            {saving ? 'Saving...' : isEdit ? 'Update Rule' : 'Submit Rule'}
          </button>
        </div>
      </div>
    </div>
  );
} 