import React from 'react'

export default function SubmitButton({isLoading,InnerDiv,type,btnAction}) {
  return (
     <button type={type} onClick={btnAction} className='mt-4 global_linear_gradient text-white flex justify-center items-center py-2 px-5 rounded-full w-full gap-3.5' >
            {isLoading ? <>
                <svg
                    className="w-4 h-4 animate-spin text-white"
                    fill="none"
                    viewBox="0 0 24 24"
                >
                    <circle
                        className="opacity-25"
                        cx="12"
                        cy="12"
                        r="10"
                        stroke="currentColor"
                        strokeWidth="4"
                    ></circle>
                    <path
                        className="opacity-75"
                        fill="currentColor"
                        d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"
                    ></path>
                </svg>
                Loading...
            </> : <InnerDiv />}

        </button>
  )
}
