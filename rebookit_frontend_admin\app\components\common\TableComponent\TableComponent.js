"use client";

import {useEffect, useRef, useState} from "react";
import {CiEdit} from "react-icons/ci";
import {VscOpenPreview} from "react-icons/vsc";
import {useRouter} from "next/navigation";
import {randomColor<PERSON>ick<PERSON><PERSON><PERSON>, shortNameHandler} from "@/app/utils/utils";
import "./TableComponent.scss";
import Image from "next/image";
import Link from "next/link";

function TableComponent(props) {
  const navigate = useRouter();
  const {
    tableDataObj,
    selectedAll,
    selectAllHandler,
    selectedData,
    singleSelectHandler,
    openActionDropdown,
    handleOpenInclusionPopup,
    loading,
    closeDropdown,
  } = props;
  const [isList, setIsList] = useState(false);
  const [listOpen, setListOpen] = useState(false);

  const dropdownRef = useRef(null);

  // Handle clicks outside the dropdown
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        closeDropdown();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [openActionDropdown]);

  console.log("tableDataObj", tableDataObj);
  console.log("selectedData",selectedData)
  const checkboxElem = (itemId, key) => (
    <span className="absolute left-6 top-[35%]">
      {key === 0 && (
        <div className="relative inline-block h-4 w-4">
          {/* <input
            type="checkbox"
            checked={selectedData.includes(itemId)}
            onChange={() => singleSelectHandler(itemId)}
            className="absolute h-full w-full cursor-pointer appearance-none rounded-md border border-[#D0D5DD] bg-white 
                          checked:border-transparent checked:bg-[linear-gradient(268deg,#211F54_11.09%,#0161AB_98.55%)]
                          peer"
          /> */}
          <svg
            className={`absolute left-1/2 top-1/2 h-3 w-3 -translate-x-1/2 -translate-y-1/2 transform text-white 
                           opacity-0 peer-checked:opacity-100 pointer-events-none`}
            viewBox="0 0 20 20"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M16.6668 5.8335L7.50016 15.0002L3.3335 10.8335"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
      )}
    </span>
  );

  const tableDataContent = {
    default: (val, k) => (
      <td key={k} className={`pl-8 ${val?.class} text-xs`}>
        <h5
          title={typeof val?.value === "string" ? val?.value : ""}
          className={`text-left text-xs break-all break-words line-clamp-1 leading-5 ${
            val?.href && "hover:underline cursor-pointer text-blue-400"
          }`}
          onClick={() => {
            val?.href && navigate.push(val?.href);
          }}
        >
          {val?.value}
        </h5>
      </td>
    ),
    withoutLineClamp: (val, k) => (
      <td key={k} className={`pl-8 ${val?.class} w-[250px]`}>
        <h5
          title={val?.value || ""}
          className="text-left text-xs break-all break-words line-clamp-2 capitalize leading-5 cursor-pointer"
        >
          {val?.value}
        </h5>
      </td>
    ),
    link: (val, k) => (
      <td key={k} className={`pl-8 relative ${val?.class}`}>
        {k === 0 && selectedAll && (
          // <input
          //   type="checkbox"
          //   checked
          //   className="accent-transparent h-[13px] cursor-pointer absolute left-2 top-6 checked:bg-transparent checked:text-transparent invert border-0 active:bg-transparent pointer-events-none"
          // />
          <></>
        )}
        <h5
          title={val?.value || ""}
          className={`text-left ${
            val?.href && "hover:underline cursor-pointer text-blue-400"
          }`}
          onClick={() => val?.href && navigate.push(val?.href)}
        >
          {val?.value}
        </h5>
      </td>
    ),
    profileMix: (val, k) => (
      <td
        key={k}
        className={`${
          k === 0 ? "pl-14" : "pl-8"
        } flex justify-start w-[100%] items-center gap-2 h-[inherit] text-sm text-left relative ${
          val?.class
        }`}
      >
        {checkboxElem(val?.id, k)}
        <Link
          href={`/book-detail?id=${val.id}`}
          className="flex items-center gap-2 w-full cursor-pointer"
        >
          <div className="w-[44px] h-[50px] relative overflow-hidden">
            <img
              src={val?.image || "/images/image_1.jpg"}
              alt="img"
              fill
              className="object-cover w-full h-full"
              sizes="33w"
            />
          </div>

          <div className="w-[90%]">
            <p
              title={val?.name || ""}
              className={`text-left capitalize break-all  line-clamp-1 leading-5 font-medium ${
                val?.link
                  ? "hover:underline hover:cursor-pointer underline-offset-2"
                  : ""
              }`}
              onClick={() => val?.link && navigate.push(val?.link)}
            >
              {val?.name}
            </p>
            {val?.secondaryValue && (
              <p
                title={val?.secondaryValue || ""}
                className="leading-5 text-[#9A9A9A] break-all line-clamp-1"
              >
                {val?.secondaryValue || "-"}
              </p>
            )}
          </div>
        </Link>
      </td>
    ),
    secondary: (val, k) => (
      <td key={k} className={`pl-8 ${val?.class} text-sm`}>
        {val?.value && (
          <p
            title={val?.value || ""}
            className="text-left capitalize break-all line-clamp-1"
          >
            {val?.value || "-"}
          </p>
        )}
        <p
          className="inline-flex underline text-blue-400 cursor-pointer text-[11px]"
          onClick={() => val?.href && navigate.push(val?.href)}
        >
          {val?.textToShowAsLInk || "View History"}
        </p>
      </td>
    ),
    popUp: (val, k) => (
      <td key={k} className={`pl-8 ${val?.class} relative`}>
        {val?.value && (
          <p
            title={val?.value || ""}
            className=" text-left capitalize break-all line-clamp-1"
          >
            {val?.value || "-"}
          </p>
        )}
        <p
          className="inline-flex cursor-pointer text-[11px]"
          onClick={() => handleOpenInclusionPopup(val.dataId)}
        >
          {val?.textToShowAsLInk}
        </p>
        {val.isDialogOpen && (
          <>
            <div className="absolute right-[80%] border border-slate-300 shadow-md rounded-lg bg-white z-20 flex justify-center  p-[20px] w-fit min-w-[700px] min-h-[200px]">
              <button
                onClick={() => handleOpenInclusionPopup(val.dataId)}
                type="button"
                className="text-black-600 hover:text-red-800 focus:outline-none text-[17px] absolute top-[10px] right-[15px]"
              >
                X
              </button>
              <div className="relative w-[96%] mt-3 col-span-2">
                <h2 className="text-slate-600 text-[15px] text-600 mb-2">
                  {" "}
                  Inclusions Details{" "}
                  <span className="text-[12px]">(Plan.Id : {val.dataId})</span>
                </h2>
                <div className="border-collapse border border-slate-200 rounded-md overflow-hidden w-full">
                  <table className="table-auto w-full border-0">
                    <thead>
                      <tr className="border-b border-solid border-slate-300 text-small text-left *:pl-2 bg-slate-100 text-black">
                        <th className="w-[30%]">Service</th>
                        <th className="w-[30%]">No. of Services</th>
                        <th className="w-[20%]">Discount</th>
                      </tr>
                    </thead>

                    <tbody className="rounded-md">
                      {val?.data?.length > 0 ? (
                        val?.data?.map((item, index) => (
                          <tr
                            key={index}
                            className="border-b last:border-b-0 *:border-r *:border-[#eee] border-[#eee] *:py-1 *:px-2 text-small leading-normal group"
                          >
                            <td>{item?.serviceName}</td>
                            <td>
                              {item?.noOfServices > 0
                                ? item?.noOfServices
                                : "all"}
                            </td>
                            <td className="w-[80%] last:border-r-0">
                              <div className="flex justify-between">
                                <p>{item?.discount}%</p>
                              </div>
                            </td>
                          </tr>
                        ))
                      ) : (
                        <tr>
                          <td colSpan={3}>No data found</td>
                        </tr>
                      )}
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </>
        )}
      </td>
    ),
    status: (val, k) => (
      <td key={k} className={`px-8 text-left ${val?.class} text-sm`}>
        <p
          className={`relative inline-flex bg-opacity-10 font-bold text-smaller rounded-full px-2 capitalize ${
            (val?.customValue && val?.customValue?.status) ||
            (val.value && val.value.active)
              ? "bg-green-600 text-green-800"
              : "bg-red-500 text-red-800" // 'bg-danger'
          }
                        ${val?.active && "shadow-3"}
                    `}
        >
          {val?.customValue
            ? val?.customValue?.message
            : val.value.active
            ? "active"
            : "inactive"}
        </p>
      </td>
    ),
    locationElem: (val, k) => (
      <td
        key={k}
        className={`max-w-[220px] pl-8  relative ${val?.class} py-2 `}
      >
        <div className="flex flex-col gap-1">
          {/* First Value - Address */}
          <div className="flex items-start gap-1">
            {val?.icon1 && (
              <span className="flex-shrink-0 mt-0.5">{val.icon1}</span>
            )}
            <p
              title={val?.first_value || ""}
              className="capitalize text-sm line-clamp-2 leading-tight break-words overflow-hidden"
            >
              {val?.first_value}
            </p>
          </div>

          {/* Second Value */}
          {val?.second_value && (
            <div className="flex items-center gap-1 mt-0.5">
              {val?.icon2 && <span className="flex-shrink-0">{val.icon2}</span>}
              <p
                title={val?.second_value || ""}
                className="capitalize truncate text-sm text-gray-500"
              >
                {val?.second_value}
              </p>
            </div>
          )}
        </div>
      </td>
    ),
    actionElem: (val, k) => (
      <td key={k} className={`pl-8 relative  ${val?.class}`}>
        <div className="flex gap-4 items-center">
          <div
            className={`rounded-full shadow-2 relative flex h-[14px] w-6 cursor-pointer ${
              isList ? "bg-emerald-400" : "bg-white"
            }`}
            onClick={() => setIsList(!isList)}
          >
            <span
              className={`absolute top-[2px] rounded-full z-9 w-[45%] h-[10px] duration-200 ${
                isList
                  ? "translate-x-full bg-white right-[13px]"
                  : "bg-slate-200 left-[2px]"
              }`}
            ></span>
          </div>

          <CiEdit className="w-4.5 h-4.5  cursor-pointer" />
        </div>
      </td>
    ),
    actionMultiButtons: (val, k) => (
      <td key={k} className={`pl-8 relative ${val?.class}`}>
        <div className="flex gap-4 items-center justify-center">
          {/* edit button ========================*/}
          {val.buttons &&
            val.buttons.map((button) => {
              if (button.type === "edit") {
                return (
                  <div
                    title="Edit"
                    className="flex justify-end gap-2"
                    key="edit-button"
                  >
                    <button
                      className=" text-[5px] w-[100%] flex align-middle justify-start hover:bg-dark"
                      onClick={() => button.function(val.value)} // Navigate to edit route
                    >
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        className="text-themeColor-500 w-5 h-5"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
                      </svg>
                    </button>
                  </div>
                );
              } else if (button.type === "toggle") {
                return (
                  <div
                    title="Active/Block"
                    className={`rounded-full shadow-2 relative flex h-[14px] w-6 cursor-pointer ${
                      val.status ? "bg-emerald-400" : "bg-red-300"
                    }`}
                    onClick={() => {
                      button.function(val.value); // Call the toggle function
                    }}
                    key="toggle-button"
                  >
                    <span
                      className={`absolute top-[2px] rounded-full z-9 w-[45%] h-[10px] duration-200 ${
                        val.status
                          ? "translate-x-full bg-white right-[13px]"
                          : "bg-slate-200 left-[2px]"
                      }`}
                    ></span>
                  </div>
                );
              } else if (button.type === "view") {
                return (
                  <div
                    title="View"
                    className="flex justify-end gap-2"
                    key="view-button"
                  >
                    <button
                      className="w-[100%] flex align-middle justify-start hover:bg-dark"
                      onClick={() => button.function(val.value)} // Navigate to view route
                    >
                      <span className="text-xl">
                        <VscOpenPreview />
                      </span>
                    </button>
                  </div>
                );
              } else if (button.type === "action_btns") {
                return (
                  <div
                    key={`btn-group-${k}`}
                    className="flex justify-center items-center gap-1.5"
                  >
                    {val.value === "pending" ? (
                      <>
                        <button
                          className={button.pendingClass1 || button.class1}
                          onClick={() => button.onClick1?.()}
                        >
                          {button.pendingBtn1 || button.btn1}
                        </button>
                        <button
                          className={button.pendingClass2 || button.class2}
                          onClick={() => button.onClick2?.()}
                        >
                          {button.pendingBtn2 || button.btn2}
                        </button>
                      </>
                    ) : (
                      <button
                        className={button.class1}
                        onClick={() => button.onClick1?.()}
                      >
                        {button.btn1}
                      </button>
                    )}
                  </div>
                );
              }
              return null; // Return null for other types
            })}

          {/* <CiEdit className="w-4.5 h-4.5 cursor-pointer" /> */}
        </div>
      </td>
    ),
    dropdownElem: (val, k) => (
      <td key={k} className={`pl-3 relative ${val?.class || ""}`}>
        <div className="relative inline-block">
          <button
            aria-label="Actions"
            aria-expanded={openActionDropdown === val?.id}
            onClick={() => val?.dropdownHandlerFn(String(val?.id))}
            className="p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-200 transition-colors"
          >
            <svg
              stroke="gray"
              fill="gray"
              viewBox="0 0 16 16"
              height="1em"
              width="1em"
            >
              <path d="M9.5 13a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0zm0-5a1.5 1.5 0 1 1-3 0 1.5 1.5 0 0 1 3 0z" />
            </svg>
          </button>

          {openActionDropdown === val?.id && (
            <div
              ref={dropdownRef}
              className="absolute top-[0px] right-[25px] z-10 mt-1 min-w-[100px]   rounded-md bg-white shadow-lg border border-slate-200 py-1"
            >
              {val?.dropdownList?.map((item, index) => (
                <button
                  key={`${val.id}-${index}`}
                  onClick={(e) => {
                    e.stopPropagation();
                    item?.clickFn?.();
                    // closeDropdown();
                  }}
                  className="w-full text-left px-3 py-1.5  text-sm hover:bg-gray-100 transition-colors"
                >
                  {item?.label}
                </button>
              ))}
            </div>
          )}
        </div>
      </td>
    ),
  };

  return (
    <div className="min-h-full h-full max-w-full overflow-x-auto rounded-md scrollbar-thin border border-solid border-slate-300 dark:bg-boxdark table-component-container">
      <table className="w-full table-auto">
        <thead>
          <tr className="h-11 dark:bg-meta-4 text-sm border-b border-solid border-slate-300 px-5 text-left bg-slate-100">
            {tableDataObj?.headings?.map((heading, key, arr) => (
              <th
                key={key}
                className={`font-medium relative w-[${heading.width}%] ${
                  key === 0 ? "pl-14" : "pl-8"
                } ${key === arr.length - 1 && "pr-2"}`}
              >

                {key === 0 && (
                  <span className="absolute left-6 top-1/4">
                    <div className="relative inline-block h-5 w-5">
                      <svg
                        className={`absolute left-1/2 top-1/2 h-3 w-3 -translate-x-1/2 -translate-y-1/2 transform text-white 
                                                         opacity-0 peer-checked:opacity-100 pointer-events-none`}
                        viewBox="0 0 20 20"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M16.6668 5.8335L7.50016 15.0002L3.3335 10.8335"
                          stroke="currentColor" // Changed to currentColor
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                    </div>
                  </span>
                )}
                <span
                  className={`block leading-normal ${
                    heading?.center ? "text-center" : "w-max"
                  }`}
                >
                  {heading.key}
                </span>
              </th>
            ))}
          </tr>
        </thead>

        {!loading ? (
          tableDataObj?.content?.length > 0 ? (
            <tbody>
              {tableDataObj?.content?.map((item, key) => (
                <tr
                  key={key}
                  className="h-[72px] text-small border-b border-[#eee] last:border-b-0"
                >
                  {Object.values(item).map(
                    (val, k) => tableDataContent[val?.type]?.(val, k) ?? null
                  )}
                </tr>
              ))}
            </tbody>
          ) : (
            <tbody>
              <tr>
                <td
                  colSpan={tableDataObj?.headings?.length}
                  className="h-[530px] text-center align-middle"
                >
                  <div className="flex justify-center items-center h-full">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="350"
                      height="280"
                      viewBox="0 0 432 330"
                      fill="none"
                    >
                      <path
                        d="M215.957 329.093C98.9 329.093 0 317.397 0 303.553C0 289.709 98.9 278.014 215.957 278.014C333.014 278.014 431.914 289.714 431.914 303.553C431.914 317.392 333.014 329.093 215.957 329.093Z"
                        fill="#e2e8f0"
                      />
                      <path
                        d="M79.9759 302.395C75.9567 302.391 72.1034 300.792 69.2615 297.95C66.4196 295.108 64.8211 291.254 64.8169 287.235V84.375C64.8153 80.3498 66.4104 76.4883 69.2522 73.6376C72.0941 70.7869 75.9507 69.1799 79.9759 69.169H344.376C348.401 69.1799 352.258 70.7869 355.1 73.6376C357.941 76.4883 359.536 80.3498 359.535 84.375V287.235C359.531 291.254 357.932 295.108 355.09 297.95C352.248 300.792 348.395 302.391 344.376 302.395H79.9759Z"
                        fill="white"
                        stroke="#e2e8f0"
                        strokeWidth="2"
                      />
                      <path
                        d="M344.376 69.754C348.246 69.7646 351.954 71.3099 354.686 74.0509C357.418 76.7919 358.952 80.5048 358.95 84.375V287.236C358.946 291.1 357.409 294.805 354.677 297.537C351.945 300.27 348.24 301.807 344.376 301.812H79.9759C76.1118 301.807 72.4073 300.27 69.6751 297.537C66.9429 294.805 65.4062 291.1 65.4019 287.236V84.375C65.4001 80.5048 66.9334 76.7919 69.6656 74.0509C72.3979 71.3099 76.1058 69.7646 79.9759 69.754H344.376ZM344.376 68.585H79.9759C75.7947 68.5921 71.7874 70.2595 68.8352 73.2205C65.883 76.1815 64.2276 80.1937 64.2329 84.375V287.236C64.2312 289.304 64.6372 291.352 65.4278 293.263C66.2183 295.174 67.3779 296.91 68.8401 298.372C70.3023 299.834 72.0384 300.994 73.9492 301.785C75.86 302.575 77.908 302.982 79.9759 302.98H344.376C346.444 302.982 348.492 302.575 350.403 301.785C352.313 300.994 354.05 299.834 355.512 298.372C356.974 296.91 358.134 295.174 358.924 293.263C359.715 291.352 360.121 289.304 360.119 287.236V84.375C360.124 80.1937 358.469 76.1815 355.517 73.2205C352.564 70.2595 348.557 68.5921 344.376 68.585Z"
                        fill="#e2e8f0"
                        stroke="#e2e8f0"
                        strokeWidth="4"
                      />
                      <path
                        d="M64.8169 121.715V84.376C64.8153 80.3508 66.4104 76.4893 69.2522 73.6386C72.0941 70.7879 75.9507 69.1809 79.9759 69.17H344.376C348.401 69.1806 352.258 70.7875 355.1 73.6382C357.942 76.4889 359.537 80.3506 359.536 84.376V121.716L64.8169 121.715Z"
                        fill="#e2e8f0"
                        stroke="#e2e8f0"
                        strokeWidth="2"
                      />
                      <path
                        d="M344.376 69.754C348.246 69.7646 351.954 71.3099 354.686 74.0509C357.418 76.7919 358.952 80.5048 358.95 84.375V121.13H65.4019V84.375C65.4001 80.5048 66.9334 76.7919 69.6656 74.0509C72.3979 71.3099 76.1058 69.7646 79.9759 69.754H344.376ZM344.376 68.585H79.9759C75.7947 68.5921 71.7874 70.2595 68.8352 73.2205C65.883 76.1815 64.2276 80.1937 64.2329 84.375V122.299H360.119V84.375C360.124 80.1937 358.469 76.1815 355.517 73.2205C352.564 70.2595 348.557 68.5921 344.376 68.585Z"
                        fill="black"
                        stroke="#e2e8f0"
                        strokeWidth="2"
                      />
                      <path
                        d="M143.045 103.436C141.66 103.436 140.307 103.025 139.156 102.256C138.005 101.487 137.108 100.394 136.578 99.1148C136.048 97.8357 135.909 96.4282 136.179 95.0704C136.45 93.7125 137.116 92.4652 138.095 91.4863C139.074 90.5073 140.321 89.8406 141.679 89.5705C143.037 89.3004 144.445 89.439 145.724 89.9688C147.003 90.4987 148.096 91.3959 148.865 92.547C149.634 93.6982 150.045 95.0515 150.045 96.436C150.042 98.2917 149.304 100.071 147.992 101.383C146.68 102.695 144.901 103.433 143.045 103.436Z"
                        fill="#e2e8f0"
                      />
                      <path
                        d="M143.045 90.013C144.314 90.013 145.555 90.3893 146.61 91.0945C147.665 91.7996 148.488 92.8018 148.974 93.9743C149.459 95.1469 149.586 96.4371 149.339 97.6819C149.091 98.9267 148.48 100.07 147.582 100.967C146.685 101.865 145.542 102.476 144.297 102.724C143.052 102.971 141.762 102.844 140.589 102.359C139.417 101.873 138.415 101.05 137.709 99.9951C137.004 98.9398 136.628 97.6992 136.628 96.43C136.63 94.7287 137.307 93.0978 138.51 91.8948C139.713 90.6919 141.344 90.0151 143.045 90.013ZM143.045 88.844C141.545 88.844 140.078 89.2889 138.83 90.1225C137.583 90.956 136.611 92.1408 136.036 93.527C135.462 94.9131 135.312 96.4384 135.605 97.91C135.897 99.3815 136.62 100.733 137.681 101.794C138.742 102.855 140.093 103.578 141.565 103.87C143.037 104.163 144.562 104.013 145.948 103.439C147.334 102.864 148.519 101.892 149.353 100.645C150.186 99.397 150.631 97.9304 150.631 96.43C150.631 94.4181 149.831 92.4888 148.409 91.0662C146.986 89.6436 145.057 88.8443 143.045 88.844Z"
                        fill="black"
                        stroke="#94a3b8"
                        strokeWidth="2"
                      />
                      <path
                        d="M120.021 103.436C118.637 103.436 117.283 103.025 116.132 102.256C114.981 101.487 114.084 100.394 113.554 99.1148C113.024 97.8357 112.885 96.4282 113.156 95.0704C113.426 93.7125 114.092 92.4652 115.071 91.4863C116.05 90.5073 117.297 89.8406 118.655 89.5705C120.013 89.3004 121.421 89.439 122.7 89.9688C123.979 90.4987 125.072 91.3959 125.841 92.547C126.61 93.6982 127.021 95.0515 127.021 96.436C127.018 98.2917 126.28 100.071 124.968 101.383C123.656 102.695 121.877 103.433 120.021 103.436Z"
                        fill="#e2e8f0"
                      />
                      <path
                        d="M120.02 90.013C121.289 90.013 122.53 90.3893 123.585 91.0945C124.64 91.7996 125.463 92.8018 125.949 93.9743C126.434 95.1469 126.561 96.4371 126.314 97.6819C126.066 98.9267 125.455 100.07 124.558 100.967C123.66 101.865 122.517 102.476 121.272 102.724C120.027 102.971 118.737 102.844 117.564 102.359C116.392 101.873 115.39 101.05 114.685 99.9951C113.979 98.9398 113.603 97.6992 113.603 96.43C113.605 94.7287 114.282 93.0978 115.485 91.8948C116.688 90.6919 118.319 90.0151 120.02 90.013ZM120.02 88.844C118.52 88.844 117.053 89.2889 115.806 90.1225C114.558 90.956 113.586 92.1408 113.012 93.527C112.437 94.9131 112.287 96.4384 112.58 97.91C112.873 99.3815 113.595 100.733 114.656 101.794C115.717 102.855 117.069 103.578 118.54 103.87C120.012 104.163 121.537 104.013 122.923 103.439C124.309 102.864 125.494 101.892 126.328 100.645C127.161 99.397 127.606 97.9304 127.606 96.43C127.606 94.4181 126.806 92.4888 125.384 91.0662C123.961 89.6436 122.032 88.8443 120.02 88.844Z"
                        fill="black"
                        stroke="#94a3b8"
                        strokeWidth="2"
                      />
                      <path
                        d="M96.9961 103.436C95.6116 103.436 94.2582 103.025 93.1071 102.256C91.956 101.487 91.0588 100.394 90.5289 99.1148C89.9991 97.8357 89.8605 96.4282 90.1306 95.0704C90.4007 93.7125 91.0674 92.4652 92.0463 91.4863C93.0253 90.5073 94.2726 89.8406 95.6305 89.5705C96.9883 89.3004 98.3958 89.439 99.6749 89.9688C100.954 90.4987 102.047 91.3959 102.816 92.547C103.586 93.6982 103.996 95.0515 103.996 96.436C103.993 98.2917 103.255 100.071 101.943 101.383C100.631 102.695 98.8518 103.433 96.9961 103.436Z"
                        fill="#e2e8f0"
                      />
                      <path
                        d="M96.9962 90.013C98.2653 90.013 99.506 90.3893 100.561 91.0945C101.617 91.7996 102.439 92.8018 102.925 93.9743C103.41 95.1469 103.537 96.4371 103.29 97.6819C103.042 98.9267 102.431 100.07 101.534 100.967C100.636 101.865 99.4928 102.476 98.2481 102.724C97.0033 102.971 95.713 102.844 94.5405 102.359C93.3679 101.873 92.3657 101.05 91.6606 99.9951C90.9555 98.9398 90.5792 97.6992 90.5792 96.43C90.5813 94.7287 91.258 93.0978 92.461 91.8948C93.664 90.6919 95.2949 90.0151 96.9962 90.013ZM96.9962 88.844C95.4958 88.844 94.0291 89.2889 92.7816 90.1225C91.5341 90.956 90.5618 92.1408 89.9876 93.527C89.4134 94.9131 89.2632 96.4384 89.5559 97.91C89.8486 99.3815 90.5711 100.733 91.632 101.794C92.693 102.855 94.0447 103.578 95.5162 103.87C96.9877 104.163 98.513 104.013 99.8992 103.439C101.285 102.864 102.47 101.892 103.304 100.645C104.137 99.397 104.582 97.9304 104.582 96.43C104.582 94.4181 103.783 92.4888 102.36 91.0662C100.937 89.6436 99.008 88.8443 96.9962 88.844Z"
                        fill="black"
                        stroke="#94a3b8"
                        strokeWidth="2"
                      />
                      <path
                        d="M319.852 106.013L319.677 105.806C309.844 94.19 304.589 79.3844 304.899 64.1686C305.209 48.9529 311.062 34.3733 321.359 23.1671C331.657 11.9609 345.69 4.89857 360.826 3.30606C375.961 1.71355 391.157 5.70032 403.561 14.5179C415.966 23.3356 424.725 36.3776 428.195 51.1957C431.665 66.0138 429.606 81.5889 422.407 94.997C415.207 108.405 403.36 118.724 389.091 124.017C374.822 129.31 359.112 129.212 344.91 123.743L344.686 123.658L314.754 136.658L319.852 106.013Z"
                        fill="#e2e8f0"
                        stroke="#94a3b8"
                        strokeWidth="4"
                      />
                      <path
                        d="M367.436 3.169C376.547 3.17437 385.545 5.18506 393.792 9.05836C402.039 12.9317 409.332 18.5726 415.154 25.581C420.976 32.5893 425.184 40.7931 427.48 49.6103C429.775 58.4275 430.102 67.6418 428.437 76.5994C426.771 85.557 423.155 94.0383 417.844 101.441C412.533 108.844 405.657 114.988 397.706 119.435C389.754 123.883 380.921 126.526 371.833 127.176C362.745 127.827 353.625 126.469 345.121 123.2L344.674 123.028L344.235 123.219L315.507 135.7L320.386 106.379L320.475 105.844L320.124 105.431C312.45 96.4019 307.521 85.3647 305.92 73.6238C304.319 61.8829 306.112 49.9289 311.087 39.1745C316.063 28.4201 324.013 19.3147 333.998 12.9344C343.983 6.55408 355.586 3.16547 367.436 3.169ZM367.436 2C355.364 1.9979 343.544 5.45117 333.372 11.9518C323.2 18.4525 315.101 27.7289 310.032 38.685C304.964 49.6411 303.137 61.8191 304.768 73.7803C306.399 85.7414 311.419 96.9858 319.236 106.185L314.005 137.625L344.705 124.29C353.371 127.627 362.666 129.016 371.929 128.356C381.193 127.697 390.197 125.006 398.303 120.475C406.41 115.944 413.419 109.683 418.833 102.138C424.247 94.5932 427.934 85.9486 429.631 76.8183C431.328 67.688 430.994 58.296 428.653 49.3093C426.311 40.3226 422.02 31.9616 416.083 24.8202C410.147 17.6788 402.711 11.932 394.303 7.98805C385.896 4.04409 376.723 1.99968 367.436 2Z"
                        fill="black"
                        stroke="#cbd5e1"
                        strokeWidth="2"
                      />
                      <path
                        d="M350.96 38.291C355.425 35.2803 360.625 33.5412 366.003 33.2599C371.381 32.9787 376.734 34.166 381.489 36.6945C386.243 39.2231 390.221 42.9977 392.994 47.6138C395.768 52.23 397.233 57.5137 397.233 62.899C397.233 65.14 397.704 70.607 397.233 72.705"
                        stroke="#cbd5e1"
                        strokeWidth="3"
                        strokeMiterlimit="10"
                      />
                      <path
                        d="M384.694 87.128C379.681 90.6679 373.695 92.5674 367.558 92.5654C361.421 92.5634 355.436 90.6598 350.426 87.1166C345.415 83.5734 341.626 78.5647 339.579 72.7795C337.531 66.9944 337.327 60.717 338.994 54.811"
                        stroke="#cbd5e1"
                        strokeWidth="3"
                        strokeMiterlimit="10"
                      />
                      <path
                        d="M386.438 61.679L395.448 74.018L407.396 63.637"
                        stroke="#cbd5e1"
                        strokeWidth="3"
                        strokeMiterlimit="10"
                      />
                      <path
                        d="M327.475 62.615L340.093 53.999L347.882 67.777"
                        stroke="#cbd5e1"
                        strokeWidth="3"
                        strokeMiterlimit="10"
                      />
                      <path
                        d="M36.6807 75.993C38.085 68.4077 41.6439 61.3872 46.932 55.7707C52.2201 50.1542 59.0136 46.1793 66.5007 44.321"
                        fill="white"
                      />
                      <path
                        d="M36.6807 75.993C38.085 68.4077 41.6439 61.3872 46.932 55.7707C52.2201 50.1542 59.0136 46.1793 66.5007 44.321"
                        stroke="#94a3b8"
                        strokeWidth="2"
                      />
                      <path
                        d="M36.6807 75.993C38.085 68.4077 41.6439 61.3872 46.932 55.7707C52.2201 50.1542 59.0136 46.1793 66.5007 44.321"
                        stroke="#e2e8f0"
                        strokeWidth="4"
                        strokeMiterlimit="10"
                      />
                      <path
                        d="M9.96387 71.595C12.1428 59.2651 17.7237 47.7894 26.0771 38.4623C34.4304 29.1352 45.2239 22.3278 57.2399 18.808"
                        fill="white"
                      />
                      <path
                        d="M9.96387 71.595C12.1428 59.2651 17.7237 47.7894 26.0771 38.4623C34.4304 29.1352 45.2239 22.3278 57.2399 18.808"
                        stroke="#e2e8f0"
                        strokeWidth="2"
                      />
                      <path
                        d="M9.96387 71.595C12.1428 59.2651 17.7237 47.7894 26.0771 38.4623C34.4304 29.1352 45.2239 22.3278 57.2399 18.808"
                        stroke="#e2e8f0"
                        strokeWidth="4"
                        strokeMiterlimit="10"
                      />
                      <path
                        d="M265.387 176.171C266.859 176.171 268.298 176.608 269.522 177.425C270.746 178.243 271.7 179.406 272.263 180.766C272.827 182.126 272.974 183.622 272.687 185.066C272.4 186.51 271.691 187.836 270.65 188.877C269.609 189.918 268.283 190.627 266.839 190.914C265.395 191.201 263.899 191.054 262.539 190.49C261.179 189.927 260.016 188.973 259.198 187.749C258.38 186.525 257.944 185.086 257.944 183.614C257.946 181.641 258.731 179.749 260.127 178.354C261.522 176.958 263.414 176.173 265.387 176.171ZM265.387 174.815C263.646 174.815 261.945 175.331 260.498 176.298C259.051 177.265 257.923 178.639 257.257 180.247C256.591 181.855 256.416 183.625 256.756 185.332C257.096 187.039 257.934 188.607 259.164 189.838C260.395 191.068 261.963 191.906 263.67 192.246C265.377 192.585 267.147 192.411 268.755 191.745C270.363 191.079 271.737 189.951 272.704 188.504C273.671 187.057 274.187 185.355 274.187 183.615C274.187 182.459 273.959 181.315 273.517 180.247C273.075 179.18 272.427 178.21 271.609 177.392C270.792 176.575 269.822 175.927 268.755 175.485C267.687 175.043 266.543 174.815 265.387 174.815Z"
                        stroke="#94a3b8"
                        strokeWidth="3"
                      />
                      <path
                        d="M158.846 176.171C160.318 176.171 161.757 176.608 162.981 177.425C164.205 178.243 165.159 179.406 165.722 180.766C166.286 182.126 166.433 183.622 166.146 185.066C165.859 186.51 165.15 187.836 164.109 188.877C163.068 189.918 161.742 190.627 160.298 190.914C158.854 191.201 157.358 191.054 155.998 190.49C154.638 189.927 153.475 188.973 152.657 187.749C151.839 186.525 151.403 185.086 151.403 183.614C151.405 181.641 152.19 179.749 153.586 178.354C154.981 176.958 156.873 176.173 158.846 176.171ZM158.846 174.815C157.105 174.815 155.404 175.331 153.957 176.298C152.51 177.265 151.382 178.639 150.716 180.247C150.05 181.855 149.875 183.625 150.215 185.332C150.555 187.039 151.393 188.607 152.623 189.838C153.854 191.068 155.422 191.906 157.129 192.246C158.836 192.585 160.606 192.411 162.214 191.745C163.822 191.079 165.196 189.951 166.163 188.504C167.13 187.057 167.646 185.355 167.646 183.615C167.646 182.459 167.418 181.315 166.976 180.247C166.534 179.18 165.886 178.21 165.068 177.392C164.251 176.575 163.281 175.927 162.214 175.485C161.146 175.043 160.002 174.815 158.846 174.815Z"
                        stroke="#94a3b8"
                        strokeWidth="3"
                      />
                      <path
                        d="M237.158 242.778C234.7 238.081 231.004 234.147 226.469 231.401C221.934 228.656 216.734 227.205 211.433 227.205C206.132 227.205 200.933 228.656 196.398 231.401C191.863 234.147 188.167 238.081 185.709 242.778"
                        fill="white"
                      />
                      <path
                        d="M237.158 242.778C234.7 238.081 231.004 234.147 226.469 231.401C221.934 228.656 216.734 227.205 211.433 227.205C206.132 227.205 200.933 228.656 196.398 231.401C191.863 234.147 188.167 238.081 185.709 242.778"
                        stroke="#94a3b8"
                        strokeWidth="4"
                      />
                    </svg>
                  </div>
                </td>
              </tr>
            </tbody>
          )
        ) : (
          <tbody>
            <tr>
              <td
                colSpan={tableDataObj?.headings?.length}
                // rowSpan={10}
                className="h-[630px] text-center align-middle"
              >
                <div className="flex justify-center items-center h-full">
                  {/* <i className="fas fa-check text-3xl text-green-500" /> */}
                  loading...
                </div>
              </td>
            </tr>
          </tbody>
        )}
      </table>
    </div>
  );
}

export default TableComponent;
