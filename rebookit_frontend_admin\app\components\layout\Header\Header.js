import React, {useEffect, useState} from "react";
import {usePathname, useRouter} from "next/navigation";

import Image from "next/image";
import Link from "next/link";
import {RxHamburgerMenu} from "react-icons/rx";

import flag from "@/public/flag.png";
import logo from "@/public/landing/fine_logo.png";
import headerCss from "./header.module.scss";

import {MdKeyboardArrowDown} from "react-icons/md";

import DropdownItem from "./dropDown";

import {IoIosArrowDown} from "react-icons/io";
import {LuBookmark} from "react-icons/lu";
import {RiUserLine} from "react-icons/ri";
import dynamic from "next/dynamic";
import {useSelector} from "react-redux";
import {USER_ROUTES} from "@/app/config/api";
import {getToken, removeToken} from "@/app/utils/utils";
import {createInitialsAvatar} from "../../common/InitialAvatar/CreateInitialAvatar";

const Search = dynamic(() => import("@/app/components/common/Search"));

export default function Header(props) {
  const {className} = props;
  const pageName = useSelector((state) => state.storeData.selectedPage);
  const router = useRouter();

  const pathname = usePathname();
  const whitePaths = [
    "/login",
    "/signup",
    "/forgot-password",
    "/verify-code",
    "/create-password",
    "/become-seller",
    "/profile",
    "/book-listing",
  ];
  const [isLightPage, setIsLightPage] = useState(false);
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [adminDetails, setAdminDetails] = useState(null);

  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen);

  const token = getToken();

  const avatarUrl = createInitialsAvatar(
    adminDetails?.firstName + " " + adminDetails?.lastName || ""
  );

  useEffect(() => {
    setIsLightPage(whitePaths.indexOf(pathname) > -1);
  }, [pathname]);

  const fetchAdmin = () => {
    fetch(USER_ROUTES.GET_ADMIN, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    })
      .then(async (res) => {
        const response = await res.json();
        console.log("adminDetails response is", response);
        if (response) {
          setAdminDetails(response);
        } else {
          removeToken();
          router.push("/login");
        }
      })
      .catch((error) => {
        console.log("error", error);
      });
  };

  console.log("adminDetails", adminDetails);

  useEffect(() => {
    fetchAdmin();
  }, []);

  return (
    <div className={``}>
      <header
        className={`${headerCss.headerContainer} z-50 flex items-center md:items-start justify-between py-3 px-6 ${className}`}
      >
        {/* Left side of the header */}
        <div className="flex items-center h-full">
          <h1 className="text-2xl leading-[34px] font-medium capitalize">{`${adminDetails?.firstName} ${adminDetails?.lastName}`}</h1>
        </div>

        {/* Right side of the header */}
        <div className="flex gap-5 items-center h-full ">
          {/* <Search /> */}

          {/* <span className="flex items-center justify-center rounded-md bg-[#4d79061a] py-2 px-[9px] relative cursor-pointer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 18 18"
              fill="none"
            >
              <path
                d="M9 7.5498C9.3866 7.5498 9.7002 7.8634 9.7002 8.25C9.7002 8.6366 9.3866 8.9502 9 8.9502C8.6134 8.95019 8.2998 8.6366 8.2998 8.25C8.2998 7.8634 8.6134 7.54981 9 7.5498Z"
                fill="#4A8B40"
                stroke="#EDF2E6"
                strokeWidth="0.1"
              />
              <path
                d="M6 7.5498C6.3866 7.5498 6.7002 7.8634 6.7002 8.25C6.7002 8.6366 6.3866 8.9502 6 8.9502C5.6134 8.9502 5.2998 8.6366 5.2998 8.25C5.2998 7.8634 5.6134 7.5498 6 7.5498Z"
                fill="#4A8B40"
                stroke="#EDF2E6"
                strokeWidth="0.1"
              />
              <path
                d="M12 7.5498C12.3866 7.5498 12.7002 7.8634 12.7002 8.25C12.7002 8.6366 12.3866 8.9502 12 8.9502C11.6134 8.95019 11.2998 8.6366 11.2998 8.25C11.2998 7.8634 11.6134 7.54981 12 7.5498Z"
                fill="#4A8B40"
                stroke="#EDF2E6"
                strokeWidth="0.1"
              />
              <path
                d="M5.25 2.375H12.75C13.6511 2.375 14.5177 2.71061 15.1826 3.3125L15.3135 3.43652C15.9933 4.11634 16.375 5.03859 16.375 6V10.5C16.375 11.4614 15.9933 12.3837 15.3135 13.0635C14.6337 13.7433 13.7114 14.125 12.75 14.125H5.50586L5.46875 14.1611L3.59375 16.0361V16.0371C3.48819 16.1446 3.36172 16.2301 3.22266 16.2881C3.11818 16.3316 3.00776 16.3594 2.89551 16.3701L2.7832 16.375H2.78223C2.66721 16.3753 2.55308 16.3583 2.44336 16.3252L2.33496 16.2871L2.18262 16.2109C2.08511 16.1528 1.99709 16.0802 1.9209 15.9961L1.81641 15.8613C1.72148 15.7185 1.65945 15.5571 1.63574 15.3887L1.625 15.2188V6C1.625 5.09885 1.96061 4.2323 2.5625 3.56738L2.68652 3.43652C3.36633 2.75672 4.28861 2.37501 5.25 2.375ZM5.25 3.625C4.69888 3.62501 4.16772 3.81683 3.74512 4.16309L3.57031 4.32031C3.12493 4.76571 2.875 5.37012 2.875 6V14.9951L3.08887 14.7803L4.80469 13.0557L4.80566 13.0566C4.83481 13.0277 4.86633 13.0011 4.90039 12.9785L5.00977 12.9219C5.04773 12.9064 5.08688 12.8945 5.12695 12.8867L5.24902 12.875H12.75C13.3011 12.875 13.8323 12.6831 14.2549 12.3369L14.4297 12.1797C14.8751 11.7343 15.125 11.1299 15.125 10.5V6C15.125 5.44886 14.9332 4.91773 14.5869 4.49512L14.4297 4.32031C13.9843 3.87493 13.3799 3.625 12.75 3.625H5.25Z"
                fill="#4A8B40"
                stroke="#EDF2E6"
                strokeWidth="0.25"
              />
            </svg>

            <span className="absolute -top-2 -right-2 bg-[#EB5757] rounded-full w-5 h-5 flex justify-center items-center text-white text-[10px] leading-none">
              53
            </span>

            <div className='absolute top-[150%] left-0 w-[300px] h-[200px] rounded-2xl shadow_medium bg-white z-50 before:absolute before:-top-2 before:left-4 before:w-4 before:h-4 before:bg-white before:rotate-45 clip-path-message'>
            </div>

            <div className='absolute top-[150%] left-0 w-[300px] h-[200px] rounded-2xl shadow_medium bg-white z-50 
    before:absolute before:-top-2 before:left-4 before:w-4 before:h-4 before:bg-white before:rotate-45 before:z-50
    clip-path-[polygon(0%_12px,12px_12px,24px_0%,36px_12px,100%_12px,100%_100%,0%_100%)]'>
            </div>
          </span> */}

          <span className="flex items-center justify-center rounded-md bg-[#4d79061a] py-2 px-[9px] relative cursor-pointer">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="19"
              height="19"
              viewBox="0 0 19 19"
              fill="none"
            >
              <path
                d="M16.855 14.2363C16.3614 13.8206 15.9293 13.344 15.5703 12.8194C15.1783 12.0953 14.9433 11.3045 14.8792 10.4934V8.10454C14.8826 6.83062 14.3935 5.59937 13.5037 4.64213C12.6139 3.68489 11.3848 3.06755 10.0472 2.9061V2.28229C10.0472 2.11108 9.97519 1.94687 9.84704 1.8258C9.71889 1.70473 9.54508 1.63672 9.36385 1.63672C9.18261 1.63672 9.0088 1.70473 8.88065 1.8258C8.7525 1.94687 8.6805 2.11108 8.6805 2.28229V2.91578C7.35489 3.08886 6.14059 3.70993 5.26251 4.66395C4.38443 5.61798 3.90207 6.8403 3.90476 8.10454V10.4934C3.84066 11.3045 3.60572 12.0953 3.21374 12.8194C2.861 13.3428 2.4358 13.8193 1.94943 14.2363C1.89483 14.2816 1.85107 14.3374 1.82106 14.3999C1.79105 14.4624 1.77548 14.5303 1.77539 14.599V15.2566C1.77539 15.3849 1.82932 15.5079 1.92531 15.5986C2.02131 15.6893 2.1515 15.7402 2.28726 15.7402H16.5172C16.653 15.7402 16.7832 15.6893 16.8792 15.5986C16.9752 15.5079 17.0291 15.3849 17.0291 15.2566V14.599C17.029 14.5303 17.0134 14.4624 16.9834 14.3999C16.9534 14.3374 16.9096 14.2816 16.855 14.2363ZM2.84008 14.7731C3.31632 14.3384 3.73564 13.8514 4.08904 13.3223C4.58279 12.4478 4.87088 11.4828 4.93362 10.4934V8.10454C4.91332 7.53781 5.01395 6.97301 5.2295 6.44378C5.44506 5.91455 5.77115 5.43172 6.18834 5.02402C6.60553 4.61633 7.1053 4.29211 7.65788 4.07069C8.21046 3.84926 8.80456 3.73515 9.40479 3.73515C10.005 3.73515 10.5991 3.84926 11.1517 4.07069C11.7043 4.29211 12.2041 4.61633 12.6213 5.02402C13.0384 5.43172 13.3645 5.91455 13.5801 6.44378C13.7956 6.97301 13.8963 7.53781 13.876 8.10454V10.4934C13.9387 11.4828 14.2268 12.4478 14.7206 13.3223C15.0739 13.8514 15.4933 14.3384 15.9695 14.7731H2.84008Z"
                fill="#4A8B40"
              />
              <path
                d="M9.42747 17.3526C9.74992 17.3455 10.0593 17.231 10.301 17.0292C10.5427 16.8275 10.7011 16.5514 10.7481 16.25H8.05566C8.10402 16.5596 8.26979 16.8421 8.52213 17.045C8.77447 17.2478 9.0962 17.3571 9.42747 17.3526Z"
                fill="#4A8B40"
              />
            </svg>

            <span className="absolute -top-2 -right-2 bg-[#EB5757] rounded-full w-5 h-5 flex justify-center items-center text-white text-[10px] leading-none">
              53
            </span>
          </span>

          <div className="flex items-center gap-2.5">
            <div className="w-[46px] h-[46px] relative overflow-hidden rounded-sm">
              <Image
                // src={"/images/test_profile_image.jpg" || avatarUrl}
                src={avatarUrl}
                alt="Profile Image"
                fill
                objectFit="cover"
                className="object-cover rounded-sm"
                sizes="33w"
                priority
              />
            </div>

            <div>
              <div className="flex items-center gap-[5px]">
                <h5 className="text-[#151D48] text-base font-medium leading-[18px]">{`${
                  adminDetails?.firstName || "Admin"
                } ${adminDetails?.lastName || "User"}`}</h5>
                <MdKeyboardArrowDown />
              </div>

              <p className="mt-0.5 text-[#787878] text-xs leading-[15px]">
                Admin
              </p>
            </div>
          </div>
        </div>
      </header>
    </div>
  );
}
