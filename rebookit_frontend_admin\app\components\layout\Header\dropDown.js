"use client"

import { IoIosArrowDown } from 'react-icons/io'
import Link from 'next/link'
import { useState } from 'react'
import headerCss from "./header.module.scss";



export default function DropdownItem({ item }) {
    const [isOpen, setIsOpen] = useState(false)

    return (
        <div
            className="relative group"
            onMouseEnter={() => setIsOpen(true)}
        >
            <div className="flex items-center ml-4 cursor-pointer">
                <p className="">{item.name}</p>
                <IoIosArrowDown size={15} className="ml-2" />
            </div>

            {item.subList && isOpen && (
                <div className={`absolute left-0 top-full bg-white text-gray-600  shadow-md mt-2 z-50 min-w-max rounded text-[14px] [line-height:30px]`}
                onMouseLeave={() => setIsOpen(false)}

                >
                    {item.subList.map((sub, i) => (
                        <div key={i} className="relative group/subitem">
                            {sub.subList ? (
                                <>
                                    <div className="flex items-center justify-between px-4 py-2 hover:text-gray-900 hover:bg-gray-100 whitespace-nowrap cursor-pointer z-20 ">
                                        <span className='text-[14px]'>{sub.name}</span>
                                        <IoIosArrowDown size={12} className="ml-2" />
                                    </div>
                                    <div className="absolute left-full top-0 bg-white text-gray-600 hover:text-gray-900 shadow-md hidden group-hover/subitem:block min-w-max rounded ">
                                        {sub.subList.map((nested, j) => (
                                            <Link href={nested.link || "/"} key={j}>
                                                <div className="px-4 py-2 hover:bg-gray-100 whitespace-nowrap">
                                                    {nested.name}
                                                </div>
                                            </Link>
                                        ))}
                                    </div>
                                </>
                            ) : (
                                <Link href={sub?.link || "/"}>
                                    <div className="px-4 py-2 hover:bg-gray-100 text-gray-900 whitespace-nowrap">{sub.name}</div>
                                </Link>
                            )}
                        </div>
                    ))}
                </div>
            )}
        </div>
    )
}