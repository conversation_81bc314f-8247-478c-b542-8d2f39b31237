"use client";

import React, {useState, useEffect, useRef, Fragment} from "react";
import Image from "next/image";
import Link from "next/link";
import flag from "@/public/flag.png";
import logo from "@/public/landing/fine_logo.png";
import {usePathname, useRouter} from "next/navigation";
import {useDispatch} from "react-redux";
import {select<PERSON>age<PERSON>and<PERSON>} from "@/app/redux/slices/storeSlice";
import {RiLogoutBoxRFill} from "react-icons/ri";
import {removeToken} from "@/app/utils/utils";

function Sidebar() {
  const pathName = usePathname();
  const router = useRouter();
  const [navbarList, setNavbarList] = useState([
    [
      {
        name: "Dashboard",
        icon: (isActive) => (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="21"
            viewBox="0 0 20 21"
            fill="none"
          >
            <path
              d="M14.1663 12.1576V17.1576M11.6663 14.6576H16.6663M4.99967 8.82422H6.66634C7.58682 8.82422 8.33301 8.07803 8.33301 7.15755V5.49089C8.33301 4.57041 7.58682 3.82422 6.66634 3.82422H4.99967C4.0792 3.82422 3.33301 4.57041 3.33301 5.49089V7.15755C3.33301 8.07803 4.0792 8.82422 4.99967 8.82422ZM13.333 8.82422H14.9997C15.9201 8.82422 16.6663 8.07803 16.6663 7.15755V5.49089C16.6663 4.57041 15.9201 3.82422 14.9997 3.82422H13.333C12.4125 3.82422 11.6663 4.57041 11.6663 5.49089V7.15755C11.6663 8.07803 12.4125 8.82422 13.333 8.82422ZM4.99967 17.1576H6.66634C7.58682 17.1576 8.33301 16.4114 8.33301 15.4909V13.8242C8.33301 12.9037 7.58682 12.1576 6.66634 12.1576H4.99967C4.0792 12.1576 3.33301 12.9037 3.33301 13.8242V15.4909C3.33301 16.4114 4.0792 17.1576 4.99967 17.1576Z"
              stroke={isActive ? "white" : "black"}
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ),
        link: "/",
        initialSubRoute: "/",
      },
      {
        name: "User Management",
        icon: (isActive) => (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="21"
            viewBox="0 0 20 21"
            fill="none"
          >
            <path
              d="M16.6663 11.3242V5.49089C16.6663 4.57041 15.9201 3.82422 14.9997 3.82422H4.99967C4.0792 3.82422 3.33301 4.57041 3.33301 5.49089V11.3242M16.6663 11.3242V15.4909C16.6663 16.4114 15.9201 17.1576 14.9997 17.1576H4.99967C4.0792 17.1576 3.33301 16.4114 3.33301 15.4909V11.3242M16.6663 11.3242H14.5115C14.2905 11.3242 14.0785 11.412 13.9223 11.5683L11.9104 13.5801C11.7541 13.7364 11.5422 13.8242 11.3212 13.8242H8.67819C8.45717 13.8242 8.24521 13.7364 8.08893 13.5801L6.07709 11.5683C5.92081 11.412 5.70884 11.3242 5.48783 11.3242H3.33301"
              stroke={isActive ? "white" : "black"}
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ),
        link: "/user-management",
        initialSubRoute: "/user-management",
      },
      {
        name: "Item Listing",
        icon: (isActive) => (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="21"
            viewBox="0 0 20 21"
            fill="none"
          >
            <path
              d="M6.66667 6.32552V2.99219M13.3333 6.32552V2.99219M5.83333 9.65885H14.1667M4.16667 17.9922H15.8333C16.7538 17.9922 17.5 17.246 17.5 16.3255V6.32552C17.5 5.40505 16.7538 4.65885 15.8333 4.65885H4.16667C3.24619 4.65885 2.5 5.40505 2.5 6.32552V16.3255C2.5 17.246 3.24619 17.9922 4.16667 17.9922Z"
              stroke={isActive ? "white" : "black"}
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ),
        link: "/book-listing",
        initialSubRoute: "/book-listing",
      },
      //   {
      //     name: "Sellers",
      //     icon: (isActive) => (
      //       <svg
      //         xmlns="http://www.w3.org/2000/svg"
      //         width="20"
      //         height="21"
      //         viewBox="0 0 20 21"
      //         fill="none"
      //       >
      //         <path
      //           d="M10.8333 13.8268V5.49349C10.8333 5.03325 10.4602 4.66016 10 4.66016H3.33333C2.8731 4.66016 2.5 5.03325 2.5 5.49349V13.8268C2.5 14.2871 2.8731 14.6602 3.33333 14.6602H4.16667M10.8333 13.8268C10.8333 14.2871 10.4602 14.6602 10 14.6602H7.5M10.8333 13.8268L10.8333 7.16016C10.8333 6.69992 11.2064 6.32682 11.6667 6.32682H13.8215C14.0425 6.32682 14.2545 6.41462 14.4107 6.5709L17.2559 9.41608C17.4122 9.57236 17.5 9.78432 17.5 10.0053V13.8268C17.5 14.2871 17.1269 14.6602 16.6667 14.6602H15.8333M10.8333 13.8268C10.8333 14.2871 11.2064 14.6602 11.6667 14.6602H12.5M4.16667 14.6602C4.16667 15.5806 4.91286 16.3268 5.83333 16.3268C6.75381 16.3268 7.5 15.5806 7.5 14.6602M4.16667 14.6602C4.16667 13.7397 4.91286 12.9935 5.83333 12.9935C6.75381 12.9935 7.5 13.7397 7.5 14.6602M12.5 14.6602C12.5 15.5806 13.2462 16.3268 14.1667 16.3268C15.0871 16.3268 15.8333 15.5806 15.8333 14.6602M12.5 14.6602C12.5 13.7397 13.2462 12.9935 14.1667 12.9935C15.0871 12.9935 15.8333 13.7397 15.8333 14.6602"
      //           stroke={isActive ? "white" : "black"}
      //           strokeWidth="1.5"
      //         />
      //       </svg>
      //     ),
      //     link: "/sellers",
      //   },

      {
          name: "Ad Management",
          icon: (isActive) => <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
              <path d="M10.8333 13.8268V5.49349C10.8333 5.03325 10.4602 4.66016 10 4.66016H3.33333C2.8731 4.66016 2.5 5.03325 2.5 5.49349V13.8268C2.5 14.2871 2.8731 14.6602 3.33333 14.6602H4.16667M10.8333 13.8268C10.8333 14.2871 10.4602 14.6602 10 14.6602H7.5M10.8333 13.8268L10.8333 7.16016C10.8333 6.69992 11.2064 6.32682 11.6667 6.32682H13.8215C14.0425 6.32682 14.2545 6.41462 14.4107 6.5709L17.2559 9.41608C17.4122 9.57236 17.5 9.78432 17.5 10.0053V13.8268C17.5 14.2871 17.1269 14.6602 16.6667 14.6602H15.8333M10.8333 13.8268C10.8333 14.2871 11.2064 14.6602 11.6667 14.6602H12.5M4.16667 14.6602C4.16667 15.5806 4.91286 16.3268 5.83333 16.3268C6.75381 16.3268 7.5 15.5806 7.5 14.6602M4.16667 14.6602C4.16667 13.7397 4.91286 12.9935 5.83333 12.9935C6.75381 12.9935 7.5 13.7397 7.5 14.6602M12.5 14.6602C12.5 15.5806 13.2462 16.3268 14.1667 16.3268C15.0871 16.3268 15.8333 15.5806 15.8333 14.6602M12.5 14.6602C12.5 13.7397 13.2462 12.9935 14.1667 12.9935C15.0871 12.9935 15.8333 13.7397 15.8333 14.6602" stroke={isActive ? "white" : "black"} strokeWidth="1.5" />
          </svg>,
          link: "/ad-management",
          initialSubRoute:"/ad-management"
      },
      {
        name: "Membership",
        icon: (isActive) => (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="21"
            viewBox="0 0 20 21"
            fill="none"
          >
            <path
              d="M10.8333 13.8268V5.49349C10.8333 5.03325 10.4602 4.66016 10 4.66016H3.33333C2.8731 4.66016 2.5 5.03325 2.5 5.49349V13.8268C2.5 14.2871 2.8731 14.6602 3.33333 14.6602H4.16667M10.8333 13.8268C10.8333 14.2871 10.4602 14.6602 10 14.6602H7.5M10.8333 13.8268L10.8333 7.16016C10.8333 6.69992 11.2064 6.32682 11.6667 6.32682H13.8215C14.0425 6.32682 14.2545 6.41462 14.4107 6.5709L17.2559 9.41608C17.4122 9.57236 17.5 9.78432 17.5 10.0053V13.8268C17.5 14.2871 17.1269 14.6602 16.6667 14.6602H15.8333M10.8333 13.8268C10.8333 14.2871 11.2064 14.6602 11.6667 14.6602H12.5M4.16667 14.6602C4.16667 15.5806 4.91286 16.3268 5.83333 16.3268C6.75381 16.3268 7.5 15.5806 7.5 14.6602M4.16667 14.6602C4.16667 13.7397 4.91286 12.9935 5.83333 12.9935C6.75381 12.9935 7.5 13.7397 7.5 14.6602M12.5 14.6602C12.5 15.5806 13.2462 16.3268 14.1667 16.3268C15.0871 16.3268 15.8333 15.5806 15.8333 14.6602M12.5 14.6602C12.5 13.7397 13.2462 12.9935 14.1667 12.9935C15.0871 12.9935 15.8333 13.7397 15.8333 14.6602"
              stroke={isActive ? "white" : "black"}
              strokeWidth="1.5"
            />
          </svg>
        ),
        link: "/membership",
        initialSubRoute: "/membership/member",
        subRoutes: ["member", "plans", "editplan"],
      },
      {
        name: "Categories",
        icon: (isActive) => (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="21"
            viewBox="0 0 20 21"
            fill="none"
          >
            <path
              d="M10.8333 13.8268V5.49349C10.8333 5.03325 10.4602 4.66016 10 4.66016H3.33333C2.8731 4.66016 2.5 5.03325 2.5 5.49349V13.8268C2.5 14.2871 2.8731 14.6602 3.33333 14.6602H4.16667M10.8333 13.8268C10.8333 14.2871 10.4602 14.6602 10 14.6602H7.5M10.8333 13.8268L10.8333 7.16016C10.8333 6.69992 11.2064 6.32682 11.6667 6.32682H13.8215C14.0425 6.32682 14.2545 6.41462 14.4107 6.5709L17.2559 9.41608C17.4122 9.57236 17.5 9.78432 17.5 10.0053V13.8268C17.5 14.2871 17.1269 14.6602 16.6667 14.6602H15.8333M10.8333 13.8268C10.8333 14.2871 11.2064 14.6602 11.6667 14.6602H12.5M4.16667 14.6602C4.16667 15.5806 4.91286 16.3268 5.83333 16.3268C6.75381 16.3268 7.5 15.5806 7.5 14.6602M4.16667 14.6602C4.16667 13.7397 4.91286 12.9935 5.83333 12.9935C6.75381 12.9935 7.5 13.7397 7.5 14.6602M12.5 14.6602C12.5 15.5806 13.2462 16.3268 14.1667 16.3268C15.0871 16.3268 15.8333 15.5806 15.8333 14.6602M12.5 14.6602C12.5 13.7397 13.2462 12.9935 14.1667 12.9935C15.0871 12.9935 15.8333 13.7397 15.8333 14.6602"
              stroke={isActive ? "white" : "black"}
              strokeWidth="1.5"
            />
          </svg>
        ),
        link: "/categories",
        initialSubRoute: "/categories/category",
        subRoutes: ["category", "subcategory"],
      },
      {
          name: "Past Papers",
          icon: (isActive) => <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
              <path d="M10.8333 13.8268V5.49349C10.8333 5.03325 10.4602 4.66016 10 4.66016H3.33333C2.8731 4.66016 2.5 5.03325 2.5 5.49349V13.8268C2.5 14.2871 2.8731 14.6602 3.33333 14.6602H4.16667M10.8333 13.8268C10.8333 14.2871 10.4602 14.6602 10 14.6602H7.5M10.8333 13.8268L10.8333 7.16016C10.8333 6.69992 11.2064 6.32682 11.6667 6.32682H13.8215C14.0425 6.32682 14.2545 6.41462 14.4107 6.5709L17.2559 9.41608C17.4122 9.57236 17.5 9.78432 17.5 10.0053V13.8268C17.5 14.2871 17.1269 14.6602 16.6667 14.6602H15.8333M10.8333 13.8268C10.8333 14.2871 11.2064 14.6602 11.6667 14.6602H12.5M4.16667 14.6602C4.16667 15.5806 4.91286 16.3268 5.83333 16.3268C6.75381 16.3268 7.5 15.5806 7.5 14.6602M4.16667 14.6602C4.16667 13.7397 4.91286 12.9935 5.83333 12.9935C6.75381 12.9935 7.5 13.7397 7.5 14.6602M12.5 14.6602C12.5 15.5806 13.2462 16.3268 14.1667 16.3268C15.0871 16.3268 15.8333 15.5806 15.8333 14.6602M12.5 14.6602C12.5 13.7397 13.2462 12.9935 14.1667 12.9935C15.0871 12.9935 15.8333 13.7397 15.8333 14.6602" stroke={isActive ? "white" : "black"} strokeWidth="1.5" />
          </svg>,
          link: "/pastpapers",
          initialSubRoute:"/pastpapers",
          // subRoutes:["category","subcategory"]
      },
      // {
      //     name: "Chats",
      //     icon: (isActive) => <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
      //         <path d="M6.66667 11.3242V10.4909M10 11.3242V8.82422M13.3333 11.3242V7.15755M6.66667 17.9909L10 14.6576L13.3333 17.9909M2.5 3.82422H17.5M3.33333 3.82422H16.6667V13.8242C16.6667 14.2845 16.2936 14.6576 15.8333 14.6576H4.16667C3.70643 14.6576 3.33333 14.2845 3.33333 13.8242V3.82422Z" stroke={isActive ? "white" : "black"} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      //     </svg>,
      //     link: "/chats"
      // },
      {
          name: "Transaction",
          icon: (isActive) => <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
              <path d="M6.66667 11.3242V10.4909M10 11.3242V8.82422M13.3333 11.3242V7.15755M6.66667 17.9909L10 14.6576L13.3333 17.9909M2.5 3.82422H17.5M3.33333 3.82422H16.6667V13.8242C16.6667 14.2845 16.2936 14.6576 15.8333 14.6576H4.16667C3.70643 14.6576 3.33333 14.2845 3.33333 13.8242V3.82422Z" stroke={isActive ? "white" : "black"} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>,
          link: "/transaction",
          initialSubRoute:"/transaction/all",
          subRoutes:["all","ads","editplan"]
      },
      // {
      //     name: "Fun Fact",
      //     icon: (isActive) => <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
      //         <path d="M6.66667 11.3242V10.4909M10 11.3242V8.82422M13.3333 11.3242V7.15755M6.66667 17.9909L10 14.6576L13.3333 17.9909M2.5 3.82422H17.5M3.33333 3.82422H16.6667V13.8242C16.6667 14.2845 16.2936 14.6576 15.8333 14.6576H4.16667C3.70643 14.6576 3.33333 14.2845 3.33333 13.8242V3.82422Z" stroke={isActive ? "white" : "black"} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      //     </svg>,
      //     link: "/funfact",
      //     initialSubRoute:"/funfact",
      //     // subRoutes:["all","ads","editplan"]
      // },
             {
          name: "Manage Testimonials",
          icon: (isActive) => <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
              <path d="M6.66667 11.3242V10.4909M10 11.3242V8.82422M13.3333 11.3242V7.15755M6.66667 17.9909L10 14.6576L13.3333 17.9909M2.5 3.82422H17.5M3.33333 3.82422H16.6667V13.8242C16.6667 14.2845 16.2936 14.6576 15.8333 14.6576H4.16667C3.70643 14.6576 3.33333 14.2845 3.33333 13.8242V3.82422Z" stroke={isActive ? "white" : "black"} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>,
          link: "/testimonials",
          initialSubRoute:"/testimonials",
          // subRoutes:["all","ads","editplan"]
      },
      {
          name: "Faq Management",
          icon: (isActive) => <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
              <path d="M6.66667 11.3242V10.4909M10 11.3242V8.82422M13.3333 11.3242V7.15755M6.66667 17.9909L10 14.6576L13.3333 17.9909M2.5 3.82422H17.5M3.33333 3.82422H16.6667V13.8242C16.6667 14.2845 16.2936 14.6576 15.8333 14.6576H4.16667C3.70643 14.6576 3.33333 14.2845 3.33333 13.8242V3.82422Z" stroke={isActive ? "white" : "black"} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
          </svg>,
          link: "/faq",
          initialSubRoute:"/faq",
          // subRoutes:["all","ads","editplan"]
      },
    ],
    [
      // {
      //     name: "Notifications",
      //     icon: (isActive) => <svg xmlns="http://www.w3.org/2000/svg" width="20" height="21" viewBox="0 0 20 21" fill="none">
      //         <path d="M12.4997 14.6589H16.6663L15.4956 13.4881C15.1781 13.1706 14.9997 12.7399 14.9997 12.2909V9.65886C14.9997 7.48183 13.6083 5.62976 11.6663 4.94337V4.65885C11.6663 3.73838 10.9201 2.99219 9.99967 2.99219C9.0792 2.99219 8.33301 3.73838 8.33301 4.65885V4.94337C6.39102 5.62976 4.99967 7.48183 4.99967 9.65886V12.2909C4.99967 12.7399 4.8213 13.1706 4.50378 13.4881L3.33301 14.6589H7.49967M12.4997 14.6589V15.4922C12.4997 16.8729 11.3804 17.9922 9.99967 17.9922C8.61896 17.9922 7.49967 16.8729 7.49967 15.4922V14.6589M12.4997 14.6589H7.49967" stroke={isActive ? "white" : "black"} strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
      //     </svg>,
      //     link: "/notification"
      // },
      {
        name: "Settings",
        icon: (isActive) => (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="21"
            viewBox="0 0 20 21"
            fill="none"
          >
            <path
              d="M8.60386 4.08994C8.95919 2.62627 11.0408 2.62627 11.3961 4.08995C11.6257 5.03546 12.709 5.48417 13.5398 4.9779C14.8261 4.19418 16.298 5.66611 15.5143 6.95234C15.008 7.78323 15.4567 8.8665 16.4022 9.09604C17.8659 9.45138 17.8659 11.533 16.4022 11.8883C15.4567 12.1179 15.008 13.2011 15.5143 14.032C16.298 15.3183 14.8261 16.7902 13.5398 16.0065C12.709 15.5002 11.6257 15.9489 11.3961 16.8944C11.0408 18.3581 8.95919 18.3581 8.60386 16.8944C8.37431 15.9489 7.29105 15.5002 6.46016 16.0065C5.17392 16.7902 3.70199 15.3183 4.48571 14.032C4.99198 13.2011 4.54327 12.1179 3.59776 11.8883C2.13408 11.533 2.13408 9.45138 3.59776 9.09604C4.54327 8.8665 4.99198 7.78323 4.48571 6.95234C3.70199 5.66611 5.17392 4.19418 6.46015 4.97789C7.29105 5.48417 8.37431 5.03546 8.60386 4.08994Z"
              stroke={isActive ? "white" : "black"}
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M12.5 10.4922C12.5 11.8729 11.3807 12.9922 10 12.9922C8.61929 12.9922 7.5 11.8729 7.5 10.4922C7.5 9.11148 8.61929 7.99219 10 7.99219C11.3807 7.99219 12.5 9.11148 12.5 10.4922Z"
              stroke={isActive ? "white" : "black"}
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ),
        link: "/settings",
        initialSubRoute: "/settings",
      },
      {
        name: "Support",
        icon: (isActive) => (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="21"
            viewBox="0 0 20 21"
            fill="none"
          >
            <path
              d="M10.8333 13.8255H10V10.4922H9.16667M10 7.15885H10.0083M17.5 10.4922C17.5 14.6343 14.1421 17.9922 10 17.9922C5.85786 17.9922 2.5 14.6343 2.5 10.4922C2.5 6.35005 5.85786 2.99219 10 2.99219C14.1421 2.99219 17.5 6.35005 17.5 10.4922Z"
              stroke={isActive ? "white" : "black"}
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        ),
        link: "/support",
        initialSubRoute: "/support",
      },
    ],
  ]);
  const dispatch = useDispatch();
  const [pageName, setPageName] = useState("Dashboard");

  useEffect(() => {
    dispatch(selectPageHandler(pageName));
  }, [pageName]);

  const LogoutHandler = () => {
    removeToken();
    router.push("/login");
  };

  console.log("pathName", pathName);

  return (
    <div
      // ref={sidebarRef}
      className={`bg-white w-[20%] h-[100vh] pt-[33px] pb-[30px] flex flex-col transform transition-transform duration-300`}
      // ${isOpen ? 'translate-x-0' : 'translate-x-full'}
    >
      <section className="px-6 pb-6 flex-shrink-0 flex gap-[7px]">
        <Link href="/" aria-label="Home">
          <div className="h-[43px] w-7 relative overflow-hidden">
            <Image
              src={logo}
              fill
              priority
              alt="Rebookit Logo"
              className="object-cover"
              sizes="33w"
            />
          </div>
        </Link>
        <Link href={"/"}>
          <div className="flex items-center gap-[7px]">
            <p className="text-[21px] font-medium leading-11">ReBookIt.Club</p>
            <div className="relative overflow-hidden h-5 w-[30px]">
              <Image
                src={flag}
                alt="Jamaican Flag"
                className="object-cover"
                fill
                priority
                sizes="33w"
              />
            </div>
          </div>
        </Link>
      </section>

      <section className="flex-1 overflow-auto px-6 py-2.5">
        <ul className="flex flex-col gap-1">
          {navbarList.map((array, index) => (
            <Fragment key={index}>
              {array.map((item, idx) => {
                const isActive =
                  item.name.toLowerCase() === "dashboard"
                    ? pathName === "/"
                    : pathName.includes(item.link);

                return (
                  <Link
                    key={item.name + idx}
                    href={item.initialSubRoute}
                    onClick={() => setPageName(item.name)}
                  >
                    <li
                      key={item.name + idx}
                      className={`text-[16px] leading-5 flex gap-3 px-6 py-3 cursor-pointer ${
                        (
                          item.name.toLowerCase() === "dashboard"
                            ? pathName === "/"
                            : pathName.includes(item.link)
                        )
                          ? "rounded-full global_linear_gradient text-white"
                          : ""
                      }`}
                    >
                      {typeof item.icon === "function"
                        ? item.icon(isActive)
                        : item.icon}

                      <span className="flex gap-1 select-none items-center">
                        {item.name}
                      </span>
                    </li>
                  </Link>
                );
              })}
              {navbarList.length - 1 !== index && (
                <hr
                  key={"hr-" + index}
                  className="text-[#E2E4E5] flex-shrink-0 my-6"
                />
              )}
            </Fragment>
          ))}
        </ul>
      </section>

      <div className="flex gap-1.5 items-center justify-center">
        <div
          className="w-2/3 flex gap-1.5 items-center cursor-pointer"
          onClick={LogoutHandler}
        >
          <RiLogoutBoxRFill className="w-5 h-5" />
          <p className="text-base">Log out</p>
        </div>
      </div>
    </div>
  );
}

export default Sidebar;
