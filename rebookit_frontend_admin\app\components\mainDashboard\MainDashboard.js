import React from 'react';
import Summary from './Summary';
import SubscriptionChart from './SubscriptionChart';
import TotalAdRequestChart from './TotalAdRequestChart';
import VisitorInsightsChart from './VisitorInsightsChart';
import TransactionsTable from './TransactionsTable';
import TransactionsPieChart from './TransactionsPieChart';

const MainDashboard = () => {
  return (
    <div className="bg-white rounded-lg p-3 ">
      {/* Top Summary Row */}
      <div className="flex w-full mb-6 gap-6">
      <div className="mb-6 w-[65%]">
        <Summary />
      </div>
      {/* Top Right Chart */}
      <div className="mb-6 w-[35%]">
        <div className="md:col-span-2" />
        <SubscriptionChart />
      </div>
      </div>
      {/* Middle Charts Row */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-2 gap-6">
        <TotalAdRequestChart />
        <VisitorInsightsChart />
      </div>
      {/* Bottom Transactions Row */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <TransactionsTable />
        </div>
        <TransactionsPieChart />
      </div>
    </div>
  );
};

export default MainDashboard;
