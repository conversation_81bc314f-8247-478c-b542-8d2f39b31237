'use client';
import React, { useState } from 'react';
import { AreaChart, Area, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import styles from '../../components/ad-management/rechartsNoOutline.module.css';

// Add a style to globally remove outline/border on focus/active for recharts container
const noOutlineStyle = {
  outline: 'none',
  boxShadow: 'none',
};

const getDaysInMonth = (year, month) => {
  return new Date(year, month + 1, 0).getDate();
};

const generateDayWiseData = (year, month) => {
  const days = getDaysInMonth(year, month);
  const arr = Array.from({ length: days }, (_, i) => ({
    day: i + 1,
    lastMonth: Math.floor(200 + Math.random() * 50),
    thisMonth: Math.floor(300 + Math.random() * 100),
  }));
  // Add a dummy point at day 0 to force the area to start at the left edge
  if (arr.length > 0) {
    return [{ day: 0, lastMonth: arr[0].lastMonth, thisMonth: arr[0].thisMonth }, ...arr];
  }
  return arr;
};

const months = [
  'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
  'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
];
const years = [2024, 2025, 2026];

const SubscriptionChart = () => {
  const today = new Date();
  const defaultYear = 2025;
  const defaultMonth = today.getFullYear() === 2025 ? today.getMonth() : 0;
  const [year, setYear] = useState(defaultYear);
  const [month, setMonth] = useState(defaultMonth);
  const [showLastMonth, setShowLastMonth] = useState(true);
  const [showThisMonth, setShowThisMonth] = useState(true);

  const data = generateDayWiseData(year, month);

  return (
    <div
      className={`bg-white rounded-2xl shadow w-full h-[260px] flex flex-col ${styles.noOutlineRecharts}`}
      tabIndex={-1}
      style={noOutlineStyle}
      onMouseDown={e => e.currentTarget.blur()}
      onFocus={e => e.currentTarget.blur()}
    >
      <div className="flex justify-between items-center mb-6 p-6 pb-0">
        <div className="font-semibold text-md">Subscription</div>
        <div className="flex gap-3 text-xs items-center">
          <select
            className="rounded-lg px-2 py-1 text-xs shadow-sm border-0 bg-gray-100 focus:ring-2 focus:ring-blue-300 focus:outline-none"
            value={year}
            onChange={e => setYear(Number(e.target.value))}
          >
            {years.map(y => (
              <option key={y} value={y}>{y}</option>
            ))}
          </select>
          <select
            className="rounded-lg px-2 py-1 text-xs shadow-sm border-0 bg-gray-100 focus:ring-2 focus:ring-blue-300 focus:outline-none"
            value={month}
            onChange={e => setMonth(Number(e.target.value))}
          >
            {months.map((m, idx) => (
              <option key={m} value={idx}>{m}</option>
            ))}
          </select>
          {/* <label className="flex items-center gap-1 cursor-pointer">
            <input type="checkbox" checked={showLastMonth} onChange={() => setShowLastMonth(v => !v)} />
            <span className="text-blue-600">Last Month</span>
          </label>
          <label className="flex items-center gap-1 cursor-pointer">
            <input type="checkbox" checked={showThisMonth} onChange={() => setShowThisMonth(v => !v)} />
            <span className="text-green-600">This Month</span>
          </label> */}
        </div>
      </div>
      <div className="flex-1 w-full h-full pr-6 " tabIndex={-1} style={noOutlineStyle}>
        <ResponsiveContainer width="100%">
          <AreaChart data={data} margin={{ top: 0, right: 0, left: 0, bottom: 0 }}>
            <defs>
              <linearGradient id="colorLastMonth" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#2563eb" stopOpacity={0.5}/>
                <stop offset="95%" stopColor="#2563eb" stopOpacity={0}/>
              </linearGradient>
              <linearGradient id="colorThisMonth" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#22c55e" stopOpacity={0.5}/>
                <stop offset="95%" stopColor="#22c55e" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" />
            <XAxis
              dataKey="day"
              type="number"
              domain={['dataMin', 'dataMax']}
              tick={false}
              axisLine={false}
              tickLine={false}
              padding={{ left: 0, right: 0 }}
            />
            <YAxis tick={{ fontSize: 12 }} />
            <Tooltip />
            {/* Hide the legend so "Last Month" and "This Month" text do not show */}
            {/* <Legend verticalAlign="top" height={36} /> */}
            {showLastMonth && (
              <Area type="monotone" dataKey="lastMonth" stroke="#2563eb" fillOpacity={1} fill="url(#colorLastMonth)" name="Last Month" />
            )}
            {showThisMonth && (
              <Area type="monotone" dataKey="thisMonth" stroke="#22c55e" fillOpacity={1} fill="url(#colorThisMonth)" name="This Month" />
            )}
          </AreaChart>
        </ResponsiveContainer>
      </div> 
      <div className="flex justify-end gap-6 mt-2 text-xs px-6 py-4">
        <span className="text-blue-600">
          Last Month:
          <span className="ml-1 font-semibold">
            {data && data.length > 0 ? data[data.length - 1].lastMonth : 0}
          </span>
        </span>
        <span className="text-green-600">
          This Month:
          <span className="ml-1 font-semibold">
            {data && data.length > 0 ? data[data.length - 1].thisMonth : 0}
          </span>
        </span>
      </div>
    </div>
  );
};

export default SubscriptionChart; 