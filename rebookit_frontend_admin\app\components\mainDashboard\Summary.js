"use client";
import { USER_ROUTES } from "@/app/config/api";
import { getToken } from "@/app/utils/utils";
import React, { useEffect, useState } from "react";
// Static configuration for summary cards
const summaryConfig = [
  {
    label: "Total Transaction",
    key: "totalTransactions",
    format: (value) => `J$${value}`,
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="34" height="34" viewBox="0 0 34 34" fill="none">
        <circle cx="16.87" cy="17.0775" r="16.8699" fill="#026CBE" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.9661 9.48849C10.0344 9.48849 9.27911 10.2438 9.27911 11.1755V22.9844C9.27911 23.9161 10.0344 24.6714 10.9661 24.6714H22.7751C23.7068 24.6714 24.4621 23.9161 24.4621 22.9844V11.1755C24.4621 10.2438 23.7068 9.48849 22.7751 9.48849H10.9661ZM13.4966 17.9235C13.4966 17.4576 13.119 17.08 12.6531 17.08C12.1873 17.08 11.8096 17.4576 11.8096 17.9235V21.2974C11.8096 21.7633 12.1873 22.1409 12.6531 22.1409C13.119 22.1409 13.4966 21.7633 13.4966 21.2974V17.9235ZM16.8706 14.5495C17.3364 14.5495 17.7141 14.9271 17.7141 15.393V21.2974C17.7141 21.7633 17.3364 22.1409 16.8706 22.1409C16.4047 22.1409 16.0271 21.7633 16.0271 21.2974V15.393C16.0271 14.9271 16.4047 14.5495 16.8706 14.5495ZM21.9316 12.8625C21.9316 12.3966 21.5539 12.019 21.0881 12.019C20.6222 12.019 20.2446 12.3966 20.2446 12.8625V21.2974C20.2446 21.7633 20.6222 22.1409 21.0881 22.1409C21.5539 22.1409 21.9316 21.7633 21.9316 21.2974V12.8625Z"
          fill="white"
        />
      </svg>
    ),
    color: "from-blue-700 to-blue-900",
  },
  {
    label: "Total Book listed",
    key: "totalListedItems",
    format: (value) => `${value}`,
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="35" height="34" viewBox="0 0 35 34" fill="none">
        <circle cx="17.4653" cy="17.0783" r="16.8699" fill="#E8141B" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M10.7169 12.0205C10.7169 10.1571 12.2275 8.6465 14.0909 8.6465H19.1519V12.0205C19.1519 13.8839 20.6625 15.3945 22.5259 15.3945H24.2129V22.1424C24.2129 24.0058 22.7023 25.5164 20.8389 25.5164H14.0909C12.2275 25.5164 10.7169 24.0058 10.7169 22.1424V12.0205ZM14.0909 16.238C13.6251 16.238 13.2474 16.6156 13.2474 17.0815C13.2474 17.5473 13.6251 17.925 14.0909 17.925H15.7779C16.2437 17.925 16.6214 17.5473 16.6214 17.0815C16.6214 16.6156 16.2437 16.238 15.7779 16.238H14.0909ZM14.0909 19.612C13.6251 19.612 13.2474 19.9896 13.2474 20.4555C13.2474 20.9213 13.6251 21.299 14.0909 21.299H17.4649C17.9307 21.299 18.3084 20.9213 18.3084 20.4555C18.3084 19.9896 17.9307 19.612 17.4649 19.612H14.0909ZM21.414 10.5012L21.3035 12.1579C21.2696 12.6671 21.6921 13.0896 22.2013 13.0556L23.858 12.9452C24.5811 12.897 24.9107 12.0195 24.3983 11.5071L22.852 9.96084C22.3396 9.4484 21.4622 9.77808 21.414 10.5012Z"
          fill="white"
        />
      </svg>
    ),
    color: "from-red-600 to-red-800",
  },
  {
    label: "Total User",
    key: "totalUsers",
    format: (value) => `${value}`,
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="35" height="34" viewBox="0 0 35 34" fill="none">
        <circle cx="17.443" cy="17.0782" r="16.8699" fill="#56A24A" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M21.3482 11.4L18.5648 11.7976C18.1396 11.8584 17.7922 12.0611 17.5441 12.3412L11.0496 18.8356C10.3908 19.4944 10.3908 20.5626 11.0496 21.2214L13.4354 23.6072C14.0942 24.266 15.1623 24.266 15.8212 23.6072L22.3156 17.1127C22.5957 16.8646 22.7984 16.5172 22.8592 16.092L23.2568 13.3086C23.4159 12.1953 22.4615 11.2409 21.3482 11.4ZM19.3998 15.257C19.7292 15.5864 20.2633 15.5864 20.5927 15.257C20.9221 14.9276 20.9221 14.3935 20.5927 14.0641C20.2633 13.7347 19.7292 13.7347 19.3998 14.0641C19.0704 14.3935 19.0704 14.9276 19.3998 15.257Z"
          fill="white"
        />
      </svg>
    ),
    color: "from-green-500 to-green-700",
  },
  {
    label: "Total New User",
    key: "newUser",
    format: (value) => `${value}`,
    icon: (
      <svg xmlns="http://www.w3.org/2000/svg" width="35" height="34" viewBox="0 0 35 34" fill="none">
        <circle cx="17.4218" cy="17.0783" r="16.8699" fill="#2F2C71" />
        <path
          d="M15.7329 17.9293C18.9937 17.9293 21.6369 19.817 21.6372 22.1461C21.6372 23.0778 20.8823 23.8335 19.9507 23.8336H11.5151C10.5837 23.8333 9.82861 23.0776 9.82861 22.1461C9.82889 19.8171 12.4722 17.9294 15.7329 17.9293ZM22.4819 12.026C22.9478 12.026 23.3257 12.4039 23.3257 12.8697V13.7135H24.1685C24.6342 13.7135 25.012 14.0906 25.0122 14.5563C25.0122 15.0221 24.6343 15.4 24.1685 15.4H23.3257V16.2438C23.3256 16.7095 22.9477 17.0875 22.4819 17.0875C22.0161 17.0875 21.6383 16.7095 21.6382 16.2438V15.4H20.7944C20.3288 15.3997 19.9517 15.0219 19.9517 14.5563C19.9518 14.0907 20.3289 13.7137 20.7944 13.7135H21.6382V12.8697C21.6382 12.4039 22.0161 12.026 22.4819 12.026ZM15.7329 10.3375C17.5963 10.3375 19.1069 11.8481 19.1069 13.7115C19.1069 15.5749 17.5963 17.0856 15.7329 17.0856C13.8696 17.0855 12.3589 15.5749 12.3589 13.7115C12.3589 11.8482 13.8696 10.3376 15.7329 10.3375Z"
          fill="white"
        />
      </svg>
    ),
    color: "from-purple-700 to-purple-900",
  },
];
function SummaryCard({ value, label, icon, color, onClick }) {
  return (
    <div
      className={`flex flex-col justify-between rounded-2xl shadow-lg p-6 min-h-[120px] cursor-pointer bg-gradient-to-br ${color} transition-transform hover:scale-105 hover:shadow-xl`}
      onClick={onClick}
      tabIndex={0}
      role="button"
      aria-label={label}
    >
      <div className="flex items-center gap-3 mb-2">
        <span className="text-3xl">{icon}</span>
      </div>
      <div className="text-xl font-bold text-white">{value}</div>
      <div className="text-white text-[10px] opacity-90 mt-1">{label}</div>
    </div>
  );
}
const Summary = ({ onCardClick }) => {
  const [apiData, setApiData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const token = getToken();
  const fetchData = () => {
    setLoading(true);
    setError(null);
    fetch(USER_ROUTES.GET_OVERVIEW, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    })
      .then(async (res) => {
        if (!res.ok) {
          throw new Error(`HTTP error! status: ${res.status}`);
        }
        const response = await res.json();
        setApiData(response);
        setLoading(false);
      })
      .catch((error) => {
        console.error("Error fetching summary data:", error);
        setError(error.message);
        setLoading(false);
      });
  };
  useEffect(() => {
    fetchData();
  }, []);
  if (loading) {
    return (
      <div className="bg-white rounded-2xl shadow p-6 w-full h-[260px] flex flex-col">
        <div>
          <div className="text-md font-bold text-gray-900 mb-1">Summary</div>
          <div className="text-gray-400 text-sm mb-4">Website Summary</div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 w-full">
          {summaryConfig.map((_, idx) => (
            <div key={idx} className="animate-pulse flex flex-col justify-between rounded-2xl shadow-lg p-6 min-h-[120px] bg-gradient-to-br from-gray-200 to-gray-300">
              <div className="flex items-center gap-3 mb-2">
                <div className="bg-gray-400 rounded-full w-8 h-8"></div>
              </div>
              <div className="h-6 w-3/4 bg-gray-400 rounded"></div>
              <div className="h-4 w-1/2 bg-gray-400 rounded mt-1"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }
  if (error) {
    return (
      <div className="bg-white rounded-2xl shadow p-6 w-full h-[260px] flex flex-col">
        <div>
          <div className="text-md font-bold text-gray-900 mb-1">Summary</div>
          <div className="text-gray-400 text-sm mb-4">Website Summary</div>
        </div>
        <div className="text-red-500">Error: {error}</div>
      </div>
    );
  }
  return (
    <div className="bg-white rounded-2xl shadow p-6 w-full h-[260px] flex flex-col">
      <div>
        <div className="text-md font-bold text-gray-900 mb-1">Summary</div>
        <div className="text-gray-400 text-sm mb-4">Website Summary</div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-6 w-full">
        {summaryConfig.map((config) => {
          const value = apiData ? apiData[config.key] : 0;
          return (
            <SummaryCard
              key={config.label}
              value={config.format ? config.format(value) : value}
              label={config.label}
              icon={config.icon}
              color={config.color}
              onClick={() => onCardClick && onCardClick(config.label)}
            />
          );
        })}
      </div>
    </div>
  );
};
export default Summary;