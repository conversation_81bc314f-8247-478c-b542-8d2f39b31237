'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';
import styles from '../../components/ad-management/rechartsNoOutline.module.css';

const monthlyData = {
  '2024': [
    { month: 'January', banner: 12000, grid: 10000 },
    { month: 'February', banner: 15000, grid: 12000 },
    { month: 'March', banner: 18000, grid: 14000 },
    { month: 'April', banner: 17000, grid: 13000 },
    { month: 'May', banner: 20000, grid: 15000 },
    { month: 'June', banner: 22000, grid: 16000 },
    { month: 'July', banner: 21000, grid: 17000 },
    { month: 'August', banner: 19000, grid: 15000 },
    { month: 'September', banner: 17000, grid: 14000 },
    { month: 'October', banner: 16000, grid: 13000 },
    { month: 'November', banner: 15000, grid: 12000 },
    { month: 'December', banner: 14000, grid: 11000 },
  ],
  '2025': [
    { month: 'January', banner: 13000, grid: 11000 },
    { month: 'February', banner: 16000, grid: 13000 },
    { month: 'March', banner: 19000, grid: 15000 },
    { month: 'April', banner: 18000, grid: 14000 },
    { month: 'May', banner: 21000, grid: 16000 },
    { month: 'June', banner: 23000, grid: 17000 },
    { month: 'July', banner: 22000, grid: 18000 },
    { month: 'August', banner: 20000, grid: 16000 },
    { month: 'September', banner: 18000, grid: 15000 },
    { month: 'October', banner: 17000, grid: 14000 },
    { month: 'November', banner: 16000, grid: 13000 },
    { month: 'December', banner: 15000, grid: 12000 },
  ],
};

const years = Object.keys(monthlyData);

const TotalAdRequestChart = () => {
  const [selectedYear, setSelectedYear] = useState(years[0]);
  const data = monthlyData[selectedYear];

  return (
    <div className={`bg-white rounded-2xl shadow p-6 w-full h-[340px] ${styles.noOutlineRecharts} flex flex-col`}>
      <div className="flex justify-between items-center mb-6">
        <div className="font-semibold text-md">Total Ad Request</div>
        <select
          className="rounded-lg px-3 py-1 text-xs shadow-sm border-0 bg-gray-100 focus:ring-2 focus:ring-purple-300 focus:outline-none transition-all duration-150"
          value={selectedYear}
          onChange={e => setSelectedYear(e.target.value)}
        >
          {years.map(year => (
            <option key={year} value={year}>{year}</option>
          ))}
        </select>
      </div>
      <div className="w-full overflow-x-auto hide-scrollbar">
        <div style={{ minWidth: 1100, height: 230 }}>
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={data} margin={{ top: 10, right: 20, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="month" tick={{ fontSize: 12 }} />
              <YAxis tick={{ fontSize: 12 }} />
              <Tooltip />
              <Legend />
              <Bar dataKey="banner" fill="#232c6a" barSize={24} radius={[8,8,0,0]} legendType="none" />
              <Bar dataKey="grid" fill="#22c55e" barSize={24} radius={[8,8,0,0]} legendType="none" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </div>
      <div className="flex gap-6 mt-2 text-xs">
        <span className="text-[#232c6a]">Banner Ad</span>
        <span className="text-[#22c55e]">Grid Ad</span>
      </div>
    </div>
  );
};

export default TotalAdRequestChart; 