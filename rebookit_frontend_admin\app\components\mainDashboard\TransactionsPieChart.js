'use client';

import React, { useState } from "react";
import ReactApex<PERSON>hart from "react-apexcharts";

const transactionData = [
  {
    label: "Ad",
    value: 30000,
    color: "#2563eb",
    labelColor: "#211F54",
  },
  {
    label: "Membership",
    value: 15251,
    color: "#f59e42",
    labelColor: "#8B8B8B",
  },
];

export default function TransactionsPieChart() {
  const [series] = useState(transactionData.map(item => item.value));
  const colors = transactionData.map(item => item.color);
  const labels = transactionData.map(item => item.label);
  const total = series.reduce((acc, val) => acc + val, 0);

  const options = {
    chart: {
      type: "donut",
      fontFamily: 'Poppins, sans-serif',
    },
    colors,
    labels,
    legend: { show: false },
    plotOptions: {
      pie: {
        donut: {
          size: '65%',
        },
      },
    },
    dataLabels: { enabled: false },
    stroke: { show: false },
    responsive: [
      {
        breakpoint: 640,
        options: { chart: { width: 200 } },
      },
    ],
  };

  return (
    <div className="bg-white gap-4 rounded-xl shadow p-6 flex flex-col" style={{ minHeight: 350, maxWidth: 420 }}>
      <span className="text-md font-semibold mb-4">Transactions</span>
      <div className="flex items-center flex-row justify-between w-full gap-6">
        <div className="flex flex-col items-start" style={{ minWidth: 110 }}>
          {transactionData.map((item, idx) => (
            <div className={`flex items-center ${idx === 0 ? "mb-3" : "mb-6"}`} key={item.label}>
              <span className="inline-block w-3 h-3 rounded-full mr-2" style={{ background: item.color }}></span>
              <span className="text-[14px] font-medium" style={{ color: item.labelColor }}>{item.label}</span>
            </div>
          ))}
          <button
            className="mt-4 px-6 py-2 rounded-full text-white font-semibold text-[10px] shadow"
            style={{
              background: 'linear-gradient(90deg, #2563eb 0%, #211F54 100%)',
              outline: 'none',
              border: 'none',
            }}
          >
            View Detail
          </button>
        </div>
        <div className="mb-4 flex items-center w-full justify-center" style={{ position: 'relative', minWidth: 160, minHeight: 160 }}>
          <ReactApexChart options={options} series={series} type="donut" width={170} height={170} />
          {/* Overlay total in the center of the donut */}
          <div
            style={{
              position: 'absolute',
              left: 0,
              top: 0,
              width: '100%',
              height: '100%',
              zIndex: 100,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              pointerEvents: 'none',
            }}
          >
            <span style={{ color: '#211F54', fontSize: 14, fontWeight: 500, marginBottom: 4 }}>Total</span>
            <span style={{ color: '#2563eb', fontSize: 18, fontWeight: 700 }}>${total.toLocaleString()}</span>
          </div>
        </div>
      </div>
    </div>
  );
} 