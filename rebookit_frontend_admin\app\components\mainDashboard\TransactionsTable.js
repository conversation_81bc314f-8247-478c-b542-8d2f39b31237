import React from 'react';

const mockTransactions = [
  {
    name: '<PERSON>',
    avatar: 'https://randomuser.me/api/portraits/men/32.jpg',
    refId: '#456789356',
    amount: '+$5,670.00',
    status: 'Completed',
  },
  {
    name: '<PERSON>',
    avatar: 'https://randomuser.me/api/portraits/men/45.jpg',
    refId: '#456789357',
    amount: '+$2,340.00',
    status: 'Pending',
  },
  {
    name: '<PERSON>',
    avatar: 'https://randomuser.me/api/portraits/women/65.jpg',
    refId: '#456789358',
    amount: '+$1,200.00',
    status: 'Completed',
  },
  {
    name: '<PERSON>',
    avatar: 'https://randomuser.me/api/portraits/men/12.jpg',
    refId: '#456789359',
    amount: '+$3,500.00',
    status: 'Failed',
  },
];

const statusColors = {
  Completed: 'text-green-600 bg-green-100',
  Pending: 'text-yellow-600 bg-yellow-100',
  Failed: 'text-red-600 bg-red-100',
};

const TransactionsTable = () => {
  return (
    <div className="bg-white rounded-2xl shadow p-6 w-full">
      <div className="font-semibold text-md mb-6">Transactions</div>
      <div className="overflow-x-auto">
        <table className="min-w-full text-sm">
          <thead>
            <tr className="text-left text-gray-500 border-b ">
              <th className="py-2 font-medium text-black">Name</th>
              <th className="py-2 font-medium text-black">Ref ID</th>
              <th className="py-2 font-medium text-black">Amount</th>
              <th className="py-2 font-medium text-black">Status</th>
            </tr>
          </thead>
          <tbody>
            {mockTransactions.map((tx, idx) => (
              <tr key={tx.refId} className="border-b last:border-b-0 border-gray-200">
                <td className="py-3 flex items-center gap-3">
                  <img src={tx.avatar} alt={tx.name} className="w-8 h-8 rounded-full object-cover" />
                  <span className="text-[12px] font-medium text-gray-600">{tx.name}</span>
                </td>
                <td className="py-3 text-[12px]">{tx.refId}</td>
                <td className="py-3 font-semibold text-[12px] text-green-700">{tx.amount}</td>
                <td className="py-3">
                  <span className={`px-3 py-1  text-[12px] rounded-full text-xs font-semibold ${statusColors[tx.status]}`}>{tx.status}</span>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default TransactionsTable; 