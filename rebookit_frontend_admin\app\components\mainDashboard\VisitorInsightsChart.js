'use client';
import React, { useState } from 'react';
import { Line<PERSON>hart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import styles from '../../components/ad-management/rechartsNoOutline.module.css';

const yearlyData = {
  '2024': [
    { month: 'Jan', visitors: 300, members: 200 },
    { month: 'Feb', visitors: 320, members: 220 },
    { month: 'Mar', visitors: 350, members: 250 },
    { month: 'Apr', visitors: 330, members: 230 },
    { month: 'May', visitors: 340, members: 240 },
    { month: 'Jun', visitors: 370, members: 270 },
    { month: 'Jul', visitors: 400, members: 300 },
    { month: 'Aug', visitors: 350, members: 250 },
    { month: 'Sep', visitors: 300, members: 200 },
    { month: 'Oct', visitors: 200, members: 180 },
    { month: 'Nov', visitors: 150, members: 170 },
    { month: 'Dec', visitors: 100, members: 160 },
  ],
  '2025': [
    { month: 'Jan', visitors: 200, members: 100 },
    { month: 'Feb', visitors: 220, members: 120 },
    { month: 'Mar', visitors: 250, members: 150 },
    { month: 'Apr', visitors: 230, members: 130 },
    { month: 'May', visitors: 240, members: 140 },
    { month: 'Jun', visitors: 270, members: 170 },
    { month: 'Jul', visitors: 300, members: 200 },
    { month: 'Aug', visitors: 250, members: 150 },
    { month: 'Sep', visitors: 200, members: 100 },
    { month: 'Oct', visitors: 180, members: 80 },
    { month: 'Nov', visitors: 170, members: 70 },
    { month: 'Dec', visitors: 160, members: 60 },
  ],
};

const years = Object.keys(yearlyData);

const VisitorInsightsChart = () => {
  const [selectedYear, setSelectedYear] = useState(years[0]);
  const data = yearlyData[selectedYear];

  return (
    <div className={`bg-white rounded-2xl shadow p-6 w-full h-[340px] flex flex-col ${styles.noOutlineRecharts}`}>
      <div className="flex justify-between items-center mb-6">
        <div className="font-semibold text-md">Visitor Insights</div>
        <select
          className="rounded-lg px-3 py-1 text-xs shadow-sm border-0 bg-gray-100 focus:ring-2 focus:ring-purple-300 focus:outline-none transition-all duration-150"
          value={selectedYear}
          onChange={e => setSelectedYear(e.target.value)}
        >
          {years.map(year => (
            <option key={year} value={year}>{year}</option>
          ))}
        </select>
      </div>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={data} margin={{ top: 10, right: 20, left: 0, bottom: 0 }}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="month" tick={{ fontSize: 12 }} />
          <YAxis tick={{ fontSize: 12 }} />
          <Tooltip />
          {/* Legend removed */}
          <Line type="monotone" dataKey="visitors" stroke="#f43f5e" strokeWidth={3} dot={{ r: 5 }} />
          <Line type="monotone" dataKey="members" stroke="#22c55e" strokeWidth={3} dot={{ r: 5 }} />
        </LineChart>
      </ResponsiveContainer>
      <div className="flex gap-6 mt-2 text-xs">
        <span className="text-[#f43f5e]">100 Visitors</span>
        <span className="text-[#22c55e]">200 Current Members</span>
      </div>
    </div>
  );
};

export default VisitorInsightsChart; 