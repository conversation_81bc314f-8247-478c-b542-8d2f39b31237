import membershipCss from "@/app/components/membership/membership.module.scss";

import { USER_ROUTES } from '@/app/config/api';
import { resetSubscription } from "@/app/redux/slices/storeSlice";
import { saveMemberShipPlan, updateMemberShipPlan } from "@/app/service/membership";
import { getToken } from '@/app/utils/utils';
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { HiLightningBolt } from "react-icons/hi";
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';



export default function MembershipForm(props) {
    const { setSelectedTab } = props;

    const store = useSelector(x => x.storeData)
    const dispatch = useDispatch()
    const {
        register,
        watch,
        formState: { errors },
        getValues,
        handleSubmit,
        setValue,
        reset,
        resetField,
        setError,


    } = useForm({
        defaultValues: { ...store.currentSubscription }
    })
    const plan = store.currentSubscription
    const [openInputs, setOpenInputs] = useState(["postListing", "featuredListing", "adsListing","active"]);

    const inputsVisibilityHandler = (inputName) => {
        setOpenInputs(prev => !prev.includes(inputName) ? [...prev, inputName] : prev.filter(p => p !== inputName));
    }
    console.log("store", store)
    console.log("openInputs", openInputs)
    // Update Plan if the one of the three does not exists, then remove them ["postListing", "featuredListing", "ad"]
    useEffect(() => {
        if (watch()?.isEdit) {
            const updatedInputs = openInputs.filter(key => !!plan[key]);
            console.log("updatedInputs", updatedInputs)
            setOpenInputs(updatedInputs);
        }
    }, []);

    console.log("watch", watch())

    const onSubmit = async data => {
        console.log("onSubmit data", data)
        try {

            const token = getToken();
            let planPayload = {
                "planName": data.planName,
                "planMonths": data.planMonths,
                "planType": data.planType,
                "price": data.price,
                "postListing": data.postListing,
                "featuredListing": data.featuredListing,
                "adsListing": data.freeAds,
                "ed_Directory": data["ed_Directory"],
                "pastPapers": data.pastPapers,
                "educational_Resource": data["educational_Resource"],
                "active": true
            }

            if (watch()?.isEdit) {
                let response = await updateMemberShipPlan(getValues("_id"), planPayload)

                if (!response.error && response?.data?._id) {
                    toast.success("Successfully Updated !");
                    setSelectedTab(1);
                    dispatch(resetSubscription())

                } else {
                    toast.error(response.message || "Error")
                }
                // fetch(USER_ROUTES.UPDATE_SUBSCRIPTION?.replace("{{planId}}", getValues("_id")), {
                //     method: "PUT",
                //     headers: {
                //         "Content-Type": "application/json",
                //         Authorization: `Bearer ${token}`

                //     },
                //     body: JSON.stringify(planPayload)
                // }).then(async res => {
                //     const response = await res.json();
                //     console.log("response", response)
                //     if (!response.error && response?.data?._id) {
                //         toast.success("Successfully Updated !");
                //         // reset()
                //         setSelectedTab(1);
                //         dispatch(resetSubscription())

                //     } else {
                //         toast.error(response.message || "Error")
                //     }
                // })
            }
            else {
                let response = await saveMemberShipPlan(planPayload)
                if (response.status == 200) {
                    toast.success("Successfully Created the Plan!");
                    resetField("planName")
                    resetField("price")
                    resetField("postListing", { defaultValue: 1 })
                    resetField("featuredListing", { defaultValue: 1 })
                    resetField("freeAds", { defaultValue: 1 })
                    resetField("ed_Directory")
                    resetField("educational_Resource")
                    resetField("pastPapers")
                }
                // fetch(USER_ROUTES.CREATE_SUBSCRIPTION, {
                //     method: "POST",
                //     headers: {
                //         "Content-Type": "application/json",
                //         Authorization: `Bearer ${token}`

                //     },
                //     body: JSON.stringify(planPayload)
                // }).then(async res => {
                //     const response = await res.json();
                //     console.log("response", response)
                //     // if (!response.error && Object.keys(response) > 1) {
                //     if (!response.error) {
                //         toast.success("Successfully Created the Plan!");
                //         // reset()
                //         resetField("planName")
                //         resetField("price")
                //         resetField("postListing", { defaultValue: 1 })
                //         resetField("featuredListing", { defaultValue: 1 })
                //         resetField("freeAds", { defaultValue: 1 })
                //         resetField("ed_Directory")
                //         resetField("educational_Resource")
                //         resetField("pastPapers")
                //     } else {
                //         toast.error(response.message || "Error")
                //     }
                // })
            }
        } catch (error) {
            console.log("error", error)
            toast.error(error.error)
        }
    }



    return (
        <div className={`border border-[#F3F3F3] mt-5 h-full ${membershipCss.container} overflow-auto no_scrollbar`}>
            <div className='w-[55%] overflow-auto'>
                <div className='flex flex-col gap-6 p-6'>
                    <h3 className='text-xl font-semibold leading-normal text-[#0A090B]'>Create Pricing Plan</h3>
                    <div className='py-3 px-4 flex flex-col gap-3 rounded-md border border-[#211F54] bg-[#0161ab0d] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)]'>
                        <div className='p-2'>
                            <HiLightningBolt className='w-6 h-6' />
                        </div>

                        <div className='flex flex-col gap-1.5'>
                            <p className='text-sm font-semibold leading-5'>Membership</p>
                            <p className='text-[13px] leading-[18px] text-[#4F4D55]'>Split the full bundle price over several monthly payments</p>
                        </div>
                    </div>
                </div>

                <form className="bg-[#FDFDFD] p-6 flex flex-col gap-6" onSubmit={handleSubmit(onSubmit)}>
                    <div className="relative flex flex-col gap-[7px]">
                        <label htmlFor="name" className="text-[#2D2B32] text-sm font-medium -tracking-[0.05px] leading-5">Name<span className="text-[#E12121] ">*</span></label>

                        <input
                            type="text"
                            name="name"
                            className="pl-3 pr-2 py-2.5 text-sm leading-5 -tracking-[0.5px] text-[#0A090B] bg-white rounded-lg border border-[#E6E6E6] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)] focus:outline-none"
                            placeholder="Enter Plan Name"
                            {...register('planName', {
                                required: 'Plan Name is required',
                                
                            })}
                        />
                        {errors.planName && (
                            <p className="text-red-500 text-[11px] mt-1 absolute right-0 -bottom-5">
                                {errors.planName.message}
                            </p>
                        )}
                    </div>

                    <div className="flex gap-6 items-center">
                        <div className="flex gap-2 items-center">
                            <div className="relative inline-block h-5 w-5">
                                <input
                                    type="radio"
                                    value="paid"
                                    defaultChecked
                                    className="absolute h-full w-full cursor-pointer appearance-none rounded-sm border border-[#AEAEB2] bg-white checked:border-transparent checked:bg-[linear-gradient(268deg,#211F54_11.09%,#0161AB_98.55%)] peer checked:border-0"
                                    {...register('planType', {})}
                                />
                                <svg
                                    className={`absolute left-1/2 top-1/2 h-3 w-3 -translate-x-1/2 -translate-y-1/2 transform text-white 
                                    opacity-0 peer-checked:opacity-100 pointer-events-none`}
                                    viewBox="0 0 20 20"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M16.6668 5.8335L7.50016 15.0002L3.3335 10.8335"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                            </div>

                            <label htmlFor="planType" className="text-sm leading-normal capitalize text-[#2C2C2E]">Paid Membership plan</label>
                        </div>

                        <div className="flex gap-2 items-center">
                            <div className="relative inline-block h-5 w-5">
                                <input
                                    type="radio"
                                    value="free"
                                    className="absolute h-full w-full cursor-pointer appearance-none rounded-sm border border-[#AEAEB2] bg-white checked:border-transparent checked:bg-[linear-gradient(268deg,#211F54_11.09%,#0161AB_98.55%)] peer checked:border-0"
                                    {...register('planType', {})}
                                />
                                <svg
                                    className={`absolute left-1/2 top-1/2 h-3 w-3 -translate-x-1/2 -translate-y-1/2 transform text-white 
                                    opacity-0 peer-checked:opacity-100 pointer-events-none`}
                                    viewBox="0 0 20 20"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M16.6668 5.8335L7.50016 15.0002L3.3335 10.8335"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                            </div>

                            <label htmlFor="planType" className="text-sm leading-normal capitalize text-[#2C2C2E]">Free Membership plan</label>
                        </div>
                    </div>

                    {getValues("planType") === "paid" && <div className="flex gap-4">
                        <div className="flex flex-col gap-[7px] w-1/2 relative">
                            <label htmlFor="price" className="text-[#2D2B32] text-sm font-medium -tracking-[0.05px] leading-5">Price<span className="text-[#E12121] ">*</span></label>
                            <div className="flex">
                                <select className="px-2 py-2.5 text-sm leading-5 -tracking-[0.5px] text-[#0A090B] bg-white rounded-tl-lg rounded-bl-lg border-r-0 border border-[#E6E6E6] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)] focus:outline-none"
                                    {...register('priceUnit', {
                                        required: getValues("planType") === "paid" ? 'priceUnit is required' : false,
                                    })}
                                >
                                    <option className="px-2" value="JMD">JMD</option>
                                    <option className="px-2" value="USD">USD</option>
                                </select>
                                <input type="number" className="w-[80%] px-2 py-2.5 text-sm leading-5 -tracking-[0.5px] text-[#0A090B] bg-white rounded-tr-lg rounded-br-lg border border-[#E6E6E6] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)] focus:outline-none"
                                    placeholder="Enter Price"
                                    disabled={getValues("planType") == "free"}
                                    {...register('price', {
                                        required: getValues("planType") === "paid" ? 'Price is required' : false,
                                        min: {
                                            value: 1,
                                            message: 'Minimum value is 1',
                                        },
                                    })}
                                />
                            </div>
                            {errors.price && (
                                <p className="text-red-500 text-[11px] mt-1 absolute right-0 -bottom-5">
                                    {errors.price.message}
                                </p>
                            )}
                        </div>

                        <div className="flex flex-col gap-[7px] w-1/2 relative">
                            <label htmlFor="price" className="text-[#2D2B32] text-sm font-medium -tracking-[0.05px] leading-5">Billing Period<span className="text-[#E12121] ">*</span></label>
                            <select className="px-2 py-2.5 text-sm leading-5 -tracking-[0.5px] text-[#0A090B] bg-white rounded-lg border border-[#E6E6E6] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)] focus:outline-none"
                                {...register('planMonths', {
                                    required: getValues("planType") === "paid" ? 'planmonths is required' : false,
                                })}
                            >
                                <option className="px-2" value="Monthly">Monthly</option>
                                <option className="px-2" value="Yearly">Yearly</option>
                            </select>
                            {errors.planMonths && (
                                <p className="text-red-500 text-[11px] mt-1 absolute right-0 -bottom-5">
                                    {errors.planMonths.message}
                                </p>
                            )}
                        </div>
                    </div>}

                    <div className="flex flex-col gap-[7px]">
                        <div className="flex items-center gap-1.5 relative">
                            <p className="text-sm font-medium leading-5 -tracking-[0.5px] text-[#2D2B32]">Post Listing</p>
                            <label className="inline-flex items-center cursor-pointer">
                                <input type="checkbox" value="" className="sr-only peer" checked={openInputs.includes("postListing")} onChange={() => inputsVisibilityHandler("postListing")} />
                                <div className="relative w-[32px] h-[17px] bg-[#F2F2F7] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full after:content-[''] after:absolute after:top-[1.5px] after:start-[2px] after:bg-[#E5E5EA] peer-checked:after:bg-white after:rounded-full after:h-[14px] after:w-[14px] after:transition-all peer-checked:bg-[#1DC9A0]"></div>
                            </label>
                        </div>

                        {openInputs.includes("postListing") && <input type="number" name="name" defaultValue={1} min={0} className="pl-3 pr-2 py-2.5 text-sm leading-5 -tracking-[0.5px] text-[#0A090B] bg-white rounded-lg border border-[#E6E6E6] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)] focus:outline-none"
                            {...register('postListing', {})}
                        />}
                    </div>

                    <div className="flex flex-col gap-[7px]">
                        <div className="flex items-center gap-1.5 relative">
                            <p className="text-sm font-medium leading-5 -tracking-[0.5px] text-[#2D2B32]">Featured Listing</p>
                            <label className="inline-flex items-center cursor-pointer">
                                <input type="checkbox" value="" className="sr-only peer" checked={openInputs.includes("featuredListing")} onChange={() => inputsVisibilityHandler("featuredListing")} />
                                <div className="relative w-[32px] h-[17px] bg-[#F2F2F7] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full after:content-[''] after:absolute after:top-[1.5px] after:start-[2px] after:bg-[#E5E5EA] peer-checked:after:bg-white after:rounded-full after:h-[14px] after:w-[14px] after:transition-all peer-checked:bg-[#1DC9A0]"
                                ></div>
                            </label>
                        </div>

                        {openInputs.includes("featuredListing") && <input type="number" name="name" min={0} defaultValue={1} className="pl-3 pr-2 py-2.5 text-sm leading-5 -tracking-[0.5px] text-[#0A090B] bg-white rounded-lg border border-[#E6E6E6] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)] focus:outline-none"
                            {...register('featuredListing', {})}
                        />}
                    </div>

                    <div className="flex flex-col gap-[7px]">
                        <div className="flex items-center gap-1.5 relative">
                            <p className="text-sm font-medium leading-5 -tracking-[0.5px] text-[#2D2B32]">Ad</p>
                            <label className="inline-flex items-center cursor-pointer">
                                <input type="checkbox" value="" className="sr-only peer" checked={openInputs.includes("adsListing")} onChange={() => inputsVisibilityHandler("ad")} />
                                <div className="relative w-[32px] h-[17px] bg-[#F2F2F7] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full after:content-[''] after:absolute after:top-[1.5px] after:start-[2px] after:bg-[#E5E5EA] peer-checked:after:bg-white after:rounded-full after:h-[13.5px] after:w-[14px] after:transition-all peer-checked:bg-[#1DC9A0]"
                                ></div>
                            </label>
                        </div>

                        {openInputs.includes("adsListing") && <input type="number" name="name" defaultValue={1} min={0} className="pl-3 pr-2 py-2.5 text-sm leading-5 -tracking-[0.5px] text-[#0A090B] bg-white rounded-lg border border-[#E6E6E6] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)] focus:outline-none"
                            {...register('freeAds', {})}
                        />}
                    </div>
                    <div className="flex items-center gap-1.5 relative">
                        <p className="text-sm font-medium leading-5 -tracking-[0.5px] text-[#2D2B32]">Is active</p>
                        <label className="inline-flex items-center cursor-pointer">
                            <input type="checkbox" value="" className="sr-only peer" checked={openInputs.includes("active")} onChange={() => inputsVisibilityHandler("active")} />
                            <div className="relative w-[32px] h-[17px] bg-[#F2F2F7] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full after:content-[''] after:absolute after:top-[1.5px] after:start-[2px] after:bg-[#E5E5EA] peer-checked:after:bg-white after:rounded-full after:h-[14px] after:w-[14px] after:transition-all peer-checked:bg-[#1DC9A0]"
                            ></div>
                        </label>
                    </div>
                    <div className="flex flex-col gap-[15px]">
                        <div className="flex gap-2 items-center">
                            <div className="relative inline-block h-4 w-4">
                                <input
                                    type="checkbox"
                                    className="absolute h-full w-full cursor-pointer appearance-none rounded-sm border-2 border-[#DCDCDE] bg-white checked:border-transparent checked:bg-[linear-gradient(268deg,#211F54_11.09%,#0161AB_98.55%)] peer checked:border-0"
                                    {...register('ed_Directory', {})}
                                />
                                <svg
                                    className={`absolute left-1/2 top-1/2 h-3 w-3 -translate-x-1/2 -translate-y-1/2 transform text-white 
                                    opacity-0 peer-checked:opacity-100 pointer-events-none`}
                                    viewBox="0 0 20 20"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M16.6668 5.8335L7.50016 15.0002L3.3335 10.8335"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                            </div>

                            <label htmlFor="Ed-Directory" className="text-sm leading-5 -tracking-[0.05px]">Access to the Ed-irectory</label>
                        </div>

                        <div className="flex gap-2 items-center">
                            <div className="relative inline-block h-4 w-4">
                                <input
                                    type="checkbox"
                                    className="absolute h-full w-full cursor-pointer appearance-none rounded-sm border-2 border-[#DCDCDE] bg-white checked:border-transparent checked:bg-[linear-gradient(268deg,#211F54_11.09%,#0161AB_98.55%)] peer checked:border-0"
                                    {...register('pastPapers', {})}
                                />
                                <svg
                                    className={`absolute left-1/2 top-1/2 h-3 w-3 -translate-x-1/2 -translate-y-1/2 transform text-white 
                                    opacity-0 peer-checked:opacity-100 pointer-events-none`}
                                    viewBox="0 0 20 20"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M16.6668 5.8335L7.50016 15.0002L3.3335 10.8335"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                            </div>

                            <p className="text-sm leading-5 -tracking-[0.05px]">Access to Past Papers</p>
                        </div>

                        <div className="flex gap-2 items-center">
                            <div className="relative inline-block h-4 w-4">
                                <input
                                    type="checkbox"
                                    className="absolute h-full w-full cursor-pointer appearance-none rounded-sm border-2 border-[#DCDCDE] bg-white checked:border-transparent checked:bg-[linear-gradient(268deg,#211F54_11.09%,#0161AB_98.55%)] peer checked:border-0"
                                    {...register('educational_Resource', {})}
                                />
                                <svg
                                    className={`absolute left-1/2 top-1/2 h-3 w-3 -translate-x-1/2 -translate-y-1/2 transform text-white 
                                    opacity-0 peer-checked:opacity-100 pointer-events-none`}
                                    viewBox="0 0 20 20"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M16.6668 5.8335L7.50016 15.0002L3.3335 10.8335"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                    />
                                </svg>
                            </div>

                            <p className="text-sm leading-5 -tracking-[0.05px]">Access to other Educational Resources</p>
                        </div>
                    </div>

                    <button type="submit" className="global_linear_gradient px-3.5 h-10 text-center text-white text-sm leading-5 -tracking-[0.05px] rounded-full">Save</button>
                </form>

            </div>
        </div>)
}
