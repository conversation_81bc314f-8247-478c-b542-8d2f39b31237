"use client";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, CartesianGrid } from "recharts";

export default function AdTypeChart({ adTypeData, adTypeYear, setAdTypeYear, years }) {
  return (
    <div className="bg-white rounded-xl shadow p-6 col-span-1 flex flex-col" style={{ minHeight: 320 }}>
      <div className="flex justify-between items-center mb-2">
        <span className="font-semibold text-lg">Ad Type</span>
        <div className="flex items-center gap-4">
          <select
            className="rounded-lg px-3 py-1 text-xs shadow-sm border-0 bg-gray-100 focus:ring-2 focus:ring-blue-300 focus:outline-none transition-all duration-150  text-black"
            value={adTypeYear}
            onChange={e => setAdTypeYear(Number(e.target.value))}
          >
            {years.map(y => (
              <option key={y} value={y}>{y}</option>
            ))}
          </select>
          <span className="text-2xl font-bold">200</span>
        </div>
      </div>
      <ResponsiveContainer width="100%" height="100%">
        <LineChart data={adTypeData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="month" tick={{ fontSize: 13 }} axisLine={false} tickLine={false} interval={0} />
          <YAxis tick={{ fontSize: 13 }} axisLine={false} tickLine={false} />
          <Tooltip formatter={v => v} />
          <Line type="monotone" dataKey="Banner" stroke="#ef4444" strokeWidth={3} dot={false} activeDot={{ r: 8, fill: '#ef4444' }} />
          <Line type="monotone" dataKey="Grid" stroke="#22c55e" strokeWidth={3} dot={false} activeDot={{ r: 8, fill: '#22c55e' }} />
        </LineChart>
      </ResponsiveContainer>
      <div className="flex gap-4 text-xs items-center mt-2">
        <span className="flex items-center gap-1"><span className="w-3 h-3 bg-red-500 inline-block rounded-sm"></span> Banner</span>
        <span className="flex items-center gap-1"><span className="w-3 h-3 bg-green-500 inline-block rounded-sm"></span> Grid</span>
      </div>
    </div>
  );
} 