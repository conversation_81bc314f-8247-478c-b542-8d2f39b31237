"use client";
import { useState } from "react";
import SummaryCards from "./SummaryCards";
import AdType<PERSON>hart from "./AdTypeChart";
import TotalSpendChart from "./TotalSpendChart";
import ViewsPerAdTypePie from "./ViewsPerAdTypePie";
import WeeklyExpensesChart from "./WeeklyExpensesChart";

import {
  LineChart, Line, XAxis, YAxis, Tooltip, ResponsiveContainer, CartesianGrid, BarChart, Bar, PieChart, Pie, Cell, Legend
} from "recharts";

const years = [2025, 2024, 2023];

const summaryCards = [
  { label: "$1k", sub: "Total Spend", color: "bg-purple-700", icon: true },
  { label: "250", sub: "Total Ads", color: "bg-green-700", icon: true },
  { label: "50", sub: "Total Days", color: "bg-orange-400", icon: true },
  { label: "J$1,000", sub: "Your balance", color: "bg-green-300", icon: false },
];

const adTypeDataByYear = {
  2025: [
    { month: "Jan", Banner: 50000, Grid: 80000 },
    { month: "Feb", Banner: 60000, Grid: 90000 },
    { month: "Mar", Banner: 55000, Grid: 85000 },
    { month: "Apr", Banner: 70000, Grid: 100000 },
    { month: "May", Banner: 60000, Grid: 95000 },
    { month: "Jun", Banner: 80000, Grid: 110000 },
    { month: "Jul", Banner: 75000, Grid: 105000 },
    { month: "Aug", Banner: 65000, Grid: 95000 },
    { month: "Sep", Banner: 70000, Grid: 120000 },
    { month: "Oct", Banner: 60000, Grid: 110000 },
    { month: "Nov", Banner: 55000, Grid: 90000 },
    { month: "Dec", Banner: 65000, Grid: 115000 },
  ],
  2024: [
    { month: "Jan", Banner: 40000, Grid: 70000 },
    { month: "Feb", Banner: 50000, Grid: 80000 },
    { month: "Mar", Banner: 45000, Grid: 75000 },
    { month: "Apr", Banner: 60000, Grid: 90000 },
    { month: "May", Banner: 50000, Grid: 85000 },
    { month: "Jun", Banner: 70000, Grid: 100000 },
    { month: "Jul", Banner: 65000, Grid: 95000 },
    { month: "Aug", Banner: 55000, Grid: 85000 },
    { month: "Sep", Banner: 60000, Grid: 110000 },
    { month: "Oct", Banner: 50000, Grid: 100000 },
    { month: "Nov", Banner: 45000, Grid: 80000 },
    { month: "Dec", Banner: 55000, Grid: 105000 },
  ],
  2023: [
    { month: "Jan", Banner: 30000, Grid: 60000 },
    { month: "Feb", Banner: 40000, Grid: 70000 },
    { month: "Mar", Banner: 35000, Grid: 65000 },
    { month: "Apr", Banner: 50000, Grid: 80000 },
    { month: "May", Banner: 40000, Grid: 75000 },
    { month: "Jun", Banner: 60000, Grid: 90000 },
    { month: "Jul", Banner: 55000, Grid: 85000 },
    { month: "Aug", Banner: 45000, Grid: 75000 },
    { month: "Sep", Banner: 50000, Grid: 100000 },
    { month: "Oct", Banner: 40000, Grid: 90000 },
    { month: "Nov", Banner: 35000, Grid: 70000 },
    { month: "Dec", Banner: 45000, Grid: 95000 },
  ],
};

const spendDataByYear = {
  2025: [
    { month: "Jan", spend: 400 },
    { month: "Feb", spend: 300 },
    { month: "Mar", spend: 500 },
    { month: "Apr", spend: 700 },
    { month: "May", spend: 600 },
    { month: "Jun", spend: 800 },
    { month: "Jul", spend: 500 },
  ],
  2024: [
    { month: "Jan", spend: 300 },
    { month: "Feb", spend: 200 },
    { month: "Mar", spend: 400 },
    { month: "Apr", spend: 600 },
    { month: "May", spend: 500 },
    { month: "Jun", spend: 700 },
    { month: "Jul", spend: 400 },
  ],
  2023: [
    { month: "Jan", spend: 200 },
    { month: "Feb", spend: 100 },
    { month: "Mar", spend: 300 },
    { month: "Apr", spend: 500 },
    { month: "May", spend: 400 },
    { month: "Jun", spend: 600 },
    { month: "Jul", spend: 300 },
  ],
};

const pieDataByTab = {
  Weekly: [
    { name: "Banner", value: 35, color: "#fb7e4b" },
    { name: "Grid", value: 65, color: "#1e293b" },
  ],
  Monthly: [
    { name: "Banner", value: 45, color: "#fb7e4b" },
    { name: "Grid", value: 55, color: "#1e293b" },
  ],
  Yearly: [
    { name: "Banner", value: 25, color: "#fb7e4b" },
    { name: "Grid", value: 75, color: "#1e293b" },
  ],
};

const weeklyExpensesByYear = {
  2025: [
    { week: "00", Banner: 100, Grid: 200, Featured: 150 },
    { week: "04", Banner: 120, Grid: 180, Featured: 170 },
    { week: "08", Banner: 90, Grid: 160, Featured: 140 },
    { week: "12", Banner: 110, Grid: 210, Featured: 160 },
    { week: "14", Banner: 130, Grid: 190, Featured: 180 },
    { week: "18", Banner: 140, Grid: 220, Featured: 200 },
    { week: "20", Banner: 150, Grid: 230, Featured: 210 },
    { week: "22", Banner: 160, Grid: 240, Featured: 220 },
    { week: "24", Banner: 170, Grid: 250, Featured: 230 },
    { week: "26", Banner: 180, Grid: 260, Featured: 240 },
    { week: "28", Banner: 190, Grid: 270, Featured: 250 },
  ],
  2024: [
    { week: "00", Banner: 80, Grid: 150, Featured: 120 },
    { week: "04", Banner: 100, Grid: 130, Featured: 140 },
    { week: "08", Banner: 70, Grid: 110, Featured: 110 },
    { week: "12", Banner: 90, Grid: 160, Featured: 120 },
    { week: "14", Banner: 110, Grid: 140, Featured: 140 },
    { week: "18", Banner: 120, Grid: 170, Featured: 160 },
    { week: "20", Banner: 130, Grid: 180, Featured: 170 },
    { week: "22", Banner: 140, Grid: 190, Featured: 180 },
    { week: "24", Banner: 150, Grid: 200, Featured: 190 },
    { week: "26", Banner: 160, Grid: 210, Featured: 200 },
    { week: "28", Banner: 170, Grid: 220, Featured: 210 },
  ],
  2023: [
    { week: "00", Banner: 60, Grid: 100, Featured: 80 },
    { week: "04", Banner: 80, Grid: 90, Featured: 100 },
    { week: "08", Banner: 50, Grid: 70, Featured: 60 },
    { week: "12", Banner: 70, Grid: 120, Featured: 80 },
    { week: "14", Banner: 90, Grid: 100, Featured: 100 },
    { week: "18", Banner: 100, Grid: 130, Featured: 120 },
    { week: "20", Banner: 110, Grid: 140, Featured: 130 },
    { week: "22", Banner: 120, Grid: 150, Featured: 140 },
    { week: "24", Banner: 130, Grid: 160, Featured: 150 },
    { week: "26", Banner: 140, Grid: 170, Featured: 160 },
    { week: "28", Banner: 150, Grid: 180, Featured: 170 },
  ],
};

const COLORS = ["#fb7e4b", "#1e293b", "#22c55e"];

export default function MainDashboard() {
  const [pieTab, setPieTab] = useState("Weekly");
  const [adTypeYear, setAdTypeYear] = useState(years[0]);
  const [spendYear, setSpendYear] = useState(years[0]);
  const [weeklyYear, setWeeklyYear] = useState(years[0]);

  return (
    <div className="bg-white rounded-lg p-3 ">
      {/* Summary Cards */}
      <SummaryCards summaryCards={summaryCards} />
      {/* Main Grid */}
      <div className="grid grid-cols-2 gap-6 mb-6">
        <AdTypeChart
          adTypeData={adTypeDataByYear[adTypeYear]}
          adTypeYear={adTypeYear}
          setAdTypeYear={setAdTypeYear}
          years={years}
        />
        <TotalSpendChart
          spendData={spendDataByYear[spendYear]}
          spendYear={spendYear}
          setSpendYear={setSpendYear}
          years={years}
        />
      </div>
      {/* Lower Grid */}
      <div className="grid grid-cols-2 gap-6">
        <ViewsPerAdTypePie
          pieTab={pieTab}
          setPieTab={setPieTab}
          pieData={pieDataByTab[pieTab]}
          pieDataRaw={pieDataByTab[pieTab]}
        />
        <WeeklyExpensesChart
          weeklyExpenses={weeklyExpensesByYear[weeklyYear]}
          weeklyYear={weeklyYear}
          setWeeklyYear={setWeeklyYear}
          years={years}
        />
      </div>
    </div>
  );
} 