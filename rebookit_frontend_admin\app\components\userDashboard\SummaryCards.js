"use client";

export default function SummaryCards({ summaryCards }) {
  return (
    <div className="grid grid-cols-4 gap-6 mb-6">
      {summaryCards.map((card, i) => (
        <div key={i} className={`rounded-xl shadow flex items-center p-6 ${card.color} text-white relative`} style={{ minHeight: 90 }}>
          <div>
            <div className="text-2xl font-bold flex items-center gap-2">
              {card.icon && (
                <svg width="24" height="24" fill="none" className="inline-block"><rect width="24" height="24" rx="6" fill="#fff" fillOpacity=".1"/><path d="M7 17h10M9 13v4m3-8v8m3-6v6" stroke="#fff" strokeWidth="2" strokeLinecap="round"/></svg>
              )}
              {card.label}
            </div>
            <div className="text-sm opacity-80">{card.sub}</div>
          </div>
          {!card.icon && (
            <div className="absolute top-3 right-3">
              <svg width="40" height="40"><circle cx="32" cy="8" r="8" fill="#fff" fillOpacity=".3"/></svg>
            </div>
          )}
        </div>
      ))}
    </div>
  );
} 