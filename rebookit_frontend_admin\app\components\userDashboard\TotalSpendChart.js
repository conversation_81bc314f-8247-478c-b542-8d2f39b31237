"use client";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>sponsive<PERSON><PERSON><PERSON>, CartesianGrid } from "recharts";

export default function TotalSpendChart({ spendData, spendYear, setSpendYear, years }) {
  return (
    <div className="bg-white rounded-xl shadow p-6 col-span-1 flex flex-col" style={{ minHeight: 320 }}>
      <div className="flex justify-between items-center mb-2">
        <span className="font-semibold text-lg">Total Spend</span>
        <div className="flex items-center gap-4">
       
        <select
          className="rounded-lg px-3 py-1 text-xs shadow-sm border-0 bg-gray-100 focus:ring-2 focus:ring-blue-300 focus:outline-none transition-all duration-150  text-black"
          value={spendYear}
          onChange={e => setSpendYear(Number(e.target.value))}
        >
          {years.map(y => (
            <option key={y} value={y}>{y}</option>
          ))}
        </select>
        <span className="text-2xl font-bold">J$ 1k</span>
        </div>
      </div>
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={spendData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="month" tick={{ fontSize: 13 }} axisLine={false} tickLine={false} interval={0} />
          <YAxis tick={{ fontSize: 13 }} axisLine={false} tickLine={false} />
          <Tooltip formatter={v => v} />
          <Bar dataKey="spend" fill="url(#spendGradient)" barSize={32} radius={[6, 6, 0, 0]} />
          <defs>
            <linearGradient id="spendGradient" x1="0" y1="0" x2="0" y2="1">
              <stop offset="0%" stopColor="#3b82f6" />
              <stop offset="100%" stopColor="#a7f3d0" />
            </linearGradient>
          </defs>
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
} 