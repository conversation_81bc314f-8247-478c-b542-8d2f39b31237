"use client";
import { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, ResponsiveContainer } from "recharts";

const YEARS = [2024, 2023, 2022];
const PERIODS = ["Weekly", "Monthly", "Yearly"];
const DEFAULT_DATA = {
  2024: {
    Weekly: [
      { name: "<PERSON>", value: 35, color: "#EA6B4D" },
      { name: "Grid", value: 65, color: "#1B4065" },
    ],
    Monthly: [
      { name: "Banner", value: 40, color: "#EA6B4D" },
      { name: "Grid", value: 60, color: "#1B4065" },
    ],
    Yearly: [
      { name: "<PERSON>", value: 30, color: "#EA6B4D" },
      { name: "Grid", value: 70, color: "#1B4065" },
    ],
  },
  2023: {
    Weekly: [
      { name: "Banner", value: 45, color: "#EA6B4D" },
      { name: "Grid", value: 55, color: "#1B4065" },
    ],
    Monthly: [
      { name: "<PERSON>", value: 50, color: "#EA6B4D" },
      { name: "<PERSON>rid", value: 50, color: "#1B4065" },
    ],
    Yearly: [
      { name: "<PERSON>", value: 35, color: "#EA6B4D" },
      { name: "Grid", value: 65, color: "#1B4065" },
    ],
  },
  2022: {
    Weekly: [
      { name: "Banner", value: 60, color: "#EA6B4D" },
      { name: "Grid", value: 40, color: "#1B4065" },
    ],
    Monthly: [
      { name: "Banner", value: 55, color: "#EA6B4D" },
      { name: "Grid", value: 45, color: "#1B4065" },
    ],
    Yearly: [
      { name: "Banner", value: 50, color: "#EA6B4D" },
      { name: "Grid", value: 50, color: "#1B4065" },
    ],
  },
};

export default function ViewsPerAdTypePie({ data }) {
  const [selectedYear, setSelectedYear] = useState(YEARS[0]);
  const [pieTab, setPieTab] = useState(PERIODS[0]);
  const pieData = (data || DEFAULT_DATA)[selectedYear][pieTab];

  return (
    <div className="bg-white rounded-xl shadow p-6 flex flex-col" style={{ minHeight: 370 }}>
      {/* Heading and period tabs in one row */}
      <div className="flex justify-between items-center mb-2">
        <span className="font-semibold text-lg">Views Per Ad Type</span>
        <div className="flex gap-2 text-xs bg-gray-100 rounded-lg p-1">
          {PERIODS.map(tab => (
            <button
              key={tab}
              className={`px-3 py-1 rounded transition-all duration-150 ${pieTab === tab ? 'bg-white font-semibold shadow' : 'text-gray-500'}`}
              onClick={() => setPieTab(tab)}
            >
              {tab}
            </button>
          ))}
        </div>
      </div>
      {/* Year selector below the tabs */}
      <div className="flex justify-end mb-2">
        <select
                    className="rounded-lg px-3 py-1 text-xs shadow-sm border-0 bg-gray-100 focus:ring-2 focus:ring-blue-300 focus:outline-none transition-all duration-150  text-black"

          value={selectedYear}
          onChange={e => setSelectedYear(Number(e.target.value))}
        >
          {YEARS.map(year => (
            <option key={year} value={year}>{year}</option>
          ))}
        </select>
      </div>
      <ResponsiveContainer width="100%" height="100%">
        <PieChart>
          <Pie
            data={pieData}
            dataKey="value"
            nameKey="name"
            cx="50%"
            cy="50%"
            outerRadius={70}
            innerRadius={0}
            labelLine={false}
            isAnimationActive={false}
          >
            {pieData.map((entry, idx) => (
              <Cell key={`cell-${idx}`} fill={entry.color} />
            ))}
          </Pie>
        </PieChart>
      </ResponsiveContainer>
      <div className="flex justify-center gap-16 mt-6 text-base">
        {pieData.map((entry, idx) => (
          <div key={entry.name} className="flex flex-col items-center">
            <div className="flex items-center gap-2 mb-1">
              <span className="w-3 h-3 rounded-full" style={{ background: entry.color }}></span>
              <span className="font-medium text-gray-800">{entry.name}</span>
            </div>
            <span className="font-semibold text-gray-900">{entry.value}%</span>
          </div>
        ))}
      </div>
    </div>
  );
} 