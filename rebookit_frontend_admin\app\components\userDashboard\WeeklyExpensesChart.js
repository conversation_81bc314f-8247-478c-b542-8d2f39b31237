"use client";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Responsive<PERSON><PERSON><PERSON>, CartesianGrid } from "recharts";

export default function WeeklyExpensesChart({ weeklyExpenses, weeklyYear, setWeeklyYear, years }) {
  return (
    <div className="bg-white rounded-xl shadow p-6 flex flex-col" style={{ minHeight: 320 }}>
      <div className="flex justify-between items-center mb-2">
        <span className="font-semibold text-lg">Weekly expenses</span>
        <select
          className="rounded-lg px-3 py-1 text-xs shadow-sm border-0 bg-gray-100 focus:ring-2 focus:ring-blue-300 focus:outline-none transition-all duration-150 text-black"
          value={weeklyYear}
          onChange={e => setWeeklyYear(Number(e.target.value))}
        >
          {years.map(y => (
            <option key={y} value={y}>{y}</option>
          ))}
        </select>
      </div>
      <div style={{ flex: 1, minHeight: 0 }}>
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={weeklyExpenses} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis dataKey="week" tick={{ fontSize: 13 }} axisLine={false} tickLine={false} interval={0} />
            <YAxis tick={{ fontSize: 13 }} axisLine={false} tickLine={false} />
            <Tooltip formatter={v => v} />
            <Bar dataKey="Banner" stackId="a" fill="#1e293b" barSize={18} radius={[6, 6, 0, 0]} />
            <Bar dataKey="Grid" stackId="a" fill="#22c55e" barSize={18} radius={[6, 6, 0, 0]} />
            <Bar dataKey="Featured" stackId="a" fill="#d1fae5" barSize={18} radius={[6, 6, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
}