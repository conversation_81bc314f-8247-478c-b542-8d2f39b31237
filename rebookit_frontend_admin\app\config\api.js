const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL;

const USER_BASE_URL = `${BASE_URL}`;
const CHAT_BASE_URL = `${BASE_URL}/chat`;
const ELASTIC_BASE_URL = `${BASE_URL}/elasticsearch`;

// const USER_BASE_URL = `http://localhost:4002`;
// const CHAT_BASE_URL = `http://localhost:4001`;
// const ELASTIC_BASE_URL = `http://localhost:4004`;



const USER_ROUTES = {

    // ADMIN ROUTES
    GET_ADMIN: `${USER_BASE_URL}/api/admin`,

    //LOGIN ROUTES
    lOGIN: `${USER_BASE_URL}/api/user/login`,
    REGISTER: `${USER_BASE_URL}/api/user/register`,
    SEND_OTP: `${USER_BASE_URL}/api/user/send-otp`,
    VERIFY_OTP: `${USER_BASE_URL}/api/user/verify-otp`,
    RESEND_OTP: `${USER_BASE_URL}/api/user/resend-otp`,
    FORGOT_PASSWORD: `${USER_BASE_URL}/api/user/forgot-password`,
    CREATE_NEW_PASSWORD: `${USER_BASE_URL}/api/user/create-new-password`,


    // book search routes
    ISBN_SEARCH: `${USER_BASE_URL}/api/books/isbn/{{ISBN}}`,

    // upload
    MULTI_UPLOAD_FILES: `${USER_BASE_URL}/api/admin/multiple-upload`,
    SINGLE_UPLOAD_FILE: `${USER_BASE_URL}/api/admin/single-upload`,


    // USER INFO
    USER_INFO: `${USER_BASE_URL}/api/user`,
    EDIT_USER_INFO: `${USER_BASE_URL}/api/user/edit-profile`,


    // list item
    LIST_ITEM: `${USER_BASE_URL}/api/item/search/admin`,
    DELETE_LIST_ITEM: `${USER_BASE_URL}/api/admin/item`,
    UPDATE_LIST_ITEM: `${USER_BASE_URL}/api/admin/item/actionStatus`,



    // get subscription plans     
    GET_ALL_SUBSCRIPTION: `${USER_BASE_URL}/api/admin/subscription/plan`,
    CREATE_SUBSCRIPTION: `${USER_BASE_URL}/api/admin/create/subscription/plan`,
    UPDATE_SUBSCRIPTION: `${USER_BASE_URL}/api/ admin/subscription/plan/{{planId}}`,


    // dashboard routes
    GET_OVERVIEW: `${USER_BASE_URL}/api/admin/dashboard/overview`,
    GET_LAST_MONTH_SUBSCRIBERS: `${USER_BASE_URL}/api/admin/dashboard/lastMonthSubscribers`,
    GET_TOTAL_AD_REQUEST: `${USER_BASE_URL}/api/admin/dashboard/totalAdRequest`,








}

const CHAT_ROUTES = {

    SERVER: ``
}


const ELASTIC_DB_ROUTES = {
    SEARCH: ``
}


module.exports = {
    USER_ROUTES,
    CHAT_ROUTES,
    ELASTIC_DB_ROUTES
}