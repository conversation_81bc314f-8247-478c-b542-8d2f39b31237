const ItemKindEnum = {
  BookItem: "BookItem",
  TutorItem: "TutorItem",
  EventItem: "EventItem",
  SchoolItem: "SchoolItem",
  ExtracurricularActivityItem: "ExtracurricularActivityItem",
  ScholarshipAwardItem: "ScholarshipAwardItem",
};
const itemToKind = {
  BookItem: {
    isbn_number: "isbn_number",
    authors: "authors",
    condition: "condition",
  },
  TutorItem: {
    highestQualification: "highestQualification",
    targetClasses: "targetClasses",
    experience: "experience",
    website: "website",
  },
  SchoolItem: {
    schoolType: "schoolType",
    classesOffered: "classesOffered",
    website: "website",
  },
  EventItem: {
    eventMode: "eventMode",
    eventStartDate: "eventStartDate",
    eventEndDate: "eventEndDate",
    website: "website",
  },
  ExtracurricularActivityItem: {
    activityType: "activityType",
    frequency: "frequency",
    targetStudents: "targetStudents",
  },
  ScholarshipAwardItem: {
    eligibilityCriteria: "eligibilityCriteria",
    scholarshipType: "scholarshipType",
    website: "website",
  },
};

const labelItemToKind = {
  BookItem: {isbn_number: "ISBN", authors: "Authors", condition: "Condition"},
  TutorItem: {
    highestQualification: "Qualification",
    targetClasses: "Target Classes",
    experience: "Experience",
    website: "Website",
  },
  SchoolItem: {
    schoolType: "School Type",
    classesOffered: "Classes Offered",
    website: "Website",
  },
  EventItem: {
    eventMode: "Event Mode",
    eventStartDate: "EventStart Date",
    eventEndDate: "EventEnd Date",
    website: "Website",
  },
  ExtracurricularActivityItem: {
    activityType: "Activity Type",
    frequency: "Frequency",
    targetStudents: "Target Students",
  },
  ScholarshipAwardItem: {
    eligibilityCriteria: "Eligibility Criteria",
    scholarshipType: "Scholarship Type",
    website: "Website",
  },
};

// module {ItemKindEnum, itemToKind, labelItemToKind};
export {ItemKindEnum, itemToKind, labelItemToKind};
