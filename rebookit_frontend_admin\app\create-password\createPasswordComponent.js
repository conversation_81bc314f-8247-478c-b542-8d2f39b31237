"use client"
import { useForm } from "react-hook-form"
import dynamic from "next/dynamic";
import createPassCss from "./createPassword.module.scss"


// components 
const CustomSlider = dynamic(() => import('@/app/components/common/Slider'));
// const BuyAndSellComponent = dynamic(() => import('@/app/components/about/BuyAndSellComponent'));


import { IoEyeSharp } from "react-icons/io5";
import { FaRegEyeSlash } from "react-icons/fa";



import dummyImage from "@/public/test.jpeg"

import Link from "next/link";
import Image from "next/image";


import { RiArrowLeftSLine } from "react-icons/ri";
import { useEffect, useState } from "react";
import { USER_ROUTES } from "../config/api";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";
import { useSelector } from "react-redux";



export default function LoginPage() {
    const router = useRouter()
    const otp = useSelector(state => state.storeData.otp);
    const [passwordView, setPasswordView] = useState(false)
    const [confirm_passwordView, setconfirm_passwordView] = useState(false)
    const [token, setToken] = useState(null);

    useEffect(() => { setToken(localStorage.getItem("token")) }, [])

    const {
        register,
        watch,
        formState: { errors },
        getValues,
        handleSubmit,
        setValue,
        reset,
        setError,


    } = useForm()

    const Images = [dummyImage, dummyImage, dummyImage]


    const onSubmit = data => {
        console.log('Form submitted:', data);
        try {
            const payload = { newPassword: data.password, otp: otp.value }
            // api call 
            fetch(USER_ROUTES.CREATE_NEW_PASSWORD, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    Authorization: `Bearer ${token}`
                },
                body: JSON.stringify(payload)
            }).then(async res => {
                const response = await res.json();

                if (response.success) {
                    toast.success("Successfully Updated the password!");
                    router.push("/");
                } else {
                    toast.error("Error updating Password");
                }
            })
        } catch (error) {
            console.log("error", error)
        }

    };


    const settings = {
        dots: true,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 3000
    };

    return (
        <div className={`${createPassCss.loginContainer}`}>

            <section className="md:w-6/12">
                <div className={`${createPassCss.imageContainer}`}>
                    {/* <LoginSlider ImageSlider={Images} /> */}

                    <CustomSlider
                        sliderSettings={settings}
                    >
                        {
                            Images.map((image, idx) =>
                                <Image key={idx} src={image} alt="login page images" className="h-full w-full rounded-2xl p-1" />
                            )
                        }
                    </CustomSlider>
                </div>

            </section>


            <section className="md:w-5/12 my-5 md:mt-20">

                <h2 className="text-[18px] font-semibold md:text-[40px] md:font-semibold">Set a password?</h2>
                <p className="mt-3 font-extralight text-[14px] md:text-[18px] md:w-10/12">Your previous password has been reseted. Please set a new password for your account.</p>


                <form onSubmit={handleSubmit(onSubmit)} className="text-[#1C1B1F] my-5">
                    {/* Email Input */}
                    <div className="relative h-[90px] md:h-[100px]">
                        <fieldset className={` ${createPassCss.fields}`}>
                            <legend className="text-[14px] font-medium px-[7px]">Password</legend>
                            <label htmlFor="password" className="sr-only">Password</label>
                            <input
                                id="password"
                                type={passwordView ? "text" : "password"}
                                autoComplete="current-password"
                                {...register('password', {
                                    required: 'Valid Password is Required',
                                })}
                                className="w-full text-[13px] md:text-[17px] outline-none border-none"
                                aria-invalid={!!errors.password}
                                aria-describedby="password-error"
                            />
                            <div
                                className="absolute top-[29px] right-[17px]"
                                onClick={() => setPasswordView(!passwordView)}
                                role="button"
                                tabIndex={0}
                                aria-label={passwordView ? "Hide password" : "Show password"}
                                onKeyDown={(e) => e.key === 'Enter' && setPasswordView(!passwordView)}
                            >
                                {passwordView ? (
                                    <FaRegEyeSlash className="cursor-pointer" size={20} />
                                ) : (
                                    <IoEyeSharp className="cursor-pointer" size={20} />
                                )}
                            </div>
                        </fieldset>
                        {errors.password && (
                            <p id="password-error" className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0">
                                {errors.password.message}
                            </p>
                        )}
                    </div>

                    <div className="relative h-[90px] md:h-[100px]">
                        <fieldset className={` ${createPassCss.fields}`}>
                            <legend className="text-[14px] font-medium px-[7px]">Confirm Password</legend>
                            <label htmlFor="password" className="sr-only">Confirm Password</label>
                            <input
                                id="confirm-password"
                                type={confirm_passwordView ? "text" : "password"}
                                autoComplete="Confirm-Password"
                                {...register('Confirm_Password', {
                                    required: 'Valid Password is Required',
                                    validate: (value) =>
                                        value === watch("password") || "Passwords do not match"
                                })}
                                className="w-full text-[13px] md:text-[17px] outline-none border-none"
                                aria-invalid={!!errors.Confirm_Password}
                                aria-describedby="password-error"
                            />
                            <div
                                className="absolute top-[29px] right-[17px]"
                                onClick={() => setconfirm_passwordView(!confirm_passwordView)}
                                role="button"
                                tabIndex={0}
                                aria-label={confirm_passwordView ? "Hide password" : "Show password"}
                                onKeyDown={(e) => e.key === 'Enter' && setconfirm_passwordView(!confirm_passwordView)}
                            >
                                {confirm_passwordView ? (
                                    <FaRegEyeSlash className="cursor-pointer" size={20} />
                                ) : (
                                    <IoEyeSharp className="cursor-pointer" size={20} />
                                )}
                            </div>
                        </fieldset>
                        {errors.Confirm_Password && (
                            <p id="password-error" className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0">
                                {errors.Confirm_Password.message}
                            </p>
                        )}
                    </div>


                    {/* Submit Button */}
                    <button type="submit" className={`${createPassCss.submitButton} my-2`}>
                        Set Password
                    </button>

                </form>


            </section>



            <section className='my-5 md:my-0 md:w-12/12'>
                <BuyAndSellComponent />
            </section>


        </div>
    )
}
