"use client"

import React, { useState } from 'react'
import { RiDeleteBin6Line } from "react-icons/ri";
import { FaChevronDown } from "react-icons/fa";
import styles from  "./faq.module.scss"
import { getFaqs } from '../service/faq';
import { useRouter } from 'next/navigation';

function Component({item ,onEditHandle,deleteFAQFunc}) {
    const [open,setOpen] =useState(false)
    const router= useRouter()
    console.log("open",open)    

    const getFaqFunc =async()=>{
       let response= await getFaqs()

    }
    console.log("item",item)
    return (

        <div className='relative border-[1px] p-3 border-[#0161AB] rounded-md mt-[50px]'>
            <div className='flex justify-between '><p className='font-medium text-[20px]'>{item.question}</p>
             <div className='flex items-center text-[#0161AB]'  ><span onClick={()=>onEditHandle(item)}>Edit</span> <span className='px-2'>|</span> <RiDeleteBin6Line onClick={()=>deleteFAQFunc(item._id)} /><FaChevronDown onClick={()=>setOpen(!open)} className={`mx-2 ${styles.chvronIcon} ${open?styles.rotate:""}`}  /></div>  </div>
            <div className={`mt-3  ${styles.expandableText} ${open ? styles.open : ''}`}>{item.answer}</div>
        </div>

    )
}

export default Component