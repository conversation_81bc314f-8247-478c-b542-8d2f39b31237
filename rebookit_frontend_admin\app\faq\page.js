"use client"

import React, { useEffect, useState } from 'react'
import SubmitButton from '../components/common/SubmitButton'
import { RiDeleteBin6Line } from "react-icons/ri";
import { FaChevronDown } from "react-icons/fa";
import Component from './component';
import { addFaq, deleteFaq, getFaqs, updateFaq } from '../service/faq';
import { toast } from 'react-toastify';
import NoDataFound from '../components/common/NoDataFound';
import Popup from '../components/common/PopUp';
import { useRouter } from 'next/navigation';

export default function page() {
    const [submitisLoading, setsubmitisLoading] = useState(false)
    const [questionchange, setquestionchange] = useState("")
    const [anwserChange, setanwserChange] = useState("")
    const [selectedFaq, setselectedFaq] = useState("")
    const [allfaq, setallfaq] = useState({})
    const [isEdit, setisEdit] = useState(false)
    const [deleteBoolean, setdeleteBoolean] = useState(false)
    const [isLoading, setisLoading] = useState(false)
    const router= useRouter()
    const [deleteObject, setdeleteObject] = useState({
        title: "",
        message: "",
        confirmText: "",
        cancelText: "",
        onConfirm: {},
        onCancel: () => { },
    });
    const InnerDiv = () => {
        return <div>Publish</div>
    }

    const submitQuestion = async () => {
        setsubmitisLoading(true)

        let payload = {
            "question": questionchange,
            "answer": anwserChange,
        }
        let response = {}
        if (!isEdit) {
            response = await addFaq(payload)
        } else {
            response = await updateFaq(payload, selectedFaq._id)
        }

        console.log("response addFaq", response)
        if (response.status == 200) {
            toast.success(`${isEdit ? "updated Successfully" : "Added Successfully"}`)
            document.getElementById("myModal").classList.add("hidden")
            resetStates()
        }

        getFeqsFunction()

        setsubmitisLoading(false)
    }

    const getFeqsFunction = async () => {
        let response = await getFaqs()
        if (response.status == 200) {
            setallfaq(response.data.data)
        }
    }
    const resetStates = () => {
        setselectedFaq({})
        setanwserChange("")
        setquestionchange("")
        setisEdit(false)
    }
    const updateFaqFunc = async () => {
        let payload = {
            "question": questionchange,
            "answer": anwserChange,
        }

        let response = await updateFaq(payload)
        if (response.status == 200) {

        }
    }
    useEffect(() => {
        getFeqsFunction()
    }, [])

    console.log("allfaq", allfaq)
    const onEditHandle = (item) => {
        setselectedFaq(item)
        setanwserChange(item.answer)
        setquestionchange(item.question)
        setisEdit(true)
        document.getElementById("myModal").classList.remove("hidden")
    }


    const deleteFAQFunc = async (id) => {
        setdeleteBoolean(true)
        setdeleteObject({
            title: "Remove Item",
            confirmText: "Remove",
            cancelText: "Cancel",
            message: "Are You sure want to remove it?",
            onConfirm: async () => {
                setisLoading(true);
                let response = await deleteFaq(id);
                if (response.status == 200) {
                    toast.success("Removed successfully!");
                    getFeqsFunction();
                    setdeleteBoolean(false);
                }
                setisLoading(false);
            },
            onCancel: () => setdeleteBoolean(false),
        });
        // let response = await deleteFaq()
    }

    console.log(deleteObject, "deleteObject")
    return (
        <div className='bg-white p-4 rounded-lg'>

            <div className='flex justify-between'>
                <p className='font-semibold text-[30px]'>FAQ Management</p>
                <div><button className='global_linear_gradient px-3 py-2 rounded-full text-white' onClick={() => document.getElementById("myModal").classList.remove("hidden")}>Add More</button></div>
            </div>
            {allfaq?.length ?
                allfaq?.map((item) => {
                    return <Component item={item} onEditHandle={onEditHandle} deleteFAQFunc={deleteFAQFunc} />
                })
                : <NoDataFound
                    actionFunc={() => document.getElementById("myModal").classList.remove("hidden")}
                    title={"No Data Found"}
                    btntxt={"Add Faq"}
                />
            }

            <div id="myModal" className=" text-[black] fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden bg-[#EAEAEA]">
                <div className="bg-[#fcfcfc]  rounded-lg w-full max-w-lg shadow-lg relative">
                    <div className="flex bg-white w-[30px] cursor-pointer  h-[30px] justify-center absolute top-[-10px] right-[-10px] rounded-full  " onClick={() => {
                        let docElement = document.getElementById('myModal').classList.add("hidden")
                        resetStates()
                        console.log("docElement", docElement)
                        // setaddQuestionInput("")
                        // setmodelForAnswer(false)
                    }}>
                        <button className="text-gray-500 hover:text-red-600 text-xl font-bold">&times;</button>
                    </div>

                    <div className="py-3 bg-white rounded-lg ">
                        <h2 className="text-xl font-semibold mb-4  border-b w-fit mx-auto">{isEdit ? "Edit FAQ" : "Add FAQ"}</h2>
                    </div>

                    <div className='px-4 mt-4'>
                        <div className='mt-2'>
                            <label>Question</label>
                            <input placeholder='Add Name' maxLength={150} value={questionchange} onChange={(e) => setquestionchange(e.target.value)} className='px-2 py-2 mt-2 rounded-md w-full border-[1px] border-[gray] outline-none' />
                        </div>
                        <div className='mt-3'>
                            <label className=''>Answer</label>
                            <textarea className='w-full mt-2 bg-[#F4F4F4] rounded-lg p-4' value={anwserChange} maxLength={400} rows={7} onChange={(e) => setanwserChange(e.target.value)} />
                        </div>
                    </div>

                    {/* <!-- Action Button --> */}
                    <div class="my-2 flex justify-start mx-4">
                        {/* <div className='flex gap-3.5 mt-3 items-center justify-center md:flex-col md:justify-center md:h-full md:w-fit md:items-start md:gap-2.5'>
                                            <button className='py-[7.5px] px-[30px] text-[13px] font-semibold leading-[18px] text-center text-white rounded-full global_linear_gradient md:order-2 md:text-base md:leading-[22px]'
                                                onClick={submitQuestion}
                                            >Submit</button>
                
                                        </div> */}
                        <div className="max-w-[300px] ">
                            <SubmitButton isLoading={submitisLoading} InnerDiv={InnerDiv} type={"button"} btnAction={submitQuestion} />
                        </div>
                    </div>
                </div>
            </div>

            <Popup
                isOpen={deleteBoolean}
                actionLoading={isLoading}
                {...deleteObject}
            />
        </div>
    )
}
