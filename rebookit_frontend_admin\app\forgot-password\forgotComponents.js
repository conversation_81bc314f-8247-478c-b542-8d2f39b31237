"use client"
import { useForm } from "react-hook-form"
import dynamic from "next/dynamic";
import forgotPassCss from "./forgotPass.module.scss"


// components 
const CustomSlider = dynamic(() => import('@/app/components/common/Slider'));
// const BuyAndSellComponent = dynamic(() => import('@/app/components/about/BuyAndSellComponent'));




import dummyImage from "@/public/test.jpeg"

import Link from "next/link";
import Image from "next/image";


import { RiArrowLeftSLine } from "react-icons/ri";
import { USER_ROUTES } from "../config/api";
import { toast } from "react-toastify";
import { useRouter } from "next/navigation";



export default function LoginPage() {

    const router = useRouter()
    const {
        register,
        watch,
        formState: { errors },
        getValues,
        handleSubmit,
        setValue,
        reset,
        setError,


    } = useForm()

    const Images = [dummyImage, dummyImage, dummyImage]


    const onSubmit = data => {
        console.log('Form submitted:', data);
        try {
            // api call 
            fetch(USER_ROUTES.FORGOT_PASSWORD, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify(data)
            }).then(async res => {
                const response = await res.json();

                if (response.success) {
                    toast.success("Code sent to your email")
                    router.push(`/verify-code?email=${data.email}`)
                } else {
                    toast.error("No Email Found!")
                }
            })
        } catch (error) {
            console.log("error", error)
        }

    };


    const settings = {
        dots: true,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 3000
    };

    return (
        <div className={`${forgotPassCss.loginContainer}`}>

            <Link href={"/login"} className="block md:hidden">  <h2 className="flex justify-start items-center text-[#313131]"> <RiArrowLeftSLine size={35} className="mr-2" /> Back to Login</h2> </Link>

            <section className="md:w-6/12">
                <div className={`${forgotPassCss.imageContainer}`}>
                    {/* <LoginSlider ImageSlider={Images} /> */}

                    <CustomSlider
                        sliderSettings={settings}
                    >
                        {
                            Images.map((image, idx) =>
                                <Image key={idx} src={image} alt="login page images" className="h-full w-full rounded-2xl p-1" />
                            )
                        }
                    </CustomSlider>
                </div>

            </section>


            <section className="md:w-5/12 my-5 md:mt-20">

                <Link href={"/login"} className="hidden md:block md:ml-[-10px]">  <h2 className="flex justify-start items-center text-[#313131]"> <RiArrowLeftSLine size={35} className="mr-2" /> Back to Login</h2> </Link>

                <h2 className="text-[18px] font-semibold md:text-[40px] md:font-semibold">Forgot your password?</h2>
                <p className="mt-3 font-extralight text-[14px] md:text-[18px] md:w-10/12">Don’t worry, happens to all of us. Enter your email below to recover your password</p>


                <form onSubmit={handleSubmit(onSubmit)} className="text-[#1C1B1F] my-5">
                    {/* Email Input */}
                    <div className="relative py-2 h-[90px] md:h-[100px]">
                        <fieldset className={` ${forgotPassCss.fields}`}>
                            <legend className="text-[14px] font-medium px-[7px]">Email</legend>
                            <label htmlFor="email" className="sr-only">Email</label>
                            <input
                                id="email"
                                type="email"
                                autoComplete="email"
                                {...register('email', {
                                    required: 'Email is required',
                                    pattern: {
                                        value: /^\S+@\S+\.\S+$/,
                                        message: 'Invalid Email address',
                                    },
                                })}
                                className="w-full text-[13px] md:text-[17px] outline-none border-none"
                                aria-invalid={!!errors.email}
                                aria-describedby="email-error"
                            />
                        </fieldset>
                        {errors.email && (
                            <p id="email-error" className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0">
                                {errors.email.message}
                            </p>
                        )}
                    </div>


                    {/* Submit Button */}
                    <button type="submit" className={`${forgotPassCss.submitButton} my-2`}>
                        Send OTP
                    </button>

                </form>


            </section>



            <section className='my-5 md:my-0 md:w-12/12'>
                <BuyAndSellComponent />
            </section>


        </div>
    )
}
