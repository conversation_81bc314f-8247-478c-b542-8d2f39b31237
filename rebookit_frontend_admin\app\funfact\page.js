"use client"
import React, { useState } from 'react'
import SubmitButton from '../components/common/SubmitButton'
import { RiDeleteBin6Line } from "react-icons/ri";

export default function page() {
    const [submitisLoading, setsubmitisLoading] = useState(false)
    const InnerDiv = () => {
        return <div>Publish</div>
    }
    const btnAction = () => {

    }

    const submitQuestion = () => {

    }
    return (
        <div className='bg-white p-4 rounded-lg'>
            <div className='flex justify-between'>
                <p className='font-semibold text-[30px]'>FAQ Management</p>
                <div><button className='global_linear_gradient px-3 py-2 rounded-full text-white' onClick={() => document.getElementById("myModal").classList.remove("hidden")}>Add More</button></div>
            </div>

            <div className='relative border-[1px] p-3 border-[#0161AB] rounded-md mt-[50px]'>

                <div className='absolute left-[-20px] top-[-25px]'><img className='w-[40px]' src={"https://rebook-it.s3.us-east-1.amazonaws.com/uploads/894211fa-5012-44bb-b9a8-76c8c7e12c9f.png"} /></div>
                <div className='flex justify-between '><p className='font-medium text-[20px]'>Facts:Stories live longer when shared</p> <div className='flex items-center text-[#0161AB]'>Edit <span className='px-2'>|</span> <RiDeleteBin6Line /></div></div>
                <div className='mt-3'>Passing on a book is more than just clearing space on a shelf—it’s giving the story a new life and purpose. Each time a book changes hands, it carries with it the thoughts, emotions, and experiences of its previous reader, enriching the journey for the next. Rather than letting great stories fade in storage, sharing books allows them to continue inspiring, educating, and entertaining others. It’s a small act that extends the book’s impact far beyond its first chapter, creating a chain of connection between readers and ideas across time and place.</div>
            </div>

            <div className='relative border-[1px] p-3 border-[#0161AB] rounded-md mt-[50px]'>

                <div className='absolute left-[-20px] top-[-25px]'><img className='w-[40px]' src={"https://rebook-it.s3.us-east-1.amazonaws.com/uploads/894211fa-5012-44bb-b9a8-76c8c7e12c9f.png"} /></div>
                <div className='flex justify-between '><p className='font-medium text-[20px]'>Facts:Stories live longer when shared</p> <div className='flex items-center text-[#0161AB]'>Edit <span className='px-2'>|</span> <RiDeleteBin6Line /></div></div>
                <div className='mt-3'>Passing on a book is more than just clearing space on a shelf—it’s giving the story a new life and purpose. Each time a book changes hands, it carries with it the thoughts, emotions, and experiences of its previous reader, enriching the journey for the next. Rather than letting great stories fade in storage, sharing books allows them to continue inspiring, educating, and entertaining others. It’s a small act that extends the book’s impact far beyond its first chapter, creating a chain of connection between readers and ideas across time and place.</div>
            </div>


            <div className='relative border-[1px] p-3 border-[#0161AB] rounded-md mt-[50px]'>

                <div className='absolute left-[-20px] top-[-25px]'><img className='w-[40px]' src={"https://rebook-it.s3.us-east-1.amazonaws.com/uploads/894211fa-5012-44bb-b9a8-76c8c7e12c9f.png"} /></div>
                <div className='flex justify-between '><p className='font-medium text-[20px]'>Facts:Stories live longer when shared</p> <div className='flex items-center text-[#0161AB]'>Edit <span className='px-2'>|</span> <RiDeleteBin6Line /></div></div>
                <div className='mt-3'>Passing on a book is more than just clearing space on a shelf—it’s giving the story a new life and purpose. Each time a book changes hands, it carries with it the thoughts, emotions, and experiences of its previous reader, enriching the journey for the next. Rather than letting great stories fade in storage, sharing books allows them to continue inspiring, educating, and entertaining others. It’s a small act that extends the book’s impact far beyond its first chapter, creating a chain of connection between readers and ideas across time and place.</div>
            </div>


            <div id="myModal" className=" text-[black] fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden bg-[#EAEAEA]">
                <div className="bg-[#fcfcfc]  rounded-lg w-full max-w-lg shadow-lg relative">
                    <div className="flex bg-white w-[30px] cursor-pointer  h-[30px] justify-center absolute top-[-10px] right-[-10px] rounded-full  " onClick={() => {
                        let docElement = document.getElementById('myModal').classList.add("hidden")
                        console.log("docElement", docElement)
                        // setaddQuestionInput("")
                        // setmodelForAnswer(false)
                    }}>
                        <button className="text-gray-500 hover:text-red-600 text-xl font-bold">&times;</button>
                    </div>

                    <div className="py-3 bg-white rounded-lg ">
                        <h2 className="text-xl font-semibold mb-4  border-b w-fit mx-auto">{"Add Fact"}</h2>
                    </div>

                    <div className='px-4 mt-4'>
                        <div className='mt-2'>
                            <label>Fact : Title</label>
                            <input placeholder='Add Name' onChange={(e) => ""} className='px-2 py-2 mt-2 rounded-md w-full border-[1px] border-[gray] outline-none' />
                        </div>
                        <div className='mt-3'>
                            <label className=''>Fun Fact</label>
                            <textarea className='w-full mt-2 bg-[#F4F4F4] rounded-lg p-4' rows={7} />
                        </div>
                    </div>
                    {/* <!-- Action Button --> */}
                    <div class="my-2 flex justify-start mx-4">
                        {/* <div className='flex gap-3.5 mt-3 items-center justify-center md:flex-col md:justify-center md:h-full md:w-fit md:items-start md:gap-2.5'>
                                        <button className='py-[7.5px] px-[30px] text-[13px] font-semibold leading-[18px] text-center text-white rounded-full global_linear_gradient md:order-2 md:text-base md:leading-[22px]'
                                            onClick={submitQuestion}
                                        >Submit</button>
            
                                    </div> */}
                        <div className="max-w-[300px] ">
                            <SubmitButton isLoading={submitisLoading} InnerDiv={InnerDiv} type={"button"} btnAction={submitQuestion} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
