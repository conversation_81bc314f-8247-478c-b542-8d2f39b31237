@import url("https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap");
@import "tailwindcss";

:root {
  --background: #fafbfc;
  --foreground: #171717;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: "Poppins", sans-serif;
  color: #212a30;
}

.custom_slider_dots .slick-dots li div {
  width: 40px;
  height: 5px;
  background-color: #9ca3af;
  border-radius: 9999px;
  transition: all 0.3s ease;
  margin-top: 10px;
}

.custom_slider_dots li.slick-active div {
  background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
  width: 40px;
  height: 5px;
}

.custom_inside_dots {
  display: flex !important;
  justify-content: center;
  gap: 2px;
}

.custom_inside_dots li {
  margin: 0 !important;
  width: 10px !important;
  height: 10px !important;
}

.custom_inside_dots li button {
  padding: 0 !important;
}

.custom_inside_dots li button::before {
  color: white !important;
  opacity: 0.5;
}

.custom_inside_dots li.slick-active button::before {
  /* color: white !important; */
  opacity: 1;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0 30px white inset !important;
}

/* react select  */

.css-13cymwt-control {
  border: none;
  outline: none;
  width: 100% !important;
}

.css-1im77uy-control {
  width: 100%;
  outline: none;
  border: none !important;
}

/* Chrome, Safari, Edge, Opera */
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type="number"] {
  -moz-appearance: textfield;
}

button {
  cursor: pointer;
}

.global_linear_gradient {
  background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
}

.global_text_linear_gradient {
  background: linear-gradient(268deg, #211f54 11.09%, #0161ab 98.55%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.no_scrollbar::-webkit-scrollbar {
  display: none;
}

.shadow_medium {
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}

.shadow_medium::before {
  box-shadow: rgba(0, 0, 0, 0.16) 0px 1px 4px;
}

.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none;  /* IE and Edge */
}
.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
