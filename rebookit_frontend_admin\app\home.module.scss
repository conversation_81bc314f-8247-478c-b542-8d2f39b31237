// styles/Home.module.scss
.bannerContainer {

  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 10px;
  margin: 10px auto;

  .banner {
    width: 370px;
    height: 236px;
    flex-shrink: 0;
    border-radius: 52px;
    overflow: hidden;

    div {
      height: 400px;
      // background-color: pink;
    }

    img {
      transform: translate(269px, -33px);
    }

    button {
      width: 164.938px;
      height: 41.234px;
      flex-shrink: 0;
      border-radius: 25.375px;
      border: 1.586px solid var(--Linear, #211F54);
      background: #FFF;
      box-shadow: 0px 3.172px 3.172px 0px rgba(255, 255, 255, 0.32) inset;
      font-size: 14px;
    }
  }



  @media (min-width: 769px) {
    padding: 40px 100px;
    justify-content: space-between;

    .banner {
      width: 400px;
      height: 387px;
      flex-shrink: 0;

      img {
        transform: translate(267px, -25px) scale(1.2);
      }
    }
  }

}

.bookTypeContainer {

  background-color: #fff;
  min-height: 30vh;
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 10px;

  .bookType {
    width: 370px;
    height: 236px;
    flex-shrink: 0;
    border-radius: 52.612px;
    overflow: hidden;


    button {
      border-radius: 52.612px;
      background: #211F54;
      border-radius: 19.692px;
      border: 1.231px solid var(--Linear, #211F54);
      background: #FFF;
      box-shadow: 0px 2.462px 2.462px 0px rgba(255, 255, 255, 0.32) inset;
      width: 128px;
      height: 32px;
      flex-shrink: 0;
    }
  }

  @media (min-width: 769px) {

    padding: 40px 100px;
    justify-content: space-between;


    .bookType {

      width: 600px;
      height: 309px;
      flex-shrink: 0;
      border-radius: 69px;


      button {
        width: 232.856px;
        height: 65.886px;
        flex-shrink: 0;
        border-radius: 40.537px;
        border: 2.534px solid var(--Linear, #211F54);
        background: #FFF;
        box-shadow: 0px 5.067px 5.067px 0px rgba(255, 255, 255, 0.32) inset;
        margin-top: 23px;
      }
    }
  }

}

.infoContainer {

  height: 40vh;
  padding: 10px;
  overflow: hidden;

  .logo {
    width: 75.688px;
    height: 150px;
    flex-shrink: 0;
    aspect-ratio: 75.69/116.00;
  }

  .cloudDiv {

    background-image: url("/landing/cloud.svg");
    background-position:
      0;
    background-repeat: no-repeat;
    background-size: 120% 83%;
    height: 26vh;
    padding:
      16px;
    position: absolute;
    top: 104px;
    right: -100px;


  }


  @media (min-width: 769px) {

    padding: 40px 100px;
    height: 60vh;
    margin: 20px 0;

    .logo {
      width: 276px;
      height: 423px;
      flex-shrink: 0;
      aspect-ratio: 92/141;
    }

    .cloudDiv {
      background-image: url("/landing/cloud.svg");
      background-position:
        7px 28px;
      background-repeat: no-repeat;
      background-size: 114% 83%;
      padding:
        8px;
      position: absolute;
      top: 89px;
      right: -55px;
      height: 100%;

    }
  }
}