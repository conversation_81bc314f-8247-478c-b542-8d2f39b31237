'use client';

import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { Provider } from 'react-redux';
import {store, persistor } from './redux/store';
// import { publicRoutes } from './utils/publicRoutes';

import dynamic from 'next/dynamic';
import { ToastContainer } from 'react-toastify';

import 'slick-carousel/slick/slick.css';
import 'slick-carousel/slick/slick-theme.css';
import './globals.css';

const Header = dynamic(() => import('@/app/components/layout/Header/Header'), { suspense: true });
const Sidebar = dynamic(() => import('@/app/components/layout/Sidebar/Sidebar'), { suspense: true });


import { getToken, removeToken } from './utils/utils';
import { PersistGate } from 'redux-persist/integration/react';
import { USER_ROUTES } from './config/api';


const publicRoutes = [
  '/login',
  '/create-password',
  '/forgot-password',
];

export default function RootLayout({ children }) {
  const pathname = usePathname();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  const isPublicRoute = publicRoutes.includes(pathname);

  const fetchAdmin = () => {
    let token = getToken();
    fetch(USER_ROUTES.GET_ADMIN, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`
      }
    }).then(async (res) => {
      if (res.status === 401) {
        removeToken();
        router.push("/login");
        setIsLoading(false);
        return;
      }
      const response = await res.json();
      if (!response?.data) {
        // Optionally show an error, but do not log out
        setIsLoading(false);
        return;
      }
      setIsLoading(false);
    }).catch(error => {
      setIsLoading(false);
      // Optionally show an error, but do not log out
    })
  }

  useEffect(() => {
    setIsLoading(true)
    fetchAdmin()
  }, [])

  useEffect(() => {

    const auth = getToken();

    if (auth && isPublicRoute) {
      router.replace("/")
      return;
    }

    // If auth exists or it's a public route, stop loading
    if (auth || isPublicRoute) {
      setIsLoading(false);
      return;
    }


    // If no auth and not public route, redirect
    if (!auth && !isPublicRoute) {
      router.replace('/login');
    }
  }, [pathname]);


  if (isLoading) {
    return (
      <html lang="en">
        <body className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-4 text-lg">Loading...</p>
          </div>
        </body>
      </html>
    );
  }


  return (
    <html lang="en">
      <Provider store={store}>
        <PersistGate loading={null} persistor={persistor}>
        <body style={{ overflow: 'hidden' }}>
          <ToastContainer className="z-[999]" />
          {isPublicRoute ? (
            <div className="w-full h-screen overflow-y-auto">{children}</div>
          ) : (
            <div className="flex h-screen">
              <Sidebar />
              <div className="flex flex-col flex-1 h-screen">
                <Header className="h-[100px]" />
                <div
                  className="max-w-[1440px] w-full mx-auto p-[24px] md:p-[24px] overflow-y-auto"
                  style={{ height: 'calc(100vh - 100px)', scrollBehavior: 'smooth' }}
                >
                  {children}
                </div>
              </div>
            </div>
          )}
        </body>
        </PersistGate>
      </Provider>
    </html>
  );
}