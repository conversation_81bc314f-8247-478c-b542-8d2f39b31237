"use client"
import { useForm } from "react-hook-form"
import dynamic from "next/dynamic";
import loginCss from "./login.module.scss"

// components 
const CustomSlider = dynamic(() => import('@/app/components/common/Slider'));
// const BuyAndSellComponent = dynamic(() => import('@/app/components/about/BuyAndSellComponent'));




import dummyImage from "@/public/test.jpeg"



import { IoEyeSharp } from "react-icons/io5";
import { FaRegEyeSlash } from "react-icons/fa";
import { useEffect, useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { USER_ROUTES } from "../config/api";
import { useRouter } from "next/navigation";
import { toast } from "react-toastify";
import { getToken, setToken } from "../utils/utils";
import { login } from "../service/userManagment";


export default function LoginPage() {

    const router = useRouter()
    const {
        register,
        watch,
        formState: { errors },
        getValues,
        handleSubmit,
        setValue,
        reset,
        setError,


    } = useForm()



    // useEffect(() => {
    //     const token = getToken();
    //     if (token) {
    //       router.replace('/');
    //     }
    //   }, []);



    const Images = [dummyImage, dummyImage, dummyImage]

    const [passwordView, setPasswordView] = useState(false)

    const onSubmit =async data => {
        try {
            // api call 
            
           let response= await login(data)
           if(response.status==200){
                    setToken(response?.data?.token)
                    localStorage.setItem("userData",JSON.stringify(response.data.userData))
                    toast.success("Logged in successfully!")
                    router.push("/")
           }
            // fetch(USER_ROUTES.lOGIN, {
            //     method: "POST",
            //     headers: {
            //         "Content-Type": "application/json"
            //     },
            //     body: JSON.stringify(data)
            // }).then(async res => {
            //     const response = await res.json();

            //     console.log("response",response)
            //     let authData = response
            //     if (authData?.success) {
            //         setToken(authData?.token)
            //         toast.success("Logged in successfully!")
            //         router.push("/")
            //     } else {
            //         toast.error(response?.message || "Incorrect credentials! Try again")
            //     }
            // })
        } catch (error) {
            console.log("error", error)
        }

    };


    const settings = {
        dots: true,
        infinite: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 3000
    };

    return (
        <div className={`${loginCss.loginContainer}`}>


            <section className="md:w-6/12">
                <div className={`${loginCss.imageContainer}`}>
                    {/* <LoginSlider ImageSlider={Images} /> */}

                    <CustomSlider
                        sliderSettings={settings}
                    >
                        {
                            Images.map((image, idx) =>
                                <Image key={idx} src={image} alt="login page images" className="h-full w-full rounded-2xl p-1" />
                            )
                        }
                    </CustomSlider>
                </div>

            </section>


            <section className="md:w-5/12 md:mt-20">

                <h2 className="text-[18px] font-semibold md:text-[40px] md:font-semibold">Login</h2>
                <p className="mt-3 font-extralight text-[14px] md:text-[16px]">Login to access your <strong>Rebookit </strong> account</p>


                <form onSubmit={handleSubmit(onSubmit)} className="text-[#1C1B1F] my-4">
                    {/* Email Input */}
                    <div className="relative py-2 h-[90px] md:h-[100px]">
                        <fieldset className={` ${loginCss.fields}`}>
                            <legend className="text-[14px] font-medium px-[7px]">Email</legend>
                            <label htmlFor="email" className="sr-only">Email</label>
                            <input
                                id="email"
                                type="email"
                                autoComplete="email"
                                {...register('email', {
                                    required: 'Email is required',
                                    pattern: {
                                        value: /^\S+@\S+\.\S+$/,
                                        message: 'Invalid Email address',
                                    },
                                })}
                                className="w-full text-[13px] md:text-[17px] outline-none border-none"
                                aria-invalid={!!errors.email}
                                aria-describedby="email-error"
                            />
                        </fieldset>
                        {errors.email && (
                            <p id="email-error" className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0">
                                {errors.email.message}
                            </p>
                        )}
                    </div>

                    {/* Password Input */}
                    <div className="relative h-[90px] md:h-[100px]">
                        <fieldset className={` ${loginCss.fields}`}>
                            <legend className="text-[14px] font-medium px-[7px]">Password</legend>
                            <label htmlFor="password" className="sr-only">Password</label>
                            <input
                                id="password"
                                type={passwordView ? "text" : "password"}
                                autoComplete="current-password"
                                {...register('password', {
                                    required: 'Valid Password is Required',
                                })}
                                className="w-full text-[13px] md:text-[17px] outline-none border-none"
                                aria-invalid={!!errors.password}
                                aria-describedby="password-error"
                            />
                            <div
                                className="absolute top-[29px] right-[17px]"
                                onClick={() => setPasswordView(!passwordView)}
                                role="button"
                                tabIndex={0}
                                aria-label={passwordView ? "Hide password" : "Show password"}
                                onKeyDown={(e) => e.key === 'Enter' && setPasswordView(!passwordView)}
                            >
                                {passwordView ? (
                                    <FaRegEyeSlash className="cursor-pointer" size={20} />
                                ) : (
                                    <IoEyeSharp className="cursor-pointer" size={20} />
                                )}
                            </div>
                        </fieldset>
                        {errors.password && (
                            <p id="password-error" className="text-red-500 text-[12px] mt-1 absolute right-3 bottom-0">
                                {errors.password.message}
                            </p>
                        )}
                    </div>

                    {/* Remember Me + Forgot Password */}
                    <div className="mb-4 flex items-center justify-between">
                        <div className="mb-4 flex items-center">
                            <input
                                id="agree"
                                type="checkbox"
                                className="mr-2 h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                            <label htmlFor="agree" className="text-[12px] md:text-[16px] text-gray-700">
                                Remember me
                            </label>
                        </div>
                        <Link href="/forgot-password">
                            <p className="text-[#FF8682] text-[12px] md:text-[16px] font-medium cursor-pointer">
                                Forgot Password
                            </p>
                        </Link>
                    </div>

                    {/* Submit Button */}
                    <button type="submit" className={`${loginCss.submitButton}`}>
                        Login
                    </button>

                    {/* Sign up prompt */}
                    {/* <p className="text-center text-[12px] md:text-[16px] my-3">
                        Don’t have an account?{' '}
                        <Link href="/signup">
                            <span className="text-[#FF8682] font-medium cursor-pointer">Sign up</span>
                        </Link>
                    </p> */}
                </form>


            </section>
            {/* <section className='my-5 md:my-0 md:w-12/12'>
                <BuyAndSellComponent />
            </section> */}


        </div>
    )
}
