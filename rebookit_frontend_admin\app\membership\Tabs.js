"use client";

import {useDispatch, useSelector} from "react-redux";
import MembershipScss from "./membership.module.scss";
import {changeMemberShipTab} from "../redux/slices/storeSlice";
import {usePathname, useRouter} from "next/navigation";
import {useEffect} from "react";

export default function Tabs() {
  const dispatch = useDispatch();
  const router = useRouter();
  const pathname = usePathname(); // Get current path

  const storeData = useSelector((state) => state.storeData);

  const routeMap = {
    member: "Members",
    plans: "Current Membership Plan",
    editplan: "Create / Edit Plan",
  };

  const tabs = Object.entries(routeMap);

  // Synchronize Redux state with current route
  useEffect(() => {
    const currentTabKey = pathname.split("/").pop();
    const tabIndex = Object.keys(routeMap).findIndex(
      (key) => key === currentTabKey
    );
    // Update Redux state if tab index is valid and different from current state
    if (tabIndex !== -1 && tabIndex !== storeData.memberShipTab) {
      dispatch(changeMemberShipTab(tabIndex));
    }
  }, [pathname, storeData.memberShipTab, dispatch]);

  const activeIndex = storeData.memberShipTab;

  return (
    <div
      className={`bg-white min-h-full h-full rounded-2xl p-5 ${MembershipScss.membershipContainer}`}
    >
      <div className="relative flex justify-around items-center py-2 px-3 rounded-[5px] border border-[#F3F3F3] gap-2 h-[64px] overflow-hidden">
        {/* Animated background for active tab */}
        <div
          className="absolute h-[36px] rounded-[8px] bg-gradient-to-r from-[#0161AB] to-[#211F54] transition-all duration-400 ease-in-out"
          style={{
            width: `calc(100% / ${tabs.length})`,
            left: `calc(${activeIndex} * 100% / ${tabs.length})`,
            top: "50%",
            transform: "translateY(-50%)",
            zIndex: 10,
          }}
        />

        {/* Tabs */}
        {tabs.map(([key, label], index) => (
          <span
            key={key}
            className={`py-1 px-1.5 text-sm leading-[29px] text-center w-1/3 cursor-pointer z-20 transition-colors duration-300 ${
              activeIndex === index ? "text-white font-semibold" : "text-[#444]"
            }`}
            onClick={() => {
              dispatch(changeMemberShipTab(index));
              router.push(`/membership/${key}`);
            }}
          >
            {label}
          </span>
        ))}
      </div>
    </div>
  );
}
