.container {

    .form {


        .inputContaienr {
            height: 80px;

            display: flex;
            flex-direction: column;
            justify-content: space-evenly;


            .joinright {
                border-right: 0;
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
            }

            .joinleft {
                // border-left: 0;
                border-top-left-radius: 0;
                border-bottom-left-radius: 0;
            }

            input,
            select {
                border-radius: 8px;
                border: 1px solid #E6E6E6;
                background: #FFF;
                padding: 0px 8px 0px 12px;
                height: 40px;
                box-shadow: 0px 1.5px 4px -1px rgba(10, 9, 11, 0.07);
                font-size: 12px;

                &:focus-visible {
                    outline: 1px solid #E6E6E6;

                }
            }

            .areaHighlight{
                border: 1px solid red;
            }

        }

        .submit {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 8px;
            flex: 1 0 0;
            border-radius: 66px;
            background: linear-gradient(268deg, #211F54 11.09%, #0161AB 98.55%);
            color: #fff;
            height: 58px;
            margin: 20px 0;
            font-size: 16px;
            padding: 10px 14px;
        }


    }

}