"use client"
import membershipCss from "@/app/components/membership/membership.module.scss";

import { USER_ROUTES } from '@/app/config/api';
import { changeMemberShipTab, resetcurrentSubscription, resetSubscription } from "@/app/redux/slices/storeSlice";
import { saveMemberShipPlan, updateMemberShipPlan } from "@/app/service/membership";
import { getToken } from '@/app/utils/utils';
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { HiLightningBolt } from "react-icons/hi";
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'react-toastify';
import Tabs from "../Tabs";
import { getCategories } from "@/app/service/category";
import { useRouter } from "next/navigation";



export default function MembershipForm(props) {
    const { setSelectedTab } = props;

    const store = useSelector(x => x.storeData)
    const dispatch = useDispatch()
    const router = useRouter()
    const [categories, setcategories] = useState([])

    const {
        register,
        watch,
        formState: { errors },
        getValues,
        handleSubmit,
        setValue,
        reset,
        resetField,
        setError
    } = useForm({
        defaultValues: { ...store.currentSubscription, boosts_checkbox: true }
    })

    const plan = store.currentSubscription
    console.log("Redux plan", plan)
    const [openInputs, setOpenInputs] = useState(["postListing", "featuredListing", "adsListing", "active"]);
    const inputsVisibilityHandler = (inputName) => {
        setOpenInputs(prev => !prev.includes(inputName) ? [...prev, inputName] : prev.filter(p => p !== inputName));
    }
    console.log("store", store)
    console.log("openInputs", openInputs)
    console.log(watch(), "watch")
    // Update Plan if the one of the three does not exists, then remove them ["postListing", "featuredListing", "ad"]
    useEffect(() => {
        if (watch()?.isEdit) {
            const updatedInputs = openInputs.filter(key => !!plan[key]);
            console.log("updatedInputs", updatedInputs)
            setOpenInputs(updatedInputs);
        }
    }, []);


    const resetAll = () => {
        let formatedArr = categories.map((item) => {
            setValue(`${item.name}`, "1")
            setValue(`${item.name}__listingValidityDays`, "")
        })
        dispatch(resetcurrentSubscription())
        toast.success("Data Reseted")
        dispatch(changeMemberShipTab(1));
        router.push("/membership/plans")
    }
    const setPlanDetails = () => {
        try {

            if (categories.length) {
                let testObj = {}
                let formatedArr = categories.map((item) => { testObj[item._id] = item.name })

                plan?.listings?.map((item) => {

                    let name = testObj[item.category]
                    setValue(`${name}`, item.noOfListing)
                    setValue(`${name}_listingValidityDays`, item.listingValidityDays)
                })
            }
        } catch (err) {
            console.log("err in setPlanDetails", err)
        }
    }
    useEffect(() => {
        if (plan?.listings?.length) {
            setPlanDetails()
        }
    }, [plan, categories])

    useEffect(() => {
        if (categories.length) {

            categories.map(item => {
                console.log("item in loop", item.name)
                setValue(`${item.name}_checkbox`, true)
            })


        }
    }, [categories])


    console.log("watch", watch())

    const onSubmit = async data => {
        console.log("onSubmit data", data)
        debugger
        try {

            const token = getToken();
            let allKeys = Object.keys(watch())
            let planPayload = {
                "planName": data.planName,
                "planMonths": data.planMonths,
                "planType": data.planType,
                "active": getValues("active"),
                "listings": []
            }

            // if (data.planType == "paid") {
                let tempArr = []
                categories.map((item) => {
                    debugger
                    if (data[`${item.name}_listingValidityDays`]) {
                        tempArr.push({
                            "category": item.name == allKeys.filter((each) => each == item.name)[0] ? item._id : "",
                            "noOfListing": data[item.name],
                            "listingValidityDays": data[`${item.name}_listingValidityDays`],
                        })
                    }


                })
                planPayload = {
                    ...planPayload, "listings":tempArr,
                    "price": data.price||0,
                    "boosts": getValues("boosts"),
                }

            // }
            console.log(planPayload, "planPayload")

            if (watch()?.isEdit) {
                delete planPayload.planType
                let response = await updateMemberShipPlan(getValues("_id"), planPayload)
                if (!response.error && response?.data?.updatedPlan) {

                    toast.success("Successfully Updated !");
                    // setSelectedTab(1);
                    dispatch(changeMemberShipTab(1));

                    router.push("/membership/plans")

                    dispatch(resetSubscription())
                } else {
                    toast.error(response.message || "Error")
                }
            }
            else {
                let response = await saveMemberShipPlan(planPayload)
                if (response.status == 200) {
                    toast.success("Successfully Created the Plan!");
                    categories.map((item) => {
                        setValue(`${item.name}`, 1)
                        setValue(`${item.name}_listingValidityDays`, "")
                    })

                    resetField("planName")
                    resetField("price")
                    resetField("postListing", { defaultValue: 1 })
                    resetField("featuredListing", { defaultValue: 1 })
                    resetField("freeAds", { defaultValue: 1 })
                    resetField("ed_Directory")
                    resetField("educational_Resource")
                    resetField("pastPapers")


                }
            }
        } catch (error) {
            console.log("error", error)
            toast.error(error.error)
        }
    }


    let days = [
        { label: "15 Days", value: "15" },
        { label: "30 Days", value: "30" },
        { label: "90 Days", value: "90" },
        { label: "6 Month", value: "180" },
        { label: "1 Year", value: "365" },

    ]

    const getCategoriesFunc = async () => {
        let response = await getCategories()
        console.log("response getCategories", response)
        if (response.status == 200) {
            setcategories(response.data.categories)
        }
    }

    useEffect(() => {
        getCategoriesFunc()
    }, [])

    console.log("watch", watch())
    console.log("errors", errors)

    return (
        <div>
            <Tabs />
            <div className={`border border-[#F3F3F3] mt-5 h-full ${membershipCss.container} overflow-auto no_scrollbar`}>

                <div className=' overflow-auto'>
                    <div className='flex flex-col gap-6 p-6'>
                        <h3 className='text-xl font-semibold leading-normal text-[#0A090B]'>Create Pricing Plan</h3>
                        <div className='py-3 px-4 flex flex-col gap-3 rounded-md border border-[#211F54] bg-[#0161ab0d] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)]'>
                            <div className='p-2'>
                                <HiLightningBolt className='w-6 h-6' />
                            </div>

                            <div className='flex flex-col gap-1.5'>
                                <p className='text-sm font-semibold leading-5'>Membership</p>
                                <p className='text-[13px] leading-[18px] text-[#4F4D55]'>Split the full bundle price over several monthly payments</p>
                            </div>
                        </div>
                    </div>

                    <form className="bg-[#FDFDFD] p-6 flex flex-col gap-6" onSubmit={handleSubmit(onSubmit)}>
                        <div className="relative flex flex-col gap-[7px]">
                            <label htmlFor="name" className="text-[#2D2B32] text-sm font-medium -tracking-[0.05px] leading-5">Name<span className="text-[#E12121] ">*</span></label>

                            <input
                                type="text"
                                name="name"
                                className="pl-3 pr-2 py-2.5 text-sm leading-5 -tracking-[0.5px] text-[#0A090B] bg-white rounded-lg border border-[#E6E6E6] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)] focus:outline-none"
                                placeholder="Enter Plan Name"
                                {...register('planName', {
                                    required: 'Plan Name is required',

                                })}
                            />
                            {errors.planName && (
                                <p className="text-red-500 text-[11px] mt-1 absolute right-0 -bottom-5">
                                    {errors.planName.message}
                                </p>
                            )}
                        </div>

                        <div className="flex gap-6 items-center">
                            <div className="flex gap-2 items-center">
                                <div className="relative inline-block h-5 w-5">
                                    <input
                                        type="radio"
                                        value="paid"
                                        defaultChecked ={!watch().planType}
                                        className="absolute h-full w-full cursor-pointer appearance-none rounded-sm border border-[#AEAEB2] bg-white checked:border-transparent checked:bg-[linear-gradient(268deg,#211F54_11.09%,#0161AB_98.55%)] peer checked:border-0"
                                        {...register('planType', {})}
                                    />
                                    <svg
                                        className={`absolute left-1/2 top-1/2 h-3 w-3 -translate-x-1/2 -translate-y-1/2 transform text-white 
                                    opacity-0 peer-checked:opacity-100 pointer-events-none`}
                                        viewBox="0 0 20 20"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M16.6668 5.8335L7.50016 15.0002L3.3335 10.8335"
                                            stroke="currentColor"
                                            strokeWidth="2"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                        />
                                    </svg>
                                </div>

                                <label htmlFor="planType" className="text-sm leading-normal capitalize text-[#2C2C2E]">Paid Membership plan</label>
                            </div>

                            <div className="flex gap-2 items-center">
                                <div className="relative inline-block h-5 w-5">
                                    <input
                                        type="radio"
                                        value="free"
                                        className="absolute h-full w-full cursor-pointer appearance-none rounded-sm border border-[#AEAEB2] bg-white checked:border-transparent checked:bg-[linear-gradient(268deg,#211F54_11.09%,#0161AB_98.55%)] peer checked:border-0"
                                        {...register('planType', {})}
                                    />
                                    <svg
                                        className={`absolute left-1/2 top-1/2 h-3 w-3 -translate-x-1/2 -translate-y-1/2 transform text-white 
                                    opacity-0 peer-checked:opacity-100 pointer-events-none`}
                                        viewBox="0 0 20 20"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M16.6668 5.8335L7.50016 15.0002L3.3335 10.8335"
                                            stroke="currentColor"
                                            strokeWidth="2"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                        />
                                    </svg>
                                </div>

                                <label htmlFor="planType" className="text-sm leading-normal capitalize text-[#2C2C2E]">Free Membership plan</label>
                            </div>
                        </div>

                        {<div> <div className="flex gap-4">
                            <div className="flex flex-col gap-[7px] w-1/2 relative">
                                <label htmlFor="price" className="text-[#2D2B32] text-sm font-medium -tracking-[0.05px] leading-5">Price<span className="text-[#E12121] ">*</span></label>
                                <div className="flex">
                                    <select className="px-2 py-2.5 text-sm leading-5 -tracking-[0.5px] text-[#0A090B] bg-white rounded-tl-lg rounded-bl-lg border-r-0 border border-[#E6E6E6] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)] focus:outline-none"
                                        {...register('priceUnit', {
                                            required: getValues("planType") === "paid" ? 'priceUnit is required' : false,
                                        })}
                                    >
                                        <option className="px-2" value="JMD">JMD</option>
                                        <option className="px-2" value="USD">USD</option>
                                    </select>
                                    <div className="w-[80%]  relative">
                                        <input type="number" className="w-full px-2 py-2.5 text-sm leading-5 -tracking-[0.5px] text-[#0A090B] bg-white rounded-tr-lg rounded-br-lg border border-[#E6E6E6] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)] focus:outline-none"
                                            placeholder="Enter Price"
                                            disabled={watch().planType=="free"}
                                            {...register('price', {
                                                required: getValues("planType") === "paid" ? 'Price is required' : false,
                                                min: {
                                                    value: watch().planType=="free"?0:1,
                                                    message: 'Minimum value is 1',
                                                },
                                            })}
                                        />
                                        {errors.price && (
                                            <p className="text-red-500 text-[11px] mt-1 absolute right-0 -bottom-5">
                                                {errors.price.message}
                                            </p>
                                        )}
                                    </div>
                                </div>

                            </div>

                            <div className="flex flex-col gap-[7px] w-1/2 relative">
                                <label htmlFor="price" className="text-[#2D2B32] text-sm font-medium -tracking-[0.05px] leading-5">Billing Period<span className="text-[#E12121] ">*</span></label>
                                <select className="mt-1 px-2 py-2.5 text-sm leading-5 -tracking-[0.5px] text-[#0A090B] bg-white rounded-lg border border-[#E6E6E6] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)] focus:outline-none"
                                    {...register('planMonths', {
                                        required: getValues("planType") === "paid" ? 'planmonths is required' : false,
                                    })}
                                >
                                    <option className="px-2" value="Monthly">Monthly</option>
                                    <option className="px-2" value="Yearly">Yearly</option>
                                </select>
                                {errors.planMonths && (
                                    <p className="text-red-500 text-[11px] mt-1 absolute right-0 -bottom-5">
                                        {errors.planMonths.message}
                                    </p>
                                )}
                            </div>
                        </div>
                            <div className="mt-3">
                                {categories.map((item) => {
                                    const checkboxKey = `${item.name}_checkbox`;
                                    const valueKey = item.name;
                                    const validityKey = `${item.name}_listingValidityDays`;
                                    const isChecked = watch(checkboxKey);
                                    console.log(`watch(${valueKey})`, watch(valueKey))
                                    return (
                                        <div key={item.name} className="grid grid-cols-2 gap-[7px] mt-3">
                                            <div>
                                                {/* Label and Toggle Switch */}
                                                <div className="w-full flex items-center gap-1.5 relative w-1/2">
                                                    <p className=" text-sm font-medium leading-5 -tracking-[0.5px] text-[#2D2B32]">{item.name}</p>
                                                    <label className="inline-flex items-center cursor-pointer">
                                                        <input
                                                            type="checkbox"
                                                            {...register(checkboxKey)}
                                                            className="sr-only peer"
                                                            onChange={() => {
                                                                setValue(valueKey, "");
                                                                setValue(validityKey, "");
                                                            }}
                                                        />
                                                        <div className="relative w-[32px] h-[17px] bg-[#F2F2F7] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full after:content-[''] after:absolute after:top-[1.5px] after:start-[2px] after:bg-[#E5E5EA] peer-checked:after:bg-white after:rounded-full after:h-[14px] after:w-[14px] after:transition-all peer-checked:bg-[#1DC9A0]"></div>
                                                    </label>
                                                </div>

                                                {/* Number Input */}
                                                <div className="w-[90%] relative">
                                                    <input
                                                        type="number"
                                                        disabled={!isChecked}
                                                        defaultValue={0}
                                                        onWheel={(e)=>e.target.blur()}
                                                        min={0}
                                                        className="w-full mt-2 pl-3 pr-2 py-2.5 text-sm leading-5 -tracking-[0.5px] text-[#0A090B] bg-white rounded-lg border border-[#E6E6E6] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)] focus:outline-none"
                                                        {...register(valueKey)}
                                                    />
                                                    {errors[valueKey] && (
                                                        <p className="text-red-500 text-[11px] mt-1 absolute right-0 -bottom-5">
                                                            {errors[valueKey].message}
                                                        </p>
                                                    )}
                                                </div>
                                            </div>

                                            {/* Validity Dropdown */}
                                            <div>
                                                <div className="h-full w-full flex flex-col align-items-end w-1/2 relative">
                                                    <label htmlFor="price" className="text-[#2D2B32] text-sm font-medium -tracking-[0.05px] leading-5">
                                                        Validity<span className="text-[#E12121]"></span>
                                                    </label>
                                                    <select
                                                        disabled={!isChecked}
                                                        className="mt-2 px-2 py-2.5 text-sm leading-5 -tracking-[0.5px] text-[#0A090B] bg-white rounded-lg border border-[#E6E6E6] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)] focus:outline-none w-full"
                                                        {...register(validityKey, {
                                                            validate: (val) => {
                                                                if (isChecked && watch(valueKey)>0 && !val) return "Field is required";
                                                                return true;
                                                            },
                                                        })}
                                                    >
                                                        <option value="">Select validity</option>
                                                        {days.map((day) => (
                                                            <option key={day.value} value={day.value}>
                                                                {day.label}
                                                            </option>
                                                        ))}
                                                    </select>
                                                    {errors[validityKey] && (
                                                        <p className="text-red-500 text-[11px] mt-1 absolute right-0 -bottom-5">
                                                            {errors[validityKey].message}
                                                        </p>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    );
                                })}
                            </div>
                        </div>}




                        { <div className="grid grid-cols-2 gap-[7px]">
                            <div className=" ">
                                <div className="w-full flex items-center gap-1.5 relative w-1/2">
                                    <p className=" text-sm font-medium leading-5 -tracking-[0.5px] text-[#2D2B32]">Boost Up's</p>
                                    <label className="inline-flex items-center cursor-pointer">
                                        <input type="checkbox" {...register("boosts_checkbox")} value="" className="sr-only peer" />
                                        <div className="relative w-[32px] h-[17px] bg-[#F2F2F7] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full after:content-[''] after:absolute after:top-[1.5px] after:start-[2px] after:bg-[#E5E5EA] peer-checked:after:bg-white after:rounded-full after:h-[14px] after:w-[14px] after:transition-all peer-checked:bg-[#1DC9A0]"></div>
                                    </label>
                                </div>
                                {<input type="number" name="name" defaultValue={1} min={0} className="mt-2 pl-3 pr-2 py-2.5 text-sm leading-5 -tracking-[0.5px] text-[#0A090B] bg-white rounded-lg border border-[#E6E6E6] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)] focus:outline-none w-[90%]"
                                    {...register("boosts", {})}
                                />}
                            </div>

                            {/* <div className=" ">
                                <div className="w-full flex items-center gap-1.5 relative w-1/2">
                                    <p className=" text-sm font-medium leading-5 -tracking-[0.5px] text-[#2D2B32]">Chat Limit</p>
                                    <label className="inline-flex items-center cursor-pointer">
                                        <input type="checkbox" value="" className="sr-only peer" checked={openInputs.includes("postListing")} onChange={() => inputsVisibilityHandler("postListing")} />
                                        <div className="relative w-[32px] h-[17px] bg-[#F2F2F7] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full after:content-[''] after:absolute after:top-[1.5px] after:start-[2px] after:bg-[#E5E5EA] peer-checked:after:bg-white after:rounded-full after:h-[14px] after:w-[14px] after:transition-all peer-checked:bg-[#1DC9A0]"></div>
                                    </label>
                                </div>
                                {openInputs.includes("postListing") && <input type="number" name="name" defaultValue={1} min={0} className="mt-2 pl-3 pr-2 py-2.5 text-sm leading-5 -tracking-[0.5px] text-[#0A090B] bg-white rounded-lg border border-[#E6E6E6] shadow-[0_1.5px_4px_-1px_rgba(10,9,11,0.07)] focus:outline-none w-full"
                                    {...register('chatLimit', { required: false })}
                                />}
                            </div> */}
                        </div>
                        }


                        <div className="flex items-center gap-1.5 relative">
                            <p className="text-sm font-medium leading-5 -tracking-[0.5px] text-[#2D2B32]">Is active</p>
                            <label className="inline-flex items-center cursor-pointer">
                                <input type="checkbox" {...register("active", { require: true })} className="sr-only peer" />
                                <div className="relative w-[32px] h-[17px] bg-[#F2F2F7] peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full rtl:peer-checked:after:-translate-x-full after:content-[''] after:absolute after:top-[1.5px] after:start-[2px] after:bg-[#E5E5EA] peer-checked:after:bg-white after:rounded-full after:h-[14px] after:w-[14px] after:transition-all peer-checked:bg-[#1DC9A0]"
                                ></div>
                            </label>
                        </div>
                        {/* <div className="flex flex-col gap-[15px]">
                            <div className="flex gap-2 items-center">
                                <div className="relative inline-block h-4 w-4">
                                    <input
                                        type="checkbox"
                                        className="absolute h-full w-full cursor-pointer appearance-none rounded-sm border-2 border-[#DCDCDE] bg-white checked:border-transparent checked:bg-[linear-gradient(268deg,#211F54_11.09%,#0161AB_98.55%)] peer checked:border-0"
                                        {...register('ed_Directory', {})}
                                    />
                                    <svg
                                        className={`absolute left-1/2 top-1/2 h-3 w-3 -translate-x-1/2 -translate-y-1/2 transform text-white 
                                    opacity-0 peer-checked:opacity-100 pointer-events-none`}
                                        viewBox="0 0 20 20"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M16.6668 5.8335L7.50016 15.0002L3.3335 10.8335"
                                            stroke="currentColor"
                                            strokeWidth="2"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                        />
                                    </svg>
                                </div>

                                <label htmlFor="Ed-Directory" className="text-sm leading-5 -tracking-[0.05px]">Access to the Ed-irectory</label>
                            </div>

                            <div className="flex gap-2 items-center">
                                <div className="relative inline-block h-4 w-4">
                                    <input
                                        type="checkbox"
                                        className="absolute h-full w-full cursor-pointer appearance-none rounded-sm border-2 border-[#DCDCDE] bg-white checked:border-transparent checked:bg-[linear-gradient(268deg,#211F54_11.09%,#0161AB_98.55%)] peer checked:border-0"
                                        {...register('pastPapers', {})}
                                    />
                                    <svg
                                        className={`absolute left-1/2 top-1/2 h-3 w-3 -translate-x-1/2 -translate-y-1/2 transform text-white 
                                    opacity-0 peer-checked:opacity-100 pointer-events-none`}
                                        viewBox="0 0 20 20"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M16.6668 5.8335L7.50016 15.0002L3.3335 10.8335"
                                            stroke="currentColor"
                                            strokeWidth="2"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                        />
                                    </svg>
                                </div>

                                <p className="text-sm leading-5 -tracking-[0.05px]">Access to Past Papers</p>
                            </div>

                            <div className="flex gap-2 items-center">
                                <div className="relative inline-block h-4 w-4">
                                    <input
                                        type="checkbox"
                                        className="absolute h-full w-full cursor-pointer appearance-none rounded-sm border-2 border-[#DCDCDE] bg-white checked:border-transparent checked:bg-[linear-gradient(268deg,#211F54_11.09%,#0161AB_98.55%)] peer checked:border-0"
                                        {...register('educational_Resource', {})}
                                    />
                                    <svg
                                        className={`absolute left-1/2 top-1/2 h-3 w-3 -translate-x-1/2 -translate-y-1/2 transform text-white 
                                    opacity-0 peer-checked:opacity-100 pointer-events-none`}
                                        viewBox="0 0 20 20"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M16.6668 5.8335L7.50016 15.0002L3.3335 10.8335"
                                            stroke="currentColor"
                                            strokeWidth="2"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                        />
                                    </svg>
                                </div>

                                <p className="text-sm leading-5 -tracking-[0.05px]">Access to other Educational Resources</p>
                            </div>
                        </div> */}
                        <div className="gap-3">
                            <button type="submit" className="global_linear_gradient w-[100px] px-3.5 h-10 text-center text-white text-sm leading-5 -tracking-[0.05px] rounded-full">Save</button>
                            <button type="button" className="ms-2 px-3.5 h-10 text-center border w-[100px] text-sm leading-5 -tracking-[0.05px] rounded-full" onClick={() => resetAll()}>Disgard</button>
                        </div>
                    </form>

                </div>
            </div>
        </div>

    )
}
