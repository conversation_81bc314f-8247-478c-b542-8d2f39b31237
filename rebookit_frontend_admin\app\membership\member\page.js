"use client";

import {useEffect, useState} from "react";
import Tabs from "../Tabs";
import {BsThreeDotsVertical} from "react-icons/bs";
import {getMembers} from "@/app/service/membership";
import {toast} from "react-toastify";
import moment from "moment";
import {debouncFunc} from "@/app/utils/utils";
import Pagination from "@/app/components/common/Pagination";
import MagnifierIcon from "@/public/icons/magnifier_icon.svg";
import Image from "next/image";

function Member({setSelectedTab}) {
  const [membersList, setmembersList] = useState([]);
  const [isLoading, setisLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  useEffect(() => {
    // setSelectedTab(0); // <- maybe causing loop
    getMembersFunc();
  }, [currentPage, pageSize]);

  let memberTable = [
    {
      image:
        "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/ba6004fb-9270-41d6-98c2-a882b4c5508b.png",
      name: "Tenner Finisha",
      email: "<EMAIL>",
      planName: "Gold",
      expireDate: "15-03-2025",
      transactionId: "#2342",
      status: "active",
    },
    {
      image:
        "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/ba6004fb-9270-41d6-98c2-a882b4c5508b.png",
      name: "Emeto  Winner",
      email: "<EMAIL>",
      planName: "Basic",
      expireDate: "15-03-2025",
      transactionId: "#45634",
      status: "active",
    },
    {
      image:
        "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/ba6004fb-9270-41d6-98c2-a882b4c5508b.png",
      name: "Tessy  Ommah",
      email: "<EMAIL>",
      planName: "Basic",
      expireDate: "15-03-2025",
      transactionId: "#45634",
      status: "inActive",
    },
  ];

  // let activeInactive={
  //     active:return <div className="rounded-full bg-[#ECFDF3] px-3 py-1 w-fit flex items-center "> <div className="w-[8px] h-[8px] bg-[#12B76A] rounded-lg mr-2"></div><div className="text-[#027A48]"> {item.status}</div></div>

  //     ,inActive:<div className="rounded-full bg-[#FFF2EA] px-3 py-1 w-fit flex items-center "> <div className="w-[8px] h-[8px] bg-[#F15046] rounded-lg mr-2"></div><div className="text-[#F15046]"> {item.status}</div></div>
  // }
  const ActiveInactive = (name, item) => {
    console.log("name status", name);
    if (name == "active") {
      return (
        <div className="rounded-full bg-[#ECFDF3] px-3 py-1 w-fit flex items-center ">
          {" "}
          <div className="w-[8px] h-[8px] bg-[#12B76A] rounded-lg mr-2"></div>
          <div className="text-[#027A48]"> {item.status}</div>
        </div>
      );
    } else if (name == "pending") {
      return (
        <div className="rounded-full bg-[#ECFDF3] px-3 py-1 w-fit flex items-center ">
          {" "}
          <div className="w-[8px] h-[8px] bg-[#12B76A] rounded-lg mr-2"></div>
          <div className="text-[#027A48]"> {item.status}</div>
        </div>
      );
    } else {
      return (
        <div className="rounded-full bg-[#FFF2EA] px-3 py-1 w-fit flex items-center ">
          {" "}
          <div className="w-[8px] h-[8px] bg-[#F15046] rounded-lg mr-2"></div>
          <div className="text-[#F15046]"> {item.status}</div>
        </div>
      );
    }
  };
  const getMembersFunc = async (value) => {
    setisLoading(true);
    let payload = {};
    let query = "?";
    if (currentPage) {
      query = query + `&page=${currentPage}`;
    }
    if (pageSize) {
      query = query + `&pageSize=${pageSize}`;
    }
    if (value) {
      payload.filters = {keyword: value};
    }

    let membersData = await getMembers(payload, query);
    if (membersData.status == 200) {
      setmembersList(membersData.data?.data);
      // setCurrentPage()
      setPageSize(membersData.data.pageSize);
      setTotalItems(membersData.data.totalCount);
      setTotalPages(membersData.data.totalPages);
    }
    setisLoading(false);
  };

  console.log("membersList", membersList);
  let debounceHandle = debouncFunc((e) => getMembersFunc(e.target.value), 1000);

  return (
    <div className="bg-white rounded-lg p-3 ">
      <Tabs />
      {/* <div className="flex justify-end mb-2">
        <input
          placeholder="Search Items..."
          className="rounded-full border border-gray-400 ps-3 p-2"
          onChange={debounceHandle}
        />
      </div> */}
      <div className="flex items-center  justify-end mb-2">
        <div className="w-70 bg-gray-50 flex rounded-lg items-center">
          <div className="w-6 h-6 ml-2 relative overflow-hidden rounded-lg">
            <Image
              src={MagnifierIcon}
              alt="Search Icon"
              fill
              className="object-cover"
              sizes="24px"
            />
          </div>
          <input
            placeholder="Search Items..."
            className="rounded-lg outline-none bg-gray-50 ps-2 p-2"
            onChange={debounceHandle}
          />
        </div>
      </div>
      {/* <table className=" w-full border border-[#EFF1F4] rounded-lg border-separate">
        <thead className=" border-b border-[#EFF1F4]">
          <th className="px-[10px] text-left py-[10px] font-medium text-[14px] w-[30%] bg-[#FAFBFB] flex items-center">
            <span> Members List </span>
          </th>
          <th className="text-left p-[10px] font-medium text-[14px] w-[14%] bg-[#FAFBFB] ">
            Plan
          </th>
          <th className="text-left p-[10px] font-medium text-[14px] w-[14%] bg-[#FAFBFB]">
            Expire Date
          </th>
          <th className="text-left p-[10px] font-medium text-[14px] w-[14%] bg-[#FAFBFB]">
            Transaction Id
          </th>
          <th className="text-left p-[10px] font-medium text-[14px] w-[14%] bg-[#FAFBFB]">
            Status
          </th>
          <th className="text-left p-[10px] font-medium text-[14px] w-[9%] bg-[#FAFBFB]">
            {" "}
            Action
          </th>
        </thead>
        <tbody className=" w-full">
          {!isLoading ? (
            membersList.length > 0 ? (
              membersList?.map((item, index) => {
                return (
                  <tr className="py-[10px]  bg-white px-2">
                    <td className="border-b border-[#EFF1F4]  flex px-[10px] py-[10px] bg-white items-center">
                      {" "}
                      <div>
                        <img
                          className="ml-2 w-[50px] h-[50px] rounded-full"
                          src={
                            item.userDoc?.profileImage ||
                            "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/ba6004fb-9270-41d6-98c2-a882b4c5508b.png"
                          }
                        />{" "}
                      </div>
                      <div className="ml-2 min-w-0 ">
                        {" "}
                        <div className="font-medium text-[18px] truncate ">
                          {" "}
                          {`${item.userDoc.firstName} ${item.userDoc.lastName}`}
                        </div>{" "}
                        <div className="text-[#9A9A9A]">
                          {item.userDoc.email}
                        </div>
                      </div>{" "}
                    </td>
                    <td className="bg-white border-b px-2.5 border-[#EFF1F4] ">
                      {item?.subsriptionPlanDoc?.planName || "NA"}
                    </td>
                    <td className="bg-white border-b px-2.5 border-[#EFF1F4] ">
                      {moment(item?.endDate).format("DD-MM-YYYY")}
                    </td>
                    <td className="bg-white border-b px-2.5 border-[#EFF1F4] ">
                      {item.transactionId || "#" + (index + 1)}
                    </td>
                    <td className="bg-white border-b px-2.5 border-[#EFF1F4] ">
                      {ActiveInactive(item.status, item)}
                    </td>
                    <td className="bg-white border-b px-2.5 border-[#EFF1F4]">
                      <BsThreeDotsVertical className="cursor-pointer" />
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan={6} className=" text-center">
                  No Data Found
                </td>
              </tr>
            )
          ) : (
            <tr>
              <td
                colSpan={5}
                className="border-b border-[#EFF1F4]  px-[10px] py-[10px] bg-white text-[16px] text-center "
              >
                ...Loading
              </td>
            </tr>
          )}
        </tbody>
      </table> */}
      <table className="w-full border border-[#EFF1F4] rounded-lg overflow-hidden table-fixed">
        <thead className="bg-[#FAFBFB]">
          <tr>
            <th className="px-4 py-3 text-left font-medium text-[14px] w-[30%]">
              Members List
            </th>
            <th className="px-3 py-3 text-left font-medium text-[14px] w-[8%]">
              Plan
            </th>
            <th className="px-3 py-3 text-left font-medium text-[14px] w-[11%]">
              Expire Date
            </th>
            <th className="px-3 py-3 text-left font-medium text-[14px] w-[18%]">
              Transaction Id
            </th>
            <th className="px-3 py-3 text-left font-medium text-[14px] w-[8%]">
              Status
            </th>
            <th className="px-3 py-3 text-left font-medium text-[14px] w-[5%]">
              Action
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-[#EFF1F4]">
          {!isLoading ? (
            membersList.length > 0 ? (
              membersList?.map((item, index) => (
                <tr key={index} className="hover:bg-gray-50 transition-colors">
                  <td className="px-4 text-[14px] py-3">
                    <div className="flex items-center">
                      <img
                        className="w-10 h-10 rounded-full mr-3 shrink-0"
                        src={
                          item.userDoc?.profileImage ||
                          "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/ba6004fb-9270-41d6-98c2-a882b4c5508b.png"
                        }
                        alt={`${item.userDoc.firstName} ${item.userDoc.lastName}`}
                      />
                      <div className="min-w-0">
                        <p className="font-medium text-[14px] text-base truncate">
                          {item.userDoc.firstName} {item.userDoc.lastName}
                        </p>
                        <p className="text-gray-500 text-[14px]  truncate">
                          {item.userDoc.email}
                        </p>
                      </div>
                    </div>
                  </td>
                  <td className="px-3 py-3 text-[14px] text-gray-700">
                    {item?.subsriptionPlanDoc?.planName || "NA"}
                  </td>
                  <td className="px-3 py-3 text-[14px]">
                    {moment(item?.endDate).format("DD-MM-YYYY")}
                  </td>
                  <td className="px-3 py-3 text-[14px] text-sm">
                    {item.paymentId || "NA"}
                  </td>
                  <td className="px-3 text-[14px] py-3">
                    {ActiveInactive(item.status, item)}
                  </td>
                  <td className="px-3 text-[14px] py-3">
                    <BsThreeDotsVertical className="cursor-pointer text-gray-500 hover:text-gray-800" />
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={6} className="text-center py-8 text-gray-500">
                  No Data Found
                </td>
              </tr>
            )
          ) : (
            <tr>
              <td colSpan={6} className="text-center py-8">
                <div className="flex justify-center">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"></div>
                </div>
              </td>
            </tr>
          )}
        </tbody>
      </table>
      <Pagination
        setPageSize={setPageSize}
        setCurrentPage={setCurrentPage}
        getListing={getMembersFunc}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalItems}
        pageSize={pageSize}
      />
    </div>
  );
}

export default Member;
