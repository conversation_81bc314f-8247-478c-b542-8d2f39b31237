"use client";

import {useCallback, useEffect, useState} from "react";
import {TbCircleArrowDown} from "react-icons/tb";
import MembershipForm from "../components/membership/form";
import MembershipScss from "./membership.module.scss";

import {USER_ROUTES} from "@/app/config/api";
import {useDispatch} from "react-redux";
import {
  resetSubscription,
  setcurrentSubscription,
} from "../redux/slices/storeSlice";
import {getMemberShipPlan} from "../service/membership";
import {toast} from "react-toastify";
import MembersTable from "./MembersTable";
import Tabs from "./Tabs";

function MembershipPage() {
  const [selectedTab, setSelectedTab] = useState(0);

  const [subscriptionList, setSubscriptionList] = useState([]);
  const [seeMore, setSeeMore] = useState([]);

  const dispatch = useDispatch();
  console.log("subscriptionList", subscriptionList);

  async function getSubscriptionData() {
    let subscriptionData = await fetch(USER_ROUTES.GET_ALL_SUBSCRIPTION);
    subscriptionData = await subscriptionData.json();

    setSubscriptionList(subscriptionData?.data);
  }

  useEffect(() => {
    // getSubscriptionData()
    getMemberShipPlanFunc();
    if (selectedTab === 1) {
      dispatch(resetSubscription());
    }
  }, [selectedTab]);

  const getMemberShipPlanFunc = async () => {
    let plans = await getMemberShipPlan();
    if (plans.status == 200) {
      setSubscriptionList(plans?.data);

      // toast.success("")
    } else {
      // toast.error(plans.data.message||"Something went wrong")
    }
  };
  useEffect(() => {
    // getSubscriptionData()
    getMemberShipPlanFunc();
  }, []);

  // console.log("subscriptionList",subscriptionList)

  function handleEdit(item) {
    dispatch(setcurrentSubscription(item));
    setSelectedTab(2);
  }

  const seeMoreDetailsHandler = (index) => {
    setSeeMore((prev) =>
      !prev.includes(index) ? [...prev, index] : prev.filter((p) => p !== index)
    );
  };

  // feature for a subscription
  const FeatureLine = ({text}) => (
    <div className="flex gap-2.5">
      <div className="bg-[#1DC9A0] p-0.5 w-fit self-baseline rounded-[2px] mt-1">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="9"
          height="9"
          viewBox="0 0 9 9"
          fill="none"
        >
          <path
            d="M6.83984 2.95337L3.34326 6.44995L1.75391 4.8606"
            stroke="white"
            strokeWidth="0.817383"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </svg>
      </div>
      <p className="text-xs leading-5 text-[#1a1a1ab3]">{text}</p>
    </div>
  );
  //  const handleTabChange = useCallback((tabIndex) => {
  //     setSelectedTab(tabIndex);
  //   }, []);
  const tabComponent = {
    // 0: (<MembersTable handleTabChange={handleTabChange}/>),
    1: (
      <div className="grid grid-cols-3 gap-5 overflow-auto h-full no_scrollbar">
        {/* Subscription Card */}
        {subscriptionList?.map((item, idx) => (
          <div
            key={idx}
            className="py-5 px-4 rounded-[10px] border border-[#E1E1E2] shadow-[0.41px_0.41px_3px_0_rgba(26, 26, 26, 0.08)] h-fit"
          >
            <div className="flex flex-col gap-3.5">
              <p className="text-base font-semibold leading-[26px] text-[#1a1a1ab3]">
                {item?.planName}
              </p>

              <p className="text-3xl font-bold leading-[42px]">
                {item.priceUnit}${item?.price}&nbsp;
                <span className="text-sm leading-[23px] text-[#1a1a1ab3] font-normal">
                  /per{" "}
                </span>
                <span className="text-sm leading-[23px] text-[#1a1a1ab3] font-normal capitalize">
                  {item?.planMonths}
                </span>
              </p>

              {/* <p className='text-sm leading-[23px] text-[#1a1a1ab3] h-[46px] overflow-hidden'>{item?.description}</p> */}

              <button className="rounded-full bg-black py-3 px-5 text-sm leading-[23px] font-semibold text-center text-white !cursor-default">
                Get Started
              </button>
            </div>

            <span className="my-[26px] w-full block border border-dashed border-[#1a1a1a2e]"></span>

            <div
              className={`flex flex-col gap-2.5 transition-all duration-1000 linear overflow-hidden ${
                seeMore.includes(idx)
                  ? "h-fit max-h-[200px] pb-[22px]"
                  : "max-h-0 pb-0"
              }`}
            >
              {item.postListing > 0 && (
                <FeatureLine text={`${item.postListing} Post Listings`} />
              )}
              {item.featuredListing > 0 && (
                <FeatureLine
                  text={`${item.featuredListing} Featured Listings`}
                />
              )}
              {item.freeAds > 0 && (
                <FeatureLine text={`${item.freeAds} Free Ads`} />
              )}
              {item.ed_Directory && (
                <FeatureLine text="Access to Educational Directory" />
              )}
              {item.pastPapers && <FeatureLine text="Access to Past Papers" />}
              {item.educational_Resource && (
                <FeatureLine text="Access to Educational Resources" />
              )}
            </div>

            <div
              className="flex justify-between items-center cursor-pointer"
              onClick={() => seeMoreDetailsHandler(idx)}
            >
              <p className="text-sm leading-[23px] text-[#1a1a1ab3]">
                See More Details
              </p>
              <TbCircleArrowDown
                className={`w-[27px] h-[27px] transition-transform duration-200 ease-in-out ${
                  seeMore.includes(idx) ? "-rotate-180" : ""
                }`}
              />
            </div>

            <button
              className={`w-full mt-[26px] ${MembershipScss.gradientAllRoundBorder} rounded-full py-[9px] text-base leading-[23px] font-semibold`}
              onClick={() => handleEdit(item)}
            >
              <span className="global_text_linear_gradient inline-block">
                Edit
              </span>
            </button>
          </div>
        ))}
      </div>
    ),
    // create plan tab component
    2: <MembershipForm setSelectedTab={setSelectedTab} />,
  };

  return (
    <div
      className={`bg-white min-h-full h-full rounded-2xl p-5 ${MembershipScss.membershipContainer}`}
    >
      {/* Header Tabs */}
      <Tabs setSelectedTab={setSelectedTab} selectedTab={selectedTab} />

      {/* <div className='mt-[42px] h-[83.4%]'>
                {tabComponent[selectedTab]}
            </div> */}

      {/* <a href="/membership/member">Go to Member Page</a> */}
    </div>
  );
}

export default MembershipPage;
