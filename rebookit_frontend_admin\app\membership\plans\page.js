"use client";

import { useEffect, useState } from "react"
import Tabs from "../Tabs";
import { getMemberShipPlan } from "@/app/service/membership";
import { TbCircleArrowDown } from "react-icons/tb";
import { useDispatch } from "react-redux";
import { changeMemberShipTab, setcurrentSubscription } from "@/app/redux/slices/storeSlice";
import MembershipScss from "./membership.module.scss";
import { useRouter } from 'next/navigation';
import { getCategories } from "@/app/service/category";

function Member({ setSelectedTab }) {
    const [subscriptionList, setSubscriptionList] = useState([])
    const [seeMore, setSeeMore] = useState([]);
    const dispatch = useDispatch()
    const [categories, setcategories] = useState([])
    const [formatedCategory, setformatedCategory] = useState({})
    const router = useRouter()

    useEffect(() => {
        // setSelectedTab(0); // <- maybe causing loop
    }, []);
    useEffect(() => {
        const fetchPlans = async () => {
            try {
                const plans = await getMemberShipPlan();
                if (plans.status === 200) {
                    setSubscriptionList(plans.data.subscriptionPlans);  // ✅ safe now
                }
            } catch (err) {
                console.error("Error fetching membership plans", err);
            }
        };
        fetchPlans();
    }, []);
    console.log("seeMore",seeMore)
    const FeatureLine = ({ text }) => (
        <div className='flex gap-2.5'>
            <div className='bg-[#1DC9A0] p-0.5 w-fit self-baseline rounded-[2px] mt-1'>
                <svg xmlns="http://www.w3.org/2000/svg" width="9" height="9" viewBox="0 0 9 9" fill="none">
                    <path d="M6.83984 2.95337L3.34326 6.44995L1.75391 4.8606" stroke="white" strokeWidth="0.817383" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
            </div>
            <p className='text-xs leading-5 text-[#1a1a1ab3]'>{text}</p>
        </div>
    );

    const seeMoreDetailsHandler = (index) => {
        setSeeMore(prev => !prev.includes(index) ? [...prev, index] : prev.filter(p => p !== index))
    }

    function handleEdit(item) {

        dispatch(setcurrentSubscription(item));
        // setSelectedTab(2)
        dispatch(changeMemberShipTab(2));

        router.push("/membership/editplan")

    }
    const getCategoriesFunc = async () => {
        let response = await getCategories()
        console.log("response getCategories", response)
        if (response.status == 200) {
            setcategories(response.data.categories)
            let testObj={}
            let formatedArr = response.data?.categories.map((item) => { testObj[item._id] = item.name })
            setformatedCategory(testObj)
        }
    }

    useEffect(() => {
        getCategoriesFunc()
    }, [])
    console.log("formatedCategory",formatedCategory)
    return <div className="bg-white">
        <Tabs />
        <div className='grid grid-cols-3 gap-5 overflow-auto h-full no_scrollbar'>
            {
                subscriptionList?.map((item, idx) => (

                    <div key={idx} className='py-5 px-4 rounded-[10px] border border-[#E1E1E2] shadow-[0.41px_0.41px_3px_0_rgba(26, 26, 26, 0.08)] h-fit'>
                        <div className='flex flex-col gap-3.5'>
                            <p className='text-base font-semibold leading-[26px] text-[#1a1a1ab3]'>{item?.planName}</p>

                            <p className='text-3xl font-bold leading-[42px]'>
                                {item.priceUnit}${item?.price}&nbsp;
                                <span className='text-sm leading-[23px] text-[#1a1a1ab3] font-normal'>/per </span><span className='text-sm leading-[23px] text-[#1a1a1ab3] font-normal capitalize'>{item?.planMonths}</span>
                            </p>

                            {/* <p className='text-sm leading-[23px] text-[#1a1a1ab3] h-[46px] overflow-hidden'>{item?.description}</p> */}

                            <button className='rounded-full bg-black py-3 px-5 text-sm leading-[23px] font-semibold text-center text-white !cursor-default'>Get Started</button>
                        </div>

                        <span className='my-[26px] w-full block border border-dashed border-[#1a1a1a2e]'></span>

                        <div className={`flex flex-col gap-2.5 transition-all duration-1000 linear overflow-hidden ${seeMore.includes(idx) ? "h-fit max-h-[200px] pb-[22px]" : "max-h-0 pb-0"}`}>
                            {item?.listings?.map((item) => {
                                return <FeatureLine text={`${item.noOfListing} ${formatedCategory[item.category]} (valid for ${item.listingValidityDays} Days) ` } />
                            })}
                        </div>

                        <div className='flex justify-between items-center cursor-pointer' onClick={() => seeMoreDetailsHandler(idx)}>
                            <p className='text-sm leading-[23px] text-[#1a1a1ab3]'> {seeMore.includes(idx)?"See Less":"See More Details"}</p>
                            <TbCircleArrowDown className={`w-[27px] h-[27px] transition-transform duration-200 ease-in-out ${seeMore.includes(idx) ? "-rotate-180" : ""}`} />
                        </div>

                        <button className={`w-full mt-[26px] border ${MembershipScss.gradientAllRoundBorder} rounded-full py-[9px] text-base leading-[23px] font-semibold`}
                            onClick={() => handleEdit(item)}
                        >
                            <span className='global_text_linear_gradient inline-block' >
                                Edit
                            </span>
                        </button>
                    </div>
                ))

            }
        </div>
    </div>
}

export default Member