"use client"
import React, { useEffect, useRef, useState } from 'react'
import { PiUpload } from "react-icons/pi";
import { IoChevronDownSharp } from "react-icons/io5";
import { BsThreeDotsVertical } from "react-icons/bs";
import Card from './card';
import SubmitButton from '../components/common/SubmitButton';
import style from "./pastpapers.module.scss"
import { getCategories, getSubCategories } from '../service/category';
import { RiFileUploadLine } from "react-icons/ri";

export default function Page() {
    const [submitisLoading, setsubmitisLoading] = useState(false)
    const [previewUrl, setpreviewUrl] = useState(null)
    const [categories, setcategories] = useState([])
    const [subCategories, setsubCategories] = useState([])
    const [selectedSubCategory, setselectedSubCategory] = useState("")
    const [selectedCategory, setselectedCategory] = useState("")
    const [isLoading, setisLoading] = useState(false)
    const [selectedImageOnUpload, setselectedImageOnUpload] = useState(null)
    const [selectedImage, setselectedImage] = useState(null)
    const fileInputRef =useRef(null)
    const submitQuestion = () => {
        setsubmitisLoading(true)
        setTimeout(() => {
            setsubmitisLoading(false)
        }, 2000);
    }

    const InnerDiv = () => {
        return <div className='px-5'>Publish</div>
    }

    const handleImageChange = (e) => {
        // debugger
        console.log("file", e.target.files)
        let url = URL.createObjectURL(e.target.files[0])
        setpreviewUrl(url)
    }

    const getSubCategory = async (id) => {
        let response = await getSubCategories(id)
        if (response.status == 200) {
            let list = response.data.subCategories
            setsubCategories(list)
        }
    }
    const getCategoriesFunc = async () => {
        setisLoading(true)
        let response = await getCategories()
        console.log("response getCategories", response)
        if (response.status == 200) {
            // setcategories(response.data.categories)
            let list = response.data.categories
            setcategories(list)
        }
        setisLoading(false)
    }
    useEffect(() => {
        getCategoriesFunc()
    }, [])

    useEffect(() => {
        if (selectedCategory) {
            getSubCategory(selectedCategory)
        }
    }, [selectedCategory])


    const handleButtonClick = () => {
        fileInputRef.current.click();
    };

    const handleFileChange = (e) => {
        const file = e.target.files[0];
        if (file) {
            setselectedImageOnUpload(file)
            const reader = new FileReader();
            reader.onloadend = () => {
                setTimeout(() => {

                    setselectedImage(reader.result); // base64 image string
                }, 500);
            };
            reader.readAsDataURL(file);
            console.log("Selected file:", file.name);
        }
    };

    console.log("setcategories", categories)
    return (
        <div className='bg-white p-4 h-full'>
            <div className='flex items-center justify-between border-b-[0.5px] border-gray-300 py-3'>
                <p className='text-[18px] font-medium'>Past Papers</p>
                <div className='flex gap-3'>
                    <button className='p-3 border rounded-full flex items-center' onClick={() => document.getElementById("myModal").classList.remove("hidden")}><PiUpload /><p className='ml-2'>Add New File</p></button>
                    <button className='p-3 border rounded-full flex items-center'>Change : Grade 9 <IoChevronDownSharp /></button>
                </div>
            </div>

            <div className='grid grid-cols-3'>
                {Array.from({ length: 7 }).map((items) => {
                    return <div className='col-span-1 mt-5'>
                        <Card />
                    </div>
                })}
            </div>

            <div id="myModal" className=" text-[black] fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden bg-[#EAEAEA]">
                <div className="bg-[#fcfcfc]  rounded-lg w-full max-w-lg shadow-lg relative">
                    <div className="flex bg-white w-[30px] cursor-pointer  h-[30px] justify-center absolute top-[-10px] right-[-10px] rounded-full  " onClick={() => {
                        let docElement = document.getElementById('myModal').classList.add("hidden")
                        console.log("docElement", docElement)
                        // setaddQuestionInput("")
                        // setmodelForAnswer(false)
                    }}>
                        <button className="text-gray-500 hover:text-red-600 text-xl font-bold">&times;</button>
                    </div>
                    <div className="py-3 bg-white rounded-lg ">
                        <h2 className="text-xl font-semibold mb-4  border-b w-fit mx-auto">{"Add Past Papers"}</h2>
                    </div>

                    <div className='md:px-5 py-3  w-[90%] rounded-lg mx-auto  bg-[#F5F5F5]'>
                        <div className='flex'>
                            <div className=' flex items-center '>
                                <img className='w-[120px] h-[120px] rounded-full' src={previewUrl || "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/22757bbb-7ce4-45c6-b358-11e089851d5a.png"} />
                            </div>
                            <div className='ml-3 pl-3'>
                                <div className='text-[16px] md:text-[14px] font-normal  mx-auto my-5'>
                                    <p>File size: <span className='font-extralight'>Up to 5MB</span></p>
                                    <p>Optimal dimensions:<span className='font-extralight'> 600x280px</span></p>
                                    <p>File types: <span className='font-extralight'>JPG, JPEG, PNG, GIF, WEBP</span></p>
                                </div>

                                <div className='mx-auto'>

                                    <label className={`${style.uploadButton}  `}>
                                        Upload doc
                                        <input
                                            type="file"
                                            accept="image/*"
                                            onChange={handleImageChange}
                                            style={{ display: 'none' }}
                                            autoComplete="off"
                                        />
                                    </label>
                                </div>
                            </div>
                        </div>

                    </div>
                    <div className='px-4 mt-4'>
                        <div className='mt-3'>
                            <label>Name</label>
                            <input placeholder='Add Name' onChange={(e) => ""} className='px-2 py-2 mt-3 rounded-md w-full border-[1px] border-[gray] outline-none' />
                        </div>

                        <input ref={fileInputRef} type='file' className='hidden' onChange={handleFileChange} />
                        
                        <div className='mt-4'>
                            <label>icon</label>
                            <button onClick={handleButtonClick} className='py-3 px-2 mt-3 flex items-center bg-[#f4f4f4] text-[#8C8C8C] w-full'>   <RiFileUploadLine />Upload Image</button>
                        </div>

                        <div className='my-4'>
                            <label>Category</label>
                            <select className='mt-3 px-2 py-2 mt-3 rounded-md w-full border-[1px] border-[#dddddd] outline-none' onChange={(e) => setselectedCategory(e.target.value)}>
                                {categories.map((item) => {
                                    return <option value={item._id}>{item.name}</option>
                                })}
                            </select>
                        </div>

                        <div className='my-4'>
                            <label>Sub Category</label>
                            <select className='mt-3 px-2 py-2 mt-3 rounded-md w-full border-[1px] border-[#dddddd] outline-none' onChange={(e) => setselectedSubCategory(e.target.value)}>
                                {subCategories.map((item) => {
                                    return <option value={item._id}>{item.name}</option>
                                })}
                            </select>
                        </div>
                    </div>

                    {/* <!-- Action Button --> */}
                    <div class="my-2 flex justify-start mx-4">

                        <div className="max-w-[300px] ">
                            <SubmitButton isLoading={submitisLoading} InnerDiv={InnerDiv} type={"button"} btnAction={submitQuestion} />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
