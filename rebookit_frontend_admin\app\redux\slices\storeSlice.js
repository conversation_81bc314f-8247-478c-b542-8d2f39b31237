import { createSlice } from '@reduxjs/toolkit';

const initialState = {
  otp: null,
  token: null,
  categoryTab:0,
  selectedPage: "Dashboard",
  memberShipTab:0,
  transactionTab:0,
  active:true,
  currentSubscription: {

  },
  categoriesState:{
    categoryId:"",
    subCategoryId:"",
    subSubCategoryId:""
  }
};

const storeSlice = createSlice({
  name: 'Rebookit store',
  initialState,
  reducers: {

    setOtp: (state, action) => {
      state.otp = action.payload
    },

    changeMemberShipTab:(state,action)=>{
      state.memberShipTab=action.payload
    },
    changeTransactionTab:(state,action)=>{
      state.transactionTab=action.payload
    },
    changeCategoryTab:(state,action)=>{
      state.categoryTab=action.payload
    },
    
    clearOtp: (state) => {
      state.otp = null;
    },
    setcurrentSubscription: (state, action) => {
      state.currentSubscription = { ...action.payload, isEdit: true }
    },
    resetcurrentSubscription: (state, action) => {
      state.currentSubscription = { }
    },
    resetSubscription: (state) => {
      state.currentSubscription = {}
    },
    selectPageHandler: (state, action) => {
      state.selectedPage = action.payload;
    },
    changeCategoryState:(state,action)=>{
      state.categoriesState.categoryId=action.payload
    },
    changeSubCategoryState:(state,action)=>{
      state.categoriesState.subCategoryId=action.payload
    }, 
    changeSubSubCategoryState:(state,action)=>{
      state.categoriesState.subSubCategoryId=action.payload
    },
  },
});

export const {resetcurrentSubscription,changeCategoryState,changeSubCategoryState,changeSubSubCategoryState, setOtp, clearOtp, setcurrentSubscription, resetSubscription, selectPageHandler,changeMemberShipTab,changeTransactionTab ,changeCategoryTab} = storeSlice.actions;
export default storeSlice.reducer;
