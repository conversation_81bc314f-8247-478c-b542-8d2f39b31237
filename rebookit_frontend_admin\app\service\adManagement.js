const { axiosErrorHandler } = require("../utils/axiosError.handler");
const instance = require("./axios");

let uri = {
    //create
  createAdPricingRule: "/ad-management",
  createIndividualPricingRuleInResource : "/ad-management/priceRule",
  createOverridePricingRuleInResource : "/ad-management/overrideRule",

  //get
  fetchAdPricingRules: "/ad-management", // fetch all the resource
  getAdPricingRule_byId: "/ad-management",  // fetch single resource

  // update
  updateBasePrice: "/ad-management/basePrice",  // update only base price of any resource
  updateIndividualRule: "/ad-management/priceRule",  // update any single rule of resource

  // delete
  deleteResource: "/ad-management",
  delete_AddRule: "/ad-management",
  deleteIndividualRule: "/ad-management/priceRule",
  deleteOverrightRuleDate: "/ad-management/overrideRule",
};

// Create a new ad
export const createAdPricingRule = async (data) => {
  let response = await instance
    .post(uri.createAdPricingRule, data)
    .catch(axiosErrorHandler);
  return response;
};

export const createIndividualPricingRuleInResource = async (data) => {
    let response = await instance
      .post(uri.createIndividualPricingRuleInResource, data)
      .catch(axiosErrorHandler);
    return response;
  };

  

  export const createOverridePricingRuleInResource = async (data) => {
    let response = await instance
      .post(uri.createOverridePricingRuleInResource, data)
      .catch(axiosErrorHandler);
    return response;
  };


// Get all ads (optionally with filters in data)
export const fetchAdPricingRules = async () => {
  let response = await instance
    .get(uri.fetchAdPricingRules)
    .catch(axiosErrorHandler);
  return response;
};

// Get ad by ID
export const getAdPricingRuleById = async (id) => {
  let response = await instance
    .get(`${uri.getAdPricingRule_byId}/${id}`)
    .catch(axiosErrorHandler);
  return response;
};

export const updateBasePrice = async (data) => {
  let response = await instance
    .put(`${uri.updateBasePrice}`, data)
    .catch(axiosErrorHandler);
  return response;
};

// Update ad by ID
export const updateIndividualRule = async (data) => {
  let response = await instance
    .put(`${uri.updateIndividualRule}`, data)
    .catch(axiosErrorHandler);
  return response;
};

export const deleteResource = async (id) => {
  let response = await instance
    .delete(`${uri.deleteResource}/${id}`)
    .catch(axiosErrorHandler);
  return response;
};

export const delete_AddRule = async (id, data) => {
  let response = await instance
    .delete(`${uri.delete_AddRule}/${id}`, data)
    .catch(axiosErrorHandler);
  return response;
};

export const deleteIndividualRule = async (data) => {
  let response = await instance
    .delete(`${uri.deleteIndividualRule}`, { data })
    .catch(axiosErrorHandler);
  return response;
};

export const deleteOverrightRuleDate = async (data) => {
  let response = await instance
    .delete(`${uri.deleteOverrightRuleDate}`, { data })
    .catch(axiosErrorHandler);
  return response;
};
