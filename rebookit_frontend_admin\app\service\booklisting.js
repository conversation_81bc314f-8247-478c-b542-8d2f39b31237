const {axiosErrorHandler} = require("../utils/axiosError.handler");
const instance = require("./axios");

let uri = {
  getItemList: "/item/search/admin",
  deleteItem: "admin/item",
  getItemById: "/item",
};

export const getList = async (query, data) => {
  let response = await instance
    .post(uri.getItemList + query, data)
    .catch(axiosErrorHandler);
  // console.log("login test response", response)
  return response;
};

export const getBooksById = async (id, userId) => {
  let response = await instance
    .get(`${uri.getItemById}/${id}`, {
      params: {userId},
    })
    .catch(axiosErrorHandler);
  console.log("login test response", response);
  return response;
};

export const deleteItem = async (id) => {
  let response = await instance
    .delete(uri.deleteItem + id)
    .catch(axiosError<PERSON>andler);
  // console.log("login test response", response)
  return response;
};
