const {axiosErrorHandler} = require("../utils/axiosError.handler");
const instance = require("./axios");

let uri = {
  //category
  addCategory: "/master/category",
  //sub category
  addSubCategory: "/master/subCategory",
  getSubCategories: "/master/sub-category",
  //sub sub category
  addSubSubCategory: "/master/subSubCategory",
  getSubSubCategory: "/master/sub-sub-category",
  //sub sub sub category
  addSubSubSubCategory: "/master/subsubsubCategory",
  getSubSubSubCategory: "/master/sub-sub-sub-category",
  //file upload
  upload: `/admin/single-upload`,
};

// Category
export const addCategory = async (data) => {
  let response = await instance
    .post(uri.addCategory, data)
    .catch(axiosErrorHandler);
  // console.log("login test response", response)
  return response;
};
export const getCategories = async () => {
  let response = await instance.get(uri.addCategory).catch(axiosErrorHandler);
  // console.log("login test response", response)
  return response;
};

//Sub-Category
export const addSubCategory = async (data) => {
  let response = await instance
    .post(uri.addSubCategory, data)
    .catch(axiosErrorHandler);
  // console.log("login test response", response)
  return response;
};

export const getSubCategories = async (id) => {
  let response = await instance
    .get(uri.getSubCategories + `/${id}`)
    .catch(axiosErrorHandler);
  // console.log("login test response", response)
  return response;
};

//Sub-Sub-category
export const addSubSubCategory = async (data) => {
  
  let response = await instance
    .post(uri.addSubSubCategory, data)
    .catch(axiosErrorHandler);
  return response;
};

export const getSubSubCategories = async (id) => {
  let response = await instance
    .get(uri.getSubSubCategory + `/${id}`)
    .catch(axiosErrorHandler);
  // console.log("login test response", response)
  return response;
};

//Sub-Sub-Sub-category
export const addSubSubSubCategory = async (data) => {
  let response = await instance
    .post(uri.addSubSubSubCategory, data)
    .catch(axiosErrorHandler);
  return response;
};

export const getSubSubSubCategories = async (id) => {
  let response = await instance
    .get(uri.getSubSubSubCategory + `/${id}`)
    .catch(axiosErrorHandler);
  // console.log("login test response", response)
  return response;
};

export const uploadFile = async (data) => {
  let response = await instance.post(uri.upload, data).catch(axiosErrorHandler);
  // console.log("login test response", response)
  return response;
};
