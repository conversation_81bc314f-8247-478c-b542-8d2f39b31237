const { axiosErrorHandler } = require("../utils/axiosError.handler");
const instance = require("./axios");

let uri = {
    addFaq: "/faqs",
    getFaq: "faqs/filter-search-admin"
}


export const addFaq = async (data) => {
    let response = await instance
        .post(uri.addFaq,data)
        .catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response
}

export const getFaqs = async (data) => {
    let response = await instance
        .post(uri.getFaq,data)
        .catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response
}

export const updateFaq = async (data,id) => {
    let response = await instance
        .put(uri.addFaq+`/${id}`,data)
        .catch(axiosErrorHandler);
    // console.log("login test response", response)
    return response
}

export const deleteFaq = async (id) => {
    let response = await instance
        .delete(uri.addFaq+`/${id}`)
        .catch(axiosError<PERSON><PERSON><PERSON>);
    // console.log("login test response", response)
    return response
}