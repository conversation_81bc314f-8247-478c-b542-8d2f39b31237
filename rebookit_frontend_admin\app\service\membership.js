import { axiosErrorHandler } from "../utils/axiosError.handler";
import instance from "./axios";
let uri={
    plan:"/admin/plan",
    savePlan:"/admin/plan",
    updatePlan:`/admin/plan`,
    members:"/admin/members/filter-search"
}
export const getMemberShipPlan=async()=>{
    let response = await instance
        .get(`${uri.plan}`)
        .catch(axiosErrorHandler);
    console.log("getSubCategories response", response)
    return response
}


export const saveMemberShipPlan =async(body)=>{
    let response = await instance
        .post(`${uri.savePlan}`,body)
        .catch(axiosErrorHandler);
    console.log("getSubCategories response", response)
    return response
}
export const updateMemberShipPlan =async(id,body)=>{
    let response = await instance
        .put(`${uri.updatePlan+"/"+id}`,body)
        .catch(axiosErrorHandler);
    console.log("getSubCategories response", response)
    return response
}

export const getMembers=async(data,query)=>{
    let response = await instance
        .post(`${uri.members}`+query,data)
        .catch(axiosErrorHandler);
    console.log("getSubCategories response", response)
    return response
}


// replace("{{planId}}")