import { axiosError<PERSON>andler } from "../utils/axiosError.handler";
import instance from "./axios";
let uri={
    create:"/admin/testimonial",
    getTestimonials:"/user/testimonials"

}
export const createTestimonials=async(data)=>{
    let response = await instance
        .post(`${uri.create}`,data)
        .catch(axiosErrorHandler);
    console.log("createTestimonials response", response)
    return response
}

export const updateTestimonials=async(id,data)=>{
    let response = await instance
        .put(`${uri.create}/`+id,data)
        .catch(axiosErrorHandler);
    console.log("updateTestimonials response", response)
    return response
}

export const deleteTestimonials=async(id)=>{
    let response = await instance
        .delete(`${uri.create}/`+id)
        .catch(axiosErrorHandler);
    console.log("deleteTestimonials response", response)
    return response
}

export const getTestimonials=async(data)=>{
    let response = await instance
        .get(`${uri.getTestimonials}`,data)
        .catch(axiosErrorHandler);
    console.log("getTestimonials response", response)
    return response
}

