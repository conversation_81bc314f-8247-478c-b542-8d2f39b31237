import { axiosErrorHandler } from "../utils/axiosError.handler";
import instance from "./axios";
let uri={
    transtionQuery:"/payment/query"
}
export const getTransaction = async (currentPage) => {
    let response = await instance
        .get(`${uri.transtionQuery}?page=${currentPage}`)
        .catch(axiosErrorHandler);
    console.log("getSubCategories response", response)
    return response
}
