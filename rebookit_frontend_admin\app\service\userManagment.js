const {axiosErrorHandler} = require("../utils/axiosError.handler");
const instance = require("./axios");

let uri = {
  login: "/user/login",
  addSubCategory: "/master/subCategory",
  upload: `/admin/single-upload`,
  users: `admin/search-users`,
  user: `/user`,
  userDetail: `/admin/user`,
};

export const login = async (data) => {
  let response = await instance.post(uri.login, data).catch(axiosErrorHandler);
  // console.log("login test response", response)
  return response;
};
export const getUsers = async (data, query) => {
  let response = await instance
    .post(uri.users + query, data)
    .catch(axiosErrorHandler);
  // console.log("login test response", response)
  return response;
};

export const updateUser = async (id, data) => {
  let response = await instance
    .put(uri.user + `/${id}`, data)
    .catch(axiosError<PERSON>and<PERSON>);
  // console.log("login test response", response)
  return response;
};

export const getUserDetails = async (id) => {
  let response = await instance
    .get(uri.userDetail + `/${id}`)
    .catch(axiosErrorHandler);
  // console.log("getUserDetails response", response)
  return response;
};

// export const updateUser = async (id, data) => {
//   let response = await instance
//     .put(uri.user + `/${id}`, data)
//     .catch(axiosErrorHandler);
//   // console.log("login test response", response)
//   return response;
// };
