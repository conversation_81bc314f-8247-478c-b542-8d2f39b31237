"use client"
import React, { useEffect, useRef, useState } from 'react'
import { BsThreeDotsVertical } from "react-icons/bs";
import { RiDoubleQuotesR } from "react-icons/ri";
import MenuList from '../components/common/MenuList';

export default function Card({item,deleteTestimonialsFunc,editClick}) {
    const [openOptions, setopenOptions] = useState(false)
    const menuRef = useRef(null)
    useEffect(() => {
        const handleClickOutside = (e) => {
            if (menuRef.current && !menuRef.current.contains(e.target)) {
                setopenOptions(false);
            }
        };
        document.addEventListener('mousedown', handleClickOutside);
        return () => document.removeEventListener('mousedown', handleClickOutside);
    }, []);
    
    
    
    const actionForDelete=(item)=>{
        debugger
        deleteTestimonialsFunc(item._id)
        setselectedTestimonial(item)
    }
    return (
        <div className='p-4 relative max-h-[400px] max-w-[400px] bg-[#fafafa] rounded-md'>
            <BsThreeDotsVertical className='absolute top-[10px] right-[10px]' onClick={() => setopenOptions(!openOptions)} />
            {openOptions && <div ref={menuRef}> <MenuList item={item} setopenOptions={setopenOptions} actionForEdit={editClick} actionforDelete={actionForDelete}/></div>}
            <div className=''><img className='mx-auto w-[150px] h-[150px] rounded-full' src={item.image} /> </div>
            <p className='text-[14px]  text-center mt-2'>{item.name}</p>
            <p className='text-[10px] text-center'>{item.title}</p>
            <div className='flex mt-3'>
                <p className='text-[8px] px-4 text-[center]'>{item.content}</p>

                <RiDoubleQuotesR size={40} className='' />
            </div>
        </div>
    )
}
