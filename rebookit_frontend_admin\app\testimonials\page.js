"use client"
import React, { useEffect, useState } from 'react'
import Card from './Card'
import SubmitButton from '../components/common/SubmitButton'
import style from "./testimonials.module.scss"
import empty_image from "../../public/images/empty_image.png"
import { useForm } from 'react-hook-form'
import { createTestimonials, deleteTestimonials, getTestimonials, updateTestimonials } from '../service/testimonials'
import { toast } from 'react-toastify'
import { uploadFile } from '../service/category'
import NoDataFound from '../components/common/NoDataFound'

export default function Page() {
    const [submitisLoading, setsubmitisLoading] = useState(false)
    const [formData, setformData] = useState({
        name: "",
        title: "",
        content: ""
    })
    const [previewUrl, setpreviewUrl] = useState(null)
    const [imageObj, setimageObj] = useState({})
    const [allTestimonials, setallTestimonials] = useState([])
    const [selectedTestimonial,setselectedTestimonial]=useState({})
    const { setError,
        setValue,
        getValues,
        watch,
        formState: { errors },
        register,
        reset,
        handleSubmit,
        control } = useForm({})

    const submitQuestion = async (data) => {
        debugger
        let formData = new FormData()
        let fileData = {}
        if (!previewUrl) {
            toast.error("Image is required")
            return 
        } else {
            formData.append("file", imageObj)
            fileData = await uploadFile(formData)
            
        }
        setsubmitisLoading(true)
        let response ={}
        let payload = {
            "name": data.name,
            "title": data.title,
            "content": data.content,
            "image": fileData.data.url
        }
        if(selectedTestimonial._id){
            response=await updateTestimonials(selectedTestimonial._id,payload)
        }else{
            response = await createTestimonials(payload)
        }
        if (response.status == 200) {
            toast.success("Submitted Successfully")
        }
        setsubmitisLoading(false)
        let docElement = document.getElementById('myModal').classList.add("hidden")
        reset()
        setpreviewUrl(null)
        setimageObj({})
        getTestimonialsFunc()
        setselectedTestimonial({})
    }

    const getTestimonialsFunc = async () => {
        let response = await getTestimonials()
        console.log("response",response)
        if (response.status == 200) {
            setallTestimonials(response.data)
        }
    }

    const InnerDiv = () => {
        if(selectedTestimonial._id){
            return <div>Update</div>
            
        }else{
            return <div>Publish</div>
        }
    }

    const handleImageChange = (e) => {
        // debugger
        console.log("file", e.target.files)
        let url = URL.createObjectURL(e.target.files[0])
        setpreviewUrl(url)
        setimageObj(e.target.files[0])
        // setValue("image", url, { shouldValidate: true })
    }

    useEffect(() => {
        getTestimonialsFunc()
    }, [])

    console.log("previewUrl", previewUrl)
    console.log("errors", errors)
    console.log("watch", watch())
    const editClick =(item)=>{
        setselectedTestimonial(item)
        setValue("name",item.name)
        setValue("title",item.title)
        setValue("image",item.image)
        setValue("content",item.content)
        setpreviewUrl(item.image)
        document.getElementById("myModal").classList.remove("hidden")
    }
    const updateTestimonialsfunc =async()=>{
        let formData={}
        if(imageObj){
             formData.append("file", imageObj)
            fileData = await uploadFile(formData)
        }
        let payload={
            "name": watch().name,
            "title": watch().title,
            "content": watch().content,
            "image": fileData.data?fileData.data.url:watch().image
        }
        let response=await updateTestimonials(payload)
        if(response.status==200){
            toast.success("Updated Successfully")
        }

    }
    const deleteTestimonialsFunc=async (id)=>{
        debugger
        let response=await deleteTestimonials(id)
        if(response.status==200){
            toast.success("Delete Successfully")
            getTestimonialsFunc
        }
    }

    console.log("allTestimonials",allTestimonials)
    console.log("selectedTestimonial",selectedTestimonial)
    return (
        <div className='bg-[white] p-4 h-full'>
            {/* <div className='text-end'><button className='global_linear_gradient px-3 py-2 rounded-full text-white' onClick={() => document.getElementById("myModal").classList.remove("hidden")}>Add More</button></div> */}
            <div className='flex justify-between'>
                <p className='font-semibold text-[30px]'>Testimonials Management</p>
                <div>{allTestimonials.length>0&&<button className='global_linear_gradient px-3 py-2 rounded-full text-white' onClick={() => document.getElementById("myModal").classList.remove("hidden")}>Add More</button>}</div>
            </div>
            <div className='grid grid-cols-1 md:grid-cols-3 mt-3'>
                {allTestimonials.length?allTestimonials.map((item) => {
                    return <div className='m-3  col-span-1'>
                        <Card item={item} setselectedTestimonial={setselectedTestimonial} deleteTestimonialsFunc={deleteTestimonialsFunc} editClick={editClick}/>
                    </div>
                }):  <div className=' mx-auto flex col-span-3 items-center justify-center w-[100%]'>
                    <NoDataFound
                          actionFunc={() => document.getElementById("myModal").classList.remove("hidden")}
                          title={"No Data Found"}
                          btntxt={"Add Your Item"}
                        /></div>}
            </div>
            <div id="myModal" className=" text-[black] fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-50 hidden bg-[#EAEAEA]">
                <div className="bg-[#fcfcfc]  rounded-lg w-full max-w-lg shadow-lg relative">
                    <div className="flex bg-white w-[30px] cursor-pointer  h-[30px] justify-center absolute top-[-10px] right-[-10px] rounded-full  " onClick={() => {
                        let docElement = document.getElementById('myModal').classList.add("hidden")
                        console.log("docElement", docElement)
                        // setaddQuestionInput("")
                        // setmodelForAnswer(false)
                    }}>
                        <button className="text-gray-500 hover:text-red-600 text-xl font-bold">&times;</button>
                    </div>

                    <div className="py-3 bg-white rounded-lg ">
                        <h2 className="text-xl font-semibold mb-4  border-b w-fit mx-auto">{"Add Testimonials"}</h2>
                    </div>
                    <form onSubmit={handleSubmit(submitQuestion)} encType="multipart/form-data">
                        <div className='relative md:px-5 py-3  w-[90%] rounded-lg mx-auto  bg-[#F5F5F5]'>
                            <div className='flex'>
                                <div className=' flex items-center '>
                                    <img className='w-[100px] h-[100px] rounded-full' src={previewUrl || "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/df5d2111-1787-43da-aea3-bb92b7067102.png"} />
                                </div>
                                <div className='ml-3 pl-3'>
                                    <div className='text-[16px] md:text-[14px] font-normal  mx-auto my-5'>
                                        <p>File size: <span className='font-extralight'>Up to 5MB</span></p>
                                        <p>Optimal dimensions:<span className='font-extralight'> 600x280px</span></p>
                                        <p>File types: <span className='font-extralight'>JPG, JPEG, PNG, GIF, WEBP</span></p>
                                    </div>

                                    <div className='mx-auto'>

                                        <label className={`${style.uploadButton}  `}>
                                            Upload Image
                                            <input
                                                type="file"
                                                accept="image/*"
                                                // {...register("image",{required:"Image is required"})}
                                                onChange={handleImageChange}
                                                style={{ display: 'none' }}
                                                autoComplete="off"
                                            />
                                        </label>
                                    </div>
                                </div>
                                {errors["image"] && (
                                    <div className="absolute bottom-[-20px] right-0 mt-2 flex justify-end items-start text-red-500 text-[12px] pl-1">
                                        <span> {errors["image"].message}</span>
                                    </div>
                                )}
                            </div>

                        </div>
                        <div className='px-4 mt-4'>
                            <div className='mt-2 relative'>
                                <label>Name</label>
                                <input {...register("name", { required: "Field Are Required" })} placeholder='Add Name' className='px-2 py-2 mt-2 rounded-md w-full border-[1px] border-[gray] outline-none' />
                                {errors["name"] && (
                                    <div className="absolute bottom-[-20px] right-0 mt-2 flex justify-end items-start text-red-500 text-[12px] pl-1">
                                        <span> {errors["name"].message}</span>
                                    </div>
                                )}
                            </div>

                            <div className='mt-2 relative'>
                                <label>Title</label>
                                <input {...register("title", { required: "Field Are Required" })} placeholder='Add Name' className='px-2 py-2 mt-2 rounded-md w-full border-[1px] border-[gray] outline-none' />
                                {errors["title"] && (
                                    <div className="absolute bottom-[-20px] right-0 mt-2 flex justify-end items-start text-red-500 text-[12px] pl-1">
                                        <span> {errors["title"].message}</span>
                                    </div>
                                )}
                            </div>

                            <div className='mt-3 relative'>
                                <label className=''>Content</label>
                                <textarea {...register("content", { required: "Field Are Required" })} className='w-full mt-2 bg-[#F4F4F4] rounded-lg p-4' rows={4} />
                                {errors["content"] && (
                                    <div className="absolute bottom-[-20px] right-0 mt-2 flex justify-end items-start text-red-500 text-[12px] pl-1">
                                        <span> {errors["content"].message}</span>
                                    </div>
                                )}
                            </div>
                        </div>
                        {/* <!-- Action Button --> */}
                        <div class="my-2 flex justify-start mx-4">
                            <div className="max-w-[300px] ">
                                <SubmitButton isLoading={submitisLoading} InnerDiv={InnerDiv} type={"submit"} />
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    )
}
