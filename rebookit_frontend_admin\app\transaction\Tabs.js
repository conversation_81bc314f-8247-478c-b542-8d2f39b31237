'use client';

import { useDispatch, useSelector } from 'react-redux';
import MembershipScss from "./transaction.module.scss";
import { changeMemberShipTab, changeTransactionTab } from '../redux/slices/storeSlice';
import { usePathname, useRouter } from 'next/navigation';

export default function Tabs() {
    const dispatch = useDispatch();
    const pathName = usePathname();
    
    const router = useRouter();

    const storeData = useSelector(state => state.storeData);

    const routeMap = {
       all: "All",
        ads: "Ad Transaction",
        userplantransaction: "Membership"
    };

    const tabs = Object.entries(routeMap);
    const activeIndex = storeData.transactionTab;

    return (
        <div className={`bg-white min-h-full h-full rounded-2xl p-5 ${MembershipScss.membershipContainer}`}>
            <div className="relative flex justify-around items-center py-2 px-3 rounded-[5px] border border-[#F3F3F3] gap-2 h-[64px] overflow-hidden">
                
                {/* Animated background for active tab */}
                <div
                    className="absolute h-[36px] rounded-[8px] bg-gradient-to-r from-[#0161AB] to-[#211F54] transition-all duration-400 ease-in-out"
                    style={{
                        width: `calc(100% / ${tabs.length})`,
                        left: `calc(${activeIndex} * 100% / ${tabs.length})`,
                        top: '50%',
                        transform: 'translateY(-50%)',
                        zIndex: 10
                    }}
                />

                {/* Tabs */}
                {tabs.map(([key, label], index) => (
                    <span
                        key={key}
                        className={`py-1 px-1.5 text-sm leading-[29px] text-center w-1/3 cursor-pointer z-20 transition-colors duration-300 ${
                            activeIndex === index ? 'text-white font-semibold' : 'text-[#444]'
                        }`}
                        onClick={() => {
                            dispatch(changeTransactionTab(index));
                            router.push(`/transaction/${key}`);
                        }}
                    >
                        {label}
                    </span>
                ))}
            </div>
        </div>
    );
}
