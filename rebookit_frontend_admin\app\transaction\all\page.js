"use client";

import { useEffect, useState } from "react";
import Tabs from "../Tabs";
import { BsThreeDotsVertical } from "react-icons/bs";
import { getMembers } from "@/app/service/membership";
import { toast } from "react-toastify";
import moment from "moment";
import { PiCurrencyDollarSimpleBold } from "react-icons/pi";
import { GiOpenFolder } from "react-icons/gi";
import { FaArrowDown } from "react-icons/fa6";
import { FaArrowUp } from "react-icons/fa6";
import { MdOutlineDateRange } from "react-icons/md";
import { FaFileUpload } from "react-icons/fa";
import { getTransaction } from "@/app/service/transaction";
import { debouncFunc } from "@/app/utils/utils";
import { createInitialsAvatar } from "@/app/components/common/InitialAvatar/CreateInitialAvatar";
import Pagination from "@/app/components/common/Pagination";

function All({ setSelectedTab }) {
  const [transactions, settransactions] = useState([]);
  const [isLoading, setisLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(null);
  const [totalItems, setTotalItems] = useState(null);
  const [totalPages, setTotalPages] = useState(null);

  useEffect(() => {
    // setSelectedTab(0); // <- maybe causing loop
    getTrasctionFunc();
  }, [currentPage]);

  let memberTable = [
    {
      image:
        "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/ba6004fb-9270-41d6-98c2-a882b4c5508b.png",
      name: "Tenner Finisha",
      email: "<EMAIL>",
      planName: "Gold",
      expireDate: "15-03-2025",
      transactionId: "#2342",
      status: "active",
    },
    {
      image:
        "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/ba6004fb-9270-41d6-98c2-a882b4c5508b.png",
      name: "Emeto  Winner",
      email: "<EMAIL>",
      planName: "Basic",
      expireDate: "15-03-2025",
      transactionId: "#45634",
      status: "active",
    },
    {
      image:
        "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/ba6004fb-9270-41d6-98c2-a882b4c5508b.png",
      name: "Tessy  Ommah",
      email: "<EMAIL>",
      planName: "Basic",
      expireDate: "15-03-2025",
      transactionId: "#45634",
      status: "inActive",
    },
  ];

  // let activeInactive={
  //     active:return <div className="rounded-full bg-[#ECFDF3] px-3 py-1 w-fit flex items-center "> <div className="w-[8px] h-[8px] bg-[#12B76A] rounded-lg mr-2"></div><div className="text-[#027A48]"> {item.status}</div></div>

  //     ,inActive:<div className="rounded-full bg-[#FFF2EA] px-3 py-1 w-fit flex items-center "> <div className="w-[8px] h-[8px] bg-[#F15046] rounded-lg mr-2"></div><div className="text-[#F15046]"> {item.status}</div></div>
  // }
  const ActiveInactive = (name, item) => {
    if (name == "success") {
      return (
        <div className="rounded-full bg-[#ECFDF3] px-4 py-1 w-fit flex items-center ">
          {" "}
          <div className="text-[#027A48]"> {item.status}</div>
        </div>
      );
    } else if (name == "pending") {
      return (
        <div className="rounded-full bg-[#FFC72C26] px-4 py-1 w-fit flex items-center ">
          {" "}
          <div className="text-[#FFBB00]"> {item.status}</div>
        </div>
      );
    } else {
      return (
        <div className="rounded-full bg-[#FFF2EA] px-4 py-1 w-fit flex items-center ">
          {" "}
          <div className="text-[#F15046]"> {item.status}</div>
        </div>
      );
    }
  };
  const getTrasctionFunc = async (value) => {
    setisLoading(true);
    // let payload = {
    //     filters: {
    //     },
    //     sort: {
    //     }
    // }
    // if (value) {
    //     payload.filters = { keyword: value }
    // }
    let membersData = await getTransaction(currentPage);
    if (membersData.status == 200) {
      console.log("membersData", membersData);

      settransactions(membersData.data.data);
      setTotalItems(membersData?.data?.totalCount);
      setTotalPages(membersData.data.totalPages || 1);
      setPageSize(membersData.data.pageSize || 10);
    }
    setisLoading(false);
  };

  console.log("transactions", transactions);
  let debounceHandle = debouncFunc(
    (e) => getTrasctionFunc(e.target.value),
    1000
  );

  return (
    <div className="bg-white rounded-lg p-3 ">
      <Tabs />
      <div>
        <div className="flex items-center justify-between my-3 ">
          <div className="bg-[#FAFAFA] flex items-center p-3 rounded-lg">
            {moment().format("MMM DD, YYYY")} ,{moment().format("MMM DD, YYYY")}{" "}
            <MdOutlineDateRange className="ml-2 text-[#737375]" />
          </div>
          <div className="flex bg-[#FAFAFA] flex items-center p-3 rounded-lg">
            Export CSV <FaFileUpload className="ml-2 text-[#737375]" />
          </div>
        </div>
        <div className="flex justify-between grid grid-cols-4 gap-5">
          <div className="border rounded-lg border-[#F5F5F5] p-3 cols-span-1 w-full flex gap-2">
            <div>
              <div className="rounded-full w-[50px] h-[50px] bg-[#7A00A30A] flex items-center justify-center">
                <PiCurrencyDollarSimpleBold />
              </div>
            </div>
            <div>
              <div className="text-[14px] text-[#737375]">
                Total Transaction
              </div>
              <div className="text-[22px] font-bold">J$78,987.00</div>
            </div>
          </div>
          <div className="border rounded-lg border-[#F5F5F5] p-3 cols-span-1 w-full flex gap-2">
            <div>
              <div className="rounded-full w-[50px] h-[50px] bg-[#7A00A30A] flex items-center justify-center">
                <GiOpenFolder />
              </div>
            </div>
            <div>
              <div className="text-[14px] text-[#737375]">Ad Transaction</div>
              <div className="text-[22px] font-bold">J$78,987.00</div>
            </div>
          </div>
          <div className="border rounded-lg border-[#F5F5F5] p-3 cols-span-1 w-full flex gap-2">
            <div>
              <div className="rounded-full w-[50px] h-[50px] bg-[#7A00A30A] flex items-center justify-center">
                <FaArrowDown />
              </div>
            </div>
            <div>
              <div className="text-[14px] text-[#737375]">
                Membership Transaction
              </div>
              <div className="text-[22px] font-bold">J$78,987.00</div>
            </div>
          </div>
          <div className="border rounded-lg border-[#F5F5F5] p-3 cols-span-1 w-full flex gap-2">
            <div>
              <div className="rounded-full w-[50px] h-[50px] bg-[#7A00A30A] flex items-center justify-center">
                <FaArrowUp />
              </div>
            </div>
            <div>
              <div className="text-[14px] text-[#737375]">
                This Month Transaction
              </div>
              <div className="text-[22px] font-bold">J$78,987.00</div>
            </div>
          </div>
        </div>
      </div>
      {/* <div className="flex justify-end mb-2">
            <input placeholder="Search Items..." className="rounded-full border border-gray-400 ps-3 p-2" onChange={debounceHandle} />
        </div> */}
      <table className=" w-full border border-[#EFF1F4] rounded-lg border-separate">
        <thead className=" border-b border-[#EFF1F4]">
          <th className="px-[10px] text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB] flex items-center">
            <span> Name </span>
          </th>
          <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB] ">
            Transaction Id
          </th>
          <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]">
            Transaction Date
          </th>
          <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]">
            Amount
          </th>
          <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]">
            Status
          </th>
          <th className="text-left py-[10px] font-medium text-[14px] bg-[#FAFBFB]">
            {" "}
            Action
          </th>
        </thead>
        <tbody className=" w-full">
          {!isLoading ? (
            transactions?.map((item, index) => {
              return (
                <tr className="py-[10px]  bg-white px-2">
                  <td className="border-b border-[#EFF1F4]  flex px-[10px] py-[10px] bg-white items-center">
                    <div>
                      <img
                        className="w-10 h-10 rounded-full mr-3 shrink-0"
                        src={
                          item.image ||
                          createInitialsAvatar(
                            `${item.userId?.firstName || ""} ${
                              item.userId?.lastName || ""
                            }`.trim(),
                            { size: 40 }
                          )
                        }
                        alt={`${item.userId?.firstName || ""} ${
                          item.userId?.lastName || ""
                        }`}
                      />
                      {/* <img className="ml-2 w-[40px] h-[40px] rounded-full" src={item.userId?.profileImage || "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/ba6004fb-9270-41d6-98c2-a882b4c5508b.png"} />  */}
                    </div>
                    <div className="ml-2">
                      <div className="font-medium text-[14px]">
                        {item.userId.firstName} {item.userId.lastName}
                      </div>
                      <div className="text-[10px] text-gray-500">
                        {item.userId.email}
                      </div>
                    </div>
                  </td>

                  <td className="bg-white border-b  border-[#EFF1F4] ">
                    {item?._id || "NA"}
                  </td>
                  <td className="bg-white border-b border-[#EFF1F4] ">
                    {moment(item?.endDate).format("DD-MM-YYYY")}
                  </td>
                  <td className="bg-white border-b border-[#EFF1F4] ">
                    J$ {item?.amount || 0}
                  </td>
                  <td className="bg-white border-b border-[#EFF1F4] ">
                    {ActiveInactive(item.status, item)}
                  </td>
                  <td className="bg-white border-b border-[#EFF1F4] ">
                    <BsThreeDotsVertical />
                  </td>
                </tr>
              );
            })
          ) : (
            <tr>
              <td
                colSpan={6}
                className="border-b h-[400px] border-[#EFF1F4] px-[10px] py-[10px] bg-white text-[16px] text-center"
              >
                <div className="flex justify-center items-center py-4">
                  <span className="relative flex h-10 w-10">
                    <span className="animate-spin absolute inline-flex h-full w-full rounded-full bg-gradient-to-tr from-blue-500 to-blue-900 opacity-30"></span>
                    <span className="relative inline-flex rounded-full h-10 w-10 bg-white border-4 border-blue-600 border-t-transparent"></span>
                  </span>
                </div>
              </td>
            </tr>
          )}
        </tbody>
      </table>
      <Pagination
        setPageSize={setPageSize}
        setCurrentPage={setCurrentPage}
        getListing={getTrasctionFunc}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalItems}
        pageSize={pageSize}
      />
    </div>
  );
}

export default All;
