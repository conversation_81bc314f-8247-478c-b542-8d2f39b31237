import React, {useEffect, useRef} from "react";
import {BsThreeDotsVertical} from "react-icons/bs";
import {toast} from "react-toastify";
import {updateUser} from "../service/userManagment";

export default function ActionBox({data, funcAfterAction}) {
  let menuRef = useRef(null);
  const copyFunction = () => {
    navigator.clipboard
      .writeText(data._id)
      .then((item) => {
        toast.success("User ID Copied");
        document.getElementById(data._id).classList.add("hidden");
      })
      .catch((err) => {});
  };
  const states = {active: "active", suspend: "suspended"};
  const blockUnblockFunc = async (name) => {
    let payload = {
      status: data.status == "active" ? states.suspend : states.active,
    };
    let response = await updateUser(data._id, payload);
    console.log("response", response);
    if (response.status == 200) {
      toast.success("User Status Changes");
      document.getElementById(data._id).classList.add("hidden");
      funcAfterAction();
    }
  };
  const referseObject = (name) => {
    if (states[name] == states.active) {
      return "Suspend";
    } else {
      return states.active;
    }
  };
  console.log("referseObject", referseObject());
  let dropDownValue = [
    {label: "copy", func: copyFunction},
    {label: referseObject(data.status), func: blockUnblockFunc},
  ];

  useEffect(() => {
    const handleClickOutside = (e) => {
      if (menuRef.current && !menuRef.current.contains(e.target)) {
        document.getElementById(data._id).classList.add("hidden");
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  document.body.click(() =>
    document.getElementById(data._id).classList.add("hidden")
  );
  const DropDown = () => {
    return (
      <div
        id={data._id}
        ref={menuRef}
        className={
          "hidden absolute z-10 mt-1 min-w-[100px] max-w-[100px] w-fit rounded-md bg-white shadow-lg border border-slate-200 py-1"
        }
      >
        {dropDownValue?.map((item, index) => (
          <button
            key={`${item.id}-${index}`}
            onClick={(e) => {
              e.stopPropagation();
              item?.func?.();
              // closeDropdown();
            }}
            className="w-full text-left px-3 py-1.5 text-sm hover:bg-gray-100 transition-colors"
          >
            {item?.label}
          </button>
        ))}
      </div>
    );
  };
  return (
    <div className="relative">
      <BsThreeDotsVertical
        className="cursor-pointer mx-auto"
        onClick={() =>
          document.getElementById(data._id).classList.remove("hidden")
        }
      />
      <DropDown />
    </div>
  );
}
