import React from 'react'
import { IoChevronUp } from "react-icons/io5";
import { FaChevronDown } from "react-icons/fa6";
import style from "./userManagement.module.scss"
import { updateUser } from '../service/userManagment';
import { toast } from 'react-toastify';

export default function FilterButton() {
    let status={
        "Suspend":"suspended",
        "Active":"active"
    }
    const updateUserFunc = async(item) => {
        let payload = {
            "status": status[item]
        }
        let updatedUser =await updateUser(payload )
        if(updatedUser.status==200){
            toast.success(`User Status Changed`)
        }
    }
    return (
        <div className='relative '>
            {/* cursor-pointer relative planFilter w-fit bg-white border border-gray-400 rounded-full px-5 py-2 */}
            <div className={`${style.buttonMainDivFilter}`}>
                {/* Status Section */}
                <div>
                    <div className="flex z-20">
                        Status :
                        <span className="flex items-center font-semibold">
                            All <FaChevronDown className="ms-1" />
                        </span>
                    </div>

                </div>
                {/* Dropdown - appears below */}

            </div>
        </div>
    )
}
