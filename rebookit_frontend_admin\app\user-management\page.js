"use client";

import {use, useEffect, useState} from "react";

import {BsThreeDotsVertical} from "react-icons/bs";
import {getMembers} from "@/app/service/membership";
import {toast} from "react-toastify";
import moment from "moment";
import {getUsers} from "../service/userManagment";
import ActionBox from "./ActionBox";
import {IoChevronUp} from "react-icons/io5";
import {FaChevronDown} from "react-icons/fa6";
import {debouncFunc} from "../utils/utils";
import {createInitialsAvatar} from "../components/common/InitialAvatar/CreateInitialAvatar";
import Pagination from "../components/common/Pagination";
import MagnifierIcon from "@/public/icons/magnifier_icon.svg";
import Image from "next/image";
import {useRouter} from "next/navigation";

function Member({setSelectedTab}) {
  const router = useRouter();
  const [membersList, setmembersList] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  console.log("memberlist", membersList);
  const [isLoading, setisLoading] = useState(false);
  useEffect(() => {
    // setSelectedTab(0); // <- maybe causing loop
    getMembersFunc();
  }, [currentPage, pageSize]);

  let memberTable = [
    {
      image:
        "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/ba6004fb-9270-41d6-98c2-a882b4c5508b.png",
      name: "Tenner Finisha",
      email: "<EMAIL>",
      planName: "Gold",
      expireDate: "15-03-2025",
      transactionId: "#2342",
      status: "active",
    },
    {
      image:
        "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/ba6004fb-9270-41d6-98c2-a882b4c5508b.png",
      name: "Emeto  Winner",
      email: "<EMAIL>",
      planName: "Basic",
      expireDate: "15-03-2025",
      transactionId: "#45634",
      status: "active",
    },
    {
      image:
        "https://rebook-it.s3.us-east-1.amazonaws.com/uploads/ba6004fb-9270-41d6-98c2-a882b4c5508b.png",
      name: "Tessy  Ommah",
      email: "<EMAIL>",
      planName: "Basic",
      expireDate: "15-03-2025",
      transactionId: "#45634",
      status: "inActive",
    },
  ];

  // let activeInactive={
  //     active:return <div className="rounded-full bg-[#ECFDF3] px-3 py-1 w-fit flex items-center "> <div className="w-[8px] h-[8px] bg-[#12B76A] rounded-lg mr-2"></div><div className="text-[#027A48]"> {item.status}</div></div>

  //     ,inActive:<div className="rounded-full bg-[#FFF2EA] px-3 py-1 w-fit flex items-center "> <div className="w-[8px] h-[8px] bg-[#F15046] rounded-lg mr-2"></div><div className="text-[#F15046]"> {item.status}</div></div>
  // }
  const ActiveInactive = (name) => {
    if (name == "active") {
      return (
        <div className="rounded-full bg-[#ECFDF3] px-3 py-1 w-fit flex items-center mx-auto ">
          {" "}
          <div className="w-[8px] h-[8px] bg-[#12B76A] rounded-lg mr-2"></div>
          <div className="text-[#027A48] text-sm"> {name}</div>
        </div>
      );
    } else {
      return (
        <div className="rounded-full bg-[#FFF2EA] px-3 py-1 w-fit flex items-center mx-auto ">
          {" "}
          <div className="w-[8px] h-[8px] bg-[#F15046] rounded-lg mr-2"></div>
          <div className="text-[#F15046] text-sm"> {name}</div>
        </div>
      );
    }
  };
  const copyFunction = () => {};

  const getMembersFunc = async (value = "") => {
    let query = "?";
    if (currentPage) {
      query = query + `&page=${currentPage}`;
    }
    if (pageSize) {
      query = query + `&pageSize=${pageSize}`;
    }
    setisLoading(true);
    let membersData = await getUsers(value, query);
    if (membersData.status == 200) {
      setmembersList(membersData.data.data);
      setPageSize(membersData.data.pageSize);
      setTotalPages(membersData.data.totalPages);
      setTotalItems(membersData.data.totalCount);
    }
    // setTotalItems(bookData?.totalCount || 0);
    // setTotalPages(bookData?.totalPages || 1);

    setisLoading(false);
  };

  console.log("membersList", membersList);

  const searchHandle = (event) => {
    console.log(event.target.value, "debounce event");
    let payload = {
      searchTerm: event.target.value || "",
    };
    getMembersFunc(payload);
  };
  let debounceHandle = debouncFunc(searchHandle, 1000);

  return (
    <div className="bg-white rounded-lg p-3 ">
      {/* <Tabs /> */}
      <div className="flex justify-between my-2">
        <p className="font-semibold text-[20px]">User Management</p>
        <div className="flex gap-2">
          {/* <input
            placeholder="Search User..."
            className="rounded-full border border-gray-400 ps-3"
            onChange={debounceHandle}
          /> */}
          <div className="bg-gray-50 px-2 rounded-lg flex items-center">
            <div className="w-6 h-6  relative overflow-hidden">
              <Image
                src={MagnifierIcon}
                alt="Search Icon"
                fill
                className="object-cover"
                sizes="24px"
              />
            </div>
            <input
              placeholder="Search Items..."
              className="rounded-lg outline-none bg-gray-50 ps-2 p-2"
              onChange={debounceHandle}
            />
          </div>
          {/* <div className="cursor-pointer planFilter w-fit flex border border-gray-400 rounded-full px-5 py-2">
            Plan :{" "}
            <span className="flex items-center font-semibold">
              All
              <FaChevronDown className="ms-1" />
            </span>{" "}
          </div>
          <div className="cursor-pointer relative planFilter w-fit bg-white border border-gray-400 rounded-full px-5 py-2">

            <div className="flex z-20">
              Status :
              <span className="flex items-center font-semibold">
                All <FaChevronDown className="ms-1" />
              </span>
            </div>
          </div> */}

          <div className="statusFilter"></div>
        </div>
      </div>
      <table className="w-full border border-[#EFF1F4] rounded-lg border-separate table-fixed">
        <thead className="border-b border-[#EFF1F4]">
          <tr>
            {/* <th className="px-3 py-3 font-medium text-[14px] bg-[#FAFBFB] w-[5%] text-center">
              <input type="checkbox" />
            </th> */}
            <th className="px-3 py-3 font-medium text-[14px] bg-[#FAFBFB] w-[30%] text-left">
              Customer
            </th>
            <th className="py-3 font-medium text-[14px] w-[13%] bg-[#FAFBFB] text-center">
              Book Listed
            </th>
            <th className="py-3 font-medium text-[14px] w-[13%] bg-[#FAFBFB] text-center">
              Regd. Date
            </th>
            <th className="py-3 font-medium text-[14px] w-[13%] bg-[#FAFBFB] text-center">
              Plan Name
            </th>
            <th className="py-3 font-medium text-[14px] w-[13%] bg-[#FAFBFB] text-center">
              Status
            </th>
            <th className="py-3 font-medium text-[14px] w-[8%] bg-[#FAFBFB] text-center">
              Action
            </th>
          </tr>
        </thead>
        <tbody>
          {!isLoading ? (
            membersList?.map((item, index) => (
              <tr key={index} className="bg-white">
                {/* <td className="border-b border-[#EFF1F4] px-3 py-3 bg-white text-center">
                  <input type="checkbox" />
                </td> */}
                <td className="border-b border-[#EFF1F4] px-3 py-3 bg-white">
                  <div
                    onClick={() => router.push(`/user-detail?id=${item._id}`)}
                    className="flex items-center cursor-pointer"
                  >
                    <img
                      className="w-10 h-10 rounded-full mr-3 shrink-0"
                      src={
                        item.image ||
                        createInitialsAvatar(
                          `${item.firstName || ""} ${
                            item.lastName || ""
                          }`.trim(),
                          {size: 40}
                        )
                      }
                      alt={`${item.firstName} ${item.lastName}`}
                    />
                    <div className=" min-w-0">
                      <div className="font-medium text-[14px] truncate">
                        {item?.firstName} {item?.lastName}
                      </div>
                      <div className="text-gray-300 text-[13px] truncate">
                        {item?.email}
                      </div>
                    </div>
                  </div>
                </td>
                <td className="border-b border-[#EFF1F4] py-3 text-[14px] bg-white text-center">
                  {item?.allListingItems?.length}
                </td>
                <td className="border-b border-[#EFF1F4] py-3 text-[14px] bg-white text-center">
                  {item?.createdAt || "NA"}
                </td>
                <td className="border-b border-[#EFF1F4] py-3 text-[14px] bg-white text-center">
                  {item?.planDoc?.planName}
                </td>
                <td className="border-b border-[#EFF1F4] py-3 text-[14px] bg-white text-center">
                  {ActiveInactive(item?.status, item)}
                </td>
                <td className="border-b border-[#EFF1F4] py-3 text-[14px] bg-white text-center">
                  <ActionBox data={item} funcAfterAction={getMembersFunc} />
                </td>
              </tr>
            ))
          ) : (
            <tr>
              <td
                colSpan={7}
                className="border-b border-[#EFF1F4] px-3 py-4 bg-white text-center"
              >
                ...Loading
              </td>
            </tr>
          )}
        </tbody>
      </table>
      <Pagination
        setPageSize={setPageSize}
        setCurrentPage={setCurrentPage}
        getListing={getMembersFunc}
        currentPage={currentPage}
        totalPages={totalPages}
        totalItems={totalItems}
        pageSize={pageSize}
      />
    </div>
  );
}

export default Member;
