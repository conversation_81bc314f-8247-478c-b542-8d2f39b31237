import { toast } from "react-toastify";
// import history from "./history";
import { removeToken } from "../utils/utils";


export const axiosErrorHandler = (error, action, checkUnauthorized = true) => {

    console.log("error",error)
    const requestStatus = error?.request?.status;
    const responseStatus = error?.response?.status;
    const dataStatus = error?.data?.statusCode;

    // Only log out on true 401 Unauthorized from response
    if (responseStatus === 401) {
        removeToken();
        if (typeof window !== 'undefined' && window.location) {
            window.location.href = "/login";
        }
        return;
    }
    if (dataStatus === 404 || responseStatus === 404 || requestStatus === 404) {
        if (Array.isArray(error?.response?.data?.error) || Array?.isArray(error?.data?.error)) error?.response?.data?.error?.map(er => toast.error(er.messages)) || error?.data?.error?.map(er => toast.error(er))
        else
            toast.error(
                error?.response?.data?.error || error?.response?.data?.error || error?.data?.error,
            );
    }
    if (dataStatus === 400 || responseStatus === 400 || requestStatus === 400 || requestStatus === 502) {
        console.log("error log is", error)
        if (Array.isArray(error?.response?.data?.message) || Array?.isArray(error?.data?.message)) error?.response?.data?.message?.map(er => toast.error(er)) || error?.data?.message?.map(er => toast.error(er))
        else
            toast.error(
                error?.response?.data?.message || error?.response?.data?.data || error?.data?.message,
            );
    }
    if (
        checkUnauthorized &&
        (dataStatus === 409 || responseStatus === 409 || requestStatus === 409)
    ) {
        if (localStorage.getItem("token")) {
            toast.error(error?.response?.data?.message);
        }
    }

    if (action === "uploadImage") {
        if (dataStatus === 500 || responseStatus === 500 || requestStatus === 500) {
            if (localStorage.getItem("token")) {
                const message = error?.response?.data?.message;
                message && toast.error(message);
            } else history.push("/");
        }
    }

    if (error?.response) return error.response;
    else if (error?.request) return error.request;
    else return error?.message;
};