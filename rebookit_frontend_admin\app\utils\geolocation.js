"use client";

import { useEffect } from "react";

const LocationUpdater = () => {
    useEffect(() => {
        let watchId;

        const updateCoordinates = async (latitude, longitude) => {
            console.log("Location tracking:", latitude, longitude);

            // API call
            try {

            } catch (err) {
                console.error("Failed to update coordinates:", err);
            }
        };

        if (navigator.geolocation) {
            watchId = navigator.geolocation.watchPosition(
                (position) => {
                    const { latitude, longitude } = position.coords;
                    updateCoordinates(latitude, longitude);
                },
                (error) => {
                    console.error("Geolocation error:", error);
                },
                {
                    enableHighAccuracy: true,
                    timeout: 10000,
                    maximumAge: 0,
                }
            );
        } else {
            console.warn("Geolocation is not supported.");
        }

        return () => {
            if (watchId !== undefined) {
                navigator.geolocation.clearWatch(watchId);
            }
        };
    }, []);

    return null;
};

export default LocationUpdater;
