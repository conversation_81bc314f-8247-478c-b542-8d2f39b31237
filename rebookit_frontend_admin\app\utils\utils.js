export const setToken = (token) => {
  localStorage.setItem("token", token);
};

export const getToken = () => {
  return localStorage.getItem("token");
};

export const removeToken = () => {
  return localStorage.removeItem("token");
};

export const randomColorPickHandler = () => {
  const colors = [
    "bg-slate-200",
    "bg-red-200",
    "bg-orange-200",
    "bg-amber-200",
    "bg-yellow-200",
    "bg-lime-200",
    "bg-green-200",
    "bg-emerald-200",
    "bg-teal-200",
    "bg-cyan-200",
    "bg-sky-200",
    "bg-blue-200",
    "bg-indigo-200",
    "bg-violet-200",
    "bg-purple-200",
    "bg-fuchsia-200",
    "bg-pink-200",
    "bg-rose-200",
  ];

  return colors[Math.floor(Math.random() * colors.length)];
};

export const shortNameHandler = (val) => {
  if (!val || val === "undefined") return val;
  let arr = val?.split(" ");
  let result =
    (arr[0][0] === undefined ? "" : arr[0][0]) +
    (arr[1] ? (arr[1][0] === undefined ? "" : arr[1][0]) : "");
  return result;
};
export const debouncFunc = (func, delay) => {
  let interval = null;
  return (args) => {
    clearTimeout(interval);
    interval = setTimeout(() => {
      func(args);
    }, delay);
  };
};

export const userDataFromLocal = () => {
  if (typeof window !== "undefined") {
    return JSON.parse(localStorage.getItem("userData"));
  }
  return null;
};
