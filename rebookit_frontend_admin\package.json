{"name": "frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start -p 3001", "lint": "next lint"}, "dependencies": {"@reduxjs/toolkit": "^2.6.1", "@vis.gl/react-google-maps": "^1.5.2", "apexcharts": "^5.2.0", "axios": "^1.9.0", "moment": "^2.30.1", "next": "15.3.0", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-confetti": "^6.4.0", "react-dom": "^19.0.0", "react-fast-marquee": "^1.6.5", "react-hook-form": "^7.56.0", "react-icons": "^5.5.0", "react-loader-spinner": "^6.1.6", "react-loading-skeleton": "^3.5.0", "react-paginate": "^8.3.0", "react-redux": "^9.2.0", "react-select": "^5.10.1", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "recharts": "^3.1.0", "redux": "^5.0.1", "redux-persist": "^6.0.0", "sass": "^1.86.3", "slick-carousel": "^1.8.1"}, "devDependencies": {"@svgr/webpack": "^8.1.0", "@tailwindcss/postcss": "^4", "tailwindcss": "^4"}}